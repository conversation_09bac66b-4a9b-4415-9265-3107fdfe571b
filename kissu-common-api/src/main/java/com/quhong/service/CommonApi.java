package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.common.data.*;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.service.AsyncHttpClient;
import com.quhong.common.service.HttpOperation;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.enums.ApiHttpCode;
import com.quhong.exceptions.WebException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/12 11:39
 * @description CommonApi
 */
@Getter
@Slf4j
public class CommonApi {

    @Value("${api.path}")
    private String apiPath;

    //是否是容器启动，默认true
    @Value("${api.isContainer:true}")
    private boolean isContainer = true;

    @Resource
    protected HttpOperation httpOperation;

    @Resource
    protected AsyncHttpClient asyncHttpClient;

    //服务名，由子类传递进来
    private final String serviceName;

    //最终路径
    private String finalUrl;


    protected Map<String, String> headerMap;


    @PostConstruct
    public void init() {
        headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");

        if (isContainer && ServerConfiguration.isProduct()) {//测试环境临时走apiPath
            //容器启动时，走k8s内部调用
            finalUrl = String.format("http://%s:8080", serviceName);
        } else {
            //非容器启动时，走外部调用
            finalUrl = apiPath;
        }
    }

    /**
     * 构造函数
     *
     * @param serviceName 子类具体服务名
     */
    public CommonApi(String serviceName) {
        this.serviceName = serviceName;
    }


    public <V> V sendGet(String path, Class<V> VClass) {
        return sendGet(path, VClass, false);
    }

    protected <V> V sendGet(String path, Class<V> VClass, boolean isReturnBody) {
        String url = this.finalUrl + "/" + path;
        return get(url, VClass, isReturnBody, null, null);
    }

    protected <V> V get(String url, Class<V> VClass, boolean isReturnBody, Object params, Integer readTimeout) {

        Map<String, Object> paramsMap;
        if (params == null) {
            paramsMap = null;
        } else if (params instanceof Map) {
            // jsonObject也是map的子类
            paramsMap = (Map<String, Object>) params;
        } else {
            paramsMap = (JSONObject) JSON.toJSON(params);
        }
        HttpResponseData<String> responseData = httpOperation.get(HttpRequestDTO.newBuilder().url(url).params(paramsMap).readTimeout(readTimeout).build());
        if (responseData.isError()) {
            throw new WebException(null, HttpCode.createHttpCode(HttpCode.HTTP_STATUS_ERROR, true, "status=" + responseData.getStatus()));
        }
        return createResult(url, VClass, new BaseHttpData(), responseData, isReturnBody);
    }

    public <V> V sendRequest(String path, BaseHttpData dto, Class<V> VClass) {
        if (dto == null) {
            dto = new HttpEnvData();
        }
        return post(path, dto, VClass, false, null);
    }

    public <V> V sendRequest(String path, BaseHttpData dto, Class<V> VClass, boolean isReturnBody) {
        if (dto == null) {
            dto = new HttpEnvData();
        }
        return post(path, dto, VClass, isReturnBody, null);
    }

    public <V> V sendRequest(String path, BaseHttpData dto, Class<V> VClass, boolean isReturnBody, Integer readTimeout) {
        if (dto == null) {
            dto = new HttpEnvData();
        }
        return post(path, dto, VClass, isReturnBody, readTimeout);
    }

    protected <T> T post(String url, BaseHttpData dto, Class<T> VClass, boolean isReturnBody, Integer readTimeout) {
        url = this.finalUrl + "/" + url;
        if (dto == null) {
            dto = new BaseHttpData();
        }
        HttpResponseData<String> responseData = httpOperation.post(HttpRequestDTO.newBuilder().url(url).readTimeout(readTimeout).headers(headerMap).data(dto).build());
        if (responseData.isError()) {
            log.error("response error. url={} dto={} status={}", url, dto, responseData.getStatus());
            throw new WebException(dto, HttpCode.createHttpCode(HttpCode.HTTP_STATUS_ERROR, true, "status=" + responseData.getStatus()));
        }
        return createResult(url, VClass, dto, responseData, isReturnBody);
    }

    protected <V> V createResult(String url, Class<V> VClass, BaseHttpData dto, HttpResponseData<String> responseData, boolean isReturnBody) {
        String body = responseData.getBody();
        if (StringUtils.isEmpty(body)) {
            log.error("request error, status={} requestId={}", responseData.getStatus(), dto.getRequestId());
            throw new WebException(null, ApiHttpCode.API_ERROR);
        }
//        logger.info("request result. body={} requestId={}", body, dto.getRequestId());
        JSONObject resultObj = JSON.parseObject(body);
        ApiResult<String> result = new ApiResult<>();
        if (resultObj.get("status") != null) {
            // 兼容status,message,data
            result.setCode(resultObj.getIntValue("status"));
            result.setMsg(resultObj.getString("message"));
            result.setData(resultObj.getString("data"));
        } else {
            if (resultObj.get("code") instanceof JSONObject) {
                // 兼容 HttpCode,data形式
                JSONObject codeData = resultObj.getJSONObject("code");
                result.setCode(codeData.getIntValue("code"));
                result.setMsg(codeData.getString("msg"));
                result.setData(resultObj.getString("data"));
            } else {
                // 兼容code,msg,data形式
                result.setCode(resultObj.getIntValue("code"));
                result.setMsg(resultObj.getString("msg"));
                result.setData(resultObj.getString("data"));
            }
        }
        if (result.isError()) {
            log.error("request error, code={} msg={} url={} requestId={}", result.getCode(), result.getMsg(), url, dto.getRequestId());
            throw new WebException(dto, result.getHttpCode());
        }

        // body 整个返回
        if (isReturnBody) {
            if (VClass == String.class) {
                return (V) body;
            } else {
                return JSON.parseObject(body, VClass);
            }
        }

        if (result.getData() == null) {
            return null;
        }
        if (VClass == String.class || VClass == Integer.class || VClass == Long.class || VClass == Boolean.class) {
            return (V) result.getData();
        } else {
            return JSON.parseObject(result.getData(), VClass);
        }
    }

    /**
     * @param path        路径
     * @param dto         请求dto
     * @param readTimeout 读取超时，单位 毫秒
     * @param callBack
     * @return 返回指定类型
     */
    public <V> void asyncPostRequest(String path, BaseHttpData dto, Class<V> VClass, boolean isReturnBody, Integer readTimeout, ApiCallBack<V> callBack) {
        if (dto == null) {
            dto = new HttpEnvData();
        }
        asyncPost(apiPath + "/" + path, dto, VClass, isReturnBody, readTimeout, callBack);
    }

    /**
     * 异步的post请求
     *
     * @param url
     * @param dto
     * @param VClass
     * @param isReturnBody
     * @param readTimeout
     * @param callBack
     */
    protected <V> void asyncPost(String url, BaseHttpData dto, Class<V> VClass, boolean isReturnBody, Integer readTimeout, ApiCallBack<V> callBack) {
        asyncHttpClient.post(HttpRequestDTO.newBuilder().url(url).readTimeout(readTimeout).headers(headerMap).data(dto).build(), responseData -> {
            if (responseData.isError()) {
                log.error("response data error. status={} body={}", responseData.getStatus(), responseData.getBody());
                callBack.failed(new WebException(dto, HttpCode.createHttpCode(HttpCode.HTTP_STATUS_ERROR, true, "status=" + responseData.getStatus())));
            }
            try {
                V data = createResult(url, VClass, dto, responseData, isReturnBody);
                callBack.complete(data);
            } catch (WebException e) {
                log.error("response data error. httpCode={}", e.getHttpCode());
                callBack.failed(e);
            }
        });
    }


    public String getServerName() {
        return serviceName;
    }


    /**
     * 测试接口,用于测试
     *
     * @return
     */
    public String testApi() {
        String url = finalUrl + "/inner/testApi/receive";
        HashMap<String, Object> map = new HashMap<>();
        HttpResponseData<String> responseData = httpOperation.get(HttpRequestDTO.newBuilder().url(url).params(map).readTimeout(3000).build());
        return responseData.getBody();
    }


}
