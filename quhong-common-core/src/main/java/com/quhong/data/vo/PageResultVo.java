package com.quhong.data.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分页结果视图对象
 * 
 * <AUTHOR>
 * @since 2024/4/15
 */
@Data
@NoArgsConstructor
public class PageResultVo<T> {
    
    /**
     * 总记录数
     */
    private long total;
    
    /**
     * 当前页数据
     */
    private T data;
    
    /**
     * 构造函数
     * 
     * @param total 总记录数
     * @param data 当前页数据
     */
    public PageResultVo(long total, T data) {
        this.total = total;
        this.data = data;
    }
} 