package com.quhong.core.date;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjuster;
import java.time.temporal.TemporalAdjusters;
@Slf4j
public class DayTimeSupport {
    public static final DayTimeSupport UTC = new DayTimeSupport(ZoneOffset.UTC);
    public static final DayTimeSupport BEIJING = new DayTimeSupport(ZoneOffset.of("+8"));
    public static final DayTimeSupport INDIAN = new DayTimeSupport(ZoneOffset.of("+05:30"));

    public static final TemporalAdjuster currWeekMondayAdjuster = TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY);
    public static final TemporalAdjuster currWeekSundayAdjuster = TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY);

    private static DateTimeFormatter yyyy_mm_dd_hh_mm_ss = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static long strParseToSec(String time, String zoneOffset, String dateFormat) {
        LocalDateTime parse = LocalDateTime.parse(time, DateTimeFormatter.ofPattern(dateFormat));
        return parse.toEpochSecond(ZoneOffset.of(zoneOffset));
    }

    public static String secFormatStr(Long time, String zoneOffset, String dateFormat) {
        try {
            return LocalDateTime.ofEpochSecond(time, 0, ZoneOffset.of(zoneOffset))
                    .format(DateTimeFormatter.ofPattern(dateFormat));
        } catch (Exception e) {
            log.error("error to dateFormat, msg={}", e.getMessage(), e);
            return "<format error>";
        }
    }

    public static DayTimeSupport init(String offsetId) {
        return new DayTimeSupport(ZoneOffset.of(offsetId));
    }

    public static LocalDateTime getWeekDay(int minusWeeks, TemporalAdjuster adjuster, LocalTime localTime) {
        return getWeekDay(minusWeeks, adjuster, localTime, null);
    }

    /**
     * @param minusWeeks 当前周减去的周数
     * @param adjuster
     * @param localTime
     * @return
     */
    public static LocalDateTime getWeekDay(int minusWeeks, TemporalAdjuster adjuster, LocalTime localTime, String zoneOffset) {
        ZoneOffset finalZoneOffset = ZoneOffset.UTC;
        if (!StringUtils.isEmpty(zoneOffset)) {
            finalZoneOffset = ZoneOffset.of(zoneOffset);
        }
        return LocalDateTime.now(finalZoneOffset)
                .minusWeeks(minusWeeks)
                .with(adjuster)
                .with(localTime);
    }

    public static LocalDateTime getWeekDay(Long currSecond, int minusWeeks, TemporalAdjuster adjuster, LocalTime localTime) {
        return getWeekDay(currSecond, minusWeeks, adjuster, localTime, null);
    }

    /**
     * @param currSecond 当前秒级时间戳
     * @param minusWeeks 当前周减去的周数
     * @param adjuster
     * @param localTime
     * @return
     */
    public static LocalDateTime getWeekDay(Long currSecond, int minusWeeks, TemporalAdjuster adjuster, LocalTime localTime, String zoneOffset) {
        ZoneOffset finalZoneOffset = ZoneOffset.UTC;
        if (!StringUtils.isEmpty(zoneOffset)) {
            finalZoneOffset = ZoneOffset.of(zoneOffset);
        }
        return LocalDateTime.ofEpochSecond(currSecond, 0, finalZoneOffset)
                .minusWeeks(minusWeeks)
                .with(adjuster)
                .with(localTime);
    }

    /**
     * 通过开始时间戳和结束时间戳获取自然周数
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param zoneOffset 时区
     * @return int 自然周数
     */
    public static int getNaturalWeeks(long startTime, long endTime, String zoneOffset) {
        LocalDateTime startWeek = getWeekDay(startTime, 0, TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY), LocalTime.MIN, zoneOffset);
        LocalDateTime endWeek = getWeekDay(endTime, 0, TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY), LocalTime.MAX, zoneOffset);
        return (int) ((endWeek.toEpochSecond(ZoneOffset.of(zoneOffset)) - startWeek.toEpochSecond(ZoneOffset.of(zoneOffset)) + 1) / 604800);
    }

    /**
     * 解析，默认格式 yyyy-MM-dd HH:mm:ss
     *
     * @param time
     * @return
     */
    public static LocalDateTime parse(String time) {
        return LocalDateTime.parse(time, yyyy_mm_dd_hh_mm_ss);
    }

    /**
     * 格式化，默认格式 yyyy-MM-dd HH:mm:ss
     *
     * @param time
     * @return
     */
    public static String format(LocalDateTime time) {
        return time.format(yyyy_mm_dd_hh_mm_ss);
    }

    public static Duration getDuration(LocalDateTime start, LocalDateTime end) {
        return Duration.between(start, end);
    }

    private ZoneOffset zoneOffset;

    public DayTimeSupport(ZoneOffset zoneOffset) {
        this.zoneOffset = zoneOffset;
    }

    /**
     * 获取当前time
     *
     * @return
     */
    public LocalDateTime getNow() {
        return LocalDateTime.now(zoneOffset);
    }

    public LocalDateTime fromTimeSeconds(long seconds) {
        return LocalDateTime.ofEpochSecond(seconds, 0, zoneOffset);
    }

    public long getTimeSeconds(LocalDateTime time) {
        return time.toEpochSecond(zoneOffset);
    }

    public long getTimeMillis(LocalDateTime time) {
        return time.toEpochSecond(zoneOffset) * 1000;
    }
}
