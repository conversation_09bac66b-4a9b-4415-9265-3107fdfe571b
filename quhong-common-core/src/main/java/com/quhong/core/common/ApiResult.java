package com.quhong.core.common;

import lombok.Data;

/**
 * 统一响应类
 *
 * <AUTHOR>
 * @since 2024/4/15
 */
@Data
public class ApiResult<T> {

    /**
     * 成功状态码
     */
    public static final int SUCCESS_CODE = 200;

    /**
     * 失败状态码
     */
    public static final int ERROR_CODE = 500;

    /**
     * 状态码
     */
    private int code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 默认构造函数
     */
    public ApiResult() {
    }

    /**
     * 构造函数
     *
     * @param code 状态码
     * @param message 响应消息
     * @param data 响应数据
     */
    public ApiResult(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 创建成功响应
     *
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResult<T> success() {
        return new ApiResult<>(SUCCESS_CODE, "操作成功", null);
    }

    /**
     * 创建带数据的成功响应
     *
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResult<T> success(T data) {
        return new ApiResult<>(SUCCESS_CODE, "操作成功", data);
    }

    /**
     * 创建带消息的成功响应
     *
     * @param message 响应消息
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResult<T> success(String message) {
        return new ApiResult<>(SUCCESS_CODE, message, null);
    }

    /**
     * 创建带消息和数据的成功响应
     *
     * @param message 响应消息
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResult<T> success(String message, T data) {
        return new ApiResult<>(SUCCESS_CODE, message, data);
    }

    /**
     * 创建失败响应
     *
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ApiResult<T> error() {
        return new ApiResult<>(ERROR_CODE, "操作失败", null);
    }

    /**
     * 创建带消息的失败响应
     *
     * @param message 响应消息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ApiResult<T> error(String message) {
        return new ApiResult<>(ERROR_CODE, message, null);
    }

    /**
     * 创建带状态码和消息的失败响应
     *
     * @param code 状态码
     * @param message 响应消息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ApiResult<T> error(int code, String message) {
        return new ApiResult<>(code, message, null);
    }

    /**
     * 创建带状态码、消息和数据的失败响应
     *
     * @param code 状态码
     * @param message 响应消息
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ApiResult<T> error(int code, String message, T data) {
        return new ApiResult<>(code, message, data);
    }

    /**
     * 判断是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return this.code == SUCCESS_CODE;
    }
}
