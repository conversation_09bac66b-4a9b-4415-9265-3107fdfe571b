package com.quhong.core.constant;

public interface WarnName {
    /**
     * 通常的告警
     */
    String COMMON = "common";
    /**
     * 系统告警，最高等级
     */
    String SYSTEM = "java_system";
    /**
     * Hotchat未成年告警
     */
    String HOTCHAT_UNDER_AGE_MONITOR = "hotchat_under_age_monitor";
    /**
     * Tikko未成年告警
     */
    String TIKKO_UNDER_AGE_MONITOR = "tikko_under_age_monitor";
    /**
     * 服务自调用告警
     */
    String SERVER_CALL_SELF = "server_call_self_monitor";
    /**
     * sql告警
     */
    String SQL = "sql";
    /**
     * 榜单奖励通知
     */
    String RANK_REWARD_NOTICE = "rank_reward_notice";

    /**
     * 字典配置问题告警
     */
    String DICTIONARY_CONFIG_QUESTION = "dictionary_config_question";
    /**
     * 活动榜单通知
     */
    String ACTIVITY_RANK_NOTICE = "activity_rank_notice";
    /**
     * 方法监控通知
     */
    String METHOD_MONITOR_NOTICE = "method_monitor_notice";
    /**
     * 活动成本超预算通知
     */
    String EVENT_COST_OVERFLOW = "event_cost_overflow";
    /**
     * 注册ip标红账号告警
     */
    String DANGER_ACTOR_MONITOR = "danger_actor_monitor";
    /**
     * 审核版本不设置审核人员，但告警
     */
    String PLAY_GAME_COIN_EXCEPTION = "play_game_coin_exception";
    /**
     * admin封号告警
     */
    String BLOCK_ACC_BY_OPERATOR = "block_acc_by_operator";
    /**
     * 变为审核人员告警
     */
    String BECOME_REVIEWER = "become_reviewer";
    /**
     * 金币突增告警
     */
    String CURRENCY_CHANGE_UPRUSH = "currency_change_uprush";
    /**
     * 金币突增告警
     */
    String REVIEWER_VER_MONITOR_NOT_BECOME_REVIEWER = "reviewer_ver_monitor_not_become_reviewer";
    /**
     * 游戏邀请风控邀请
     */
    String GAME_INVITE_RISK_CONTROL = "game_invite_risk_control";
    /**
     * BigQuery预警
     */
    String BIG_QUERY_WARN = "big_query_warn";
    /**
     * 调用次数告警
     */
    String THIRD_API_CALL = "third_api_call";
    /**
     * ios下架与配置不一致通知
     */
    String IOS_REMOVED_NOTIFY = "ios_removed_notify";
    /**
     * 三方支付
     */
    String PAYMENT = "payment";
    /**
     * checkBundleId
     */
    String CHECK_BUNDLEID = "checkBundleId";
    /**
     * 提醒客户端的告警
     */
    String IOS_CLIENT_WARN = "ios_client_warn";
    /**
     * 邀请风控告警
     */
    String INVITE_RISK_CONTROL_NOTICE = "invite_risk_control_notice";
    /**
     * ssl过期告警
     */
    String SSL_EXPIRATION = "ssl_expiration";
    /**
     * 单包充值预警群
     */
    String CHANNEL_DAILY_RECHARGE_MONITOR = "channel_daily_recharge_monitor";
    /**
     * kissu审核人员预警
     */
    String KISSU_REVIEWER_ALARM = "kissu_reviewer_alarm";
    /**
     * 提醒安卓客户端告警
     */
    String ANDROID_CLIENT_WARN = "android_client_warn";
    /**
     * 登录失败告警
     */
    String LOGIN_FAILED = "login_failed";
    /**
     * 人脸认证警示
     */
    String FACE_AUTH_ALARM = "face_auth_alarm";
    /**
     * ip锁定告警
     */
    String LOCK_IP_WARN = "lock_ip_warn";
    /**
     * 测试所需数据报告
     */
    String SEND_TO_TEST = "send_to_test";
    /**
     * 币商充值告警
     */
    String COIN_DEALERS_MGR_COMMON_EXCEPTION = "coin_dealers_mgr_common_exception";
    /**
     * 注册无来源告警
     */
    String REGISTER_NO_SOURCE_MONITOR = "register_no_source_monitor";

    /**
     * 通话无截图告警
     */
    String CALL_SNAP_NOTICE = "call_snap_notice";

    /**
     * report告警
     */
    String REPORT = "report";

    /**
     * 代理解散告警
     */
    String AGENT_DISBAND_NOTICE = "agent_disband_notice";

    /**
     * 货币阈值告警
     */
    String COIN_THRESHOLD_ALARM = "coin_threshold_alarm";


    /**
     * 应用功能封禁解封通知
     */
    String BAN_FUNCTION_NOTICE = "ban_function_notice";
}
