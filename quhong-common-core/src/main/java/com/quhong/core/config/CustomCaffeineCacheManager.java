package com.quhong.core.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
/**
 * 动态过期时间的Caffeine缓存管理器
 * 支持通过缓存名称配置不同的过期时间
 * <AUTHOR>
 */
@Slf4j
public class CustomCaffeineCacheManager implements CacheManager {

    /**
     * 缓存实例映射，使用线程安全的ConcurrentHashMap
     */
    private final Map<String, Cache> cacheMap = new ConcurrentHashMap<>();

    /**
     * 缓存配置属性
     */
    private final CacheProperties cacheProperties;

    /**
     * 构造函数
     */
    public CustomCaffeineCacheManager() {
        this.cacheProperties = new CacheProperties();
    }

    /**
     * 获取缓存实例
     * 如果缓存不存在，则创建新的缓存实例
     *
     * @param name 缓存名称，支持格式：cacheName#expireSeconds
     * @return Cache 缓存实例
     */
    @Override
    public Cache getCache(String name) {
        return cacheMap.computeIfAbsent(name, this::createCache);
    }

    /**
     * 获取所有缓存名称
     *
     * @return Collection<String> 缓存名称集合
     */
    @Override
    public Collection<String> getCacheNames() {
        return cacheMap.keySet();
    }

    /**
     * 创建缓存实例
     * 解析缓存名称中的过期时间配置，并创建对应的Caffeine缓存
     *
     * @param name 缓存名称（格式：cacheName#expireSeconds）
     * @return Cache 创建的缓存实例
     */
    private Cache createCache(String name) {
        log.info("开始创建缓存: {}", name);

        // 解析缓存配置
        CacheConfig config = parseCacheConfig(name);

        // 构建Caffeine缓存实例
        com.github.benmanes.caffeine.cache.Cache<Object, Object> caffeineCache = buildCaffeineCache(config);

        // 创建并返回Spring Cache实例
        Cache cache = new CaffeineCache(name, caffeineCache);
        log.info("缓存创建完成: {}, 过期时间: {}秒", config.getName(), config.getExpireSeconds());

        return cache;
    }

    /**
     * 解析缓存配置
     * 从缓存名称中解析出实际的缓存名和过期时间
     *
     * @param name 原始缓存名称
     * @return CacheConfig 解析后的缓存配置
     */
    private CacheConfig parseCacheConfig(String name) {
        CacheConfig config = new CacheConfig();
        config.setName(name);
        config.setExpireSeconds(cacheProperties.getDefaultExpireSeconds());

        if (StringUtils.hasText(name) && name.contains("#")) {
            String[] parts = name.split("#", 2);
            if (parts.length == 2) {
                config.setName(parts[0]);
                try {
                    int expireSeconds = Integer.parseInt(parts[1]);
                    // 限制最大过期时间
                    expireSeconds = Math.min(expireSeconds,
                            cacheProperties.getMaxExpireMinutes() * 60);
                    config.setExpireSeconds(expireSeconds);
                    log.info("解析到缓存过期时间: {}秒, 缓存名称: {}", expireSeconds, parts[0]);
                } catch (NumberFormatException e) {
                    log.warn("无法解析过期时间: {}, 将使用默认值: {}秒", parts[1], config.getExpireSeconds());
                }
            }
        }

        return config;
    }

    /**
     * 构建Caffeine缓存实例
     *
     * @param config 缓存配置
     * @return Caffeine缓存实例
     */
    private com.github.benmanes.caffeine.cache.Cache<Object, Object> buildCaffeineCache(CacheConfig config) {
        return Caffeine.newBuilder()
                .expireAfterWrite(config.getExpireSeconds(), TimeUnit.SECONDS)
                .initialCapacity(cacheProperties.getInitialCapacity())
                .maximumSize(cacheProperties.getMaximumSize())
                .recordStats()
                .build();
    }

    /**
     * 缓存配置数据类
     */
    @Data
    protected static class CacheConfig {
        /**
         * 缓存名称
         */
        private String name;

        /**
         * 过期时间（秒）
         */
        private int expireSeconds;
    }

    /**
     * 缓存属性配置类
     */
    @Data
    protected static class CacheProperties {
        /**
         * 默认过期时间（秒）
         */
        private int defaultExpireSeconds = 30;

        /**
         * 最大过期时间（分钟）
         */
        private int maxExpireMinutes = 30;

        /**
         * 初始容量
         */
        private int initialCapacity = 100;

        /**
         * 最大容量
         */
        private int maximumSize = 10000;
    }
}
