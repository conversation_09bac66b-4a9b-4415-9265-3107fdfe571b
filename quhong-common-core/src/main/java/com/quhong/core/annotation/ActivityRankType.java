package com.quhong.core.annotation;

import java.lang.annotation.*;

/**
 * 活动榜单类型
 * <AUTHOR>
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ActivityRankType {
    int value();
    /**
     * 礼物数量榜单
     */

    int GIFT_COUNT_RANK = 1;
    /**
     * 营收榜单
     */

    int INCOME_RANK = 2;
    /**
     * 幸运用户活动榜单
     */

    int LUCKY_PERSON = 3;
    /**
     * 礼物价值榜单
     */
    int GIFT_PRICE_RANK = 4;
    /**
     * 排灯节活动专用榜单
     */
    int DIWALI_RANK = 5;
    /**
     * 周星活动榜单（v2）
     */
    int WEEK_STAR_V2_RANK = 6;
    /**
     * 情人节活动榜单
     */
    int LOVER_GIFT_PRICE_RANK = 7;
    /**
     * 射门活动榜单
     */
    int SHOOT_GIFT_PRICE_RANK = 8;
    /**
     * 射门活动v2410榜单
     */
    int SHOOT_V2410_GIFT_PRICE_RANK = 9;
    /**
     * 排灯节 2410榜单
     */
    int DIWALI_2410_RANK = 10;

    //活动模板
    /**
     * 币商充值金币
     */
    @ConstantFiledDesc(name = "币商充值金币")
    int COIN_MERCHANT_RECHARGE = 1001;
    /**
     * 在线充值金币
     */
    @ConstantFiledDesc(name = "在线充值金币")
    int ONLINE_RECHARGE = 1002;
    /**
     * 钻石兑换金币
     */
    @ConstantFiledDesc(name = "钻石兑换金币")
    int DIAMOND_EXCHANGE_FOR_GOLD_COINS = 1003;
    /**
     * 直播间收礼获得钻石
     */
    @ConstantFiledDesc(name = "直播间收礼获得钻石(未指定礼物id代表所有)")
    int LIVE_ROOM_GAIN_DIAMOND = 1004;
    /**
     * 语聊房收礼获得钻石
     */
    @ConstantFiledDesc(name = "语聊房收礼获得钻石(未指定礼物id代表所有)")
    int CHAT_ROOM_GAIN_DIAMOND = 1005;
    /**
     * 收礼钻石总价值
     */
    @ConstantFiledDesc(name = "收礼钻石总价值")
    int RECEIVE_GIFT_DIAMOND = 1006;
    /**
     * 游戏分成钻石奖励
     */
    @ConstantFiledDesc(name = "游戏分成钻石奖励")
    int GAME_INVITE_DIAMOND = 1007;
    /**
     * 收到指定礼物个数(未指定礼物id代表所有)
     */
    @ConstantFiledDesc(name = "收到指定礼物个数(未指定礼物id代表所有)")
    int RECEIVED_GIFT_COUNT = 1008;
    /**
     * 新增关注用户数
     */
    @ConstantFiledDesc(name = "新增关注用户数")
    int NEW_FOLLOW_USER_COUNT = 1009;
    /**
     * 收到指定礼物金币价值(未指定礼物id代表所有)
     */
    @ConstantFiledDesc(name = "收到指定礼物价值(未指定礼物id代表所有)")
    int RECEIVED_GIFT_PRICE = 1010;
    /**
     * 送出指定礼物数量(未指定礼物id代表所有)
     */
    @ConstantFiledDesc(name = "送出指定礼物数量(未指定礼物id代表所有)")
    int SEND_GIFT_COUNT = 1011;
    /**
     * 送出指定礼物金币价值(未指定礼物id代表所有)
     */
    @ConstantFiledDesc(name = "送出指定礼物价值(未指定礼物id代表所有)")
    int SEND_GIFT_PRICE = 1012;
    /**
     * 直播间有效开播时长
     */
    @ConstantFiledDesc(name = "直播间有效开播时长")
    int LIVE_ROOM_EFFECTIVE_DURATION = 1013;
    /**
     * 语聊房有效开播时长(TODO 需要确定数据口径)
     */
//    @ConstantFiledDesc(name = "语聊房有效开播时长")
    int CHAT_ROOM_EFFECTIVE_DURATION = 1014;
    /**
     * 游戏消费（未指定游戏id代表所有）
     */
    @ConstantFiledDesc(name = "游戏消费（未指定游戏id代表所有）")
    int PLAY_GAME_CONSUMPTION = 1015;
    /**
     * 游戏赢取（未指定游戏id代表所有）
     */
    @ConstantFiledDesc(name = "游戏赢取（未指定游戏id代表所有）")
    int PLAY_GAME_WIN = 1016;
    /**
     * PK胜利次数
     */
    @ConstantFiledDesc(name = "PK胜利次数")
    int PK_WIN_COUNT = 1017;
    /**
     * pk总次数
     */
    @ConstantFiledDesc(name = "pk总次数")
    int PK_TOTAL_COUNT = 1018;
    /**
     * pk总收益
     */
    @ConstantFiledDesc(name = "pk总收益")
    int PK_TOTAL_GAIN = 1019;

    /**
     * 获取活动货币榜单（不可指定金币/钻石货币组件）
     */
    @ConstantFiledDesc(name = "获得活动货币榜单（金币/钻石货币组件不可用）")
    int EVENT_CURRENCY_GAIN_RANK = 1020;
    /**
     * 消耗活动货币榜单
     */
    @ConstantFiledDesc(name = "消耗活动货币榜单")
    int EVEN_CURRENCY_CONSUME_RANK = 1021;
}
