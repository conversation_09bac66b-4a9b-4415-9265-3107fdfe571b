package com.quhong.core.cache;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.queues.TaskQueue;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 异步刷新缓存映射
 * 
 * 类似 CacheMap，但使用 CacheKey 接口进行键管理
 * 支持异步刷新功能，当过期时间到达1/2时自动触发异步刷新
 * 
 * @param <K> 缓存键类型，必须实现 CacheKey 接口
 * @param <D> 缓存数据类型
 */
public class AsyncRefreshCacheMap<K extends CacheKey, D> extends TaskQueue {
    private static final Logger logger = LoggerFactory.getLogger(AsyncRefreshCacheMap.class);
    
    /**
     * 默认缓存时间，单位毫秒
     */
    protected static final long EXPIRE_TIME_DEFAULT = 60 * 1000L;
    protected static final int LOOP_INTERVAL_MILLS = 5 * 60 * 1000;
    protected static final int TICK_SIZE = 100;
    
    /**
     * 过期时间，单位毫秒
     */
    protected long expireIntervalMillisecond;
    
    protected ConcurrentMap<Object, AsyncRefreshCacheObject<D>> cacheMap;
    protected boolean isStart = false;
    
    /**
     * 数据获取函数，用于异步刷新时获取新数据
     */
    protected Function<K, D> dataProvider;

    public AsyncRefreshCacheMap() {
        this(EXPIRE_TIME_DEFAULT);
    }

    public AsyncRefreshCacheMap(long expireIntervalMillisecond) {
        this.cacheMap = new ConcurrentHashMap<>();
        this.expireIntervalMillisecond = expireIntervalMillisecond;
        if (this.expireIntervalMillisecond <= 0) {
            this.expireIntervalMillisecond = EXPIRE_TIME_DEFAULT;
        }
    }

    public AsyncRefreshCacheMap(long expireInterval, TimeUnit timeUnit) {
        this(timeUnit.toMillis(expireInterval));
    }

    public AsyncRefreshCacheMap(long expireIntervalMillisecond, Function<K, D> dataProvider) {
        this(expireIntervalMillisecond);
        this.dataProvider = dataProvider;
    }

    public AsyncRefreshCacheMap(long expireInterval, TimeUnit timeUnit, Function<K, D> dataProvider) {
        this(timeUnit.toMillis(expireInterval), dataProvider);
    }

    /**
     * 需要定时清除数据，才需要start，规模不大，不需要
     * 在获取时，会判定
     */
    public void start() {
        if(isStart){
            return;
        }
        synchronized (this) {
            if(isStart){
                return;
            }
            isStart = true;
            // 五分钟循环一次
            TimerService.getService().addDelay(new LoopTask(this, LOOP_INTERVAL_MILLS) {
                @Override
                protected void execute() throws Exception {
                    onTick();
                }
            });
        }
    }

    public void cacheData(K key, D data) {
        start();
        cacheData(key, data, this.expireIntervalMillisecond);
    }

    /**
     * 设置单个key的过期时间
     *
     * @param key 缓存键
     * @param data 缓存数据
     * @param expireIntervalMillisecond 过期时间间隔
     */
    public void cacheData(K key, D data, long expireIntervalMillisecond) {
        if (key == null || key.cacheKey() == null) {
            logger.error("cache data error. key or key.cacheKey() is null. key={} data={}", key, data);
            return;
        }
        start();
        
        AsyncRefreshCacheObject<D> cacheObject = new AsyncRefreshCacheObject<>(expireIntervalMillisecond);
        if (dataProvider != null) {
            cacheObject.setDataProvider(() -> dataProvider.apply(key));
        }
        cacheObject.cacheData(data);
        cacheMap.put(key.cacheKey(), cacheObject);
    }

    public D getData(K key) {
        return getData(key, false);
    }

    /**
     * cache data is null. please use hasData and getData
     *
     * @param key 缓存键
     * @return 是否有数据
     */
    public boolean hasData(K key) {
        if (key == null || key.cacheKey() == null) {
            return false;
        }
        
        AsyncRefreshCacheObject<D> cacheObject = cacheMap.get(key.cacheKey());
        if (cacheObject == null) {
            return false;
        }
        
        if (cacheObject.isNearExpire()) {
            cacheMap.remove(key.cacheKey(), cacheObject);
            return false;
        }
        
        return cacheObject.hasData();
    }

    public D remove(K key) {
        if (key == null || key.cacheKey() == null) {
            logger.error("remove cache error. key or key.cacheKey() is null. key={}", key);
            return null;
        }
        
        AsyncRefreshCacheObject<D> cacheObject = cacheMap.remove(key.cacheKey());
        if (cacheObject != null) {
            return cacheObject.getData();
        }
        return null;
    }

    public D getData(K key, boolean extendExpireTime) {
        if (key == null || key.cacheKey() == null) {
            return null;
        }
        
        AsyncRefreshCacheObject<D> cacheObject = cacheMap.get(key.cacheKey());
        if (cacheObject == null) {
            return null;
        }
        
        if (cacheObject.isNearExpire()) {
            cacheMap.remove(key.cacheKey(), cacheObject);
            return null;
        }
        
        return cacheObject.getData(extendExpireTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        cacheMap.forEach((k, v) -> {
            sb.append("k=").append(k).append("->v=").append(v.getData()).append("\n");
        });
        return sb.toString();
    }

    protected void onTick() {
        if (cacheMap.size() < TICK_SIZE) {
            return;
        }
        for (AsyncRefreshCacheObject<D> cacheObject : cacheMap.values()) {
            if (cacheObject.isExpire()) {
                // 找到对应的key并移除
                cacheMap.entrySet().removeIf(entry -> entry.getValue() == cacheObject);
                cacheObject.clear();
            }
        }
    }
    
    public int getSize() {
        return this.cacheMap.size();
    }

    public void clear() {
        this.cacheMap = new ConcurrentHashMap<>();
    }

    /**
     * 设置数据提供者，用于异步刷新
     *
     * @param dataProvider 数据获取函数
     */
    public void setDataProvider(Function<K, D> dataProvider) {
        this.dataProvider = dataProvider;
    }
}
