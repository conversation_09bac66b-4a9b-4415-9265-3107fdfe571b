package com.quhong.core.cache;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Supplier;

/**
 * 异步刷新缓存对象
 * 
 * 参考 CacheObject 实现，添加异步刷新功能
 * 当过期时间到达1/2时，自动触发异步刷新
 * 
 * @param <D> 缓存数据类型
 */
public class AsyncRefreshCacheObject<D> {
    private static final Logger logger = LoggerFactory.getLogger(AsyncRefreshCacheObject.class);
    protected static final long EXPIRE_TIME_DEFAULT = 60 * 1000L;

    protected D data;
    protected long expireTimeMillisecond;
    protected long expireIntervalMillisecond;
    protected long closeToExpireMillisecond = 1000L; //接近过期时间为1s
    protected volatile boolean asyncRefreshTriggered = false; //是否已触发异步刷新
    
    /**
     * 数据提供者，用于异步刷新时获取新数据
     */
    protected Supplier<D> dataProvider;

    public AsyncRefreshCacheObject() {
        this(EXPIRE_TIME_DEFAULT);
    }

    public AsyncRefreshCacheObject(long expireIntervalMillisecond) {
        this.expireIntervalMillisecond = expireIntervalMillisecond;
        if (this.expireIntervalMillisecond <= 1000) {
            this.closeToExpireMillisecond = expireIntervalMillisecond / 10;
        }
        this.extendExpireTime();
    }

    public AsyncRefreshCacheObject(long expireIntervalMillisecond, Supplier<D> dataProvider) {
        this(expireIntervalMillisecond);
        this.dataProvider = dataProvider;
    }

    public void reset() {
        this.clear();
        this.extendExpireTime();
        this.asyncRefreshTriggered = false;
    }

    public void clear() {
        this.data = null;
    }

    public void extendExpireTime() {
        this.expireTimeMillisecond = System.currentTimeMillis() + this.expireIntervalMillisecond;
        this.asyncRefreshTriggered = false;
    }

    public boolean isExpire() {
        return System.currentTimeMillis() >= this.expireTimeMillisecond;
    }

    /**
     * 是否接近过期
     *
     * @return
     */
    public boolean isNearExpire() {
        return System.currentTimeMillis() >= this.expireTimeMillisecond - closeToExpireMillisecond;
    }

    /**
     * 是否到达过期时间的1/2，用于触发异步刷新
     *
     * @return
     */
    public boolean isHalfExpired() {
        long currentTime = System.currentTimeMillis();
        long createTime = this.expireTimeMillisecond - this.expireIntervalMillisecond;
        long halfExpireTime = createTime + (this.expireIntervalMillisecond / 2);
        return currentTime >= halfExpireTime;
    }

    /**
     * 检查并设置异步刷新标记
     *
     * @return 如果成功设置标记返回true，如果已经设置过返回false
     */
    public boolean trySetAsyncRefreshTriggered() {
        if (!asyncRefreshTriggered) {
            synchronized (this) {
                if (!asyncRefreshTriggered) {
                    asyncRefreshTriggered = true;
                    return true;
                }
            }
        }
        return false;
    }

    public D getData() {
        return getData(false);
    }

    public D getData(boolean extendExpireTime) {
        if (data == null) {
            return null;
        }
        if (isExpire()) {
            data = null;
            return null;
        }
        if (extendExpireTime) {
            extendExpireTime();
        }
        // 检查是否需要异步刷新
        if (dataProvider != null && isHalfExpired() && trySetAsyncRefreshTriggered()) {
            triggerAsyncRefresh();
        }

        return data;
    }

    public void cacheData(D data) {
        this.data = data;
        this.extendExpireTime();
    }

    /**
     * 设置数据提供者，用于异步刷新
     *
     * @param dataProvider 数据获取函数
     */
    public void setDataProvider(Supplier<D> dataProvider) {
        this.dataProvider = dataProvider;
    }

    /**
     * 触发异步刷新
     */
    protected void triggerAsyncRefresh() {
        if (dataProvider == null) {
            logger.warn("dataProvider is null, cannot trigger async refresh");
            return;
        }

        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() throws Exception {
                try {
                    logger.debug("Starting async refresh for cache object");
                    D newData = dataProvider.get();
                    if (newData != null) {
                        cacheData(newData);
                        logger.debug("Async refresh completed for cache object");
                    } else {
                        logger.debug("Async refresh returned null data for cache object");
                    }
                } catch (Exception e) {
                    logger.error("Error during async refresh for cache object", e);
                }
            }
        });
    }

    /**
     * 获取过期时间间隔
     *
     * @return 过期时间间隔（毫秒）
     */
    public long getExpireIntervalMillisecond() {
        return expireIntervalMillisecond;
    }

    /**
     * 获取过期时间
     *
     * @return 过期时间戳（毫秒）
     */
    public long getExpireTimeMillisecond() {
        return expireTimeMillisecond;
    }

    /**
     * 检查是否有数据
     *
     * @return 如果有数据且未过期返回true
     */
    public boolean hasData() {
        return data != null && !isExpire();
    }
}
