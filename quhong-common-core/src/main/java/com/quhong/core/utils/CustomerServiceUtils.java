package com.quhong.core.utils;

import java.util.HashSet;
import java.util.Set;

public class CustomerServiceUtils {
    private static Set<String> customerServiceSet = new HashSet<>();
    private static Set<String> kissboyChannelSet = new HashSet<>();

    static {
        // kissboy 客服 id  50000068
        customerServiceSet.add("5f44ff359a870e1b4e5fa1de");
        // kissu 客服 id 50000066
        customerServiceSet.add("5f47ad4b68410819fa4d545e");
        customerServiceSet.add("621733ae8f2478456e3538b2");
        customerServiceSet.add("60d97afd2e7a7f2c67af51c5");
        kissboyChannelSet.add("googleplay_kissboy");
        kissboyChannelSet.add("appstore_kissboy");
        kissboyChannelSet.add("appstore_qboy");
    }

    public static final String TIKKO_OFFICIAL_AGENT_ID = "4370";
    public static final String TIKKO_OFFICIAL_AGENT_UID = "6618e68af25f3a5471836004";

    public static boolean isCustomerService(String uid) {
        return customerServiceSet.contains(uid);
    }

    public static boolean decideCustomerChannel(String uid, int gender) {
        switch (gender) {
            case 1:
                return "5f47ad4b68410819fa4d545e".equals(uid);
            case 2:
                return "5f44ff359a870e1b4e5fa1de".equals(uid);
            default:
                return false;
        }
    }

    public static String getCustomerUid(String channel) {
        if (kissboyChannelSet.contains(channel)) {
            return "5f44ff359a870e1b4e5fa1de";
        } else {
            return "5f47ad4b68410819fa4d545e";
        }
    }

    /**
     * 获取发礼物的官方号
     * @return
     */
    public static String getSendGiftOfficialUid() {
        return "5f44ff359a870e1b4e5fa1de";
    }

    public static Set<String> getAllCustomerSet() {
        return customerServiceSet;
    }
}
