package com.quhong.core.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 已写入数据库 app_config_channel
 *
 * <AUTHOR> 2021-09-27
 */
public enum ChannelEnum {

    // googleplay
    GOOGLEPLAY("googleplay"),
    // appstore
    APPSTORE("appStore"),
    CDD("cdd"),
    CDC("cdc"),
    CDF("cdf"),

    /**
     * googleplay_data
     */
    CDE("cde"),

    GOOGLEPLAY_KISSBOY("googleplay_kissboy"),
    APPSTORE_KISSU("appstore_kissu"),
    APPSTORE_KISSBOY("appstore_kissboy"),
    APPSTORE_JESSY("appstore_jessy"),
    APPSTORE_DAKA("appstore_daka"),
    APPSTORE_HARA("appstore_hara"),
    APPSTORE_RAKOO("appstore_rakoo"),
    APPSTORE_DADA("appstore_dada"),
    APPSTORE_NICI("appstore_nici"),
    APPSTORE_CHACHA("appstore_chacha"),
    APPSTORE_QBOY("appstore_qboy"),
    HUAWEI_CHACHA("hw_cdd2"),
    APPSTORE_YAYO("appstore_yayo"),
    APPSTORE_HALLA("appstore_halla"),
    GP_CAMCHAT("gp_camchat"),
    WEB_CAMCHAT("web_camchat"),
    WEB_MOCHAT("web_mochat"),
    IOS_WEB_MOCHAT("ios_web_mochat"),
    APPSTORE_CAMCHAT("appstore_camchat"),

    WAHO("waho"),
    CD_LIVE("cdlive"),
    // default
    DEFAULT(""),
    GP_SUKY("gp_suky"),
    GP_NOMI("gp_nomi"),
    GP_SECFUN("secfun"),
    PC_WEB_CAMCHAT("pc_web_camchat"),
    PC_WEB_ALL("pc_web"),
    APPSTORE_PURN("appstore_purn"),//主播端
    APPSTORE_HOLE("appstore_hole"),//主播端
    APPSTORE_POP("appstore_pop"),
    APPSTORE_HICHAT("appstore_hichat"),
    APPSTORE_SIO("appstore_sio"),
    G11("g11"),
    APPSTORE_MAKI("appstore_maki"),
    APPSTORE_YOCA("appstore_yoca"),
    APPSTORE_OMOM("appstore_omom"),
    APPSTORE_TATA("appstore_tata"),
    APPSTORE_LACO("appstore_laco"),
    GILAMOUR("gilamour"),
    PC_WEB_CAMCHAT_MEETCHATS("pc_web_camchat_meetchats"),
    APPSTORE_YOHA("appstore_yoha"),
    APPSTORE_YFUNS("appstore_yfuns"),
    APPSTORE_TIKKO("appstore_tikko"),
    APPSTORE_LALO("appstore_lalo"),
    APPSTORE_CAMMEY("appstore_cammey"),
    APPSTORE_HEYOO("appstore_heyoo"),
    APPSTORE_BUZZIY("appstore_buzziy"),
    APPSTORE_QUEAN("appstore_quean"),
    APPSTORE_WIGOR("appstore_wigor"),
    APPSTORE_GROBY("appstore_groby"),
    APPSTORE_DAFUL("appstore_daful"),
    APPSTORE_GLIPY("appstore_glipy"),
    APPSTORE_ARAGI("appstore_aragi"),
    APPSTORE_YONA("appstore_yona"),
    APPSTORE_HALAHUB("appstore_halahub"),
    APPSTORE_JOYMEN("appstore_joymen"),
    APPSTORE_PERRL("appstore_perrl"),
    PC_WEB_PADDL("pc_web_paddl"),
    APPSTORE_ANOCHAT("appstore_anochat"),
    APPSTORE_MOCES("appstore_moces"),

    ;

    private final String name;

    ChannelEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static ChannelEnum getValueByName(String name) {
        name = name.toLowerCase();
        for (ChannelEnum channel : ChannelEnum.values()) {
            if (channel.getName().toLowerCase().equals(name)) {
                return channel;
            }
        }
        return ChannelEnum.DEFAULT;
    }

    public static ChannelEnum getValueBySimilarName(String name) {
        ChannelEnum channelEnum = getValueByName(name);
        if (channelEnum != ChannelEnum.DEFAULT) {
            return channelEnum;
        }
        name = name.toLowerCase();
        for (ChannelEnum channel : ChannelEnum.values()) {
            if (name.contains(channel.getName().toLowerCase())) {
                return channel;
            }
        }
        return ChannelEnum.DEFAULT;
    }

    public static List<String> getIosAnchorChannel() {
        return Arrays.asList(APPSTORE_KISSBOY.getName(), APPSTORE_QBOY.getName(), APPSTORE_HALLA.getName(), APPSTORE_PURN.getName(), APPSTORE_HOLE.getName(), APPSTORE_YOCA.getName());
    }

    public static List<String> getLargePictureChannel() {
        return Arrays.asList(PC_WEB_CAMCHAT_MEETCHATS.getName(), PC_WEB_CAMCHAT.getName());
    }

    public static List<String> getAndroidAnchorChannel() {
        return Arrays.asList(GOOGLEPLAY_KISSBOY.getName());
    }

    /**
     * 如果是主播端app则返回true
     *
     * @param channel
     * @return
     */
    public static boolean isAnchorApp(String channel) {
        return getIosAnchorChannel().contains(channel) || getAndroidAnchorChannel().contains(channel);
    }

    public static List<String> getAnchorApp() {
        List<String> iosAnchorChannel = getIosAnchorChannel();
        List<String> androidAnchorChannel = getAndroidAnchorChannel();
        List<String> anchorAppChannelList = new ArrayList<>();
        anchorAppChannelList.addAll(androidAnchorChannel);
        anchorAppChannelList.addAll(iosAnchorChannel);
        return anchorAppChannelList;
    }

    public static List<String> getTikkoChannel() {
        return Arrays.asList(APPSTORE_TIKKO.getName(), CDE.getName());
    }

    public static boolean isTikkoChannel(String channel) {
        return APPSTORE_TIKKO.getName().equals(channel) || CDE.getName().equals(channel);
    }

    //    APPSTORE_WIGOR("appstore_wigor"),
    //    APPSTORE_GROBY("appstore_groby"),
    //    APPSTORE_DAFUL("appstore_daful"),
    //    APPSTORE_GLIPY("appstore_glipy"),
    //    APPSTORE_ARAGI("appstore_aragi"),
    public static List<String> rewriteABFun() {
        return Arrays.asList(APPSTORE_WIGOR.getName(), APPSTORE_GROBY.getName(), APPSTORE_DAFUL.getName(), APPSTORE_GLIPY.getName(), APPSTORE_ARAGI.getName(), APPSTORE_YONA.getName());
    }

}
