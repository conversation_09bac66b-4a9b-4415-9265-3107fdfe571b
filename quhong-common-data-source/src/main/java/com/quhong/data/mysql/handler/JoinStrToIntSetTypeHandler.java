package com.quhong.data.mysql.handler;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/25 18:15
 */
@Slf4j
@MappedTypes({Integer.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class JoinStrToIntSetTypeHandler extends StringToSetTypeHandler<Integer> {
    @Override
    protected Set<Integer> parse(String str) {
        String[] strArray = StringUtils.delimitedListToStringArray(str, ",");
        return Arrays.stream(strArray).map(String::trim)
                .filter(StringUtils::hasLength)
                .map(Integer::valueOf)
                .collect(Collectors.toSet());
    }

    @Override
    protected String toStr(Set<Integer> list) {
        if (ObjectUtils.isEmpty(list)) {
            return "";
        }
        List<Integer> finalList = list.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(finalList)) {
            return "";
        }
        return StringUtils.collectionToDelimitedString(finalList, ",");
    }
}
