package com.quhong.data.redis;

import com.quhong.core.constant.BaseRedisBeanConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class HostLevelRedis {
    private static final Logger logger = LoggerFactory.getLogger(HostLevelRedis.class);

    private static final String ACTOR_LEVEL_KEY = "hash_host_level_";

    @Resource(name = BaseRedisBeanConstant.OTHER_BEAN)
    protected StringRedisTemplate otherRedisTemplate;

    public HostLevelRedis() {

    }

//    public int getLevel(String uid) {
//        try {
//            String key = ACTOR_LEVEL_KEY + uid;
//            String levelStr = (String) mainRedisTemplate.opsForHash().get(key, "level");
//            if (StringUtils.isEmpty(levelStr)) {
//                return 5;
//            }
//            Integer value = Integer.valueOf(levelStr);
//            if (value > 5) {
//                value = 5;
//            }
//            return value;
//        } catch (Exception e) {
//            logger.error("get level error. uid={} {}", uid, e.getMessage(), e);
//        }
//        return 5;
//    }

    public int getLevel(String uid) {
        return getHostMonthLevel(uid);
    }

    public int getHostMonthLevel(String uid) {
        try {
            String key = getHostMonthLevelKey(uid);
            String thisCycleShowLevel = (String) otherRedisTemplate.opsForHash().get(key, "thisCycleShowLevel");
//            logger.info("uid={}， thisCycleShowLevel={}", uid, thisCycleShowLevel);
            if (thisCycleShowLevel == null) {
                return 0;
            }
            return Integer.parseInt(thisCycleShowLevel);
        } catch (Exception e) {
            logger.error("get host month level. error uid={} {}", uid, e.getMessage(), e);
        }
        return 0;
    }

    public String getHostMonthLevelKey(String uid) {
        return "hash:hostMonthLevel:uid:" + uid;
    }
}
