package com.quhong.config;

import com.quhong.common.config.InterceptConfig;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class RoomInterceptConfig extends InterceptConfig {

    @Override
    protected Map<String, Long> getRequestWarnDurationMap() {
        Map<String, Long> map = new HashMap<>();
        map.put(baseUrl + "/list/actor_detail_list", 5000L);
        map.put(baseUrl + "/enter_room", 4000L);
        return map;
    }
}
