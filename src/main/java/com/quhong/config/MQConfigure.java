package com.quhong.config;

import com.quhong.mq.config.BaseRabbitMqConfig;
import com.quhong.mq.data.MQConfigData;
import com.quhong.mq.enums.MQConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName MQConfig
 * <AUTHOR>
 * @date 2021/12/25 15:21
 */
@Slf4j
@Configuration
public class MQConfigure extends BaseRabbitMqConfig {
    public static final String TEST_RABBIT_BEAN = "activity.rabbitMq";

    @Bean(name = TEST_RABBIT_BEAN)
    public RabbitTemplate rabbitTemplate() {
        MQConfigData configData = createConfigData();
        return createTemplate(configData);
    }

    @Override
    protected void bindQueues(AmqpAdmin admin) {
//        bindTopicExchangeQueue(admin,
//                MQConstant.ACTIVITY_ROUTE,
//                new TopicExchange(MQConstant.ACTIVITY_EXCHANGE),
//                new Queue(MQConstant.ACTIVITY_QUEUE));
//        admin.deleteQueue(MQConstant.ACTIVITY_ROOM_CHAT_MSG_QUEUE);
//        admin.deleteQueue(MQConstant.ACTIVITY_QUEUE);
//        admin.deleteQueue(MQConstant.ACTIVITY_LUCKY_USER_QUEUE);
//        admin.deleteQueue(MQConstant.ACTIVITY_PLAY_YXSK_GAME_QUEUE);
        TopicExchange activityExchange = new TopicExchange(MQConstant.ACTIVITY_EXCHANGE);
        bindTopicExchangeQueue(admin,
                MQConstant.EVENT_GAME_LUCKY_DATA_PROCESSING_ROUTE,
                activityExchange,
                new Queue(MQConstant.EVENT_GAME_LUCKY_DATA_PROCESSING_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.MONEY_DETAIL_ROUTE,
                new TopicExchange(MQConstant.MONEY_CENTER_EXCHANGE),
                new Queue(MQConstant.EVENT_GAME_LUCKY_MONEY_DETAIL_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.MONEY_DETAIL_ROUTE,
                new TopicExchange(MQConstant.MONEY_CENTER_EXCHANGE),
                new Queue(MQConstant.PLAY_GAME_WIN_QUEUE));
//        bindTopicExchangeQueue(admin,
//                MQConstant.SEND_GIFT_SUCCESS_ROUTE,
//                new TopicExchange(MQConstant.GIFT_EXCHANGE),
//                new Queue(MQConstant.ACTIVITY_LUCKY_USER_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.SEND_GIFT_SUCCESS_ROUTE,
                new TopicExchange(MQConstant.GIFT_EXCHANGE),
                new Queue(MQConstant.ACTIVITY_SEND_GIFT_SUCCESS_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.FIRST_PAYMENT_ROUTE,
                new TopicExchange(MQConstant.PAY_EXCHANGE),
                new Queue(MQConstant.ACTIVITY_FIRST_PAYMENT_QUEUE));
//        bindTopicExchangeQueue(admin,
//                MQConstant.YXSK_GAME_PLAY_ROUTE,
//                activityExchange,
//                new Queue(MQConstant.ACTIVITY_PLAY_YXSK_GAME_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.ENTER_ROOM_ROUTE,
                new TopicExchange(MQConstant.ROOM_EXCHANGE),
                new Queue(MQConstant.ACTIVITY_ENTER_ROOM_MSG_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.ROOM_CHAT_MSG_ROUTE,
                new TopicExchange(MQConstant.ROOM_EXCHANGE),
                new Queue(MQConstant.ACTIVITY_ROOM_CHAT_MSG_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.PAYMENT_ROUTE,
                new TopicExchange(MQConstant.PAY_EXCHANGE),
                new Queue(MQConstant.ACTIVITY_PAYMENT_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.EVENT_COST_COUNT_ROUTE,
                activityExchange,
                new Queue(MQConstant.EVENT_COST_COUNT_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.LEAVE_ROOM_ROUTE,
                new TopicExchange(MQConstant.ROOM_EXCHANGE),
                new Queue(MQConstant.ACTIVITY_LEAVE_ROOM_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.DOWN_WHEAT_ROUTE,
                new TopicExchange(MQConstant.ROOM_EXCHANGE),
                new Queue(MQConstant.EVENT_DOWN_WHEAT_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.GAME_PLAY_ROUTE,
                new TopicExchange(MQConstant.GAME_EXCHANGE),
                new Queue(MQConstant.GAME_PLAY_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.MONEY_DETAIL_ROUTE,
                new TopicExchange(MQConstant.MONEY_CENTER_EXCHANGE),
                new Queue(MQConstant.EVENT_MONEY_DETAIL_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.ROOM_CHAPTER_LOG,
                new TopicExchange(MQConstant.ROOM_EXCHANGE),
                new Queue(MQConstant.EVENT_ROOM_CHAPTER_LOG_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.INVITE_DIVIDE_INTO_ROUTE,
                new TopicExchange(MQConstant.INVITE_EXCHANGE),
                new Queue(MQConstant.INVITE_DIVIDE_INTO_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.LUCKY_GIFT_PROCESS_ROUTE,
                activityExchange,
                new Queue(MQConstant.EVENT_LUCKY_GIFT_PROCESS_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.STARRY_GET_GIFT_ROUTE,
                new TopicExchange(MQConstant.GAMES_EXCHANGE),
                new Queue(MQConstant.EVENT_STARRY_GET_GIFT_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.INVITE_USER_SUCCESS_ROUTE,
                activityExchange,
                new Queue(MQConstant.EVNET_INVITE_USER_SUCCESS_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.PK_START_ROUTE,
                new TopicExchange(MQConstant.PK_EXCHANGE),
                new Queue(MQConstant.EVENT_PK_START_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.PK_END_ROUTE,
                new TopicExchange(MQConstant.PK_EXCHANGE),
                new Queue(MQConstant.EVENT_PK_END_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.FOLLOW_ROUTE,
                new TopicExchange(MQConstant.USER_OPERATION_EXCHANGE),
                new Queue(MQConstant.EVENT_FOLLOW_QUEUE));
        bindTopicExchangeQueue(admin,
                MQConstant.EVENT_UNIT_CURRENCY_ROUTE,
                new TopicExchange(MQConstant.ACTIVITY_EXCHANGE),
                new Queue(MQConstant.EVENT_UNIT_CURRENCY_QUEUE));
    }
}
