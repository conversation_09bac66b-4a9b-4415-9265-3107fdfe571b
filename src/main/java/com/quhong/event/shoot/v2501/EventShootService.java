package com.quhong.event.shoot.v2501;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.LangIdConstant;
import com.quhong.constant.OfficialUserGroup;
import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.constant.levelConfig.LevelTypeConstant;
import com.quhong.core.annotation.ActivityRankType;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.WarnName;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.ActorDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.EventShootDrawLogDao;
import com.quhong.dao.EventShootRecordDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.log.EventShootDrawLogData;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.dto.RankingDTO;
import com.quhong.data.thData.ActivityTicketsEvent;
import com.quhong.data.thData.DrawPrizesRecordLogData;
import com.quhong.data.vo.CountVO;
import com.quhong.enums.EventCode;
import com.quhong.enums.RewardItemType;
import com.quhong.enums.TopTypeEnum;
import com.quhong.event.shoot.data.vo.TeamRankItem;
import com.quhong.event.shoot.data.vo.TeamRankVO;
import com.quhong.event.shoot.v2410.data.bo.TeamInfoBO;
import com.quhong.event.shoot.v2410.data.bo.TeamPersonalInfoBO;
import com.quhong.event.shoot.v2410.data.bo.UserInfoBO;
import com.quhong.event.shoot.v2410.data.config.TaskConfig;
import com.quhong.event.shoot.v2410.data.vo.PrizeConfigVO;
import com.quhong.event.shoot.v2410.data.vo.ShootDrawRecordVO;
import com.quhong.event.shoot.v2410.data.vo.ShootVO;
import com.quhong.exceptions.WebException;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.redis.BaseHashSaveRedis;
import com.quhong.redis.BaseZSetRedis;
import com.quhong.redis.base.ActivityCommonRedis;
import com.quhong.redis.base.currency.task.BaseEveryLimitRedis;
import com.quhong.redis.shoot.ShootActivityRedis;
import com.quhong.report.EventReport;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.activity.model.GiftPriceRankBaseService;
import com.quhong.service.common.RewardService;
import com.quhong.service.common.RoomFloatingImService;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/5/22 11:08
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ActivityRankType(ActivityRankType.SHOOT_V2410_GIFT_PRICE_RANK)
public class EventShootService extends GiftPriceRankBaseService {
    private static final boolean PROD = ServerConfiguration.isProduct();

    private static final String GOLD_BALL_SHOOT_TYPE = "gold_ball_shoot";     // 金球射门
    private static final String BALL_SHOOT_TYPE = "ball_shoot";     // 足球射门
    private static final String NOTICE_IMG = "https://statics.kissu.mobi/event/shot/notice01.jpg";

    private static final String EVENT_URL = PROD ? "https://videochat.kissu.site/shot_jackpot_202501/" : "https://testvideochat.kissu.site/shot_jackpot_202501/";
    private static final List<String> IM_NOTICE_AWARD_KEY_SET = Arrays.asList("a", "d");

    private static final int TEAM_MAX_MEMBERS = 4;
    private static final int LIMIT_INIT_POOL = 10;
    private static final int ZERO_INIT_POOL = 0;
    private static final int EVENT_CODE = EventCode.EVENT_SHOOT_2501;

    /**
     * 送礼消耗金币数每达标，获得奖励
     */
    public static final TaskConfig SEND_GIFT_PRICE_TASK = new TaskConfig(TaskTypeConstant.SEND_GIFT, 200, 1, -1);
    /**
     * 团队成员奖励
     */
    private static final RewardInfoData TEAM_MEMBER_REWARDS = new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900053 : 30001);
    /**
     * 团队榜Top1奖励
     */
    List<RewardInfoData> TEAM_RANK_TOP1_REWARDS = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 25, RewardItemType.GOLD, 0, 1));
        add(new RewardInfoData(EVENT_CODE, 3, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
        add(new RewardInfoData(EVENT_CODE, 3, RewardItemType.LORD_DAYS, 6));
        add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, PROD ? 628 : 628));
        add(new RewardInfoData(EVENT_CODE, 50000, RewardItemType.LEVEL_SCORE, LevelTypeConstant.TYPE_USER_LEVEL));
    }};
    /**
     * 团队射门达标奖励
     */
    private static final List<RewardTaskConfig> TEAM_REWARDS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GOLD, 0, 1));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards1).setTaskName("1"));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5, RewardItemType.GOLD, 0, 1));
        }};
        add(new RewardTaskConfig().setCheckParams(20).setRewards(rewards2).setTaskName("2"));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.GOLD, 0, 1));
        }};
        add(new RewardTaskConfig().setCheckParams(50).setRewards(rewards3).setTaskName("3"));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 250, RewardItemType.GOLD, 0, 1));
        }};
        add(new RewardTaskConfig().setCheckParams(500).setRewards(rewards4).setTaskName("4"));

        List<RewardInfoData> rewards5 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 500, RewardItemType.GOLD, 0, 1));
        }};
        add(new RewardTaskConfig().setCheckParams(1000).setRewards(rewards5).setTaskName("5"));

    }};

    private static final List<RewardTaskConfig> SEND_REWARDS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, 900017));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 251 : 55));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900018 : 90003));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.SEAT_FRAME, PROD ? 251 : 55));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900019 : 90001));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 251 : 55));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

//        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
//            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
//            add(new RewardInfoData(EVENT_CODE, 3, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
//            add(new RewardInfoData(EVENT_CODE, 3, RewardItemType.SEAT_FRAME, PROD ? 251 : 55));
//        }};
//        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));
    }};

    private static final List<RewardTaskConfig> RECEIVE_REWARDS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900050 : 900018));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 252 : 65));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900050 : 900018));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.SEAT_FRAME, PROD ? 252 : 65));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900050 : 900018));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 252 : 65));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

//        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
//            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0));
//            add(new RewardInfoData(EVENT_CODE, 3, RewardItemType.SEAT_FRAME, PROD ? 252 : 65));
//        }};
//        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));
    }};
    public static final String ROW_FORMAT = "Top#top #rid\n";


    private final EventShootConfig eventShootConfig;
    private final ActivityCommonRedis activityCommonRedis;
    private final ShootActivityRedis shootActivityRedis;
    private final EventShootRecordDao shootRecordDao;
    private final EventShootDrawLogDao shootDrawDao;
    private final RoomFloatingImService roomFloatingImService;
    private final AppConfigActivityDao appConfigActivityDao;
    private final GiveOutRewardService giveOutRewardService;
    private final EventReport eventReport;
    private final ActorMgr actorMgr;
    private final ActorDao actorDao;
    private final OfficialNoticeService officialNoticeService;
    private final BaseHashSaveRedis baseHashSaveRedis;
    private final RewardService rewardService;
    private final BaseEveryLimitRedis baseEveryLimitRedis;
    private final BaseZSetRedis baseZSetRedis;

    /**
     * 全局用户信息
     * hashKey : uid
     *
     * @param eventCode
     * @return
     */
    private String hashGlobalUserInfoKey(String eventCode) {
        return "hash:event:shoot:user_info:" + eventCode;
    }

    /**
     * 每日组队邀请射门数据
     * hashKey : leaderUid
     *
     * @param eventCode
     * @param date
     * @return
     */
    private String hashDailyTeamInfoKey(String eventCode, String date) {
        return "hash:event:shoot:daily_team_info:" + eventCode + ":" + date;
    }

    /**
     * 队伍总榜数据
     * member: leaderUid
     */
    private String zsetTeamRankKey(String eventCode) {
        return "zset:event:shoot:team_rank:" + eventCode;
    }

    private static String genRankName(int rankType, String localDateStr) {
        String rankName;
        if (rankType == 2) {
            if (StringUtils.hasLength(localDateStr)) {
                rankName = "Daily Receiver(" + localDateStr + ")";
            } else {
                rankName = "charm ranking";
            }
        } else {
            rankName = "rich ranking";
        }
        return rankName;
    }

    private static TopTypeEnum genTopType(int rankType, String localDateStr) {
        TopTypeEnum topType = TopTypeEnum.AWARD;
        if (rankType == 2 && StringUtils.hasLength(localDateStr)) {
            topType = TopTypeEnum.SHOOT_DAILY_RANK_AWARD;
        }
        return topType;
    }

    private static List<RewardTaskConfig> genRewardConfigs(int rankType, String localDateStr) {
        List<RewardTaskConfig> rewardConfigs;
        if (rankType == 1) {
            rewardConfigs = SEND_REWARDS;
        } else {
            if (StringUtils.hasLength(localDateStr)) {
                rewardConfigs = null;
            } else {
                rewardConfigs = RECEIVE_REWARDS;
            }
        }
        return rewardConfigs;
    }

    private static void fillDrawRecordList(EventShootConfig.ShootDrawConfig drawConfig, List<ShootVO.DrawRecord> drawRecordList) {
        ShootVO.DrawRecord drawRecord = new ShootVO.DrawRecord();
        drawRecord.setName(drawConfig.getTitle());
        drawRecord.setTitle(drawConfig.getTitle());
        drawRecord.setTitleAr(drawConfig.getTitleAr());
        drawRecord.setIcon(drawConfig.getIcon());
        drawRecord.setIconAr(drawConfig.getIcon());
        drawRecordList.add(drawRecord);
    }

    @Override
    public String getRankItemIcon(AppConfigActivityData configData, ActorData currActor, Integer gender) {
        return "";
    }

    @Override
    public void activityRankNotice(AppConfigActivityData configData, int rankType, String localDateStr) {
        exchangeTime(localDateStr, configData);
        TopTypeEnum topType = genTopType(rankType, localDateStr);
        List<RankingDTO> rankList = getRankingList(configData, rankType, topType);
        // 奖励发放逻辑
        List<RewardTaskConfig> rewardConfigs = genRewardConfigs(rankType, localDateStr);
        if (ObjectUtils.isEmpty(rewardConfigs)) {
            return;
        }
        IntStream.range(0, rankList.size())
                .forEach(index -> giveOutReward(rewardConfigs, index + 1, rankList.get(index), rankType));
        String rankName = genRankName(rankType, localDateStr);
        //通知头部信息填充
        StringBuilder content = fillNoticeHead(configData, localDateStr, rankName);
        StringBuilder globalNotice = new StringBuilder();
        globalNotice.append("\uD83D\uDE01 Congratulations to the follows get Top3 in Lucky Shot Event charm ranQueen").append(rankName).append("\n");
        IntStream.rangeClosed(1, rankList.size()).forEachOrdered(rankNum -> fillContentAndSendNotice(localDateStr, rankNum, rankList, content, rankName, globalNotice));
        globalNotice.append("\uD83D\uDE01 The reward has been issued~");
        int currTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        Set<String> channelSet = StringUtils.getStrSetFromStr(configData.getChannel(), ",");
        if (!ObjectUtils.isEmpty(channelSet)) {
            channelSet.forEach(channel -> officialNoticeService.sendGlobalNotice(configData.getName(), globalNotice.toString(), NOTICE_IMG, EVENT_URL, channel,
                    currTime, configData.getActivityCode(), LangIdConstant.ENGLISH, null, OfficialUserGroup.CHANNEL));
        }
        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, content.toString());
    }

    public void fillContentAndSendNotice(String localDateStr, int rankNum, List<RankingDTO> rankList, StringBuilder content, String rankName, StringBuilder globalNotice) {
        RankingDTO currRankData = rankList.get(rankNum - 1);
        fillContentBody(rankNum, content, currRankData);
        String row = ROW_FORMAT.replace("#top", String.valueOf(rankNum))
                .replace("#rid", String.valueOf(currRankData.getRid()));
        globalNotice.append(row);
    }

    public void fillContentAndSendNotice(String localDateStr, int rankNum, List<RankingDTO> rankList, StringBuilder content, String rankName) {
        RankingDTO currRankData = rankList.get(rankNum - 1);
        fillContentBody(rankNum, content, currRankData);
        //榜单官方消息
        String notice = "Top #rankNum #rankName of The Lucky Shot  Event June. 2024".replace("#rankName", rankName);

        try {
            sendOfficialNoticeToUser(rankNum, currRankData.getUid(), notice, NOTICE_IMG, EVENT_URL, ChannelEnum.CDE.getName());
        } catch (Exception e) {
            log.error("error to send notice");
            try {
                sendOfficialNoticeToUser(rankNum, currRankData.getUid(), notice, NOTICE_IMG, EVENT_URL, ChannelEnum.CDE.getName());
            } catch (Exception ex) {
                log.error("error to send notice");
            }

        }
    }

    private void giveOutReward(List<RewardTaskConfig> rewardConfigs, int rankNum, RankingDTO rankData, int rankType) {
        RewardTaskConfig rewardTaskConfig = rewardConfigs.stream()
                .filter(data -> data.getCheckParams().equals(rankNum))
                .findFirst().orElse(null);
        if (rewardTaskConfig == null) {
            log.info("rewardTaskConfig is not found, rankNum={}", rankNum);
            return;
        }
        ActorData rankActor = actorMgr.getActorData(rankData.getUid());
        if (rankActor == null) {
            log.info("rankActor is null, uid={}", rankData.getUid());
            return;
        }
        //奖励发放
        giveOutRewardService.giveEventReward(rankData.getUid(), rewardTaskConfig.getRewards(), EVENT_CODE, rankType == 1 ? "rank-send gift" : "rank-receive gift");
    }


    public ShootVO shootConfig(String activityId, String uid, String inviterUid) {
        int eventCode = Integer.parseInt(activityId);
        AppConfigActivityData activity = appConfigActivityDao.getOneByEventCodeThrowWebException(eventCode);
        ActorData currActor = actorMgr.getCurrActorData(uid);
        if (StringUtils.hasLength(activity.getChannel()) && !activity.getChannel().contains(currActor.getChannel())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("your app not join this event"));
        }
        DateHelper dateHelper = DateHelper.genDateHelper(currActor.getChannel());
        ShootVO vo = new ShootVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        String userInfoKey = hashGlobalUserInfoKey(activityId);
        String dailyTeamInfoKey = hashDailyTeamInfoKey(activityId, dateHelper.getToday());
        UserInfoBO currUserInfo = genUserInfoBO(uid, userInfoKey);
        vo.setGoldBallBalance(currUserInfo.getGoldBallBalance());

        // 金球抽奖配置
        List<EventShootConfig.ShootDrawConfig> goldBallDrawList = eventShootConfig.getGoldBallDrawList();
        List<PrizeConfigVO> goldBallPoolConfigs = goldBallDrawList.stream()
                .map(this::fillDrawConfigVO)
                .collect(Collectors.toList());
        vo.setGoldBallPoolConfigs(goldBallPoolConfigs);

        // 足球抽奖配置
        List<EventShootConfig.ShootDrawConfig> ballDrawList = eventShootConfig.getBallDrawList();
        List<PrizeConfigVO> ballPoolConfigs = ballDrawList.stream()
                .map(this::fillDrawConfigVO)
                .collect(Collectors.toList());
        vo.setBallPoolConfigs(ballPoolConfigs);

        // 射门抽奖滚屏记录
        List<ShootVO.DrawRecord> drawRecordList = fillLastDrawLog(eventCode);
        vo.setDrawRecordList(drawRecordList);
        TeamInfoBO teamInfoBO = fillTeamInfoBO(currUserInfo, userInfoKey, dailyTeamInfoKey);
        vo.setTeamInfoBO(teamInfoBO);

        long currTime = DateHelper.getCurrTime();
        if (currTime < activity.getStartTime() || currTime > activity.getEndTime()) {
            return vo;
        }

        if (StringUtils.hasLength(inviterUid)) {
            // 组队邀请逻辑处理
            doTeamInviteLogic(currUserInfo, inviterUid, userInfoKey, currActor, dailyTeamInfoKey, activity);
        }

        //  队员每日赠送一个足球逻辑
        checkDailyFreeBall(currUserInfo, dateHelper, userInfoKey, activity);
        vo.setBallBalance(currUserInfo.getBallBalance());

        TeamInfoBO teamInfoBO1 = fillTeamInfoBO(currUserInfo, userInfoKey, dailyTeamInfoKey);
        vo.setTeamInfoBO(teamInfoBO1);

        return vo;
    }

    private UserInfoBO genUserInfoBO(String uid, String userInfoKey) {
        UserInfoBO currUserInfo = baseHashSaveRedis.getDataByRedis(userInfoKey, uid, UserInfoBO.class);
        log.debug("userInfoBO={}", currUserInfo);
        if (currUserInfo == null) {
            currUserInfo = new UserInfoBO(uid);
        }
        return currUserInfo;
    }

    private void checkDailyFreeBall(UserInfoBO currUserInfo, DateHelper dateHelper, String userInfoKey, AppConfigActivityData activity) {
        if (StringUtils.isEmpty(currUserInfo.getLeaderUid())) {
            return;
        }
        String today = dateHelper.getToday();
        if (currUserInfo.getReceivedDateSet().contains(today)) {
            return;
        }
        currUserInfo.increaseBallBalance(1);
        currUserInfo.getReceivedDateSet().add(today);
        baseHashSaveRedis.saveToRedis(userInfoKey, currUserInfo.getUid(), currUserInfo, Duration.ofDays(14));
        // 足球获得 上报数数
        reportGetTicketToThinkData(activity, currUserInfo.getUid(), 2, "3", 1);
    }

    private TeamInfoBO fillTeamInfoBO(UserInfoBO currUserInfo, String userInfoKey, String dailyTeamInfoKey) {
        if (!StringUtils.hasLength(currUserInfo.getLeaderUid())) {
            return null;
        }
        if (currUserInfo.getUid().equals(currUserInfo.getLeaderUid())) {
            // 每日数据
            return fillDailyTeamUserInfo(currUserInfo, dailyTeamInfoKey);
        }
        UserInfoBO leaderUserInfo = baseHashSaveRedis.getDataByRedis(userInfoKey, currUserInfo.getLeaderUid(), UserInfoBO.class);
        return leaderUserInfo.getTeamInfoBO();
    }

    private TeamInfoBO fillDailyTeamUserInfo(UserInfoBO currUserInfo, String dailyTeamInfoKey) {
        TeamInfoBO dailyTeamInfo = baseHashSaveRedis.getDataByRedis(dailyTeamInfoKey, currUserInfo.getLeaderUid(), TeamInfoBO.class);
        if (dailyTeamInfo == null) {
            dailyTeamInfo = initDailyTeamInfo(currUserInfo);
            baseHashSaveRedis.saveToRedis(dailyTeamInfoKey, currUserInfo.getLeaderUid(), dailyTeamInfo, Duration.ofDays(7));
        }
        return dailyTeamInfo;
    }

    private PrizeConfigVO fillDrawConfigVO(EventShootConfig.ShootDrawConfig drawConfig) {
        PrizeConfigVO prizeConfigVO = new PrizeConfigVO();
        prizeConfigVO.setIconEn(drawConfig.getIcon());
        prizeConfigVO.setNameShortEn(drawConfig.getTitle());
        prizeConfigVO.setNameAr(drawConfig.getTitleAr());
        prizeConfigVO.setNameShortAr(drawConfig.getTitleAr());
        prizeConfigVO.setNameEn(drawConfig.getTitle());
        prizeConfigVO.setMp4Url(drawConfig.getMp4Url());
        prizeConfigVO.setPrice(drawConfig.getPrice());
        return prizeConfigVO;
    }

    private List<ShootVO.DrawRecord> fillLastDrawLog(int eventCode) {
        List<EventShootDrawLogData> drawList = shootDrawDao.selectLastLog(eventCode, 10);
        Map<String, EventShootConfig.ShootDrawConfig> drawConfigMap = genAllDrawMap();
        return drawList.stream()
                .map(drawData -> this.fillLastLogVO(drawData, drawConfigMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private ShootVO.DrawRecord fillLastLogVO(EventShootDrawLogData drawData, Map<String, EventShootConfig.ShootDrawConfig> drawConfigMap) {
        ShootVO.DrawRecord drawRecord = new ShootVO.DrawRecord();
        ActorData actor = actorMgr.getActorData(drawData.getUid());
        drawRecord.setName(actor == null ? "XXXX" : actor.getName());
        drawRecord.setHead(actor == null ? "" : moderationService.dealRankHeadModeration(actor));
        EventShootConfig.ShootDrawConfig drawConfig = drawConfigMap.get(drawData.getAwardKey());
        if (drawConfig == null) {
            return null;
        }
        drawRecord.setTitle(drawConfig.getTitle());
        drawRecord.setTitleAr(drawConfig.getTitleAr());
        drawRecord.setIcon(drawConfig.getIcon());
        drawRecord.setIconAr(drawConfig.getIcon());
        return drawRecord;
    }


    // 发送礼物抽奖
    public void shootGiftHandle(SendGiftSuccessMsgData giftData) {
        String activityId = String.valueOf(EVENT_CODE);
        // 礼物id界定
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        if (giftData.getTime() < configData.getStartTime() || giftData.getTime() > configData.getEndTime()) {
            return;
        }
        if (!configData.getDataId().contains(giftData.getGiftId().intValue())) {
            log.info("gift id is not event gift id");
            return;
        }
        String fromUid = giftData.getUid();
        ActorData currActor = actorMgr.getActorData(fromUid);
        if (!configData.getChannel().contains(currActor.getChannel())) {
            return;
        }
        long cost = giftData.getCost();
        int incr = baseEveryLimitRedis.increaseAndGetRewards(fromUid, EVENT_CODE, cost, SEND_GIFT_PRICE_TASK);
        log.info("shoot gift handle, cost:{}, incr:{}", cost, incr);
        if (incr < 1) {
            return;
        }
        // 下发金球
        String key = hashGlobalUserInfoKey(activityId);
        log.info("shoot gift handle, key:{}， fromUid={}", key, fromUid);
        UserInfoBO userInfoBO = genUserInfoBO(fromUid, key);
        log.info("shoot gift handle, userInfoBO:{}", JSON.toJSONString(userInfoBO));
        userInfoBO.increaseGoldBallBalance(incr);
        baseHashSaveRedis.saveToRedis(key, fromUid, userInfoBO, Duration.ofDays(14));
        // 获得金球 上报数数
        reportGetTicketToThinkData(configData, fromUid, 1, "1", incr);
    }


    private void checkActivityTime(AppConfigActivityData configData, long currTime, String channel) {
        if (currTime < configData.getStartTime() || currTime > configData.getEndTime()) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("not in event time"));
        }
        if (StringUtils.hasLength(configData.getChannel()) && !configData.getChannel().contains(channel)) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("your app not join this event"));
        }

    }


    /**
     * 奖池指定位置指定奖品逻辑
     */
    private void needShootPool(String taskType, Map<String, EventShootConfig.ShootDrawConfig> drawMap, int initPoolSize) {
        List<String> poolList = new ArrayList<>(Collections.nCopies(initPoolSize, "-1"));
        List<Integer> indexList = IntStream.rangeClosed(0, initPoolSize - 1).boxed().collect(Collectors.toList());
        Collections.shuffle(indexList);

        drawMap.values().forEach(drawData -> {
            String drawKey = drawData.getCardKey();
            int drawRate = drawData.getRate();
            if (drawKey.equals("a")) {
                poolList.set(321, drawKey);
                indexList.remove(Integer.valueOf(321));
                return;
            }

//            if (drawKey.equals("b")) {
//                poolList.set(249, drawKey);
//                poolList.set(849, drawKey);
//                indexList.remove(Integer.valueOf(249));
//                indexList.remove(Integer.valueOf(849));
//                return;
//            }


            for (int i = 0; i < drawRate; i++) {
                if (!indexList.isEmpty()) {
                    int index = indexList.remove(0);
                    poolList.set(index, drawKey);
                } else {
                    poolList.add(drawKey);
                }
            }

        });

        poolList.removeAll(Collections.singleton("-1"));
        shootActivityRedis.initPoolSize(taskType, poolList);
    }

    private void initShootPool(String taskType, Map<String, EventShootConfig.ShootDrawConfig> drawMap, int initPoolSize) {

        int poolSize = shootActivityRedis.getPoolSize(taskType);
        if (poolSize > ZERO_INIT_POOL && poolSize <= LIMIT_INIT_POOL) {
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    needShootPool(taskType, drawMap, initPoolSize);
                }
            });
        } else if (poolSize <= ZERO_INIT_POOL) {
            needShootPool(taskType, drawMap, initPoolSize);
        }
    }

    // 抽奖
    private String shootDrawFromRedisPool(String taskType, Map<String, EventShootConfig.ShootDrawConfig> drawMap, int initPoolSize) {
        initShootPool(taskType, drawMap, initPoolSize);
        String awardKey = shootActivityRedis.drawCardKey(taskType);

        if (StringUtils.isEmpty(awardKey)) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.SERVER_ERROR).setMsg("error to shootDraw"));
        }
        return awardKey;
    }

    // 下发奖励及写记录
    private void recordShootDB(ActorData currActor, String awardKey, EventShootConfig.ShootDrawConfig drawConfig, String activityId, Integer ballType) {

        RewardInfoData rewardInfo = new RewardInfoData(Integer.parseInt(activityId), drawConfig.getRewardNum(), drawConfig.getRewardType(), PROD ? drawConfig.getDataId() : drawConfig.getTestDataId());
        giveOutRewardService.giveEventReward(currActor.getUid(), Collections.singletonList(rewardInfo), EVENT_CODE, ballType == 1 ? "draw-person" : "draw-team");

        saveDrawLog(currActor, awardKey, activityId, ballType);

        //  大奖通报
        dealBigAwardGlobalNotice(currActor, drawConfig);
    }

    private void dealBigAwardGlobalNotice(ActorData currActor, EventShootConfig.ShootDrawConfig drawConfig) {
        if (IM_NOTICE_AWARD_KEY_SET.contains(drawConfig.getCardKey())) {
//            String notice = "\uD83C\uDF89\uD83D\uDC4F\uD83C\uDF8ACongratulations to #username for shot #awardName in the Lucky Shot event\n" +
//                    "\uD83C\uDF89\uD83D\uDC4F\uD83C\uDF8AParticipate in the Lucky Shot event and Shot big prizes";
//            notice = notice.replace("#username", currActor.getName())
//                    .replace("#awardName", drawConfig.getTitle());
//            officialNoticeService.sendGlobalNotice("", notice, NOTICE_IMG, EVENT_URL, currActor.getChannel(), DateHelper.getCurrentTime(), 0, LangIdConstant.ENGLISH, null, 0);
            RewardInfoData reward = new RewardInfoData().setName(drawConfig.getTitle()).setNums(drawConfig.getRewardNum()).setIcon(drawConfig.getIcon()).setType(drawConfig.getRewardType());
            rewardService.sendUniversalActivityPopMsg(currActor, Collections.singletonList(reward), EVENT_URL, "Shot together",
                    "", "In lucky shot event get");
        }
    }

    private void saveDrawLog(ActorData currActor, String awardKey, String activityId, Integer ballType) {
        EventShootDrawLogData drawData = new EventShootDrawLogData();
        drawData.setUid(currActor.getUid());
        drawData.setAwardKey(awardKey);
        drawData.setCtime(DateHelper.getCurrTime());
        drawData.setBallType(ballType);
        drawData.setEventCode(Integer.valueOf(activityId));
        shootDrawDao.insertOneSelective(drawData);
    }


    /**
     * 射门卡抽取奖池逻辑
     */
    public List<ShootVO.DrawRecord> shootDraw(String activityId, String uid, int zone, Integer ballType, int amount) {
        long currTime = DateHelper.getCurrTime();
        int eventCode = Integer.parseInt(activityId);
        ActorData currActor = actorMgr.getCurrActorData(uid);
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventCode);
        checkActivityTime(configData, currTime, currActor.getChannel());

        if (amount != 1 && amount != 10) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "amount illegal"));
        }
        DateHelper dateHelper = DateHelper.genDateHelper(currActor.getChannel());
        String userInfoKey = hashGlobalUserInfoKey(activityId);
        String dailyTeamInfoKey = hashDailyTeamInfoKey(activityId, dateHelper.getToday());
        UserInfoBO currUserInfoBO = genUserInfoBO(uid, userInfoKey);
        // 抽奖逻辑
        List<ShootVO.DrawRecord> drawRecordList = doDraw(activityId, zone, ballType, amount, currUserInfoBO, currActor);
        baseHashSaveRedis.saveToRedis(userInfoKey, currUserInfoBO.getUid(), currUserInfoBO, Duration.ofDays(14));
        // 队长足球获取逻辑
        dealLeaderGetBall(currUserInfoBO, dailyTeamInfoKey, userInfoKey, configData);
        // 队伍榜单数据统计 以及节点任务奖励触发
        if (zone != 0 && StringUtils.hasLength(currUserInfoBO.getLeaderUid())) {
            checkAndSendTeamRankTaskReward(amount, configData, currUserInfoBO, userInfoKey);
        }
        // 抽奖数据上报数数
        reportShootDrawToThinkData(uid, ballType, amount, currActor, currTime, eventCode, drawRecordList, currUserInfoBO.getLeaderUid());
        return drawRecordList;
    }

    private void checkAndSendTeamRankTaskReward(int amount, AppConfigActivityData configData, UserInfoBO currUserInfoBO, String userInfoKey) {
        String teamRankKey = zsetTeamRankKey(configData.getActivityCode().toString());
        long preCount = baseZSetRedis.getOne(teamRankKey, currUserInfoBO.getLeaderUid()).getCount();
        long afterCount = baseZSetRedis.increaseAndGet(teamRankKey, currUserInfoBO.getLeaderUid(), amount, Duration.ofDays(30));

        List<RewardTaskConfig> canGiveRewardConfigs = TEAM_REWARDS.stream()
                .filter(config -> config.getCheckParams() > preCount && config.getCheckParams() <= afterCount)
                .collect(Collectors.toList());
        if (canGiveRewardConfigs.isEmpty()) {
            return;
        }
        List<RewardInfoData> rewards = new ArrayList<>(8);
        AtomicReference<String> taskNames = new AtomicReference<>("lv");
        canGiveRewardConfigs.forEach(config -> {
            rewards.addAll(config.getRewards());
            taskNames.updateAndGet(v -> v + config.getTaskName() + "/");
        });
        UserInfoBO leaderUserInfo = genUserInfoBO(currUserInfoBO.getLeaderUid(), userInfoKey);
        giveTeamRankTaskRewardAndNotice(leaderUserInfo.getUid(), rewards, taskNames.get());
        leaderUserInfo.getTeamInfoBO().getTeamMembers()
                .forEach(memberInfo -> giveTeamRankTaskRewardAndNotice(memberInfo.getUid(), rewards, taskNames.get()));
    }

    private void giveTeamRankTaskRewardAndNotice(String uid, List<RewardInfoData> rewards, String pointsStr) {
        ActorData actorData = actorMgr.getActorData(uid);
        if (actorData == null) return;
        giveOutRewardService.giveEventReward(uid, rewards, EVENT_CODE, "team task-reward " + pointsStr);
        // 发放官方通知
        String notice = "\uD83D\uDE01 Congratulations to the follows get Team shot level Rewards in Lucky Shot Even；\n" +
                "\uD83D\uDE01 The reward has been issued~";
        int fixTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        officialNoticeService.sendOfficialNotice(uid, "Congratulations", notice, NOTICE_IMG, EVENT_URL, actorData.getChannel(), fixTime, EVENT_CODE);
    }

    private void reportShootDrawToThinkData(String uid, Integer ballType, int amount, ActorData currActor, long currTime, int eventCode, List<ShootVO.DrawRecord> drawRecordList, String leaderUid) {
        DrawPrizesRecordLogData logData = new DrawPrizesRecordLogData();
        logData.setUid(uid);
        Map<String, Integer> resMap = fillResMap(drawRecordList);
        logData.setChannel(currActor.getChannel())
                .setCtime(currTime)
                .setScene(eventCode)
                .setCostTicket((long) amount)
                .setDrawNums(amount)
                .setDrawSuccessNums(drawRecordList.size())
                .setDrawDesc(ballType.toString())
                .setDrawDetail(leaderUid)
                .setDrawResult(JSON.toJSONString(resMap));
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private static Map<String, Integer> fillResMap(List<ShootVO.DrawRecord> drawRecordList) {
        Map<String, Integer> resMap = new HashMap<>(10);
        if (!ObjectUtils.isEmpty(drawRecordList)) {
            drawRecordList.forEach(drawRecord -> fillResMap(drawRecord, resMap));
        }
        return resMap;
    }

    private static void fillResMap(ShootVO.DrawRecord drawRecord, Map<String, Integer> resMap) {
        Integer nums = resMap.getOrDefault(drawRecord.getTitle(), 0);
        resMap.put(drawRecord.getTitle(), nums + 1);
    }

    private List<ShootVO.DrawRecord> doDraw(String activityId, int zone, Integer ballType, int amount, UserInfoBO currUserInfoBO, ActorData currActor) {
        List<ShootVO.DrawRecord> drawRecordList = new ArrayList<>(10);
        switch (ballType) {
            case 1:
                // 金球逻辑
                doGoldBallDraw(activityId, ballType, amount, currUserInfoBO, drawRecordList, currActor, zone);
                break;
            case 2:
                // 足球逻辑
                dowBallDraw(activityId, ballType, amount, currUserInfoBO, drawRecordList, currActor, zone);
                break;
            default:
                throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("ballType error"));

        }
        return drawRecordList;
    }

    private void dealLeaderGetBall(UserInfoBO currUserInfoBO, String dailyTeamInfoKey, String userInfoKey, AppConfigActivityData configData) {
        if (!StringUtils.hasLength(currUserInfoBO.getLeaderUid()) || currUserInfoBO.getLeaderUid().equals(currUserInfoBO.getUid())) {
            return;
        }
        TeamInfoBO dailyTeamInfo = baseHashSaveRedis.getDataByRedis(dailyTeamInfoKey, currUserInfoBO.getLeaderUid(), TeamInfoBO.class);
        if (dailyTeamInfo == null) {
            return;
        }
        dailyTeamInfo.getTeamMembers().stream()
                .filter(member -> member.getUid().equals(currUserInfoBO.getUid()))
                .filter(member -> member.getStatus() == 1)
                .findFirst().ifPresent(member -> dealGiveLeaderBall(member, dailyTeamInfo, userInfoKey, configData));
        baseHashSaveRedis.saveToRedis(dailyTeamInfoKey, dailyTeamInfo.getLeaderUid(), dailyTeamInfo, Duration.ofDays(7));
    }

    private void dealGiveLeaderBall(TeamPersonalInfoBO member, TeamInfoBO dailyTeamInfo, String userInfoKey, AppConfigActivityData configData) {
        member.setStatus(2);
        UserInfoBO leaderUserInfo = genUserInfoBO(dailyTeamInfo.getLeaderUid(), userInfoKey);
        int incr = 1;
        leaderUserInfo.increaseBallBalance(incr);
        baseHashSaveRedis.saveToRedis(userInfoKey, leaderUserInfo.getUid(), leaderUserInfo, Duration.ofDays(14));
        // 足球获得，上报数数
        reportGetTicketToThinkData(configData, leaderUserInfo.getUid(), 2, "2", incr);
    }

    private void reportGetTicketToThinkData(AppConfigActivityData configData, String uid, int ticketType, String resource, int incr) {
        ActivityTicketsEvent logData = new ActivityTicketsEvent();
        logData.setUid(uid);
        logData.setCtime(DateHelper.getCurrTime());
        ActorData actorData = actorMgr.getActorData(uid);
        if (actorData != null) {
            logData.setChannel(actorData.getUid());
        } else {
            logData.setChannel(configData.getChannel());
        }
        logData.setEventCode(configData.getActivityCode());
        logData.setActivityName(configData.getName());
        logData.setTicketType(ticketType);
        logData.setActivityTicketsResource(resource);
        logData.setActivityTickets(incr);
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private void doTeamInviteLogic(UserInfoBO currUserInfoBO, String inviterUid, String userInfoKey, ActorData currActor, String dailyTeamInfoKey, AppConfigActivityData configData) {
        if (StringUtils.hasLength(currUserInfoBO.getLeaderUid()) || inviterUid.equals(currUserInfoBO.getUid())) {
            return;
        }
        UserInfoBO inviterUserInfo = genUserInfoBO(inviterUid, userInfoKey);
        if (StringUtils.hasLength(inviterUserInfo.getLeaderUid()) && !inviterUserInfo.getLeaderUid().equals(inviterUid)) {
            return;
        }
        ActorData inviterActor = actorMgr.getActorData(inviterUid);
        if (inviterActor == null) {
            return;
        }
        TeamPersonalInfoBO member = new TeamPersonalInfoBO(currActor, 0, DateHelper.getCurrTime());
        if (inviterUserInfo.getTeamInfoBO() == null) {
            TeamInfoBO teamInfo = initTeamInfo(inviterActor);
            teamInfo.getTeamMembers().add(member);
            inviterUserInfo.setLeaderUid(inviterActor.getUid());
            inviterUserInfo.setTeamInfoBO(teamInfo);
            // 队长勋章下发
            dealTeamMemberReward(inviterUserInfo, configData, currActor.getChannel());

        } else {
            if (inviterUserInfo.getTeamInfoBO().getTeamMembers().size() >= TEAM_MAX_MEMBERS) {
                return;
            }
            inviterUserInfo.getTeamInfoBO().getTeamMembers().add(member);
        }
        baseHashSaveRedis.saveToRedis(userInfoKey, inviterUid, inviterUserInfo, Duration.ofDays(14));
        currUserInfoBO.setLeaderUid(inviterUid);
        // 队员勋章下发(根据时间下发）
        dealTeamMemberReward(currUserInfoBO, configData, currActor.getChannel());

        //日数据队员射门状态处理
        dealDailyTeamInfoMembers(inviterUid, dailyTeamInfoKey, inviterUserInfo, member);
        baseHashSaveRedis.saveToRedis(userInfoKey, currActor.getUid(), currUserInfoBO, Duration.ofDays(14));
    }

    private void dealTeamMemberReward(UserInfoBO currUserInfoBO, AppConfigActivityData configData, String channel) {
//        long expireSec = configData.getEndTime() - DateHelper.getCurrTime();
//        long daySec = Duration.ofDays(1).getSeconds();
//        long nums = expireSec / daySec;
//        if (expireSec % daySec > 0) {
//            nums++;
//        }
//        RewardInfoData reward = SpringUtils.copyObj(TEAM_MEMBER_REWARDS, RewardInfoData.class);
//        reward.setNums((int) nums);
        giveOutRewardService.giveEventReward(currUserInfoBO.getUid(), Collections.singletonList(TEAM_MEMBER_REWARDS), configData.getActivityCode(), "Team member reward");
        // 组队成功官方消息
        String notice = "Congratulations on forming a team successfully, and getting the team football Madel. go to shoot more rewards for free~";
        officialNoticeService.sendOfficialNotice(currUserInfoBO.getUid(), configData.getName(), notice, NOTICE_IMG, EVENT_URL, channel,
                DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50), configData.getActivityCode());
    }

    private void dealDailyTeamInfoMembers(String inviterUid, String dailyTeamInfoKey, UserInfoBO inviterUserInfo, TeamPersonalInfoBO member) {
        TeamInfoBO dailyTeamInfo = baseHashSaveRedis.getDataByRedis(dailyTeamInfoKey, inviterUid, TeamInfoBO.class);
        if (dailyTeamInfo == null) {
            dailyTeamInfo = initDailyTeamInfo(inviterUserInfo);
        } else {
            if (dailyTeamInfo.getTeamMembers().stream()
                    .anyMatch(memberInfo -> memberInfo.getUid().equals(member.getUid()))) {
                return;
            }
            dailyTeamInfo.getTeamMembers().add(member);
        }
        baseHashSaveRedis.saveToRedis(dailyTeamInfoKey, inviterUid, dailyTeamInfo, Duration.ofDays(7));
    }

    private TeamInfoBO initDailyTeamInfo(UserInfoBO inviterUserInfo) {
        TeamInfoBO dailyTeamInfo;
        dailyTeamInfo = new TeamInfoBO(inviterUserInfo.getTeamInfoBO());
        inviterUserInfo.getTeamInfoBO().getTeamMembers()
                .forEach(memberInfo -> memberInfo.setStatus(0));
        dailyTeamInfo.setTeamMembers(inviterUserInfo.getTeamInfoBO().getTeamMembers());
        return dailyTeamInfo;
    }

    private TeamInfoBO initTeamInfo(ActorData inviterActor) {
        return new TeamInfoBO()
                .setLeaderUid(inviterActor.getUid())
                .setHead(inviterActor.getHeadIcon())
                .setTeamName(inviterActor.getName());
    }

    private void dowBallDraw(String activityId, Integer ballType, int amount, UserInfoBO currUserInfoBO, List<ShootVO.DrawRecord> drawRecordList, ActorData currActor, int zone) {
        if (currUserInfoBO.getBallBalance() < amount) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.BALANCE_NOT_ENOUGH).setMsg("your ball not enough"));
        }
        currUserInfoBO.increaseBallBalance(-amount);
        if (zone == 0) {
            return;
        }
        Map<String, EventShootConfig.ShootDrawConfig> ballDrawMap = genBallDrawMap();
        IntStream.range(0, amount)
                .mapToObj(i -> shootDrawFromRedisPool(BALL_SHOOT_TYPE, ballDrawMap, 100))
                .forEachOrdered(awardKey -> dealAfterProcess(activityId, awardKey, ballDrawMap, drawRecordList, currActor, ballType));
    }

    private void doGoldBallDraw(String activityId, Integer ballType, int amount, UserInfoBO currUserInfoBO, List<ShootVO.DrawRecord> drawRecordList, ActorData currActor, int zone) {
        if (currUserInfoBO.getGoldBallBalance() < amount) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.BALANCE_NOT_ENOUGH).setMsg("your ball not enough"));
        }
        currUserInfoBO.increaseGoldBallBalance(-amount);
        if (zone == 0) {
            return;
        }
        Map<String, EventShootConfig.ShootDrawConfig> goldBallDrawMap = genGoldBallDrawMap();
        IntStream.range(0, amount)
                .mapToObj(i -> shootDrawFromRedisPool(GOLD_BALL_SHOOT_TYPE, goldBallDrawMap, 1000))
                .forEachOrdered(awardKey -> dealAfterProcess(activityId, awardKey, goldBallDrawMap, drawRecordList, currActor, ballType));
    }

    private void dealAfterProcess(String activityId, String awardKey, Map<String, EventShootConfig.ShootDrawConfig> drawMap, List<ShootVO.DrawRecord> drawRecordList, ActorData currActor, Integer ballType) {
        EventShootConfig.ShootDrawConfig drawConfig = drawMap.get(awardKey);
        if (drawConfig == null) {
            return;
        }
        fillDrawRecordList(drawConfig, drawRecordList);
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                recordShootDB(currActor, awardKey, drawConfig, activityId, ballType);
            }
        });
    }

    private Map<String, EventShootConfig.ShootDrawConfig> genGoldBallDrawMap() {
        return eventShootConfig.getGoldBallDrawList().stream()
                .collect(Collectors.toMap(EventShootConfig.ShootDrawConfig::getCardKey, Function.identity()));
    }

    private Map<String, EventShootConfig.ShootDrawConfig> genBallDrawMap() {
        return eventShootConfig.getBallDrawList().stream()
                .collect(Collectors.toMap(EventShootConfig.ShootDrawConfig::getCardKey, Function.identity()));
    }

    private Map<String, EventShootConfig.ShootDrawConfig> genAllDrawMap() {
        return Stream.concat(genGoldBallDrawMap().values().stream(), genBallDrawMap().values().stream())
                .collect(Collectors.toMap(EventShootConfig.ShootDrawConfig::getCardKey, Function.identity()));
    }

    /**
     * 射门抽奖记录
     */
    public ShootDrawRecordVO shootDrawRecord(String uid, String activityId, int page) {
        ShootDrawRecordVO vo = new ShootDrawRecordVO();
        int pageSize = 10;
        int eventCode = Integer.parseInt(activityId);
        List<EventShootDrawLogData> recordList = shootDrawDao.selectList(uid, eventCode, page, pageSize);
        if (ObjectUtils.isEmpty(recordList)) {
            return vo;
        }

        Map<String, EventShootConfig.ShootDrawConfig> drawConfigMap = genAllDrawMap();
        List<ShootDrawRecordVO.DrawRecord> drawRecordList = recordList.stream()
                .map(drawData -> this.fillDrawRecordVO(drawData, drawConfigMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        vo.setDrawRecordList(drawRecordList);
        vo.setNextUrl(recordList.size() < pageSize ? -1 : page + 1);
        return vo;
    }

    private ShootDrawRecordVO.DrawRecord fillDrawRecordVO(EventShootDrawLogData drawData, Map<String, EventShootConfig.ShootDrawConfig> drawConfigMap) {
        ShootDrawRecordVO.DrawRecord drawRecord = new ShootDrawRecordVO.DrawRecord();
        EventShootConfig.ShootDrawConfig drawConfig = drawConfigMap.get(drawData.getAwardKey());
        if (drawConfig == null) {
            return null;
        }
        drawRecord.setTitle(drawConfig.getTitle());
        drawRecord.setTitleAr(drawConfig.getTitleAr());
        drawRecord.setIcon(drawConfig.getIcon());
        drawRecord.setCtime(drawData.getCtime());
        drawRecord.setBallType(drawData.getBallType());
        return drawRecord;
    }

    public ShootVO inviteShoot(String activityId, String uid, String invitedUid) {
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(uid);
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(Integer.parseInt(activityId));
        checkActivityTime(configData, currTime, currActor.getChannel());
        DateHelper dateHelper = DateHelper.genDateHelper(currActor.getChannel());

        String dailyTeamInfoKey = hashDailyTeamInfoKey(activityId, dateHelper.getToday());
        TeamInfoBO dailyTeamInfo = baseHashSaveRedis.getDataByRedis(dailyTeamInfoKey, uid, TeamInfoBO.class);
        if (dailyTeamInfo == null) {
            dailyTeamInfo = initDailyTeamInfo(activityId, uid);
        }
        dailyTeamInfo.getTeamMembers().stream()
                .filter(member -> member.getUid().equals(invitedUid))
                .filter(member -> member.getStatus() == 0)
                .findFirst()
                .ifPresent(member -> dealMemberShootStatusAndSendNotice(member, currActor, currTime, configData));
        baseHashSaveRedis.saveToRedis(dailyTeamInfoKey, uid, dailyTeamInfo, Duration.ofDays(7));
        return shootConfig(activityId, uid, "");
    }

    private void dealMemberShootStatusAndSendNotice(TeamPersonalInfoBO member, ActorData currActor, long currTime, AppConfigActivityData configData) {
        member.setStatus(1);
        // 发送官方消息给被邀请人
        String notice = "I have successfully invited you，Play football with me and get free rewards.";
        officialNoticeService.sendOfficialNotice(member.getUid(), configData.getName(), notice, NOTICE_IMG, EVENT_URL, currActor.getChannel(),
                (int) (currTime + MathUtils.randomSplitInt(0, 50)), configData.getActivityCode());
    }

    private TeamInfoBO initDailyTeamInfo(String activityId, String uid) {
        TeamInfoBO dailyTeamInfo;
        String userInfoKey = hashGlobalUserInfoKey(activityId);
        UserInfoBO currUserInfo = genUserInfoBO(uid, userInfoKey);
        if (!currUserInfo.getLeaderUid().equals(currUserInfo.getUid())) {
            throw new WebException(new HttpEnvData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "you are not leader"));
        }
        dailyTeamInfo = initDailyTeamInfo(currUserInfo);
        return dailyTeamInfo;
    }

    /**
     * 队伍榜奖励
     */
    public void teamRankRewardAndNotice(AppConfigActivityData configData) {
        String teamRankKey = zsetTeamRankKey(configData.getActivityCode().toString());
        String globalUserInfoKey = hashGlobalUserInfoKey(configData.getActivityCode().toString());

        List<CountVO> dataList = baseZSetRedis.getRange(teamRankKey, 1, 10);
        if (ObjectUtils.isEmpty(dataList)) {
            return;
        }
        CountVO top1 = dataList.get(0);
        UserInfoBO leaderUserInfo = genUserInfoBO(top1.getUid(), globalUserInfoKey);
        giveTeamTop1Reward(configData, leaderUserInfo.getUid());
        leaderUserInfo.getTeamInfoBO().getTeamMembers()
                .forEach(member -> giveTeamTop1Reward(configData, member.getUid()));
        // 团队榜第一奖励全服通知文案
        String notice = ("\uD83D\uDE01 Congratulations to the #teamName's Team shot Most successful goals\n" +
                "\uD83D\uDE01Team members received the highest honor award of the team\n" +
                "\uD83D\uDE01 The reward has been issued~")
                .replace("#teamName", leaderUserInfo.getTeamInfoBO().getTeamName());
        int fixTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        Set<String> channelSet = StringUtils.getStrSetFromStr(configData.getChannel(), ",");
        channelSet.forEach(channel ->
                officialNoticeService.sendGlobalNotice(configData.getName(), notice, NOTICE_IMG, EVENT_URL, channel, fixTime,
                        configData.getActivityCode(), LangIdConstant.ENGLISH, null, OfficialUserGroup.CHANNEL));
    }

    private void giveTeamTop1Reward(AppConfigActivityData configData, String uid) {
        ActorData actorData = actorMgr.getActorData(uid);
        if (actorData == null) return;
        giveOutRewardService.giveEventReward(uid, TEAM_RANK_TOP1_REWARDS, configData.getActivityCode(), "team rank-mvp");
    }

    public TeamRankVO teamRank(String activityId, String uid) {
        String teamRankKey = zsetTeamRankKey(activityId);
        String globalUserInfoKey = hashGlobalUserInfoKey(activityId);
        List<CountVO> dataList = baseZSetRedis.getRange(teamRankKey, 1, 10);
        if (ObjectUtils.isEmpty(dataList)) {
            return new TeamRankVO();
        }
        List<TeamRankItem> rankList = IntStream.range(0, dataList.size())
                .mapToObj(index -> this.fillTeamRankItem(index + 1, dataList.get(index), globalUserInfoKey))
                .collect(Collectors.toList());

        //个人数据获取
        long teamCount = 0L;
        UserInfoBO userInfoBO = genUserInfoBO(uid, globalUserInfoKey);
        if (StringUtils.hasLength(userInfoBO.getLeaderUid())) {
            CountVO meTeamCountVO = baseZSetRedis.getOne(teamRankKey, userInfoBO.getLeaderUid());
            teamCount = meTeamCountVO.getCount();
        }
        return new TeamRankVO().setTeamRankList(rankList).setTeamCount(teamCount);
    }

    private TeamRankItem fillTeamRankItem(int rankNum, CountVO data, String globalUserInfoKey) {
        UserInfoBO leaderUserInfo = genUserInfoBO(data.getUid(), globalUserInfoKey);
        return new TeamRankItem()
                .setRankNum(rankNum)
                .setTeamInfo(leaderUserInfo.getTeamInfoBO())
                .setScore(data.getCount());
    }
}
