package com.quhong.event.lover.v2501;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpRes;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.data.dto.BaseDrawDTO;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.dto.RecordDTO;
import com.quhong.data.vo.BaseHistoryVO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.event.lover.data.dto.ExchangeDTO;
import com.quhong.event.lover.data.vo.AwardRecordVO;
import com.quhong.event.lover.data.vo.GuardianRankVO;
import com.quhong.event.lover.data.vo.LetterRecordVO;
import com.quhong.event.lover.data.vo.PageInitVO;
import com.quhong.utils.DistributeLockUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * V2501/情人节活动
 *
 * <AUTHOR>
 * @since 2025/1/18 11:36
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("${server.baseurl}/lover/v2501")
public class LoverControllerV2501 {
    /**
     * pre+eventCode+uid
     */
    private static final String LOCK_KEY = "event:lock:";


    private final LoverServiceV2501 loverServiceV2501;
    private final DistributeLockUtils distributeLockUtils;

    /**
     * 页面初始化
     */
    @GetMapping("/pageInit")
    public HttpRes<PageInitVO> pageInit(CommonDTO dto) {
        log.info("pre lover pageInit,dto={}", JSON.toJSONString(dto));
        PageInitVO vo = loverServiceV2501.pageInit(dto);
        log.info("end lover pageInit result:{}", JSON.toJSONString(vo));
        return HttpRes.success(vo);
    }

    /**
     * 抽奖
     */
    @GetMapping("/draw")
    public HttpRes<List<AwardInfo>> draw(BaseDrawDTO dto) {
        log.info("pre lover draw, dto={}", JSON.toJSONString(dto));
        String lockKey = LOCK_KEY + dto.getEventType() + ":" + dto.getUid();
        List<AwardInfo> vo = distributeLockUtils.distributeMethod(dto, lockKey, loverServiceV2501::draw);
        log.info("end lover draw result:{}", JSON.toJSONString(vo));
        return HttpRes.success(vo);
    }

    /**
     * 抽奖记录
     */
    @GetMapping("/draw/record")
    public HttpRes<BaseHistoryVO<AwardRecordVO>> drawRecord(RecordDTO dto) {
        log.info("pre lover draw record, dto={}", JSON.toJSONString(dto));
        BaseHistoryVO<AwardRecordVO> vo = loverServiceV2501.drawRecord(dto);
        log.info("end lover draw record result:{}", JSON.toJSONString(vo));
        return HttpRes.success(vo);
    }

    /**
     * 兑换物品
     */
    @GetMapping("/exchange")
    public HttpRes<Long> exchange(ExchangeDTO dto) {
        log.info("pre lover exchange, dto={}", JSON.toJSONString(dto));
        String lockKey = LOCK_KEY + dto.getEventType() + ":" + dto.getUid();
        Long balance = distributeLockUtils.distributeMethod(dto, lockKey, loverServiceV2501::exchange);
        log.info("end lover exchange result:{}", balance);
        return HttpRes.success(balance);
    }

    /**
     * 兑换物品记录
     */
    @GetMapping("/exchange/record")
    public HttpRes<BaseHistoryVO<AwardRecordVO>> exchangeRecord(RecordDTO dto) {
        log.info("pre lover exchange record, dto={}", JSON.toJSONString(dto));
        BaseHistoryVO<AwardRecordVO> vo = loverServiceV2501.exchangeRecord(dto);
        log.info("end lover exchange record result:{}", JSON.toJSONString(vo));
        return HttpRes.success(vo);
    }

    /**
     * 发送信件
     *
     * @param rid  接受者
     * @param mode 1私聊  2官方通知
     */
    @GetMapping("/send/letter")
    public HttpRes<Long> sendLetter(CommonDTO dto, Long rid, Integer mode, String receiveUid) {
        log.info("pre lover send letter, dto={}, rid={}, receiveUid={}", JSON.toJSONString(dto), rid, receiveUid);
        String lockKey = LOCK_KEY + dto.getEventType() + ":" + dto.getUid();
        Long balance = distributeLockUtils.distributeMethod(dto, lockKey, (d)->loverServiceV2501.sendLetter(d, rid, mode, receiveUid));
        log.info("end lover send letter result:{}", balance);
        return HttpRes.success(balance);
    }

    /**
     * 发送信件记录
     */
    @GetMapping("/send/letter/record")
    public HttpRes<BaseHistoryVO<LetterRecordVO>> sendLetterRecord(RecordDTO dto) {
        log.info("pre lover send letter record, dto={}", JSON.toJSONString(dto));
        BaseHistoryVO<LetterRecordVO> vo = loverServiceV2501.letterRecord(dto);
        log.info("end lover send letter record result:{}", JSON.toJSONString(vo));
        return HttpRes.success(vo);
    }

    /**
     * 爱心排行榜
     */
    @GetMapping("/lover/rank")
    public HttpRes<ModelRankVO<RankRowVO>> loverRank(CommonDTO dto) {
        log.info("pre lover rank, dto={}", JSON.toJSONString(dto));
        ModelRankVO<RankRowVO> vo = loverServiceV2501.loverRank(dto);
        log.info("end lover rank result:{}", JSON.toJSONString(vo));
        return HttpRes.success(vo);
    }

    /**
     * 守护榜
     */
    @GetMapping("/receiver/rank")
    public HttpRes<ModelRankVO<GuardianRankVO>> receiverRank(CommonDTO dto) {
        log.info("pre receiver rank, dto={}", JSON.toJSONString(dto));
        ModelRankVO<GuardianRankVO> vo = loverServiceV2501.receiverRank(dto);
        log.info("end receiver rank result:{}", JSON.toJSONString(vo));
        return HttpRes.success(vo);
    }

    /**
     * 榜单奖励测试
     * @param rankType 1:爱心榜  2:守护榜
     */
    @GetMapping("/test/rank/reward")
    public HttpRes<Boolean> testRankReward(CommonDTO dto, Integer rankType) {
        if (ServerConfiguration.isProduct()) {
            return HttpRes.success(false);
        }
        log.info("rank get, uid={}, eventType={}, rankType={}", dto.getUid(), dto.getEventType(), rankType);
        switch (rankType) {
            case 1:
                loverServiceV2501.loverRankReward(dto.getEventType());
                break;
            case 2:
                loverServiceV2501.receiverRankReward(dto.getEventType());
                break;
            default:
                return HttpRes.success(false);
        }
        return HttpRes.success(true);
    }

}
