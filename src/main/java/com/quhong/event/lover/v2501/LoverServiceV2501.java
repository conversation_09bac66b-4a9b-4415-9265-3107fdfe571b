package com.quhong.event.lover.v2501;

import com.alibaba.fastjson.JSON;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.LangIdConstant;
import com.quhong.constant.OfficialUserGroup;
import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.ActorDao;
import com.quhong.dao.ActorExternalDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.LogPayRecordDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.config.TaskConfig;
import com.quhong.data.dto.BaseDrawDTO;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.dto.RecordDTO;
import com.quhong.data.thData.ActivitySpecialItemsEvent;
import com.quhong.data.thData.DrawPrizesRecordLogData;
import com.quhong.data.vo.BaseAwardVO;
import com.quhong.data.vo.BaseHistoryVO;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.ActorInfo;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.enums.EventCode;
import com.quhong.enums.PayChannelType;
import com.quhong.enums.RewardItemType;
import com.quhong.event.diwali.v2412.data.vo.GiftPackVO;
import com.quhong.event.lover.data.bo.UserInfoBO;
import com.quhong.event.lover.data.dto.ExchangeDTO;
import com.quhong.event.lover.data.vo.AwardRecordVO;
import com.quhong.event.lover.data.vo.GuardianRankVO;
import com.quhong.event.lover.data.vo.LetterRecordVO;
import com.quhong.event.lover.data.vo.PageInitVO;
import com.quhong.exceptions.WebException;
import com.quhong.moderatoin.ModerationService;
import com.quhong.mq.data.activity.PlayGameMsgData;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.redis.BaseHashSaveRedis;
import com.quhong.redis.base.currency.task.BaseEveryLimitRedis;
import com.quhong.redis.base.reward.BaseOneLimitRewardRedis;
import com.quhong.report.EventReport;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.common.*;
import com.quhong.utils.StringUtils;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2025/1/18 11:38
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LoverServiceV2501 {
    public static final boolean PROD = ServerConfiguration.isProduct();
    public static final int EVENT_CODE = EventCode.EVENT_LOVER_2501;
    public static final String EVENT_URL = PROD ? "https://videochat.kissu.site/hi_202502/" : "https://testvideochat.kissu.site/hi_202502/";
    public static final String NOTICE_IMG = "https://statics.kissu.mobi/event/Valentine's/Day/2025/preview/notice.jpg";
    public static final String HEART_ICON = "https://statics.kissu.mobi/event/Valentine's/Day/2025/v5/like-ico.png";

    public static final int MIN_PAY_USD = 50;
    private static final Set<Integer> PAY_TYPE_SET = new HashSet<Integer>() {{
        add(PayChannelType.MIDDLE_ORDER);
        add(PayChannelType.MIDDLE_SUBSCRIPTION);
    }};

    private static final Set<Integer> STATUS_SET = new HashSet<Integer>() {{
        add(1);
        if (!PROD) add(10);
    }};


    private static final List<RewardInfoData> DAILY_SEND_GIFT_TASK_REWARD = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.SEAT_FRAME, PROD ? 345 : 21, 0));
    }};
    private static final RewardTaskConfig DAILY_SEND_GIFT_TASK = new RewardTaskConfig(TaskTypeConstant.SEND_GIFT,
            1, 1, DAILY_SEND_GIFT_TASK_REWARD);
    private static final TaskConfig PLAY_GAME_TASK = new TaskConfig(TaskTypeConstant.PLAY_GAME_COST_GOLD, 10000, 1, -1);

    private static final TaskConfig SEND_EVENT_GIFT_TASK = new TaskConfig(TaskTypeConstant.SEND_GIFT, 200, 1, -1);

    /**
     * 情书奖励
     */
    private static final List<RewardInfoData> LETTER_REWARDS = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.SEAT_FRAME, PROD ? 346 : 20, 0));
    }};
    /**
     * 奖池配置
     */
    public static final List<AwardInfo> AWARD_POOL_CONFIGS = new ArrayList<AwardInfo>() {{
        add(new AwardInfo().setRate(0).setRate1(100).setAwardType(RewardItemType.ENTER_EFFECT).setAwardId(9).setAwardName("Gifts car").setDataId(PROD ? 350 : 22).setNums(1).setUnitPrice(0).setShowNums("1day").setShowPrice("").setAwardIcon("https://statics.kissu.mobi/room_item/icon/1737541257331/gifts_car.png"));
        add(new AwardInfo().setRate(100).setRate1(0).setAwardType(RewardItemType.ENTER_EFFECT).setAwardId(1).setAwardName("Ferris wheel of love").setDataId(PROD ? 345 : 66).setNums(1).setUnitPrice(0).setShowNums("1day").setShowPrice("").setAwardIcon("https://statics.kissu.mobi/room_item/origin/1737532917216/Ferris_wheel_of_love.webp"));
        add(new AwardInfo().setRate(147).setRate1(130).setAwardType(RewardItemType.GIFT).setAwardId(2).setAwardName("Tell Him").setDataId(612).setNums(1).setUnitPrice(0).setShowNums("1").setShowPrice("").setAwardIcon("https://statics.kissu.mobi/Event/Speak/speak.png"));
        add(new AwardInfo().setRate(300).setRate1(300).setAwardType(RewardItemType.SEAT_FRAME).setAwardId(3).setAwardName("Golden aperture").setDataId(PROD ? 353 : 101).setNums(1).setUnitPrice(0).setShowNums("1day").setShowPrice("").setAwardIcon("https://statics.kissu.mobi/room_item/origin/1737620080710/Golden_aperture.webp"));
        add(new AwardInfo().setRate(10).setRate1(10).setAwardType(RewardItemType.GOLD).setAwardId(4).setAwardName("10000 coins").setDataId(0).setNums(250).setUnitPrice(1).setShowNums("10000").setShowPrice("").setAwardIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png"));
        add(new AwardInfo().setRate(2).setRate1(10).setAwardType(RewardItemType.GOLD).setAwardId(5).setAwardName("20000 coins").setDataId(0).setNums(500).setUnitPrice(1).setShowNums("20000").setShowPrice("").setAwardIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png"));
        add(new AwardInfo().setRate(100).setRate1(100).setAwardType(RewardItemType.GOLD).setAwardId(6).setAwardName("1000 coins").setDataId(0).setNums(25).setUnitPrice(1).setShowNums("1000").setShowPrice("").setAwardIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png"));
        add(new AwardInfo().setRate(100).setRate1(100).setAwardType(RewardItemType.EVENT_CURRENCY).setAwardId(7).setAwardName("Ring").setDataId(1).setNums(1).setUnitPrice(0).setShowNums("1").setShowPrice("500coins").setAwardIcon("https://statics.kissu.mobi/even/df8c9f928579c83dbff0c593888e0452f3a471715bea-g7Qc51_fw658webp_拷贝@2x.png"));
        add(new AwardInfo().setRate(241).setRate1(250).setAwardType(RewardItemType.EVENT_CURRENCY).setAwardId(8).setAwardName("love letter").setDataId(2).setNums(1).setUnitPrice(0).setShowNums("1").setShowPrice("").setAwardIcon("https://statics.kissu.mobi/even/601c90a6fb60184f149023a1bcd7d2d4cda91739884a-5sQ6kC_fw658webp_拷贝*********"));
    }};

    /**
     * 礼包配置
     */
    private static final List<GiftPackVO> GIFT_PACKS = new ArrayList<GiftPackVO>() {{
        List<BaseAwardVO> pack1 = new ArrayList<BaseAwardVO>() {{
            BaseAwardVO award1 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/event/Valentine's/Day/2025/v2/Purple_Villa.png")
                    .name("purple villa").count(1).type(RewardItemType.GIFT).unitCost(250)
                    .showNums("1")
                    .showPrice("10000coins")
                    .showVideo("https://statics.kissu.mobi/event/Valentine's/Day/2025/v2/Purple_Villa_combined.mp4")
                    .dataId(PROD ? 2030 : 206).build();
            add(award1);
        }};
        add(new GiftPackVO().setPackId(1).setNeedCardType(0).setPrice(20).setPackName("purple villa")
                .setPackIcon("https://statics.kissu.mobi/event/Valentine's/Day/2025/v2/Purple_Villa.png").setAwards(pack1));

        List<BaseAwardVO> pack2 = new ArrayList<BaseAwardVO>() {{
            BaseAwardVO award1 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/event/Valentine's/Day/2025/v3/Romantic_Swing.png")
                    .name("Romantic swing").count(1).type(RewardItemType.GIFT).unitCost(375)
                    .showNums("1")
                    .showPrice("15000coins")
                    .showVideo("https://statics.kissu.mobi/event/Valentine's/Day/2025/v3/Romantic_Swing_combined.mp4")
                    .dataId(PROD ? 2032 : 207).build();
            add(award1);
        }};
        add(new GiftPackVO().setPackId(2).setNeedCardType(0).setPrice(30).setPackName("Romantic swing")
                .setPackIcon("https://statics.kissu.mobi/event/2025/Christmas/Love_gift.png").setAwards(pack2));

        List<BaseAwardVO> pack3 = new ArrayList<BaseAwardVO>() {{
            BaseAwardVO award1 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/event/Valentine's/Day/2025/v1/Music_Box.png")
                    .name("Music Box").count(1).type(RewardItemType.GIFT).unitCost(125)
                    .showNums("1")
                    .showPrice("5000coins")
                    .showVideo("https://statics.kissu.mobi/event/Valentine's/Day/2025/v1/%E5%90%88%E6%88%90_1_1.mp4")
                    .dataId(PROD ? 2027 : 208).build();
            add(award1);
        }};
        add(new GiftPackVO().setPackId(3).setNeedCardType(0).setPrice(10).setPackName("Music Box")
                .setPackIcon("https://statics.kissu.mobi/event/Valentine's/Day/2025/v1/Music_Box.png").setAwards(pack3));

        List<BaseAwardVO> pack4 = new ArrayList<BaseAwardVO>() {{
            BaseAwardVO award1 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/room_item/icon/1737628122468/Y.webp")
                    .name("Happy Valentine's Day").count(1).type(RewardItemType.DESIGNATION).unitCost(0)
                    .showNums("1day")
                    .dataId(PROD ? 354 : 91).build();
            add(award1);
        }};
        add(new GiftPackVO().setPackId(4).setNeedCardType(0).setPrice(1).setPackName("Happy Valentine's Day")
                .setPackIcon("https://statics.kissu.mobi/room_item/icon/1737628122468/Y.webp")
                .setAwards(pack4));

        List<BaseAwardVO> pack5 = new ArrayList<BaseAwardVO>() {{
            BaseAwardVO award1 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/icon/aristocrat/King_little.png")
                    .name("King").count(1).type(RewardItemType.LORD_DAYS).unitCost(0)
                    .showNums("1day")
                    .dataId(6).build();
            add(award1);
        }};
        add(new GiftPackVO().setPackId(5).setNeedCardType(0).setPrice(5).setPackName("King")
                .setPackIcon("https://statics.kissu.mobi/icon/aristocrat/King_little.png")
                .setAwards(pack5));
        List<BaseAwardVO> pack6 = new ArrayList<BaseAwardVO>() {{
            BaseAwardVO award1 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/room_item/icon/1737541707741/merry-go-round.png")
                    .name("merry-go-round").count(1).type(RewardItemType.BUBBLE_FRAME).unitCost(0)
                    .showNums("1day")
                    .dataId(PROD ? 351 : 17).build();
            add(award1);
        }};
        add(new GiftPackVO().setPackId(6).setNeedCardType(0).setPrice(1).setPackName("merry-go-round")
                .setPackIcon("https://statics.kissu.mobi/room_item/icon/1737541707741/merry-go-round.png")
                .setAwards(pack6));
    }};


    private static final List<RewardTaskConfig> LOVE_RANK_REWARD = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 348 : 97));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.SEAT_FRAME, PROD ? 347 : 55));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.BUBBLE_FRAME, PROD ? 352 : 61));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 11, RewardItemType.ENTER_EFFECT, PROD ? 348 : 97));
            add(new RewardInfoData(EVENT_CODE, 11, RewardItemType.SEAT_FRAME, PROD ? 347 : 55));
            add(new RewardInfoData(EVENT_CODE, 11, RewardItemType.BUBBLE_FRAME, PROD ? 352 : 61));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 9, RewardItemType.ENTER_EFFECT, PROD ? 348 : 97));
            add(new RewardInfoData(EVENT_CODE, 9, RewardItemType.SEAT_FRAME, PROD ? 347 : 55));
            add(new RewardInfoData(EVENT_CODE, 9, RewardItemType.BUBBLE_FRAME, PROD ? 352 : 61));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 348 : 97));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 347 : 55));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.BUBBLE_FRAME, PROD ? 352 : 61));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));
    }};

    private static final List<RewardTaskConfig> RECEIVE_RANK_REWARD = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 348 : 97));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.SEAT_FRAME, PROD ? 347 : 55));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.BUBBLE_FRAME, PROD ? 352 : 61));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 11, RewardItemType.ENTER_EFFECT, PROD ? 348 : 97));
            add(new RewardInfoData(EVENT_CODE, 11, RewardItemType.SEAT_FRAME, PROD ? 347 : 55));
            add(new RewardInfoData(EVENT_CODE, 11, RewardItemType.BUBBLE_FRAME, PROD ? 352 : 61));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 9, RewardItemType.ENTER_EFFECT, PROD ? 348 : 97));
            add(new RewardInfoData(EVENT_CODE, 9, RewardItemType.SEAT_FRAME, PROD ? 347 : 55));
            add(new RewardInfoData(EVENT_CODE, 9, RewardItemType.BUBBLE_FRAME, PROD ? 352 : 61));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 348 : 97));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 347 : 55));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.BUBBLE_FRAME, PROD ? 352 : 61));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));
    }};

    private final AppConfigActivityDao appConfigActivityDao;
    private final ActorMgr actorMgr;
    private final BaseEveryLimitRedis baseEveryLimitRedis;
    private final GiveOutRewardService giveOutRewardService;
    private final OfficialNoticeService officialNoticeService;
    private final ActorExternalDao actorExternalDao;
    private final EventBaseRankService eventBaseRankService;
    private final BaseHashSaveRedis baseHashSaveRedis;
    private final ModerationService moderationService;
    private final ActorDao actorDao;
    private final BaseOneLimitRewardRedis baseOneLimitRewardRedis;
    private final AwardPoolService awardPoolService;
    private final LogPayRecordDao logPayRecordDao;
    private final EventBaseRecordService eventBaseRecordService;
    private final EventReport eventReport;
    private final RoomFloatingImService roomFloatingImService;
    private final RewardService rewardService;

    /**
     * 每日送礼奖励key
     */
    private String dailyLimitRewardTaskKey(Integer eventCode, String date) {
        return eventCode + ":" + date;
    }

    /**
     * 用户活动信息
     */
    private String hashUserInfoKey(Integer eventCode) {
        return "hash:event:user_info:" + eventCode;
    }

    /**
     * 抽奖记录key
     */
    private String listDrawRecordKey(Integer eventCode, String uid) {
        return "list:event:draw_record:" + eventCode + ":" + uid;
    }

    /**
     * 兑换物品记录key
     */
    private String listExchangeRecordKey(Integer eventCode, String uid) {
        return "list:event:exchange_record:" + eventCode + ":" + uid;
    }

    /**
     * 送情书记录key
     */
    private String listSendLetterRecordKey(Integer eventCode, String uid) {
        return "list:event:send_letter_record:" + eventCode + ":" + uid;
    }

    /**
     * love榜单Key
     */
    private String zsetLoverRankKey(Integer eventCode) {
        return "zset:event:lover_rank:" + eventCode;
    }

    /**
     * 守护榜key
     */
    private String zsetReceiverRankKey(Integer eventCode) {
        return "zset:event:receiver_rank:" + eventCode;
    }

    /**
     * 守护子榜key
     */
    private String zsetReceiverSonRankKey(Integer eventCode, String receiverUid) {
        return "zset:event:receiver_son_rank:" + eventCode + ":" + receiverUid;
    }

    public PageInitVO pageInit(CommonDTO dto) {
        dto.checkParams();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        String userInfoKey = hashUserInfoKey(dto.getEventType());
        UserInfoBO userInfo = findUserInfo(currActor.getUid(), userInfoKey);
        //三方支付奖池标签逻辑判定
        long currTime = DateHelper.getCurrTime();
        if (configData.getStartTime() <= currTime
                && configData.getEndTime() >= currTime
                && configData.getChannel().contains(currActor.getChannel())) {
            signThirdPayUser(userInfo, userInfoKey);
        }
        boolean isThirdPayUser = userInfo.getIsThirdPayUser() != null && userInfo.getIsThirdPayUser();
        List<AwardInfo> pool = genPool(isThirdPayUser);
        return new PageInitVO()
                .setUid(currActor.getUid())
                .setStartTime(configData.getStartTime())
                .setEndTime(configData.getEndTime())
                .setLoveBalance(userInfo.getLoveBalance())
                .setRingBalance(userInfo.getRingBalance())
                .setLetterBalance(userInfo.getLetterBalance())
                .setPrizePoolConfig(pool)
                .setGiftPackList(GIFT_PACKS);
    }

    private static List<AwardInfo> genPool(boolean isThirdPayUser) {
        if (isThirdPayUser) {
            return AWARD_POOL_CONFIGS.stream()
                    .filter(awardInfo -> awardInfo.getRate1() > 0)
                    .collect(Collectors.toList());
        }
        return AWARD_POOL_CONFIGS.stream()
                .filter(awardInfo -> awardInfo.getRate() > 0)
                .collect(Collectors.toList());

    }

    private void signThirdPayUser(UserInfoBO userInfo, String userInfoKey) {
        if (userInfo.getIsThirdPayUser() != null) {
            return;
        }
        long startTime = DateHelper.getCurrTime() - Duration.ofDays(30).getSeconds();
        String usd = logPayRecordDao.queryUsdPriceSum(userInfo.getUid(), PAY_TYPE_SET, STATUS_SET, startTime, null);
        boolean isThirdPayUser = StringUtils.hasLength(usd)
                && Double.parseDouble(usd) >= MIN_PAY_USD;
        userInfo.setIsThirdPayUser(isThirdPayUser);
        baseHashSaveRedis.saveToRedis(userInfoKey, userInfo.getUid(), userInfo, Duration.ofDays(30));
    }

    public List<AwardInfo> draw(BaseDrawDTO dto) {
        dto.checkParams();
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        checkLimit(configData, currTime, currActor);
        //货币扣除
        String userInfoKey = hashUserInfoKey(dto.getEventType());
        UserInfoBO userInfo = decreaseLoverBalance(dto, userInfoKey);
        //抽奖消耗爱心上报数数
        reportCostLove(dto, currActor, configData);
        //抽奖逻辑
        String poolType = userInfo.getIsThirdPayUser() != null && userInfo.getIsThirdPayUser() ? "1" : "0";
        List<AwardInfo> awardInfos = doDraw(dto, poolType);
        // 发奖励
        List<RewardInfoData> rewards = awardInfos.stream()
                .map(this::fillRewardInfoData)
                .collect(Collectors.toList());
        giveOutRewardService.giveEventReward(dto.getUid(), rewards, dto.getEventType(), "2");
        // 抽奖记录上报数数
        reportDrawRecord(dto, currActor, currTime, configData, poolType, awardInfos);
        // 大奖全服广播
        dealBigRewardUniversalFullNotice(dto, rewards, configData);

        //抽奖记录逻辑
        saveDrawRecord(dto, awardInfos, currTime);
        afterProcess(dto, awardInfos, userInfoKey, currActor, configData);
        awardInfos.sort(Comparator.comparingInt(AwardInfo::getAwardId));
        return awardInfos;
    }

    private void dealBigRewardUniversalFullNotice(BaseDrawDTO dto, List<RewardInfoData> rewards, AppConfigActivityData configData) {
        List<RewardInfoData> bigRewards = rewards.stream().filter(reward -> reward.getId() == 5)
                .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(bigRewards)) {
            return;
        }
        bigRewards.forEach(reward -> dealNotice(dto, configData, reward));
    }

    private void dealNotice(BaseDrawDTO dto, AppConfigActivityData configData, RewardInfoData reward) {
        reward.setNums(-1);
        rewardService.sendUniversalFullServiceNoticeMsg(dto.getUid(), reward, EVENT_URL, configData.getName(),
                "#name get #rewardIcon #rewardName #num in " + configData.getName(), true);
    }

    private void reportCost(ActorData currActor, AppConfigActivityData configData, String itemId,
                            int itemCount, String fromType, String fromTypeDesc, ActorData receiverActor, Integer exchangeAmount) {
        ActivitySpecialItemsEvent logData = new ActivitySpecialItemsEvent();
        logData.setUid(currActor.getUid());
        logData.setCtime(DateHelper.getCurrTime());
        logData.setChannel(currActor.getChannel());
        logData.setEventCode(configData.getActivityCode());
        logData.setActivityName(configData.getName());
        logData.setItemId(itemId);
        logData.setChangeAction(2);
        logData.setFromType(fromType);
        logData.setFromTypeDesc(fromTypeDesc);
        logData.setItemCount(itemCount);
        if (exchangeAmount != null) {
            logData.setExchangeAmount(exchangeAmount);
        }
        if (receiverActor != null) {
            logData.setToUid(receiverActor.getUid());
            logData.setToRid(receiverActor.getRid());
        }
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private UserInfoBO decreaseBalance(String userInfoKey, String uid, int amount, String balanceTypeStr) {
        BalanceTypeEnum balanceTypeEnum = BalanceTypeEnum.fromString(balanceTypeStr);
        UserInfoBO userInfo = findUserInfo(uid, userInfoKey);
        long currentBalance = balanceTypeEnum.getGetBalance().apply(userInfo);
        if (currentBalance < amount) {
            throw new WebException(HttpCode.ITEM_NOT_ENOUGH);
        }
        balanceTypeEnum.getIncrementBalance().accept(userInfo, (long) -amount);
        baseHashSaveRedis.saveToRedis(userInfoKey, uid, userInfo);
        return userInfo;
    }


    private void reportGet(ActorData currActor, AppConfigActivityData configData, String itemId, int itemCount, String fromType) {
        ActivitySpecialItemsEvent logData = new ActivitySpecialItemsEvent();
        logData.setUid(currActor.getUid());
        logData.setCtime(DateHelper.getCurrTime());
        logData.setChannel(currActor.getChannel());
        logData.setEventCode(configData.getActivityCode());
        logData.setActivityName(configData.getName());
        logData.setItemId(itemId);
        logData.setChangeAction(1);
        logData.setFromType(fromType);
        logData.setFromTypeDesc("");
        logData.setItemCount(itemCount);
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private void reportDrawRecord(BaseDrawDTO dto, ActorData currActor, long currTime, AppConfigActivityData configData, String poolType, List<AwardInfo> awardInfos) {
        DrawPrizesRecordLogData logData = new DrawPrizesRecordLogData();
        logData.setUid(dto.getUid());
        logData.setChannel(currActor.getChannel())
                .setCtime(currTime)
                .setScene(configData.getActivityCode())
                .setSceneDetail(Integer.valueOf(poolType))
                .setCostTicket((long) dto.getAmount())
                .setDrawNums(dto.getAmount())
                .setDrawSuccessNums(dto.getAmount())
                .setDrawResult(JSON.toJSONString(awardInfos));
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private void reportCostLove(BaseDrawDTO dto, ActorData currActor, AppConfigActivityData configData) {
        reportCost(currActor, configData, "0", dto.getAmount(), "3", "", null, null);
    }


    private RewardInfoData fillRewardInfoData(AwardInfo awardInfo) {
        return new RewardInfoData(EVENT_CODE, awardInfo.getNums(), awardInfo.getAwardType(),
                awardInfo.getDataId(), awardInfo.getUnitPrice())
                .setName(awardInfo.getAwardName())
                .setIcon(awardInfo.getAwardIcon())
                .setUnitCost(awardInfo.getUnitPrice())
                .setId(awardInfo.getAwardId());
    }

    private void afterProcess(BaseDrawDTO dto, List<AwardInfo> awardInfos, String userInfoKey, ActorData currActor, AppConfigActivityData configData) {
        UserInfoBO userInfo;
        //获得戒指逻辑
        int ringIncr = computeIncrBalance(awardInfos, 1);//1代表戒指
        //获得信封逻辑
        int letterIncr = computeIncrBalance(awardInfos, 2);//2代表信封
        userInfo = findUserInfo(dto.getUid(), userInfoKey);
        if (ringIncr > 0) {
            userInfo.incrementRing(ringIncr);
            //获得戒指上报数数
            reportGetRing(currActor, configData, ringIncr);
        }
        if (letterIncr > 0) {
            userInfo.incrementLetter(letterIncr);
            //获得信封上报数数
            reportGetLetter(currActor, configData, letterIncr);
        }
        baseHashSaveRedis.saveToRedis(userInfoKey, dto.getUid(), userInfo);
    }

    private void reportGetRing(ActorData currActor, AppConfigActivityData configData, int ringIncr) {
        reportGet(currActor, configData, "1", ringIncr, "1");
    }


    private void reportGetLetter(ActorData currActor, AppConfigActivityData configData, int letterIncr) {
        reportGet(currActor, configData, "2", letterIncr, "1");
    }


    private UserInfoBO decreaseLoverBalance(BaseDrawDTO dto, String userInfoKey) {
        return decreaseBalance(userInfoKey, dto.getUid(), dto.getAmount(), "love");
    }


    private List<AwardInfo> doDraw(BaseDrawDTO dto, String poolType) {
        List<AwardInfo> awardInfos = awardPoolService.drawAwardFromRedisPool(dto.getEventType(), poolType,
                AWARD_POOL_CONFIGS, 1000, dto.getAmount());
        if (ObjectUtils.isEmpty(awardInfos)) {
            log.error("draw zone award failed,awardPool={}", poolType);
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, true, "Award pool is empty"));
        }
        return awardInfos;
    }

    private void saveDrawRecord(BaseDrawDTO dto, List<AwardInfo> awardInfos, long currTime) {
        List<AwardRecordVO> recordList = awardInfos.stream()
                .map(awardInfo -> new AwardRecordVO(awardInfo, currTime))
                .collect(Collectors.toList());
        String drawRecordKey = listDrawRecordKey(dto.getEventType(), dto.getUid());
        eventBaseRecordService.leftPushAll(drawRecordKey, recordList);
    }

    private static int computeIncrBalance(List<AwardInfo> awardInfos, int dataId) {
        return awardInfos.stream()
                .filter(awardInfo -> awardInfo.getAwardType() == RewardItemType.EVENT_CURRENCY)
                .filter(awardInfo -> awardInfo.getDataId() == dataId)
                .mapToInt(AwardInfo::getNums)
                .sum();
    }


    public Long exchange(ExchangeDTO dto) {
        dto.checkParams();
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        checkLimit(configData, currTime, currActor);

        GiftPackVO giftPack = GIFT_PACKS.stream().filter(pack -> pack.getPackId().equals(dto.getPackId()))
                .findFirst().orElse(null);
        if (giftPack == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "not found gift packs"));
        }
        int needBalance = giftPack.getPrice() * dto.getAmount();
        UserInfoBO userInfo = decreaseRingBalance(dto, needBalance);
        //消耗戒指上报数数
        reportCostRing(dto, currActor, configData, needBalance, giftPack);

        List<RewardInfoData> rewards = giftPack.getAwards().stream()
                .map(awardInfo -> genRewardInfoData(dto, configData, awardInfo))
                .collect(Collectors.toList());
        //兑换逻辑
        giveOutRewardService.giveEventReward(dto.getUid(), rewards, dto.getEventType(), "1");
        //兑换记录逻辑
        String exchangeRecordKey = listExchangeRecordKey(dto.getEventType(), dto.getUid());
        IntStream.range(0, dto.getAmount())
                .forEach(index -> saveExchangeRecords(giftPack, exchangeRecordKey));
        return userInfo.getRingBalance();
    }

    private void reportCostRing(ExchangeDTO dto, ActorData currActor, AppConfigActivityData configData, int needBalance, GiftPackVO giftPack) {
        reportCost(currActor, configData, "1", needBalance, "2", giftPack.getPackName(), null, dto.getAmount());
    }


    private UserInfoBO decreaseRingBalance(ExchangeDTO dto, int needBalance) {
        String userInfoKey = hashUserInfoKey(dto.getEventType());
        return decreaseBalance(userInfoKey, dto.getUid(), needBalance, "ring");
    }


    private void saveExchangeRecords(GiftPackVO giftPack, String exchangeRecordKey) {
        List<AwardRecordVO> records = giftPack.getAwards().stream()
                .map(AwardRecordVO::new)
                .collect(Collectors.toList());
        eventBaseRecordService.leftPushAll(exchangeRecordKey, records);
    }

    private static RewardInfoData genRewardInfoData(ExchangeDTO dto, AppConfigActivityData configData, BaseAwardVO awardInfo) {
        return new RewardInfoData()
                .setActivityType(configData.getActivityCode())
                .setType(awardInfo.getType())
                .setNums(dto.getAmount() * awardInfo.getCount())
                .setUnitCost(awardInfo.getUnitCost())
                .setDataId(awardInfo.getDataId())
                .setName(awardInfo.getName())
                .setIcon(awardInfo.getImg());
    }

    private static void checkLimit(AppConfigActivityData configData, long currTime, ActorData currActor) {
        if (configData.getStartTime() > currTime) {
            throw new WebException(HttpCode.EVENT_NOT_START);
        }
        if (configData.getEndTime() < currTime) {
            throw new WebException(HttpCode.EVENT_ENDED);
        }
        if (!configData.getChannel().contains(currActor.getChannel())) {
            throw new WebException(HttpCode.CHANNEL_NOT_JOIN_EVENT);
        }
    }

    public Long sendLetter(@NonNull CommonDTO dto, Long rid, Integer mode, String receiveUid) {
        dto.checkParams();
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        checkLimit(configData, currTime, currActor);
        if (mode == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "mode is null"));
        }
        ActorData receiverActor = findReceiverActor(rid, receiveUid);
        //送情书
        String userInfoKey = hashUserInfoKey(EVENT_CODE);
        UserInfoBO userInfo = decreaseLetterBalance(dto, userInfoKey);
        //消耗情书上报数数
        reportCostLetter(currActor, configData, receiverActor);

        giveOutRewardService.giveEventReward(dto.getUid(), LETTER_REWARDS, dto.getEventType(), "3");
        giveOutRewardService.giveEventReward(receiverActor.getUid(), LETTER_REWARDS, dto.getEventType(), "3");

        // 给对方发送官方消息
        String notice = "By received love letters to friends, you get a frame reward*1day, which has been sent to your backpack~";
        int fixTime = (int) (currTime + MathUtils.randomSplitInt(0, 50));
        officialNoticeService.sendOfficialNotice(receiverActor.getUid(), configData.getName(), notice, NOTICE_IMG,
                EVENT_URL, receiverActor.getChannel(), fixTime, configData.getActivityCode());
        //送情书记录
        saveLetterRecord(dto.getEventType(), dto.getUid(), "send", receiverActor, currTime);
        saveLetterRecord(dto.getEventType(), receiverActor.getUid(), "receive", currActor, currTime);
        return userInfo.getLetterBalance();
    }

    private ActorData findReceiverActor(Long rid, String receiveUid) {
        ActorData receiverActor;
        if (StringUtils.hasLength(receiveUid)) {
            receiverActor = actorMgr.getCurrActorData(receiveUid);
            return receiverActor;
        }
        if (rid == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "ID must have value"));
        }
        receiverActor = actorDao.getActorByRid(rid);
        if (receiverActor == null) {
            throw new WebException(HttpCode.ACTOR_NOT_EXIST);
        }
        return receiverActor;
    }

    private void reportCostLetter(ActorData currActor, AppConfigActivityData configData, ActorData receiverActor) {
        reportCost(currActor, configData, "2", 1, "2", "", receiverActor, null);
    }


    private UserInfoBO decreaseLetterBalance(CommonDTO dto, String userInfoKey) {
        return decreaseBalance(userInfoKey, dto.getUid(), 1, "letter");
    }


    private void saveLetterRecord(int eventCode, String uid, String send, ActorData actor, long currTime) {
        String letterRecordKey = listSendLetterRecordKey(eventCode, uid);
        eventBaseRecordService.leftPush(letterRecordKey, new LetterRecordVO(send, actor.getRid(), currTime));
    }


    public BaseHistoryVO<AwardRecordVO> drawRecord(RecordDTO dto) {
        String drawRecordKey = listDrawRecordKey(dto.getEventType(), dto.getUid());
        return eventBaseRecordService.findRecord(dto, drawRecordKey, AwardRecordVO.class);
    }


    public BaseHistoryVO<AwardRecordVO> exchangeRecord(RecordDTO dto) {
        String exchangeRecordKey = listExchangeRecordKey(dto.getEventType(), dto.getUid());
        return eventBaseRecordService.findRecord(dto, exchangeRecordKey, AwardRecordVO.class);
    }

    public BaseHistoryVO<LetterRecordVO> letterRecord(RecordDTO dto) {
        String letterRecordKey = listSendLetterRecordKey(dto.getEventType(), dto.getUid());
        return eventBaseRecordService.findRecord(dto, letterRecordKey, LetterRecordVO.class);
    }

    public ModelRankVO<RankRowVO> loverRank(CommonDTO dto) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        String rankKey = zsetLoverRankKey(dto.getEventType());
        return eventBaseRankService.rank(currActor, rankKey);
    }

    public ModelRankVO<GuardianRankVO> receiverRank(CommonDTO dto) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        String rankKey = zsetReceiverRankKey(dto.getEventType());
        ModelRankVO<RankRowVO> rank = eventBaseRankService.rank(currActor, rankKey);
        // 变为GuardianRankVO,并填充mvp异性数据
        //列表数据处理，个人数据处理
        List<GuardianRankVO> rankList = rank.getRankList()
                .stream()
                .map(row -> this.fillGuardianRankVO(row, dto))
                .collect(Collectors.toList());
        GuardianRankVO selfData = fillGuardianRankVO(rank.getSelf(), dto);
        return new ModelRankVO<>(selfData, rankList);
    }

    private GuardianRankVO fillGuardianRankVO(RankRowVO row, CommonDTO dto) {
        GuardianRankVO vo = new GuardianRankVO(row);
        ActorInfo mvp = new ActorInfo();
        vo.setMvpInfo(mvp);
        if (!StringUtils.hasLength(vo.getActorInfo().getUid())) {
            return vo;
        }
        String rankKey = zsetReceiverSonRankKey(dto.getEventType(), vo.getActorInfo().getUid());
        List<CountVO> rankList = eventBaseRankService.getRankList(rankKey, 1, 1);
        if (ObjectUtils.isEmpty(rankList)) {
            return vo;
        }
        ActorData mvpActor = actorMgr.getActorData(rankList.get(0).getUid());
        if (mvpActor != null) {
            mvp = new ActorInfo(mvpActor);
            String head = moderationService.dealRankHeadModeration(mvpActor);
            mvp.setHead(head);
            vo.setMvpInfo(mvp);
        }
        return vo;
    }

    public void sendGiftAction(SendGiftSuccessMsgData mqData) {
        long currTime = DateHelper.getCurrTime();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        if (configData.getStartTime() > currTime || configData.getEndTime() < currTime) {
            return;
        }
        if (!configData.getDataId().contains(mqData.getGiftId().intValue())) {
            return;
        }

        boolean sendNeed = false;
        try {
            sendNeed = dealSendLogic(mqData, configData);
        } catch (WebException e) {
            log.error("send gift error, msg={}", e.getHttpCode());
        } catch (Exception e) {
            log.error("send gift error, uid={}, giftId={}", mqData.getUid(), mqData.getGiftId(), e);
        }

        boolean receiveNeed = false;
        try {
            receiveNeed = dealReceiveLogic(mqData, configData);
        } catch (WebException e) {
            log.error("receive gift error, msg={}", e.getHttpCode());
        } catch (Exception e) {
            log.error("receive gift error, uid={}, giftId={}", mqData.getUid(), mqData.getGiftId(), e);
        }

        if (!sendNeed || !receiveNeed) {
            return;
        }
        try {
            dealReceiveSonRankLogic(mqData, configData);
        } catch (WebException e) {
            log.error("receive gift error, msg={}", e.getHttpCode());
        } catch (Exception e) {
            log.error("receive gift error, uid={}, giftId={}", mqData.getUid(), mqData.getGiftId(), e);
        }

    }

    private void dealReceiveSonRankLogic(SendGiftSuccessMsgData mqData, AppConfigActivityData configData) {
        ActorData sendActor = actorMgr.getCurrActorData(mqData.getUid());
        ActorData receiveActor = actorMgr.getCurrActorData(mqData.getToUid());
        if (sendActor.getSex().equals(receiveActor.getSex())) {
            return;
        }
        String rankKey = zsetReceiverSonRankKey(configData.getActivityCode(), mqData.getToUid());
        eventBaseRankService.rankValueIncrease(rankKey, mqData.getUid(), mqData.fetchRealCost());
    }

    private boolean dealReceiveLogic(SendGiftSuccessMsgData mqData, AppConfigActivityData configData) {
        if (actorExternalDao.isTester(mqData.getToUid())) {
            return false;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getToUid());
        if (!configData.getChannel().contains(currActor.getChannel())) {
            return false;
        }
        double finalCost = mqData.fetchRealCost();
        String rankKey = zsetReceiverRankKey(configData.getActivityCode());
        eventBaseRankService.rankValueIncrease(rankKey, mqData.getToUid(), finalCost);
        return true;
    }

    /**
     * @return 返回是否需要计算守护子榜
     */
    private boolean dealSendLogic(SendGiftSuccessMsgData mqData, AppConfigActivityData configData) {
        boolean tester = actorExternalDao.isTester(mqData.getUid());
        if (tester) {
            return false;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        if (!configData.getChannel().contains(currActor.getChannel())) {
            return false;
        }
        dealDailySendGiftReward(configData, currActor);

        //获得爱心
        double finalCoin = mqData.fetchRealCost();
        int incr = baseEveryLimitRedis.increaseAndGetRewards(mqData.getUid(), configData.getActivityCode(), finalCoin, SEND_EVENT_GIFT_TASK);
        if (incr < 1) {
            return true;
        }
        dealGetLove(configData, mqData.getUid(), incr, currActor, "1");
        return true;
    }

    private void dealGetLove(AppConfigActivityData configData, String uid, int incr, ActorData currActor, String fromType) {
        //增加爱心余额
        String userInfoKey = hashUserInfoKey(configData.getActivityCode());
        UserInfoBO userInfo = findUserInfo(uid, userInfoKey);
        userInfo.incrementLove(incr);
        baseHashSaveRedis.saveToRedis(userInfoKey, userInfo.getUid(), userInfo);
        //飘屏
        sendGetLoveRoomFloatingIm(currActor, incr, configData);
        //获得爱心上报数数
        reportGetLove(configData, currActor, incr, fromType);
        //爱心榜
        String loveRankKey = zsetLoverRankKey(configData.getActivityCode());
        eventBaseRankService.rankValueIncrease(loveRankKey, uid, incr);
    }

    private void reportGetLove(AppConfigActivityData configData, ActorData currActor, int incr, String fromType) {
        reportGet(currActor, configData, "0", incr, fromType);
    }


    private void dealDailySendGiftReward(AppConfigActivityData configData, ActorData currActor) {
        DateHelper dateHelper = DateHelper.genDateHelper(currActor.getChannel());
        String key = dailyLimitRewardTaskKey(configData.getActivityCode(), dateHelper.getToday());
        List<RewardInfoData> dailyRewards = baseOneLimitRewardRedis.checkAndGetRewards(currActor.getUid(), key, DAILY_SEND_GIFT_TASK, 1);
        if (ObjectUtils.isEmpty(dailyRewards)) {
            return;
        }
        giveOutRewardService.giveEventReward(currActor.getUid(), dailyRewards, configData.getActivityCode(), "6");
        int fixTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        String notice = "By sending biggest gift to friendsFerris wheel of love*1day, which has been sent to your backpack~";
        officialNoticeService.sendOfficialNotice(currActor.getUid(), configData.getName(), notice, NOTICE_IMG, EVENT_URL, currActor.getChannel(), fixTime, EVENT_CODE);
    }

    public void playGameAction(PlayGameMsgData mqData) {
        long currTime = DateHelper.getCurrTime();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        if (configData.getStartTime() > currTime || configData.getEndTime() < currTime) {
            return;
        }
        boolean tester = actorExternalDao.isTester(mqData.getUid());
        if (tester) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        if (!configData.getChannel().contains(currActor.getChannel())) {
            return;
        }
        //获得爱心
        double finalCoin = Double.parseDouble(mqData.fetchCoin());
        int incr = baseEveryLimitRedis.increaseAndGetRewards(mqData.getUid(), configData.getActivityCode(), finalCoin, PLAY_GAME_TASK);
        if (incr < 1) {
            return;
        }
        dealGetLove(configData, mqData.getUid(), incr, currActor, "2");
    }

    private void sendGetLoveRoomFloatingIm(ActorData currActor, int incr, AppConfigActivityData configData) {
        String testModal = "Get #ticket X #count #suffix >>>";
        roomFloatingImService.sendRoomFloatingIm(currActor, incr, HEART_ICON, "heart", "in Valentine's Day Event", configData.getActivityCode(), EVENT_URL, false, testModal);
    }


    public UserInfoBO findUserInfo(String uid, String userInfoKey) {
        UserInfoBO globalUserInfo = baseHashSaveRedis.getDataByRedis(userInfoKey, uid, UserInfoBO.class);
        if (globalUserInfo != null) {
            return globalUserInfo;
        }
        return new UserInfoBO(uid);
    }


    public void loverRankReward(Integer eventType) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventType);
        String rankKey = zsetLoverRankKey(configData.getActivityCode());
        String rankName = "love ranking";
        eventBaseRankService.rankRewards(configData, rankKey, LOVE_RANK_REWARD, rankName,
                "5", NOTICE_IMG, EVENT_URL, true);
    }

    public void receiverRankReward(Integer eventType) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventType);
        String rankKey = zsetReceiverRankKey(configData.getActivityCode());
        String rankName = "guard ranking";
        StringBuilder noticeContent = new StringBuilder();
        List<CountVO> rankList = eventBaseRankService.rankRewards(configData, rankKey, RECEIVE_RANK_REWARD, rankName,
                "4", NOTICE_IMG, EVENT_URL, false);
        if (ObjectUtils.isEmpty(rankList)) {
            return;
        }
        //子榜逻辑处理
        IntStream.range(0, rankList.size())
                .forEach(index -> dealReceiveSonRankReward(index, rankList, configData, noticeContent));

        String globalNoticeFormatter = "\uD83D\uDE01 Congratulations to the follows get Top3 in \"" + configData.getName() + "\" Event " + rankName + "\n" +
                "#content" +
                "\uD83D\uDE01 The reward has been issued>>>>";
        String globalNotice = globalNoticeFormatter.replace("#content", noticeContent.toString());
        Set<String> channelSet = StringUtils.getStrSetFromStr(configData.getChannel(), ",");
        channelSet.forEach(channel ->
                officialNoticeService.sendGlobalNotice(configData.getName(), globalNotice, NOTICE_IMG, EVENT_URL, channel, DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50),
                        configData.getActivityCode(), LangIdConstant.ENGLISH, null, OfficialUserGroup.CHANNEL));
    }

    public static final String CONTENT_TEXT_MODAL = "Top#top   #rid   Protecting users：#mvpRid\n";

    private void dealReceiveSonRankReward(int index, List<CountVO> rankList, AppConfigActivityData configData, StringBuilder noticeContent) {
        int rankNum = index + 1;
        CountVO countVO = rankList.get(index);
        String mvpUid = findMvpUid(countVO, configData);
        List<RewardInfoData> rewards = RECEIVE_RANK_REWARD.stream()
                .filter(config -> config.getCheckParams() == rankNum)
                .flatMap(config -> config.getRewards().stream())
                .filter(reward -> reward.getType() != RewardItemType.GOLD)
                .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(rewards)) {
            return;
        }
        if (StringUtils.hasLength(mvpUid)) {
            giveOutRewardService.giveEventReward(mvpUid, rewards, configData.getActivityCode(), "4");
        }
        if (rankNum > 3) {
            return;
        }
        ActorData rankActor = actorMgr.getActorUidContainDel(countVO.getUid());
        ActorData mvpActor = null;
        if (StringUtils.hasLength(mvpUid)) {
            mvpActor = actorMgr.getActorUidContainDel(mvpUid);
        }
        String rowContent = CONTENT_TEXT_MODAL.replace("#top", String.valueOf(rankNum))
                .replace("#rid", rankActor == null ? "xxxxxxxx" : rankActor.getRid().toString())
                .replace("#mvpRid", mvpActor == null ? "" : mvpActor.getRid().toString());
        noticeContent.append(rowContent);
    }

    private String findMvpUid(CountVO countVO, AppConfigActivityData configData) {
        String sonRankKey = zsetReceiverSonRankKey(configData.getActivityCode(), countVO.getUid());
        List<CountVO> sonRanks = eventBaseRankService.getRankList(sonRankKey, 1, 1);
        if (ObjectUtils.isEmpty(sonRanks)) {
            return null;
        }
        return sonRanks.get(0).getUid();
    }
}
