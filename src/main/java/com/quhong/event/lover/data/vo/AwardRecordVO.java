package com.quhong.event.lover.data.vo;

import com.quhong.core.utils.DateHelper;
import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.data.vo.BaseAwardVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 奖品记录
 * <AUTHOR>
 * @since 2025/1/20 10:32
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AwardRecordVO {
    /**
     * 奖品数量
     */
    private Integer nums;
    /**
     * 奖品名称
     */
    private String awardName;
    /**
     * 奖品图标
     */
    private String awardIcon;

    /**
     * 显示的价值
     */
    private String showPrice;
    /**
     * 时间
     */
    private Long ctime;

    public AwardRecordVO(BaseAwardVO awardInfo) {
        this.nums = awardInfo.getCount();
        this.awardName = awardInfo.getName();
        this.awardIcon = awardInfo.getImg();
        this.showPrice = awardInfo.getShowPrice();
        this.ctime = DateHelper.getCurrTime();
    }

    public AwardRecordVO(AwardInfo awardInfo, Long ctime) {
        this.nums = awardInfo.getNums();
        this.awardName = awardInfo.getAwardName();
        this.awardIcon = awardInfo.getAwardIcon();
        this.showPrice = awardInfo.getShowPrice();
        this.ctime = ctime;
    }
}
