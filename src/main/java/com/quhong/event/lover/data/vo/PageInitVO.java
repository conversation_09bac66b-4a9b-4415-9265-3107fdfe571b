package com.quhong.event.lover.data.vo;

import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.event.diwali.v2412.data.vo.GiftPackVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/18 11:47
 */
@Data
@Accessors(chain = true)
public class PageInitVO {
    private String uid;

    private Long startTime;

    private Long endTime;
    /**
     * 爱心余额
     */
    private Long loveBalance;
    /**
     * 戒指余额
     */
    private Long ringBalance;
    /**
     * 信封余额
     */
    private Long letterBalance;
    /**
     * 奖池配置
     */
    private List<AwardInfo> prizePoolConfig;

    /**
     * 奖励兑换信息列表
     */
    private List<GiftPackVO> giftPackList;
}
