package com.quhong.event.welfare.v1;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.BaseHttpData;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.LangIdConstant;
import com.quhong.constant.OfficialUserGroup;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.WarnName;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.ActivityWelfareCommingActorInfoDao;
import com.quhong.dao.ActorExternalDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorBackpackData;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.db.ActivityWelfareCommingActorInfoData;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.dto.RankingDTO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.ActorInfo;
import com.quhong.data.vo.model.rank.row.independence.day.RankVO;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.RewardItemType;
import com.quhong.enums.TopTypeEnum;
import com.quhong.event.welfare.v1.data.bo.UserInfoBO;
import com.quhong.event.welfare.v1.data.config.DailyEnterRoomAwardLimitConfig;
import com.quhong.event.welfare.v1.data.config.DailyPlayGameAwardLimitConfig;
import com.quhong.event.welfare.v1.data.vo.InitPageVO;
import com.quhong.exceptions.WebException;
import com.quhong.game.constant.GameInfoConstant;
import com.quhong.game.data.GameInfoData;
import com.quhong.moderatoin.ModerationService;
import com.quhong.mq.data.activity.PlayGameMsgData;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.mq.data.room.EnterRoomMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.redis.base.ActivityCommonRedis;
import com.quhong.redis.base.currency.task.impl.DailyEnterRoomOneAwardLimitRedis;
import com.quhong.redis.base.currency.task.impl.DailyPlayGameOneAwardLimitRedis;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.activity.model.impl.IncomeRankActivityService;
import com.quhong.service.common.RewardService;
import com.quhong.service.common.RoomFloatingImService;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2023/10/9 16:39
 */
@Slf4j
//@Service
@RequiredArgsConstructor
public class WelfareCommingService extends IncomeRankActivityService {
    public static final String NOTICE_IMG = "https://statics.kissu.mobi/Event/Welfare/Battle/09/638_349.jpg";
    private static final boolean PROD = ServerConfiguration.isProduct();
    /**
     * 活动地址
     */
    private static final String EVENT_URL = PROD ? "https://videochat.kissu.site/welfare_competition/" : "https://testvideochat.kissu.site/welfare_competition/";
    private static final String NOTICE_RANK_STR = "Top#top   #rid   win： #count\n";
    //    private static final ActivityTypeEnum ACTIVITY_TYPE_ENUM = ActivityTypeEnum.HAPPY_HALLOWEEN_ACTIVITY;
//    private static final ActivityTypeEnum ACTIVITY_TYPE_ENUM = ActivityTypeEnum.WELFARE_COMMING_ACTIVITY;
//    private static final ActivityTypeEnum ACTIVITY_TYPE_ENUM = ActivityTypeEnum.HOLI_ACTIVITY;
//    private static final ActivityTypeEnum ACTIVITY_TYPE_ENUM = ActivityTypeEnum.HAYE_GARMI_ACTIVITY;
    private static final ActivityTypeEnum ACTIVITY_TYPE_ENUM = ActivityTypeEnum.EVENT_WELFARE_2409;
    private static final int EVENT_CODE = ACTIVITY_TYPE_ENUM.getCode();
    private static final List<RewardInfoData> COLLECT_REWARDS = Arrays.asList(
            new RewardInfoData(EVENT_CODE, 1, RewardItemType.SEAT_FRAME, PROD ? 220 : 75),
            new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, PROD ? 678 : 549, 250),
            new RewardInfoData(EVENT_CODE, 20, RewardItemType.GIFT, PROD ? 679 : 526, 3));
    private static final List<String> GIFT_COLLECT_LIST = PROD ? Arrays.asList("677", "676") : Arrays.asList("549", "511");
    /**
     * 活动期间每人领取礼包上限
     */
    private static final int COLLECT_REWARD_EVERY_ONE_LIMIT = 3;
    private final ActivityWelfareCommingActorInfoDao activityWelfareCommingActorInfoDao;
    private final ActorMgr actorMgr;
    private final AppConfigActivityDao appConfigActivityDao;
    private final GiveOutRewardService giveOutRewardService;
    private final DailyEnterRoomOneAwardLimitRedis dailyEnterRoomOneAwardLimitRedis;
    private final DailyPlayGameOneAwardLimitRedis dailyPlayGameOneAwardLimitRedis;
    private final ModerationService moderationService;
    private final MonitorSender monitorSender;
    private final ActorExternalDao actorExternalDao;
    private final ActivityCommonRedis activityCommonRedis;
    private final RoomFloatingImService roomFloatingImService;
    private final OfficialNoticeService officialNoticeService;
    private final RewardService rewardService;

    private static String getUserInfoKey(Integer eventCode, String uid) {
        return eventCode + ":" + uid;
    }

    private static String getCurrDateStr(AppConfigActivityData eventConfig, long currTime) {
        ZoneId zoneId = ZoneOffset.of(eventConfig.getZoneOffset());
        return Instant.ofEpochSecond(currTime)
                .atZone(zoneId)
                .format(DateTimeFormatter.ofPattern(eventConfig.getDateFormat()));
    }

    private static Map<String, GameInfoData> genGameMap() {
        Map<String, GameInfoData> gameMap;
        if (ServerConfiguration.isProduct()) {
            gameMap = GameInfoConstant.GAME_MAP;
        } else {
            gameMap = GameInfoConstant.GAME_TEST_MAP;
        }
        return gameMap;
    }

    private static void claimAwardLimit(AppConfigActivityData eventConfig, ActorData currActor, long currTime) {
        if (eventConfig == null) {
            log.info("activity config is not exist, eventCode={}", ACTIVITY_TYPE_ENUM.getCode());
            throw new WebException(new BaseHttpData(), HttpCode.OTHER_NOT_EXISTS);
        }
        if (!eventConfig.getChannel().equals(currActor.getChannel())) {
            throw new WebException(new BaseHttpData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "your app not join this event"));
        }
        if (currTime < eventConfig.getStartTime()) {
            throw new WebException(new BaseHttpData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "It's not time for the event yet"));
        }
        if (currTime > eventConfig.getEndTime()) {
            throw new WebException(new BaseHttpData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "The event has ended"));
        }
    }

    private static boolean limitNotJoinAndNotEventDuration(AppConfigActivityData eventConfig, ActorData currActor, Long currTime) {
        if (eventConfig == null) {
            log.info("activity config is not exist, eventCode={}", ACTIVITY_TYPE_ENUM.getCode());
            throw new WebException(new BaseHttpData(), HttpCode.OTHER_NOT_EXISTS);
        }

        if (!eventConfig.getChannel().equals(currActor.getChannel())) {
            return true;
        }
        if (currTime < eventConfig.getStartTime() || currTime > eventConfig.getEndTime()) {
            return true;
        }

        return false;
    }

    private static void checkSelfInTop(RankVO self, ActivityWelfareCommingActorInfoData rankData, int rankNum) {
        if (rankData.getUid().equals(self.getActorInfo().getUid())) {
            self.setRankNum(Integer.toString(rankNum));
            self.setScore(rankData.fetchRealCount().longValue());
            self.setRealScore(rankData.fetchRealCount().toString());
        }
    }

    public void enterRoomMqAction(EnterRoomMsgData mqData) {
        if (StringUtils.isEmpty(mqData.getUid())) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        AppConfigActivityData eventConfig = appConfigActivityDao.getOneByCodeAndValid(ACTIVITY_TYPE_ENUM.getCode(), 1);
        if (limitNotJoinAndNotEventDuration(eventConfig, currActor, mqData.getCurrTime())) return;
        if (currActor.getIsPayUser() != 1) {
            return;
        }
        dailyEnterRoomOneAwardLimitRedis.canGetAwardAfterAction(mqData.getUid(), eventConfig.getActivityCode());
    }

    public Boolean claimEnterRoomAward(CommonDTO dto) {
        if (StringUtils.isEmpty(dto.getUid()) || dto.getEventType() == null) {
            throw new WebException(new BaseHttpData(), HttpCode.PARAM_ERROR);
        }
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData eventConfig = appConfigActivityDao.getOneByCodeAndValid(dto.getEventType(), 1);
        claimAwardLimit(eventConfig, currActor, currTime);
        if (currActor.getIsPayUser() != 1) {
            throw new WebException(new BaseHttpData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "not meeting the claim conditions"));
        }
        boolean canGetAward = dailyEnterRoomOneAwardLimitRedis.checkCanGetAward(currActor.getUid(), dto.getEventType());
        if (!canGetAward) {
            throw new WebException(new BaseHttpData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "not meeting the claim conditions"));
        }

        String currDateStr = getCurrDateStr(eventConfig, currTime);

        saveEnterRoomAwardCount(dto, currActor, currDateStr);
        BaseTaskFactory.Util.slowTask(dto, this::giveEnterRoomDailyReward);
        // 进房奖励发放飘屏
        roomFloatingImService.sendRoomFloatingIm(currActor, 1, "https://statics.kissu.mobi/Event/Welfare/Battle/09/Cheer_Cannont.png",
                "Reward", ", To claim", eventConfig.getActivityCode(), EVENT_URL, false, "Get #ticket*#count#suffix >>>");
        dailyEnterRoomOneAwardLimitRedis.giveAwardAfterAction(currActor.getUid(), eventConfig.getActivityCode());
        return true;
    }

    private void giveEnterRoomDailyReward(CommonDTO dto) {
        giveOutRewardService.giveEventReward(dto.getUid(), DailyEnterRoomAwardLimitConfig.CONFIG.getRewards(), dto.getEventType(), "1");
    }

    private void saveEnterRoomAwardCount(CommonDTO dto, ActorData currActor, String currDateStr) {
        ActivityWelfareCommingActorInfoData data = activityWelfareCommingActorInfoDao.getOneByEventCodeAndUid(dto.getUid(), dto.getEventType());
        if (data == null) {
            data = ActivityWelfareCommingActorInfoData.BuilderHelper.initData(dto.getUid(), currActor.getRid(), dto.getEventType());
            data.increaseEnterRoomAwardCount(1, currDateStr);
            activityWelfareCommingActorInfoDao.insertOneSelective(data);
        } else {
            data.increaseEnterRoomAwardCount(1, currDateStr);
            ActivityWelfareCommingActorInfoData updateData = ActivityWelfareCommingActorInfoData.BuilderHelper.fillUpdateEnterRoomAwardCountData(data);
            activityWelfareCommingActorInfoDao.updateOneSelective(updateData);
        }
    }

    public void playGameAction(PlayGameMsgData mqData) {
        if (StringUtils.isEmpty(mqData.getUid())) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        AppConfigActivityData eventConfig = appConfigActivityDao.getOneByEventCodeThrowWebException(ACTIVITY_TYPE_ENUM.getCode());
        if (limitNotJoinAndNotEventDuration(eventConfig, currActor, mqData.getTs())) return;

        Map<String, GameInfoData> gameMap = genGameMap();
        if (!gameMap.containsKey(mqData.getGameId().toString())) {
            throw new WebException(new BaseHttpData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "not meeting the claim conditions"));
        }
        GameInfoData gameInfoData = gameMap.get(mqData.getGameId().toString());
        if (gameInfoData == null) {
            return;
        }
        Integer segCode = gameInfoData.getActivityType();
        if (!eventConfig.getUserSegCode().contains(segCode)) {
            return;
        }
        dailyPlayGameOneAwardLimitRedis.canGetAwardAfterAction(mqData.getUid(), eventConfig.getActivityCode());
    }

    public Boolean claimPlayGameAward(CommonDTO dto) {
        if (StringUtils.isEmpty(dto.getUid()) || dto.getEventType() == null) {
            throw new WebException(new BaseHttpData(), HttpCode.PARAM_ERROR);
        }
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData eventConfig = appConfigActivityDao.getOneByCodeAndValid(dto.getEventType(), 1);
        claimAwardLimit(eventConfig, currActor, currTime);
        boolean canGetAward = dailyPlayGameOneAwardLimitRedis.checkCanGetAward(currActor.getUid(), dto.getEventType());
        if (!canGetAward) {
            throw new WebException(new BaseHttpData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "not meeting the claim conditions"));
        }
        String currDateStr = getCurrDateStr(eventConfig, currTime);

        savePlayGameAwardCount(dto, currActor, currDateStr);
        BaseTaskFactory.Util.slowTask(dto, this::givePlayGameDailyReward);
        roomFloatingImService.sendRoomFloatingIm(currActor, 1, "https://statics.kissu.mobi/Event/Welfare/Battle/09/Cheer_Cannont.png",
                "Reward", ", To claim", EVENT_CODE, EVENT_URL, false, "Get #ticket*#count#suffix >>>");
        dailyPlayGameOneAwardLimitRedis.giveAwardAfterAction(currActor.getUid(), dto.getEventType());
        return true;
    }

    private void givePlayGameDailyReward(CommonDTO dto) {
        giveOutRewardService.giveEventReward(dto.getUid(), DailyPlayGameAwardLimitConfig.CONFIG.getRewards(), dto.getEventType(), "2");
    }

    private void savePlayGameAwardCount(CommonDTO dto, ActorData currActor, String currDateStr) {
        ActivityWelfareCommingActorInfoData data = activityWelfareCommingActorInfoDao.getOneByEventCodeAndUid(dto.getUid(), dto.getEventType());
        if (data == null) {
            data = ActivityWelfareCommingActorInfoData.BuilderHelper.initData(dto.getUid(), currActor.getRid(), dto.getEventType());
            data.increasePlayGameAwardCount(1, currDateStr);
            activityWelfareCommingActorInfoDao.insertOneSelective(data);
        } else {
            data.increasePlayGameAwardCount(1, currDateStr);
            ActivityWelfareCommingActorInfoData updateData = ActivityWelfareCommingActorInfoData.BuilderHelper.fillUpdatePlayGameAwardCountData(data);
            activityWelfareCommingActorInfoDao.updateOneSelective(updateData);
        }
    }

    public void sendGiftSuccessAction(SendGiftSuccessMsgData mqData) {
        checkParams(mqData);
        ActorData currActor = actorMgr.getCurrActorData(mqData.getToUid());
        if (actorExternalDao.isTester(currActor.getUid())) {
            return;
        }
        AppConfigActivityData eventConfig = appConfigActivityDao.getOneByCodeAndValid(ACTIVITY_TYPE_ENUM.getCode(), 1);
        if (limitNotJoinAndNotEventDuration(eventConfig, currActor, mqData.getTime())) return;
        if (!eventConfig.getDataId().contains(mqData.getGiftId().intValue())) return;
        saveDataCount(mqData, currActor);
    }

    private void saveDataCount(SendGiftSuccessMsgData mqData, ActorData currActor) {
        ActivityWelfareCommingActorInfoData data = activityWelfareCommingActorInfoDao.getOneByEventCodeAndUid(currActor.getUid(), ACTIVITY_TYPE_ENUM.getCode());
        if (data == null) {
            data = ActivityWelfareCommingActorInfoData.BuilderHelper.initData(currActor.getUid(), currActor.getRid(), ACTIVITY_TYPE_ENUM.getCode());
//            data.increaseDataCount(mqData.getNum());
            data.increaseDataCount(mqData.fetchRealCost());
            activityWelfareCommingActorInfoDao.insertOneSelective(data);
        } else {
//            data.increaseDataCount(mqData.getNum());
            data.increaseDataCount(mqData.fetchRealCost());
            ActivityWelfareCommingActorInfoData updateData = ActivityWelfareCommingActorInfoData.BuilderHelper.fillUpdateDataCountData(data);
            activityWelfareCommingActorInfoDao.updateOneSelective(updateData);
        }
    }

    private void checkParams(SendGiftSuccessMsgData mqData) {
        if (StringUtils.isEmpty(mqData.getToUid())) {
            throw new WebException(new BaseHttpData(), HttpCode.PARAM_ERROR);
        }
//        if (!LIKE_GIFT_ID.equals(mqData.getGiftId())) {
//            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("giftId not match"));
//        }
        if (mqData.getNum() == null || mqData.getNum() == 0) {
            throw new WebException(new BaseHttpData(), HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "gift num is empty"));
        }
        if (mqData.getTime() == null) {
            mqData.setTime(DateHelper.getCurrTime());
        }
    }

    public InitPageVO initPage(CommonDTO dto) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = getAppConfigActivityData(dto.getEventType());
        int playGameAwardStatus = dailyPlayGameOneAwardLimitRedis.getAwardStatus(dto.getUid(), dto.getEventType());
        int enterRoomAwardStatus = dailyEnterRoomOneAwardLimitRedis.getAwardStatus(dto.getUid(), dto.getEventType());
        String userInfoKey = getUserInfoKey(configData.getActivityCode(), currActor.getUid());
        UserInfoBO userInfoBO = findUserInfoBO(userInfoKey);
        boolean buttonStatus = userInfoBO.checkCanClaim(COLLECT_REWARD_EVERY_ONE_LIMIT);
        return InitPageVO.builder()
                .startTime(configData.getStartTime())
                .endTime(configData.getEndTime())
                .dailyEnterRoomAwardStatus(enterRoomAwardStatus)
                .dailyPlayGameAwardStatus(playGameAwardStatus)
                .isPayUser(currActor.getIsPayUser())
                .collectGiftMap(userInfoBO.getCollectGiftMap())
                .claimCount(userInfoBO.getClaimCount())
                .claimButtonStatus(buttonStatus)
                .build();
    }

    private AppConfigActivityData getAppConfigActivityData(Integer activityCode) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByCodeAndValid(activityCode, 1);
        if (configData != null) {
            return configData;
        }
        log.error("activity config is not exists activity_code={}", activityCode);
        throw new WebException(new HttpEnvData(), HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error,activity is not exists"));
    }

    public ModelRankVO<RankVO> getLikeGiftRank(CommonDTO dto) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        RankVO self = new RankVO();
        self.setActorInfo(new ActorInfo(currActor));
        self.getActorInfo().setHead(moderationService.dealRankHeadModeration(currActor));

        AppConfigActivityData configData = getAppConfigActivityData(dto.getEventType());
        if (DateHelper.getCurrTime() < configData.getStartTime()) {
            return new ModelRankVO<>(self, new ArrayList<>());
        }
        List<ActivityWelfareCommingActorInfoData> dataList = activityWelfareCommingActorInfoDao.getRankListByEventCodeAndTop(dto.getEventType(), 10);
        if (ObjectUtils.isEmpty(dataList)) {
            return new ModelRankVO<>(self, new ArrayList<>());
        }
        List<RankVO> rankList = IntStream.range(0, dataList.size())
                .mapToObj(index -> fillRankVO(index, dataList, self))
                .collect(Collectors.toList());

        if (self.getScore().equals(0L)) {
            fillSelfScore(dto, self);
        }
        return new ModelRankVO<>(self, rankList);
    }

    private void fillSelfScore(CommonDTO dto, RankVO self) {
        ActivityWelfareCommingActorInfoData data = activityWelfareCommingActorInfoDao.getOneByEventCodeAndUid(dto.getUid(), dto.getEventType());
        if (data != null) {
            self.setScore(data.fetchRealCount().longValue());
            self.setRealScore(data.fetchRealCount().toString());
        }
    }

    private RankVO fillRankVO(int index, List<ActivityWelfareCommingActorInfoData> dataList, RankVO self) {
        int rankNum = index + 1;
        ActivityWelfareCommingActorInfoData rankData = dataList.get(index);
        checkSelfInTop(self, rankData, rankNum);
        return fillTopRankVO(rankNum, rankData);
    }

    private RankVO fillTopRankVO(int rankNum, ActivityWelfareCommingActorInfoData rankData) {
        RankVO vo = new RankVO();
        vo.setRankNum(String.valueOf(rankNum));
        vo.setScore(rankData.fetchRealCount().longValue());
        vo.setRealScore(rankData.fetchRealCount().toString());
        vo.setActorInfo(new ActorInfo());
        ActorData rankActor = actorMgr.getActorData(rankData.getUid());
        if (rankActor != null) {
            vo.setActorInfo(new ActorInfo(rankActor));
            vo.getActorInfo().setHead(moderationService.dealRankHeadModeration(rankActor));
        }
        return vo;
    }

    public void likeGiftRankNotice(AppConfigActivityData configData, List<RewardTaskConfig> rewardConfigs) {
        List<ActivityWelfareCommingActorInfoData> dataList = activityWelfareCommingActorInfoDao.getRankListByEventCodeAndTop(configData.getActivityCode(), 5);

        StringBuilder content = initReceiveGiftRankMonitorNotice(configData);
        StringBuilder globalContent = new StringBuilder();
        globalContent.append("\uD83D\uDE01 Congratulations to the Top 5 in Welfare Event Charming ranking\n");
        if (!ObjectUtils.isEmpty(dataList)) {
            IntStream.rangeClosed(1, dataList.size()).forEachOrdered(rankNum -> fillTableBody(rankNum, dataList, content, rewardConfigs, globalContent));
        }
        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, content.toString());
        globalContent.append("\uD83D\uDE01 The reward has been issued, please check it.");
        int currTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        officialNoticeService.sendGlobalNotice(configData.getName(), globalContent.toString(), NOTICE_IMG, EVENT_URL, configData.getChannel(),
                currTime, configData.getActivityCode(), LangIdConstant.ENGLISH, null, OfficialUserGroup.CHANNEL);
    }

    private StringBuilder initReceiveGiftRankMonitorNotice(AppConfigActivityData configData) {
        String roundDate = getRoundString(configData);
        String rankName = "## 总榜(" + configData.getName() + ")\n";
        String body = "### 活动周期（UTC" + configData.getZoneOffset() + "):" + roundDate + "\n";
        String tableHead = "排名\tID\t\t\t统计数\n";
        return new StringBuilder(rankName + body + tableHead);
    }

    private void fillTableBody(int rankNum, List<ActivityWelfareCommingActorInfoData> dataList, StringBuilder content, List<RewardTaskConfig> rewardConfigs, StringBuilder globalContent) {
        ActivityWelfareCommingActorInfoData data = dataList.get(rankNum - 1);
        content.append(rankNum).append("\t\t")
                .append(data.getRid()).append("\t\t")
                .append(data.fetchRealCount().doubleValue()).append("\n");
        String rankStr = NOTICE_RANK_STR.replace("#top", String.valueOf(rankNum))
                .replace("#rid", String.valueOf(data.getRid()))
                .replace("#count", data.fetchRealCount().multiply(BigDecimal.valueOf( 40)).doubleValue() + "");
        globalContent.append(rankStr);

        rewardConfigs.stream().filter(rewardConfig -> rewardConfig.getCheckParams() == rankNum)
                .findFirst().ifPresent(rewardConfig -> {
//                    sendOfficialNoticeToUser(rankNum, data.getUid(), rewardConfig.getNotice(), NOTICE_IMG, EVENT_URL, ChannelEnum.CDE.getName());
                    giveOutRewardService.giveEventReward(data.getUid(), rewardConfig.getRewards(), EVENT_CODE, "receive gift Rank,TOP" + rankNum);
                });
    }


    /**
     * 生成当前轮次标记
     *
     * @param configData 活动配置数据
     * @return 当前轮次标记
     */
    protected String getRoundString(AppConfigActivityData configData) {
        return super.getRoundString(configData);
    }


    public void gameIncomeRankNotice(AppConfigActivityData configData, int rankType, String localDateStr, List<RewardTaskConfig> rewardConfigs) {
        exchangeTime(localDateStr, configData);
        List<RankingDTO> rankList = getRankingList(configData, rankType, TopTypeEnum.AWARD);
        sendMonitor(configData, rankType, localDateStr, rankList);

        sendOfficialNotice(configData, rankList, rewardConfigs, rankType);

    }

    private void sendOfficialNotice(AppConfigActivityData configData, List<RankingDTO> rankList, List<RewardTaskConfig> rewardConfigs, int rankType) {
        // 游戏榜单 通知
        String noticeFormat;
        if (rankType == 1) {
            noticeFormat = "Ding, the rewards for the Daily Activities Game Winning Ranking already issued, Get more >>>";
        } else {
            noticeFormat = null;
        }
        if (!ObjectUtils.isEmpty(rankList)) {
            IntStream.rangeClosed(1, rankList.size())
                    .forEachOrdered(rankNum -> sendOfficialNoticeAndGiveAward(rankNum, rankList, noticeFormat, configData.getChannel(), rewardConfigs, rankType));
        }
    }

    private void sendMonitor(AppConfigActivityData configData, int rankType, String localDateStr, List<RankingDTO> rankList) {
        StringBuilder content = initRankMonitorContent(configData, rankType, localDateStr);
        StringBuilder globalContent = new StringBuilder();
        if (rankType == 2) {
            globalContent.append("\uD83D\uDE01 Congratulations to the follows getTop 10 in Welfare  Event Game winning ranking\n");
        }

        if (!ObjectUtils.isEmpty(rankList)) {
            IntStream.rangeClosed(1, rankList.size()).forEachOrdered(rankNum -> fillContentBody(rankNum, rankList, content, globalContent, rankType));
        }
        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, content.toString());

        if (rankType == 2) {
            globalContent.append("\uD83D\uDE01 The reward has been issued, please check it.");
            int currTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
            officialNoticeService.sendGlobalNotice(configData.getName(), globalContent.toString(), NOTICE_IMG, EVENT_URL, configData.getChannel(),
                    currTime, configData.getActivityCode(), LangIdConstant.ENGLISH, null, OfficialUserGroup.CHANNEL);
        }
    }

    private StringBuilder initRankMonitorContent(AppConfigActivityData configData, int rankType, String localDateStr) {
        String rankName = "## 日榜游戏榜";
        if (rankType == 2) {
            rankName = "## 总榜游戏榜";
        }
        rankName += "(" + configData.getName() + ")\n";
        String roundDate = getRoundString(configData);
        String eventDurationStr = localDateStr == null ? roundDate : localDateStr;
        String body = "### 活动周期（UTC" + configData.getZoneOffset() + "):" + eventDurationStr + "\n";
        String tableHead = "排名\tID\t\t\t统计数\n";
        return new StringBuilder(rankName + body + tableHead);
    }

    private void sendOfficialNoticeAndGiveAward(int rankNum, List<RankingDTO> rankList, String noticeFormat, String channel, List<RewardTaskConfig> rewardConfigs, int rankType) {
        String desc = (rankType == 1 ? "Daily" : "Total") + " Game Income Rank,TOP" + rankNum + ".";
        RankingDTO data = rankList.get(rankNum - 1);
        rewardConfigs.stream().filter(rewardConfig -> rewardConfig.getCheckParams() == rankNum)
                .findFirst().ifPresent(rewardConfig -> {
                    if (StringUtils.hasLength(noticeFormat)) {
                        sendOfficialNoticeToUser(rankNum, data.getUid(), noticeFormat, NOTICE_IMG, EVENT_URL, channel);
                    }
                    giveOutRewardService.giveEventReward(data.getUid(), rewardConfig.getRewards(), EVENT_CODE, desc);
                });

    }


    private void fillContentBody(int rankNum, List<RankingDTO> rankList, StringBuilder content, StringBuilder globalContent, int rankType) {
        RankingDTO data = rankList.get(rankNum - 1);
        content.append(rankNum).append("\t\t")
                .append(data.getRid()).append("\t\t")
                .append(data.getCount()).append("\n");
        if (rankType == 2) {
            String rankStr = NOTICE_RANK_STR.replace("#top", String.valueOf(rankNum))
                    .replace("#rid", String.valueOf(data.getRid()))
                    .replace("#count", data.getCount() * 40 + "");
            globalContent.append(rankStr);
        }
    }

    public void starryGetGiftAction(ActorBackpackData mqData) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        checkActivityTime(configData, mqData.getCtime(), currActor);
        if (!GIFT_COLLECT_LIST.contains(mqData.getDataId())) {
            return;
        }
        String giftId = mqData.getDataId();
        String userInfoKey = getUserInfoKey(configData.getActivityCode(), currActor.getUid());
        UserInfoBO userInfoBO = findUserInfoBO(userInfoKey);
        userInfoBO.increaseGiftCount(giftId, mqData.getNum());
        activityCommonRedis.setCommonStrValue(userInfoKey, JSON.toJSONString(userInfoBO));
    }

    private UserInfoBO findUserInfoBO(String userInfoKey) {
        UserInfoBO userInfoBO;
        String userInfoStr = activityCommonRedis.getCommonStrValue(userInfoKey);
        if (StringUtils.isEmpty(userInfoStr)) {
            userInfoBO = new UserInfoBO();
        } else {
            userInfoBO = JSON.parseObject(userInfoStr, UserInfoBO.class);
        }
        return userInfoBO;
    }

    private void checkActivityTime(AppConfigActivityData configData, long currTime, ActorData currActor) {
        if (currTime < configData.getStartTime()) {
            throw new WebException(new HttpEnvData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "event not start"));
        }
        if (currTime > configData.getEndTime()) {
            throw new WebException(new HttpEnvData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "event has ended"));
        }
        if (org.springframework.util.StringUtils.hasLength(configData.getChannel()) && !configData.getChannel().equals(currActor.getChannel())) {
            throw new WebException(new HttpEnvData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "your app can not join this event"));
        }
    }

    public int collectGiftReward(CommonDTO dto) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        checkActivityTime(configData, DateHelper.getCurrTime(), currActor);
        String userInfoKey = getUserInfoKey(configData.getActivityCode(), currActor.getUid());
        UserInfoBO userInfoBO = findUserInfoBO(userInfoKey);
        if (userInfoBO.getClaimCount() >= COLLECT_REWARD_EVERY_ONE_LIMIT) {
            throw new WebException(new HttpEnvData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "Reward collection has reached the upper limit"));
        }
        int increase = userInfoBO.increaseAndGetClaimCount(COLLECT_REWARD_EVERY_ONE_LIMIT);
        if (increase <= 0) {
            throw new WebException(new HttpEnvData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "Insufficient required items"));
        }
        activityCommonRedis.setCommonStrValue(userInfoKey, JSON.toJSONString(userInfoBO));
        IntStream.range(0, increase).forEach(i -> giveOutRewardService.giveEventReward(dto.getUid(), COLLECT_REWARDS, dto.getEventType(), "3"));
        // 全局飘屏通知
        rewardService.sendUniversalFullServiceNoticeMsg(dto.getUid(), COLLECT_REWARDS.get(0),
                EVENT_URL, configData.getName(), "#name successfully Collect the gift successful and get rewards >>>", true);
        return increase;
    }
}
