package com.quhong.event.rank.pk.v2411;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.BaseHttpData;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.GiftFromTypeConstant;
import com.quhong.constant.LangIdConstant;
import com.quhong.constant.OfficialUserGroup;
import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.WarnName;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MasterUtils;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.ActorExternalDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.activity.superPlayer.OtherRankingListVO;
import com.quhong.data.vo.event.miss.rose.OtherMyRankVO;
import com.quhong.data.vo.event.miss.rose.OtherRankingVO;
import com.quhong.data.vo.model.rank.row.ActorInfo;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.enums.EventCode;
import com.quhong.enums.RewardItemType;
import com.quhong.enums.RoomType;
import com.quhong.exceptions.WebException;
import com.quhong.moderatoin.ModerationService;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.mq.data.room.DownMicMsgData;
import com.quhong.mq.data.room.EnterRoomMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.redis.BaseHashSaveRedis;
import com.quhong.redis.BaseSetRedis;
import com.quhong.redis.BaseZSetRedis;
import com.quhong.redis.base.currency.task.impl.DailyEnterRoomOneAwardLimitRedis;
import com.quhong.redis.base.currency.task.impl.DailySendGiftOneAwardLimitRedis;
import com.quhong.redis.base.reward.impl.DailyEveryLimitRewardRedis;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.common.RoomFloatingImService;
import com.quhong.utils.BeanCopyUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2024/11/5 15:01
 */
@Slf4j
//@Service
@RequiredArgsConstructor
public class EventRankPkService {
    private static final boolean PROD = ServerConfiguration.isProduct();
    /**
     * 待定活动码
     */
    private static final int EVENT_CODE = EventCode.EVENT_RANK_PK_2411;

    private static final int COIN_RATE = 40;

    private static final String EVENT_URL = PROD ? "https://videochat.kissu.site/rank_pk_11/" : "https://testvideochat.kissu.site/rank_pk_11/";

    private static final String NOTICE_IMG = "https://statics.kissu.mobi/event/party/king/Banner.png";


    public static final String LOSER_NOTICE = "Thank you for your participation. We have also issued participate rewards for you, already sent, Continue to open party room to check the battle situation and receive the party activity reward.";
    private static final String SEND_RANK_NOTICE = "\"\uD83D\uDE01Congratulations on becoming the top 10 knights of this round. \n" +
            "\uD83D\uDE01All rewards have been issued. Please check.\n" +
            "\uD83D\uDE01Keep supporting your favorite hosts\"\n";
    /**
     * 每日进房奖励
     */
    private static final List<RewardInfoData> DAILY_ENTER_ROOM_REWARDS = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.BUBBLE_FRAME, PROD ? 219 : 64));
    }};
    /**
     * 每日上麦任务奖励
     */
    private static final List<RewardInfoData> DAILY_UP_MIC_REWARDS = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.SEAT_FRAME, PROD ? 258 : 63));
    }};
    private static final RewardTaskConfig DAILY_UP_MIC_TASK = new RewardTaskConfig(TaskTypeConstant.MIC_STAY_TIME,
            (int) Duration.ofMinutes(10).getSeconds(), 1, DAILY_UP_MIC_REWARDS);

    /**
     * 每日party房送礼任务奖励
     */
    private static final List<RewardInfoData> DAILY_SEND_GIFT_REWARDS = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, PROD ? 517 : 23, 5));
    }};
    /**
     * 个人贡献榜前10名基础奖励
     */
    private static final List<RewardInfoData> PERSONAL_RANK_REWARDS = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, PROD ? 673 : 672, 5));
        add(new RewardInfoData(EVENT_CODE, 3, RewardItemType.SEAT_FRAME, PROD ? 207 : 101));
    }};

    /**
     * 使用 item.checkParams <= top  取checkParams max
     */
    private static final List<RewardTaskConfig> RECEIVE_BASIC_CONFIGS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, 671, 500));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.GIFT, PROD ? 673 : 672, 5));
            add(new RewardInfoData(EVENT_CODE, 3, RewardItemType.SEAT_FRAME, PROD ? 207 : 101));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));
        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.GIFT, PROD ? 673 : 672, 5));
            add(new RewardInfoData(EVENT_CODE, 3, RewardItemType.SEAT_FRAME, PROD ? 207 : 101));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));
    }};
    private static final List<RewardTaskConfig> TOP128_SEND_CONFIGS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 3, RewardItemType.GIFT, 671, 500));
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.GIFT, PROD ? 673 : 672, 5));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 247 : 97));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 2, RewardItemType.GIFT, 671, 500));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.GIFT, PROD ? 673 : 672, 5));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 247 : 97));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, 671, 500));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.GIFT, PROD ? 673 : 672, 5));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 247 : 97));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 125, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 5, RewardItemType.GIFT, PROD ? 673 : 672, 5));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 247 : 97));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));
    }};

    private static final List<RewardTaskConfig> PK_SEND_CONFIGS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 2, RewardItemType.GIFT, 671, 500));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.GIFT, PROD ? 673 : 672, 5));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, 671, 500));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.GIFT, PROD ? 673 : 672, 5));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards2));
    }};
    private static final List<RewardTaskConfig> TOP3_SEND_CONFIGS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900038 : 900031));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 246 : 83));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 230 : 123));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 222 : 122));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 3750, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900039 : 80001));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 246 : 83));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 230 : 123));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.SEAT_FRAME, PROD ? 222 : 122));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900040 : 90002));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 246 : 83));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.ENTER_EFFECT, PROD ? 230 : 123));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.SEAT_FRAME, PROD ? 222 : 122));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

    }};
    //***************************************
    private static final List<SonEventInfo.RankRate> TOP128_RANK_RATES = new ArrayList<SonEventInfo.RankRate>() {{
        add(new SonEventInfo.RankRate(1, 400, 20, 0));
        add(new SonEventInfo.RankRate(2, 300, 10, 0));
        add(new SonEventInfo.RankRate(3, 200, 5, 0));
        add(new SonEventInfo.RankRate(65, 100, 1, 0));
        add(new SonEventInfo.RankRate(129, 0, 0, 0));
    }};
    private static final List<SonEventInfo.RankRate> IN_64_RANK_RATES = new ArrayList<SonEventInfo.RankRate>() {{
        add(new SonEventInfo.RankRate(1, 300, 20, 25));
        add(new SonEventInfo.RankRate(2, 200, 10, 10));
        add(new SonEventInfo.RankRate(33, 100, 5, 5));
        add(new SonEventInfo.RankRate(65, 0, 0, 5));
    }};

    private static final List<SonEventInfo.RankRate> IN_32_RANK_RATES = new ArrayList<SonEventInfo.RankRate>() {{
        add(new SonEventInfo.RankRate(1, 300, 20, 25));
        add(new SonEventInfo.RankRate(2, 200, 10, 10));
        add(new SonEventInfo.RankRate(17, 100, 5, 5));
        add(new SonEventInfo.RankRate(33, 0, 0, 5));
    }};

    private static final List<SonEventInfo.RankRate> IN_16_RANK_RATES = new ArrayList<SonEventInfo.RankRate>() {{
        add(new SonEventInfo.RankRate(1, 300, 20, 25));
        add(new SonEventInfo.RankRate(2, 200, 10, 10));
        add(new SonEventInfo.RankRate(9, 100, 5, 5));
        add(new SonEventInfo.RankRate(17, 0, 0, 5));
    }};

    private static final List<SonEventInfo.RankRate> TOP3_RANK_RATES = new ArrayList<SonEventInfo.RankRate>() {{
        add(new SonEventInfo.RankRate(1, 300, 20, 0));
        add(new SonEventInfo.RankRate(2, 200, 10, 0));
        add(new SonEventInfo.RankRate(4, 0, 0, 0));
    }};
    private static final List<SonEventInfo> SON_EVENT_INFOS = new ArrayList<SonEventInfo>() {{
        // 11-20~22
        add(new SonEventInfo().setActivityId(EVENT_CODE + "top128" + (PROD ? "" : "test")).setValid(1).setEventName("Round 1 (N-128)")
                .setStartTime(PROD ? 1732032000L : 1731513600L).setEndTime(PROD ? 1732291200L : 1731513700L).setTopNum(200).setWinTopNum(128)
                .setReceiveRewardConfigs(RECEIVE_BASIC_CONFIGS)
                .setRankRates(TOP128_RANK_RATES)
                .setSendRewardConfigs(TOP128_SEND_CONFIGS));
        //11-23~24
        add(new SonEventInfo().setActivityId(EVENT_CODE + "in_64" + (PROD ? "" : "test2")).setValid(1).setEventName("Round 2 (128-64)")
                .setStartTime(PROD ? 1732291200L : 1731513600L).setEndTime(PROD ? 1732464000L : 1731513800L).setTopNum(128).setWinTopNum(64)
                .setReceiveRewardConfigs(RECEIVE_BASIC_CONFIGS)
                .setRankRates(IN_64_RANK_RATES)
                .setSendRewardConfigs(PK_SEND_CONFIGS)
        );
        //11-25~26
        add(new SonEventInfo().setActivityId(EVENT_CODE + "in_32" + (PROD ? "" : "test")).setValid(1).setEventName("Round 3 (64-32)")
                .setStartTime(PROD ? 1732464000L : 1731513600L).setEndTime(PROD ? 1732636800L : 1731513900L).setTopNum(64).setWinTopNum(32)
                .setReceiveRewardConfigs(RECEIVE_BASIC_CONFIGS)
                .setRankRates(IN_32_RANK_RATES)
                .setSendRewardConfigs(PK_SEND_CONFIGS)
        );
        //11-27~28
        add(new SonEventInfo().setActivityId(EVENT_CODE + "in_16" + (PROD ? "" : "test1")).setValid(1).setEventName("Round 4 (32-16)")
                .setStartTime(PROD ? 1732636800L : 1731513600L).setEndTime(PROD ? 1732809600L : 1731514000L).setTopNum(32).setWinTopNum(16)
                .setReceiveRewardConfigs(RECEIVE_BASIC_CONFIGS)
                .setRankRates(IN_16_RANK_RATES)
                .setSendRewardConfigs(PK_SEND_CONFIGS));
        //11-29~30
        add(new SonEventInfo().setActivityId(EVENT_CODE + "top3" + (PROD ? "" : "test")).setValid(1).setEventName("Round 5")
                .setStartTime(PROD ? 1732809600L : 1731513600L).setEndTime(1732982400L).setTopNum(16).setWinTopNum(3)
                .setReceiveRewardConfigs(RECEIVE_BASIC_CONFIGS)
                .setRankRates(TOP3_RANK_RATES)
                .setSendRewardConfigs(TOP3_SEND_CONFIGS));
    }};


    private final ActorMgr actorMgr;
    private final ActorExternalDao actorExternalDao;
    private final AppConfigActivityDao appConfigActivityDao;
    private final BaseZSetRedis baseZSetRedis;
    private final BaseSetRedis baseSetRedis;
    private final BaseHashSaveRedis baseHashSaveRedis;
    private final GiveOutRewardService giveOutRewardService;
    private final ModerationService moderationService;
    private final DailyEnterRoomOneAwardLimitRedis dailyEnterRoomOneAwardLimitRedis;
    private final DailySendGiftOneAwardLimitRedis dailySendGiftOneAwardLimitRedis;
    private final DailyEveryLimitRewardRedis dailyEveryLimitRewardRedis;
    private final RoomFloatingImService roomFloatingImService;
    private final BeanCopyUtils beanCopyUtils;
    private final MasterUtils masterUtils;
    private final OfficialNoticeService officialNoticeService;
    private final MonitorSender monitorSender;


    /**
     * 送礼榜zset
     *
     * @param activityId
     * @return
     */
    private String zsetSendGiftRankKey(String activityId) {
        return "zset:event:send_gift_rank:" + activityId;
    }

    /**
     * 收礼榜zset
     */
    private String zsetReceiveGiftRankKey(String activityId) {
        return "zset:event:receive_gift_rank:" + activityId;
    }

    /**
     * 参与榜单的人员set
     */
    private String setJoinRankMemberKey(String activityId) {
        return "set:event:join_rank_member:" + activityId;
    }

    /**
     * 主播个人贡献榜zset
     */
    private String zsetPersonalRankKey(String activityId, String aid) {
        return "zset:event:personal_rank:" + activityId + ":" + aid;
    }

    /**
     * pk信息保存 hash
     * hashKey: activityId
     */
    private String hashRankPkInfoKey(int eventCode) {
        return "hash:event:rank_pk_info:" + eventCode;
    }

//    @Async("asyncPool")
//    @Scheduled(cron = "0 0 0 23 11 ?", zone = "GMT+8:00")
    public void round1Reward() {
        if (!masterUtils.isMaster()) {
            return;
        }
        SonEventInfo eventInfo = SON_EVENT_INFOS.get(0);
        SonEventInfo nextEventInfo = SON_EVENT_INFOS.get(1);
        dealRankSettlement(eventInfo, nextEventInfo);
        sendGiftRankReward(eventInfo);
        eventInfo.setValid(0);
    }


//    @Async("asyncPool")
//    @Scheduled(cron = "0 0 0 25 11 ?", zone = "GMT+8:00")
    public void round2Reward() {
        if (!masterUtils.isMaster()) {
            return;
        }
        SonEventInfo eventInfo = SON_EVENT_INFOS.get(1);
        SonEventInfo nextEventInfo = SON_EVENT_INFOS.get(2);
        dealPkSettlement(eventInfo, nextEventInfo, false);
        sendGiftRankReward(eventInfo);
        SON_EVENT_INFOS.get(1).setValid(0);
    }

//    @Async("asyncPool")
//    @Scheduled(cron = "0 0 0 27 11 ?", zone = "GMT+8:00")
    public void round3Reward() {
        if (!masterUtils.isMaster()) {
            return;
        }
        SonEventInfo eventInfo = SON_EVENT_INFOS.get(2);
        SonEventInfo nextEventInfo = SON_EVENT_INFOS.get(3);
        dealPkSettlement(eventInfo, nextEventInfo, false);
        sendGiftRankReward(eventInfo);
        SON_EVENT_INFOS.get(2).setValid(0);
    }

//    @Async("asyncPool")
//    @Scheduled(cron = "0 0 0 29 11 ?", zone = "GMT+8:00")
    public void round4Reward() {
        if (!masterUtils.isMaster()) {
            return;
        }
        SonEventInfo eventInfo = SON_EVENT_INFOS.get(3);
        SonEventInfo nextEventInfo = SON_EVENT_INFOS.get(4);
        boolean nextIsRank = true;
        dealPkSettlement(eventInfo, nextEventInfo, nextIsRank);
        sendGiftRankReward(eventInfo);
        SON_EVENT_INFOS.get(3).setValid(0);
    }

//    @Async("asyncPool")
//    @Scheduled(cron = "0 0 0 1 12 ?", zone = "GMT+8:00")
    public void round5Reward() {
        if (!masterUtils.isMaster()) {
            return;
        }
        SonEventInfo eventInfo = SON_EVENT_INFOS.get(4);
        dealRankSettlement(eventInfo, null);
        sendGiftRankReward(eventInfo);
        SON_EVENT_INFOS.get(4).setValid(0);
    }

    private void dealPkSettlement(SonEventInfo eventInfo, SonEventInfo nextEventInfo, boolean nextIsRank) {
        String activityId = eventInfo.getActivityId();
        String rankPkInfoKey = hashRankPkInfoKey(EVENT_CODE);
        String rankKey = zsetReceiveGiftRankKey(activityId);
        List<PkInfoBO> pkList = baseHashSaveRedis.getListByRedis(rankPkInfoKey, activityId, PkInfoBO.class);

        List<PlayerBO> winners = new ArrayList<>(64);
        List<PlayerBO> losers = new ArrayList<>(64);
        for (PkInfoBO pkInfo : pkList) {
            PlayerBO player1 = pkInfo.getPlayer1();
            fillPlayerInfo(activityId, player1, rankKey);
            PlayerBO player2 = pkInfo.getPlayer2();
            fillPlayerInfo(activityId, player2, rankKey);
            PlayerBO winPlayer = findWinPlayer(player1, player2);
            winners.add(winPlayer);
            losers.add(winPlayer.equals(player1) ? player2 : player1);
        }
        winners.sort(Comparator.comparingLong(player -> sortPk((PlayerBO) player)).reversed()
                .thenComparingLong(player -> sortThemPk((PlayerBO) player)));
        IntStream.range(0, winners.size())
                .forEach(index -> dealRankNumAndSelfData(null, winners, null, index, activityId));

        rankRewardAndDealNotice(eventInfo, winners);
        // 输家奖励处理
        losers.forEach(loser -> dealPkLoserRewards(eventInfo, loser));

        initNextRoundData(nextEventInfo, nextIsRank, winners);
    }

    private void initNextRoundData(SonEventInfo nextEventInfo, boolean nextIsRank, List<PlayerBO> winners) {
        String nextRankPkInfoKey = hashRankPkInfoKey(EVENT_CODE);
        String nextRoundRankKey = zsetReceiveGiftRankKey(nextEventInfo.getActivityId());
        String nextRoundJoinMemberKey = setJoinRankMemberKey(nextEventInfo.getActivityId());
        if (nextIsRank) {
            // 只参与榜单
            IntStream.range(0, winners.size())
                    .forEach(index -> dealNextRoundJoinMember(index, winners, nextEventInfo, nextRoundRankKey, nextRoundJoinMemberKey));
        } else {
            if (winners.size() < 2) {
                return;
            }
            // pk信息填充
            int winTop = nextEventInfo.getTopNum();
            if (winners.size() < winTop) {
                winTop = winners.size() % 2 == 0 ? winners.size() : winners.size() - 1;
            }
            List<PlayerBO> winLeftPlayers = winners.subList(0, winTop / 2);
            List<PlayerBO> winRightPlayers = winners.subList(winTop / 2, winTop);
            Collections.reverse(winRightPlayers);
            List<PkInfoBO> nextPkList = IntStream.range(0, winTop / 2)
                    .mapToObj(index -> dealNextPlayerInfoAndFillNextPkInfo(index, winLeftPlayers, nextEventInfo, nextRoundRankKey, nextRoundJoinMemberKey, winRightPlayers))
                    .collect(Collectors.toList());
            baseHashSaveRedis.saveToRedis(nextRankPkInfoKey, nextEventInfo.getActivityId(), nextPkList, Duration.ofDays(30));
        }
    }

    private void dealPkLoserRewards(SonEventInfo eventInfo, PlayerBO loser) {
        giveOutRewardService.giveEventReward(loser.getInfo().getActorInfo().getUid(), eventInfo.getReceiveRewardConfigs().get(1).getRewards(), EVENT_CODE,
                eventInfo.getEventName() + "loser");
        dealSonRankReward(eventInfo.getTopNum(), loser, eventInfo);
    }

    private void dealRankSettlement(SonEventInfo eventInfo, SonEventInfo nextRoundEventInfo) {
        String rankKey = zsetReceiveGiftRankKey(eventInfo.getActivityId());
        List<CountVO> dataList = baseZSetRedis.getRange(rankKey, 1, eventInfo.getTopNum());
        List<PlayerBO> playerBOS = fillPlayerBoList(eventInfo.getActivityId(), dataList);
        rankRewardAndDealNotice(eventInfo, playerBOS);

        // 写入下一轮参与者信息
        initNextRoundData(nextRoundEventInfo, playerBOS, dataList);
    }

    private void initNextRoundData(SonEventInfo nextRoundEventInfo, List<PlayerBO> playerBOS, List<CountVO> dataList) {
        if (nextRoundEventInfo != null && playerBOS.size() >= 2) {
            String rankPkInfoKey = hashRankPkInfoKey(EVENT_CODE);
            String nextRoundRankKey = zsetReceiveGiftRankKey(nextRoundEventInfo.getActivityId());
            String nextRoundJoinMemberKey = setJoinRankMemberKey(nextRoundEventInfo.getActivityId());
            int winTop = nextRoundEventInfo.getTopNum();
            if (dataList.size() < winTop) {
                winTop = dataList.size() % 2 == 0 ? dataList.size() : dataList.size() - 1;
            }
            List<PlayerBO> winLeftPlayers = playerBOS.subList(0, winTop / 2);
            List<PlayerBO> winRightPlayers = playerBOS.subList(winTop / 2, winTop);
            Collections.reverse(winRightPlayers);
            List<PkInfoBO> pkList = IntStream.range(0, winTop / 2)
                    .mapToObj(index -> dealNextPlayerInfoAndFillNextPkInfo(index, winLeftPlayers, nextRoundEventInfo, nextRoundRankKey, nextRoundJoinMemberKey, winRightPlayers))
                    .collect(Collectors.toList());
            baseHashSaveRedis.saveToRedis(rankPkInfoKey, nextRoundEventInfo.getActivityId(), pkList, Duration.ofDays(30));
        }
    }

    private void rankRewardAndDealNotice(SonEventInfo eventInfo, List<PlayerBO> playerBOS) {
        StringBuilder content = new StringBuilder();
        content.append("收礼榜（").append(eventInfo.getEventName()).append(")\n")
                .append("排名\t\t统计数\t\tuid\t\t\t\t\t\t\t\t\t\trid\n");
        StringBuilder globalNotice = new StringBuilder();
        globalNotice.append("\uD83D\uDE01 Party King Event ").append(eventInfo.getEventName()).append(" Successful completion： \n")
                .append("\uD83D\uDE01 Congratulations to the ").append(eventInfo.getWinTopNum()).append(" Winner for advancing to the next round \n");
        IntStream.range(0, playerBOS.size())
                .forEach(index -> this.rankRewardAndDealNotice(index + 1, playerBOS.get(index), content, globalNotice, eventInfo));
        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, content.toString());
        globalNotice.append("\uD83D\uDE01 Remember to support the party king in party room , Supporters can also receive reward, please visit >>");
        int fixTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        officialNoticeService.sendGlobalNotice("", globalNotice.toString(), NOTICE_IMG, EVENT_URL, ChannelEnum.CDE.getName(), fixTime, EVENT_CODE,
                LangIdConstant.ENGLISH, null, OfficialUserGroup.CHANNEL);
    }

    private PkInfoBO dealNextPlayerInfoAndFillNextPkInfo(int index, List<PlayerBO> winLeftPlayers, SonEventInfo nextRoundEventInfo, String nextRoundRankKey, String nextRoundJoinMemberKey, List<PlayerBO> winRightPlayers) {
        PlayerBO leftPlayer = dealNextRoundJoinMember(index, winLeftPlayers, nextRoundEventInfo, nextRoundRankKey, nextRoundJoinMemberKey);
        PlayerBO rightPlayer = dealNextRoundJoinMember(index, winRightPlayers, nextRoundEventInfo, nextRoundRankKey, nextRoundJoinMemberKey);
        return new PkInfoBO(leftPlayer, rightPlayer);
    }

    private PlayerBO dealNextRoundJoinMember(int index, List<PlayerBO> players, SonEventInfo nextRoundEventInfo, String nextRoundRankKey, String nextRoundJoinMemberKey) {
        PlayerBO player = players.get(index);
        int basicCoin = computeBasicCoin(nextRoundEventInfo, player);
        if (basicCoin > 0) {
            baseZSetRedis.increaseToZSet(nextRoundRankKey, player.getInfo().getActorInfo().getUid(), basicCoin, Duration.ofDays(30));
        }
        baseSetRedis.addToSet(nextRoundJoinMemberKey, player.getInfo().getActorInfo().getUid(), Duration.ofDays(30));
        return player;
    }

    private static Integer computeBasicCoin(SonEventInfo nextRoundEventInfo, PlayerBO leftPlayer) {
        return nextRoundEventInfo.getRankRates().stream()
                .filter(rankRate -> rankRate.getRank() <= Integer.parseInt(leftPlayer.getInfo().getRankNum()))
                .max(Comparator.comparingInt(SonEventInfo.RankRate::getRank))
                .map(SonEventInfo.RankRate::getBasicCoin)
                .orElse(0);
    }

    private void rankRewardAndDealNotice(int top, PlayerBO playerBO, StringBuilder content, StringBuilder globalNotice, SonEventInfo eventInfo) {
        try {
            content.append(top).append("\t\t\t").append(playerBO.getInfo().getScore()).append("\t\t").append(playerBO.getInfo().getActorInfo().getUid());
            //处理奖励下发和官方通知
            List<RewardInfoData> basicRewards = findBasicRewards(top, eventInfo);
            ActorData rankActor = actorMgr.getActorData(playerBO.getInfo().getActorInfo().getUid());
            if (rankActor == null) {
                return;
            }
            content.append(rankActor.getRid());
            if (top <= 4) {
                globalNotice.append("Top").append(top).append("  ").append(rankActor.getRid()).append("  Event gift income:").append(playerBO.getInfo().getScore() * COIN_RATE).append("\n");
            }
            int fixTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);

            if (top <= eventInfo.getWinTopNum()) {
                //添加分成奖励
                RewardInfoData goldReward = new RewardInfoData(EVENT_CODE, playerBO.getInfo().getCanGet().intValue(), RewardItemType.GOLD, 0, 1);
                basicRewards.add(goldReward);
                String notice = findNotice(eventInfo);
                officialNoticeService.sendOfficialNotice(rankActor.getUid(), "", notice, NOTICE_IMG, EVENT_URL, rankActor.getChannel(), fixTime, EVENT_CODE);
            } else {
                officialNoticeService.sendOfficialNotice(rankActor.getUid(), "", LOSER_NOTICE, NOTICE_IMG, EVENT_URL, rankActor.getChannel(), fixTime, EVENT_CODE);
            }

            if (!ObjectUtils.isEmpty(basicRewards)) {
                giveOutRewardService.giveEventReward(rankActor.getUid(), basicRewards, EVENT_CODE, eventInfo.getEventName() + ",top=" + top);
            }

            // 奉献榜处理
            dealSonRankReward(top, playerBO, eventInfo);

        } finally {
            content.append("\n");
        }

    }

    private void dealSonRankReward(int top, PlayerBO playerBO, SonEventInfo eventInfo) {
        List<RankRowVO> sonRankList = playerBO.getSonRankList();
        if (ObjectUtils.isEmpty(sonRankList)) {
            return;
        }
        IntStream.range(0, sonRankList.size())
                .forEach(index -> dealSonRankReward(index + 1, sonRankList.get(index), top, eventInfo));
    }

    private void dealSonRankReward(int rankNum, RankRowVO data, int top, SonEventInfo eventInfo) {
        List<RewardInfoData> basicRewards = new ArrayList<>(PERSONAL_RANK_REWARDS);
        ActorData rankActor = actorMgr.getActorData(data.getActorInfo().getUid());
        if (rankActor == null) {
            return;
        }
        if (top <= eventInfo.getWinTopNum()) {
            //添加分成奖励
            RewardInfoData goldReward = new RewardInfoData(EVENT_CODE, data.getCanGet().intValue(), RewardItemType.GOLD, 0, 1);
            basicRewards.add(goldReward);
        }
        giveOutRewardService.giveEventReward(data.getActorInfo().getUid(), basicRewards, EVENT_CODE, eventInfo.getEventName() + ",top=" + top + ",sonTop=" + rankNum);
    }

    private List<RewardInfoData> findBasicRewards(int top, SonEventInfo eventInfo) {
        return eventInfo.getReceiveRewardConfigs().stream()
                .filter(rewardTaskConfig -> rewardTaskConfig.getCheckParams() <= top)
                .max(Comparator.comparingInt(RewardTaskConfig::getCheckParams))
                .map(rewardTaskConfig -> new ArrayList<>(rewardTaskConfig.getRewards()))
                .orElseGet(ArrayList::new);
    }

    private static String findNotice(SonEventInfo eventInfo) {
        //第一轮提示文案
        String notice;
        if (SON_EVENT_INFOS.get(0).getActivityId().equals(eventInfo.getActivityId())) {
            notice = "\uD83D\uDE01Congratulations on advancing to the next round 1VS1 PK.\n" +
                    "\uD83D\uDE01The rewards for you and your contributors have been issued.\n" +
                    "\uD83D\uDE01Remember to check the page to follow your opponent's movements, continue to advance, " +
                    "and win the final reward of the Party King>>>";
        } else if (SON_EVENT_INFOS.get(SON_EVENT_INFOS.size() - 1).getActivityId().equals(eventInfo.getActivityId())) {//第五轮赢家数据
            notice = "\uD83D\uDE01Congratulations on advancing to the next round. \n" +
                    "\uD83D\uDE01The rewards for you and your contributors have been issued. \n" +
                    "\uD83D\uDE01Remember to check the page to follow your opponent's movements, continue to advance, and win the final reward of the Rose Queen>>>";
        } else {
            notice = "\uD83D\uDE01Congratulations on advancing to the next round.\n" +
                    "\uD83D\uDE01The rewards for you and your contributors have been issued.\n" +
                    "\uD83D\uDE01Remember to check the page to follow your opponent's movements, continue to advance, and win the final reward of the Rose Queen>>>";
        }
        return notice;
    }

    private static SonEventInfo getSonEventInfo(Long time) {
        return SON_EVENT_INFOS.stream()
                .filter(sonEventInfo -> sonEventInfo.getValid() == 1)
                .filter(sonEventInfo -> sonEventInfo.getStartTime() <= time)
                .filter(sonEventInfo -> sonEventInfo.getEndTime() > time)
                .findFirst()
                .orElse(null);
    }

    private static SonEventInfo getSonEventInfo(String activityId) {
        return SON_EVENT_INFOS.stream()
                .filter(sonEventInfo -> sonEventInfo.getActivityId().equals(activityId))
                .findFirst()
                .orElse(null);
    }

    private void sendGiftRankReward(SonEventInfo eventInfo) {
        String rankKey = zsetSendGiftRankKey(eventInfo.getActivityId());
        List<CountVO> dataList = baseZSetRedis.getRange(rankKey, 1, eventInfo.getSendRewardConfigs().size());
        if (ObjectUtils.isEmpty(dataList)) {
            return;
        }
        StringBuilder content = new StringBuilder();
        content.append("## 送礼榜（").append(eventInfo.getEventName()).append(")\n")
                .append("排名\t\t统计数\t\tuid\t\t\t\t\t\t\t\t\t\trid\n");
        IntStream.range(0, dataList.size())
                .forEach(index -> this.giveSendRankRewardAndNotice(index + 1, dataList.get(index), eventInfo.getSendRewardConfigs(), content, eventInfo));
        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, content.toString());
    }

    private void giveSendRankRewardAndNotice(int rankNum, CountVO data, List<RewardTaskConfig> sendRewardConfigs, StringBuilder content, SonEventInfo eventInfo) {
        try {
            content.append(rankNum).append("\t\t\t").append(data.getCount()).append("\t\t\t").append(data.getUid()).append("\t\t");
            ActorData rankActor = actorMgr.getActorData(data.getUid());
            if (rankActor == null) {
                return;
            }
            content.append(rankActor.getRid());
            sendRewardConfigs.stream().filter(rewardTaskConfig -> rewardTaskConfig.getCheckParams() == rankNum)
                    .findFirst()
                    .ifPresent(rewardTaskConfig -> this.giveReward(rankActor.getUid(), rewardTaskConfig, eventInfo, rankNum));
        } finally {
            content.append("\n");
        }
    }

    private void giveReward(String uid, RewardTaskConfig rewardTaskConfig, SonEventInfo eventInfo, int rankNum) {
        giveOutRewardService.giveEventReward(uid, rewardTaskConfig.getRewards(), EVENT_CODE, eventInfo.getEventName() + " send rank,top=" + rankNum);
        officialNoticeService.sendOfficialNotice(uid, "", SEND_RANK_NOTICE, NOTICE_IMG, EVENT_URL, ChannelEnum.CDE.getName(),
                DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50), EVENT_CODE);
    }

    public PageInitVO pageInit(CommonDTO dto) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        List<PageInitVO.EventVO> eventVOs = fillEventVO();
        if (configData.getStartTime() > DateHelper.getCurrTime() || configData.getEndTime() < DateHelper.getCurrTime()) {
            return new PageInitVO().setStartTime(configData.getStartTime())
                    .setEndTime(configData.getEndTime())
                    .setEvents(eventVOs);
        }
        //活动基础信息
        //任务相关
        int enterRoomAwardStatus = dailyEnterRoomOneAwardLimitRedis.getAwardStatus(dto.getUid(), dto.getEventType());
        int sendGiftAwardStatus = dailySendGiftOneAwardLimitRedis.getAwardStatus(dto.getUid(), dto.getEventType());
        long upMicMinute = computeUpMicMinute(dto, currActor);
        int upMicAwardStatus = computeUpMicAwardStatus(dto, currActor);
        //活动列表
        return new PageInitVO()
                .setStartTime(configData.getStartTime())
                .setEndTime(configData.getEndTime())
                .setDailyEnterRoomAwardStatus(enterRoomAwardStatus)
                .setDailyUpMicMin((int) upMicMinute)
                .setDailyUpMicAwardStatus(upMicAwardStatus)
                .setDailyPartyRoomSendGiftAwardStatus(sendGiftAwardStatus)
                .setEvents(eventVOs);
    }

    private List<PageInitVO.EventVO> fillEventVO() {
        return SON_EVENT_INFOS.stream()
                .map(this::fillEventVO)
                .collect(Collectors.toList());
    }

    private long computeUpMicMinute(CommonDTO dto, ActorData currActor) {
        long dailyUpMicCount = dailyEveryLimitRewardRedis.findCount(currActor.getUid(), dto.getEventType());
        if (dailyUpMicCount > DAILY_UP_MIC_TASK.getCheckParams()) {
            dailyUpMicCount = DAILY_UP_MIC_TASK.getCheckParams();
        }
        return Duration.ofSeconds(dailyUpMicCount).toMinutes();
    }

    private int computeUpMicAwardStatus(CommonDTO dto, ActorData currActor) {
        int upMicAwardStatus;
        long claimCount = dailyEveryLimitRewardRedis.findClaimCount(currActor.getUid(), dto.getEventType());
        if (claimCount >= 1) {
            upMicAwardStatus = -1;
        } else {
            long canClaimCount = dailyEveryLimitRewardRedis.computeCanClaimCount(currActor.getUid(), dto.getEventType(), DAILY_UP_MIC_TASK);
            upMicAwardStatus = canClaimCount > 0 ? 1 : 0;
        }
        return upMicAwardStatus;
    }

    private PageInitVO.EventVO fillEventVO(SonEventInfo sonEventInfo) {
        return new PageInitVO.EventVO()
                .setActivityId(sonEventInfo.getActivityId())
                .setStartTime(sonEventInfo.getStartTime())
                .setEndTime(sonEventInfo.getEndTime());
    }

    public OtherRankingVO getSendGiftRank(String activityId, String uid, int rankNum) {
        OtherRankingVO vo = new OtherRankingVO();
        SonEventInfo sonEvent = SON_EVENT_INFOS.stream().filter(sonEventInfo -> sonEventInfo.getActivityId().equals(activityId))
                .findFirst().orElse(null);
        if (sonEvent == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "ActivityId not found,activityId=" + activityId));
        }
        ActorData currActor = actorMgr.getCurrActorData(uid);
        String rankKey = zsetSendGiftRankKey(activityId);

        OtherMyRankVO myRank = fillRankSelfData(uid, currActor, rankKey);
        vo.setMyRank(myRank);

        List<CountVO> rankDataList = baseZSetRedis.getRange(rankKey, 1, rankNum);
        if (rankDataList.isEmpty()) {
            return vo;
        }
        List<OtherRankingListVO> rankList = IntStream.range(0, rankDataList.size())
                .mapToObj(index -> fillOtherRankingListVO(index + 1, rankDataList.get(index)))
                .collect(Collectors.toList());
        vo.setRankingList(rankList);
        return vo;
    }

    public void giftSendAction(SendGiftSuccessMsgData mqData) {
        checkParams(mqData);
        if (mqData.getFromType() != GiftFromTypeConstant.GIFT_FROM_PARTY) {
            return;
        }
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        if (configData.getStartTime() > mqData.getTime()
                || configData.getEndTime() < mqData.getTime()
//                || !configData.getDataId().contains(mqData.getGiftId().intValue())
        ) return;
        partySendGiftTaskAction(mqData, configData);
        SonEventInfo eventInfo = getSonEventInfo(mqData.getTime());
        log.debug("send gift rank action,eventInfo={}", JSON.toJSONString(eventInfo));
        if (eventInfo == null) return;
        sendGiftRankAction(mqData, configData, eventInfo);
        receiveGiftPkRankAction(mqData, configData, eventInfo);
    }


    private void receiveGiftPkRankAction(SendGiftSuccessMsgData mqData, AppConfigActivityData configData, SonEventInfo eventInfo) {
        if (!mqData.getRoomId().contains(mqData.getToUid())) {
            return;
        }
        if (!configData.getChannel().equals(mqData.getToChannel())) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getToUid());
        if (actorExternalDao.isTester(mqData.getToUid())) {
            return;
        }
        String rankKey = zsetReceiveGiftRankKey(eventInfo.getActivityId());
        String personalRankKey = zsetPersonalRankKey(eventInfo.getActivityId(), mqData.getToUid());
        if (SON_EVENT_INFOS.get(0).getActivityId().equals(eventInfo.getActivityId())) {
            saveReceivePKRank(mqData, rankKey, personalRankKey);
            return;
        }
        String memberKey = setJoinRankMemberKey(eventInfo.getActivityId());
        if (!baseSetRedis.isMember(memberKey, mqData.getToUid())) {
            return;
        }
        saveReceivePKRank(mqData, rankKey, personalRankKey);
    }

    private void saveReceivePKRank(SendGiftSuccessMsgData mqData, String rankKey, String personalRankKey) {
        baseZSetRedis.increaseToZSet(rankKey, mqData.getToUid(), mqData.getCost(), Duration.ofDays(30));
        baseZSetRedis.increaseToZSet(personalRankKey, mqData.getUid(), mqData.getCost(), Duration.ofDays(30));
    }

    private void sendGiftRankAction(SendGiftSuccessMsgData mqData, AppConfigActivityData configData, SonEventInfo eventInfo) {
        if (!configData.getChannel().equals(mqData.getChannel())) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        if (actorExternalDao.isTester(mqData.getUid())) {
            return;
        }
        String rankKey = zsetSendGiftRankKey(eventInfo.getActivityId());
        baseZSetRedis.increaseToZSet(rankKey, mqData.getUid(), mqData.getCost(), Duration.ofDays(30));
    }

    private OtherRankingListVO fillOtherRankingListVO(int rankNum, CountVO data) {
        OtherRankingListVO vo = new OtherRankingListVO();
        vo.setScore((int) data.getCount());
        ActorData rankActor = actorMgr.getActorData(data.getUid());
        if (rankActor != null) {
            log.error("can not find actor. uid={}", data.getUid());
            vo.setName(rankActor.getName());
            vo.setHead(moderationService.dealRankHeadModeration(rankActor));
        }
        vo.setUid(data.getUid());
        vo.setRank(rankNum);
        return vo;
    }

    private OtherMyRankVO fillRankSelfData(String uid, ActorData currActor, String rankKey) {
        OtherMyRankVO myRank = new OtherMyRankVO();
        myRank.setUid(uid);
        myRank.setName(currActor.getName());
        myRank.setGender(currActor.getSex());
        myRank.setHead(moderationService.dealRankHeadModeration(currActor));
        myRank.setSendingScore((int) baseZSetRedis.getOne(rankKey, uid).getCount());
        myRank.setReceiveScore(myRank.getSendingScore());
        myRank.setRank(baseZSetRedis.getRank(rankKey, uid).intValue());
        return myRank;
    }


    private void checkParams(SendGiftSuccessMsgData mqData) {
        if (mqData.getFromType() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "fromType is empty"));
        }
        if (StringUtils.isEmpty(mqData.getRoomId())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "roomId is empty"));
        }
        if (mqData.getTime() == null || mqData.getTime() <= 0) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "time is empty"));
        }
        if (mqData.getGiftId() == null || mqData.getGiftId() <= 0) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "gift is empty"));
        }
        if (StringUtils.isEmpty(mqData.getChannel())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "channel is empty"));
        }
        if (StringUtils.isEmpty(mqData.getToChannel())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "toChannel is empty"));
        }
        if (StringUtils.isEmpty(mqData.getUid())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "uid is empty"));
        }
        if (StringUtils.isEmpty(mqData.getToUid())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "toUid is empty"));
        }
    }

    /**
     * 语聊房开房奖励
     */
    public void enterRoomMqAction(EnterRoomMsgData mqData) {
        if (checkLimit(mqData)) return;
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        AppConfigActivityData eventConfig = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        if (limitNotJoinAndNotEventDuration(eventConfig, currActor, mqData.getCurrTime())) return;
        boolean canGetReward = dailyEnterRoomOneAwardLimitRedis.canGetAwardAfterAction(mqData.getUid(), eventConfig.getActivityCode());
        if (!canGetReward) {
            return;
        }
        // 进房奖励发放飘屏
        roomFloatingImService.sendRoomFloatingIm(currActor, 1, "https://statics.kissu.mobi/room_item/icon/201/Halloween.png",
                "Reward", ", To claim", EVENT_CODE, EVENT_URL, false, "Get #ticket*#count#suffix >>>");
    }

    public Boolean claimEnterRoomAward(CommonDTO dto) {
        dto.checkParams();
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData eventConfig = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        claimAwardLimit(eventConfig, currActor, currTime);
        boolean canGetAward = dailyEnterRoomOneAwardLimitRedis.checkCanGetAward(currActor.getUid(), dto.getEventType());
        if (!canGetAward) {
            throw new WebException(new BaseHttpData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "not meeting the claim conditions"));
        }
        dailyEnterRoomOneAwardLimitRedis.giveAwardAfterAction(currActor.getUid(), dto.getEventType());
        giveOutRewardService.giveEventReward(currActor.getUid(), DAILY_ENTER_ROOM_REWARDS, dto.getEventType(), "1");
        return true;
    }


    /**
     * 每日上麦累计时长任务处理
     */
    public void downMicAction(DownMicMsgData mqData) {
        if (StringUtils.isEmpty(mqData.getUid())) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        AppConfigActivityData eventConfig = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        if (limitNotJoinAndNotEventDuration(eventConfig, currActor, mqData.getMicDownTime())) return;
        dailyEveryLimitRewardRedis.increaseCount(currActor.getUid(), eventConfig.getActivityCode(), mqData.getMicDurationTime());
        long canClaimCount = dailyEveryLimitRewardRedis.computeCanClaimCount(currActor.getUid(), eventConfig.getActivityCode(), DAILY_UP_MIC_TASK);
        if (canClaimCount < 1) {
            return;
        }
        roomFloatingImService.sendRoomFloatingIm(currActor, 1, "https://statics.kissu.mobi/room_item/icon/1726122568423/Golden__Legend.png",
                "Reward", ", To claim", EVENT_CODE, EVENT_URL, false, "Get #ticket*#count#suffix >>>");
    }

    public Boolean claimUpMicAward(CommonDTO dto) {
        dto.checkParams();
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData eventConfig = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        claimAwardLimit(eventConfig, currActor, currTime);
        long claimCount = dailyEveryLimitRewardRedis.findClaimCount(currActor.getUid(), dto.getEventType());
        if (claimCount > 0) {
            throw new WebException(new BaseHttpData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "Today's reward has been claimed"));
        }
        List<RewardInfoData> rewards = dailyEveryLimitRewardRedis.generateCurrCanGetRewards(dto.getUid(), dto.getEventType(), DAILY_UP_MIC_TASK);
        if (ObjectUtils.isEmpty(rewards)) {
            throw new WebException(new BaseHttpData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "not meeting the claim conditions"));
        }
        giveOutRewardService.giveEventReward(dto.getUid(), rewards, dto.getEventType(), "2");
        return true;
    }


    private void partySendGiftTaskAction(SendGiftSuccessMsgData mqData, AppConfigActivityData configData) {
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        if (limitNotJoinAndNotEventDuration(configData, currActor, mqData.getTime())) return;
        boolean canGetAward = dailySendGiftOneAwardLimitRedis.canGetAwardAfterAction(mqData.getUid(), configData.getActivityCode());
        if (!canGetAward) {
            return;
        }
        roomFloatingImService.sendRoomFloatingIm(currActor, 1, "https://statics.kissu.mobi/icon/gift/lucky/Hello_Rose.png",
                "Reward", ", To claim", EVENT_CODE, EVENT_URL, false, "Get #ticket*#count#suffix >>>");
    }

    public Boolean claimPartyRoomSendGiftAward(CommonDTO dto) {
        dto.checkParams();
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData eventConfig = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        claimAwardLimit(eventConfig, currActor, currTime);
        boolean canGetAward = dailySendGiftOneAwardLimitRedis.checkCanGetAward(currActor.getUid(), dto.getEventType());
        if (!canGetAward) {
            throw new WebException(new BaseHttpData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "not meeting the claim conditions"));
        }
        giveOutRewardService.giveEventReward(dto.getUid(), DAILY_SEND_GIFT_REWARDS, dto.getEventType(), "3");
        dailySendGiftOneAwardLimitRedis.giveAwardAfterAction(currActor.getUid(), dto.getEventType());
        return true;
    }


    private static void claimAwardLimit(AppConfigActivityData eventConfig, ActorData currActor, long currTime) {
        if (!eventConfig.getChannel().equals(currActor.getChannel())) {
            throw new WebException(new BaseHttpData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "your app not join this event"));
        }
        if (currTime < eventConfig.getStartTime()) {
            throw new WebException(new BaseHttpData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "It's not time for the event yet"));
        }
        if (currTime > eventConfig.getEndTime()) {
            throw new WebException(new BaseHttpData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "The event has ended"));
        }
    }

    private static boolean checkLimit(EnterRoomMsgData mqData) {
        if (StringUtils.isEmpty(mqData.getUid())) {
            return true;
        }
        if (RoomUtils.getRoomType(mqData.getRoomId()) != RoomType.CHAT) {
            return true;
        }
        if (!mqData.getOwnerUid().equals(mqData.getUid())) {
            return true;
        }
        return false;
    }

    private static boolean limitNotJoinAndNotEventDuration(AppConfigActivityData eventConfig, ActorData currActor, Long currTime) {

        if (!eventConfig.getChannel().equals(currActor.getChannel())) {
            return true;
        }
        if (currTime < eventConfig.getStartTime() || currTime > eventConfig.getEndTime()) {
            return true;
        }

        return false;
    }

    public PkRankVO getReceivePkRank(String activityId, String uid, Integer eventCode) {
        ActorData currActor = actorMgr.getCurrActorData(uid);
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventCode);
        if (configData.getStartTime() > DateHelper.getCurrTime()) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "event not start"));
        }
        SonEventInfo eventInfo = getSonEventInfo(activityId);
        if (eventInfo.getStartTime() > DateHelper.getCurrTime()) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "The current round of activities has not started yet"));
        }
        String rankPkInfoKey = hashRankPkInfoKey(eventCode);
        String rankKey = zsetReceiveGiftRankKey(eventInfo.getActivityId());
        List<PkInfoBO> pkList = null;
        List<PlayerBO> pkSortList = new ArrayList<>(200);
        AtomicReference<PlayerBO> selfData = new AtomicReference<>(new PlayerBO());
        boolean checkLastRank = SON_EVENT_INFOS.get(4).getActivityId().equals(activityId);
        boolean checkFirstRank = SON_EVENT_INFOS.get(0).getActivityId().equals(activityId);
        if (checkFirstRank || checkLastRank) {
            pkSortList = dealRankList(activityId, checkLastRank, rankKey, uid, selfData);
            dealSelfData(activityId, uid, selfData, checkFirstRank, rankKey);
        } else {
            pkList = dealPkRankList(activityId, uid, rankPkInfoKey, rankKey, pkSortList, selfData);
        }

        return new PkRankVO().setPkTopList(pkList)
                .setPkSortList(pkSortList)
                .setSelfData(selfData.get());
    }

    private void dealSelfData(String activityId, String uid, AtomicReference<PlayerBO> selfData, boolean checkFirstRank, String rankKey) {
        PlayerBO self = selfData.get();
        if (self.getInfo() != null || !checkFirstRank) {
            return;
        }
        CountVO selfCount = baseZSetRedis.getOne(rankKey, uid);
        selfData.set(fillPlayerInfo(activityId, selfCount, 0));
    }

    private List<PkInfoBO> dealPkRankList(String activityId, String uid, String rankPkInfoKey, String rankKey, List<PlayerBO> pkSortList, AtomicReference<PlayerBO> selfData) {
        List<PkInfoBO> pkList;
        pkList = baseHashSaveRedis.getListByRedis(rankPkInfoKey, activityId, PkInfoBO.class);
        if (ObjectUtils.isEmpty(pkList)) {
            return new ArrayList<>(0);
        }
        for (PkInfoBO pkInfo : pkList) {
            PlayerBO player1 = pkInfo.getPlayer1();
            fillPlayerInfo(activityId, player1, rankKey);
            PlayerBO player2 = pkInfo.getPlayer2();
            fillPlayerInfo(activityId, player2, rankKey);
            PlayerBO winPlayer = findWinPlayer(player1, player2);
            pkSortList.add(winPlayer);
        }
        //赢家榜单排序
        pkSortList.sort(Comparator.comparingLong(player -> this.sortPk((PlayerBO) player)).reversed()
                .thenComparingLong(player -> this.sortThemPk((PlayerBO) player)));
//        Collections.reverse(pkSortList);
        IntStream.range(0, pkSortList.size())
                .forEach(index -> dealRankNumAndSelfData(uid, pkSortList, selfData, index, activityId));
        return pkList;
    }

    private long sortPk(PlayerBO player) {
        return player.getInfo().getScore();
    }

    private long sortThemPk(PlayerBO player) {
        return Long.parseLong(player.getInfo().getRankNum());
    }

    private void dealRankNumAndSelfData(String uid, List<PlayerBO> pkSortList, AtomicReference<PlayerBO> selfData, int index, String activityId) {
        PlayerBO player = pkSortList.get(index);
        player.getInfo().setRankNum(String.valueOf(index + 1));
        // 处理canGet
        SonEventInfo eventInfo = getSonEventInfo(activityId);
        List<SonEventInfo.RankRate> rankRates = eventInfo.getRankRates();
        rankRates.stream().filter(rankRate -> rankRate.getRank() <= index + 1)
                .max(Comparator.comparingInt(SonEventInfo.RankRate::getRank))
                .ifPresent(rateInfo -> this.fillCanGet(player.getInfo(),
                        rateInfo.getRate(), player.getInfo().getScore()));
//        List<RankRowVO> personalRankList = fillPersonalRankList(activityId, player.info.getActorInfo().getUid(), player.info.getScore());
//        player.setSonRankList(personalRankList);
        if (selfData != null && uid.equals(player.getInfo().getActorInfo().getUid())) {
            selfData.set(beanCopyUtils.cloneBeanByJson(player, PlayerBO.class));
        }
    }

    private List<PlayerBO> dealRankList(String activityId, boolean checkLastRank, String rankKey, String uid, AtomicReference<PlayerBO> selfData) {
        int rankShowNum = checkLastRank ? 16 : 200;
        // 只填充排行榜
        List<CountVO> range = baseZSetRedis.getRange(rankKey, 1, rankShowNum);
        if (ObjectUtils.isEmpty(range)) {
            return new ArrayList<>(0);
        }
        return fillPlayerBoList(activityId, uid, selfData, range);
    }

    private List<PlayerBO> fillPlayerBoList(String activityId, List<CountVO> dataList) {
        return fillPlayerBoList(activityId, null, null, dataList);
    }

    private List<PlayerBO> fillPlayerBoList(String activityId, String uid, AtomicReference<PlayerBO> selfData, List<CountVO> dataList) {
        return IntStream.range(0, dataList.size())
                .mapToObj(index -> fillPlayerAndFindSelf(activityId, uid, selfData, index, dataList))
                .collect(Collectors.toList());
    }

    private PlayerBO fillPlayerAndFindSelf(String activityId, String uid, AtomicReference<PlayerBO> selfData, int index, List<CountVO> dataList) {
        PlayerBO player = fillPlayerInfo(activityId, dataList.get(index), index + 1);
        if (selfData != null && uid.equals(player.getInfo().getActorInfo().getUid())) {
            selfData.set(beanCopyUtils.cloneBeanByJson(player, PlayerBO.class));
        }
        return player;
    }

    private PlayerBO findWinPlayer(PlayerBO player1, PlayerBO player2) {
        PlayerBO winPlayer;
        if (player1.getInfo().getScore() > player2.getInfo().getScore()) {
            winPlayer = beanCopyUtils.cloneBeanByJson(player1, PlayerBO.class);
            return winPlayer;
        }
        if (player2.getInfo().getScore() > player1.getInfo().getScore()) {
            winPlayer = beanCopyUtils.cloneBeanByJson(player2, PlayerBO.class);
            return winPlayer;
        }
        if (Integer.valueOf(player1.getInfo().getRankNum()).compareTo(Integer.valueOf(player2.getInfo().getRankNum())) < 0) {
            winPlayer = beanCopyUtils.cloneBeanByJson(player1, PlayerBO.class);
            return winPlayer;
        }
        return beanCopyUtils.cloneBeanByJson(player2, PlayerBO.class);
    }

    private PlayerBO fillPlayerInfo(String activityId, CountVO countVO, int rankNum) {
        RankRowVO info = fillRankRowVO(rankNum, countVO, activityId, false, countVO.getCount(), rankNum);
        List<RankRowVO> personalRankList = fillPersonalRankList(activityId, countVO.getUid(), countVO.getCount(), rankNum);
        return new PlayerBO().setInfo(info)
                .setSonRankList(personalRankList);
    }

    private void fillPlayerInfo(String activityId, PlayerBO player, String rankKey) {
        String playerUid = player.getInfo().getActorInfo().getUid();
        CountVO score = baseZSetRedis.getOne(rankKey, playerUid);
        player.getInfo().setScore(score.getCount());
        player.getInfo().setRealScore(String.valueOf(score.getRealCount()));
        Long rank = baseZSetRedis.getRank(rankKey, playerUid);
        List<RankRowVO> personalRankList = fillPersonalRankList(activityId, playerUid, score.getCount(), rank.intValue());
        player.setSonRankList(personalRankList);

    }

    private List<RankRowVO> fillPersonalRankList(String activityId, String playerUid, long count, int pTop) {
        String personalRankKey = zsetPersonalRankKey(activityId, playerUid);
        List<CountVO> personalRankDataList = baseZSetRedis.getRange(personalRankKey, 1, 10);
        List<RankRowVO> personalRankList;
        if (ObjectUtils.isEmpty(personalRankDataList)) {
            return new ArrayList<>(0);
        }
        personalRankList = IntStream.range(0, personalRankDataList.size())
                .mapToObj(index -> fillRankRowVO(index + 1, personalRankDataList.get(index), activityId, true, count, pTop))
                .collect(Collectors.toList());
        return personalRankList;
    }

    private RankRowVO fillRankRowVO(int rankNum, CountVO data, String activityId, boolean isSon, long count, int pTop) {
        String rankNumStr = fillRankNumStr(rankNum, isSon);

        RankRowVO vo = new RankRowVO(rankNumStr);
        vo.setScore(data.getCount());
        vo.setRealScore(String.valueOf(data.fetchRealCount()));
        ActorData rankActor = actorMgr.getActorData(data.getUid());
        if (rankActor != null) {
            vo.setActorInfo(new ActorInfo(rankActor));
        }
        if (rankNum == 0) {
            return vo;
        }
        // canGet计算
        fillCanGet(data, activityId, isSon, count, vo, pTop);
        return vo;
    }

    private void fillCanGet(CountVO data, String activityId, boolean isSon, long count, RankRowVO vo, int pTop) {
        SonEventInfo eventInfo = getSonEventInfo(activityId);
        List<SonEventInfo.RankRate> rankRates = eventInfo.getRankRates();
        rankRates.stream().filter(rankRate -> rankRate.getRank() <= pTop)
                .max(Comparator.comparingInt(SonEventInfo.RankRate::getRank))
                .ifPresent(rateInfo -> this.fillCanGet(vo,
                        isSon ? rateInfo.getSonRate() : rateInfo.getRate(),
                        isSon ? count : data.getCount()));
    }

    private void fillCanGet(RankRowVO vo, int rate, long count) {
        long canGet = BigDecimal.valueOf(count)
                .multiply(BigDecimal.valueOf(rate)
                        .divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP))
                .setScale(0, RoundingMode.CEILING).longValue();
        vo.setCanGet(canGet);
    }

    private static String fillRankNumStr(int rankNum, boolean isSon) {
        if (rankNum != 0) {
            return String.valueOf(rankNum);
        }
        return isSon ? "10+" : "200+";
    }

    @Data
    @Accessors(chain = true)
    public static class PageInitVO {
        private Long startTime;
        private Long endTime;
        /**
         * 每日进房奖励
         * 1可领取 -1已领取 0未达到领取条件
         */
        private int dailyEnterRoomAwardStatus;
        /**
         * 每日上麦分钟数
         */
        private int dailyUpMicMin;
        /**
         * 每日上麦达标奖励
         * 1可领取 -1已领取 0未达到领取条件
         */
        private int dailyUpMicAwardStatus;
        /**
         * 每日语聊房送礼奖励状态
         * 1可领取 -1已领取 0未达到领取条件
         */
        private int dailyPartyRoomSendGiftAwardStatus;
        /**
         * 活动列表
         */
        private List<EventVO> events;

        @Data
        @Accessors(chain = true)
        public static class EventVO {
            private String activityId;
            private Long startTime;
            private Long endTime;
        }
    }


    /**
     * pk榜信息集合
     *
     * <AUTHOR>
     * @since 2024/11/6 16:55
     */
    @Data
    @Accessors(chain = true)
    public static class PkRankVO {
        /**
         * 上轮晋级数据
         */
        private List<PkInfoBO> pkTopList;
        /**
         * 本轮赢家排行数据
         */
        private List<PlayerBO> pkSortList;
        /**
         * 个人数据
         */
        private PlayerBO selfData;


    }

    /**
     * <AUTHOR>
     * @since 2024/11/6 17:48
     */
    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    public static class PkInfoBO {
        private PlayerBO player1;
        private PlayerBO player2;

        public PkInfoBO() {
            player1 = new PlayerBO();
            player2 = new PlayerBO();
        }
    }

    /**
     * 榜单信息
     *
     * <AUTHOR>
     * @since 2024/11/6 17:46
     */
    @Data
    @Accessors(chain = true)
    public static class PlayerBO {
        /**
         * 排行信息
         */
        private RankRowVO info;

        /**
         * 贡献榜列表
         */
        private List<RankRowVO> sonRankList;
    }
}
