package com.quhong.event.holi.v2503;

import com.alibaba.fastjson.JSON;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.WarnName;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MasterUtils;
import com.quhong.core.utils.SpringUtils;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.config.TaskConfig;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.dto.RecordDTO;
import com.quhong.data.thData.ActivitySpecialItemsEvent;
import com.quhong.data.vo.BaseHistoryVO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.RewardItemType;
import com.quhong.event.holi.v2503.data.bo.CardPackBO;
import com.quhong.event.holi.v2503.data.dto.ShootDTO;
import com.quhong.event.holi.v2503.data.enums.CardTypeEnum;
import com.quhong.event.holi.v2503.data.vo.CardChangeVO;
import com.quhong.event.holi.v2503.data.vo.PageInitVO;
import com.quhong.event.holi.v2503.data.vo.ShootRecordVO;
import com.quhong.event.holi.v2503.data.vo.ShootResVO;
import com.quhong.exceptions.WebException;
import com.quhong.mq.data.activity.PlayGameMsgData;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.mq.data.room.RoomChatMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.redis.BaseHashSaveRedis;
import com.quhong.redis.base.ActivityCommonRedis;
import com.quhong.redis.base.currency.task.BaseEveryLimitRedis;
import com.quhong.report.EventReport;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.activity.AppConfigActivityService;
import com.quhong.service.common.AwardPoolService;
import com.quhong.service.common.EventBaseRankService;
import com.quhong.service.common.EventBaseRecordService;
import com.quhong.service.common.RoomFloatingImService;
import com.quhong.utils.DistributeLockUtils;
import com.quhong.utils.EnumUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


@Slf4j
@Service
@RequiredArgsConstructor
public class HoliServiceV2503 {
    public static final boolean PROD = ServerConfiguration.isProduct();

    public static final int EVENT_CODE = ActivityTypeEnum.EVENT_HOLI_2503.getCode();
    public static final String EVENT_URL = PROD ? "https://videochat.kissu.site/sheep_catching_battle/" : "https://testvideochat.kissu.site/sheep_catching_battle/";
    public static final String NOTICE_IMG = "https://statics.kissu.mobi/Event/Holi/2025/notice.jpg";
    public static final String DYE_ICON = "https://statics.kissu.mobi/icon/holi/pack.png";

    public static final Integer LIMITED_CARD_MAX_COUNT = 400;
    public static final String PROMOTION_TEXT = "Happy Holi, Join Event";
    public static final DateHelper dateHelper = DateHelper.BEIJING;

    public static final Integer RECEIVER_TYPE_RANK = 1;
    public static final Integer COLOR_TYPE_RANK = 2;

    public static final String PLAY_GAME_COMPUTE_LOCK_KEY = "lock:event:" + EVENT_CODE + ":play_game%s";
    public static final String SEND_GIFT_COMPUTE_LOCK_KEY = "lock:event:" + EVENT_CODE + ":send_gift%s";

    /**
     * 仅赠送活动礼物计入统计
     */
    private static final Set<Long> HOLI_GIFT_ID_SET = Collections.unmodifiableSet(new HashSet<>(Arrays.asList(621L, 2048L, 2050L, 622L, 623L)));

    // 分享文案
    public static final TaskConfig SEND_ACTIVITY_TEXT_TASK = new TaskConfig(TaskTypeConstant.DISSEMINATE_TIMES, 5, 1, 1);
    // 收礼得卡片
    private static final TaskConfig RECEIVE_GIFT_TASK = new TaskConfig(TaskTypeConstant.RECEIVE_COST_GOLD, 200, 1, -1);
    // 送礼得染料
    private static final TaskConfig SEND_GIFT_TASK = new TaskConfig(TaskTypeConstant.SEND_GIFT, 200, 1, -1);
    // 玩游戏得染料
    private static final TaskConfig PLAY_GAME_TASK = new TaskConfig(TaskTypeConstant.PLAY_GAME_COST_GOLD, 20000, 1, -1);

    public static final String CARD_POOL_TYPE = "card_pool";
    public static final List<AwardInfo> CARD_POOL_CONFIGS = new ArrayList<AwardInfo>() {{
        add(new AwardInfo().setRate(PROD ? 2500 : 2000).setAwardId(CardTypeEnum.RED.getValue()));
        add(new AwardInfo().setRate(PROD ? 2400 : 2000).setAwardId(CardTypeEnum.YELLOW.getValue()));
        add(new AwardInfo().setRate(PROD ? 2600 : 2000).setAwardId(CardTypeEnum.BLUE.getValue()));
        add(new AwardInfo().setRate(PROD ? 300 : 2000).setAwardId(CardTypeEnum.PINK.getValue()));
        add(new AwardInfo().setRate(PROD ? 2200 : 2000).setAwardId(CardTypeEnum.PURPLE.getValue()));
    }};


    private static final List<RewardInfoData> SYNTHESIS_REWARD = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, 2049, 100));
        add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.BUBBLE_FRAME, PROD ? 363 : 71, 0));
        add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.ENTER_EFFECT, PROD ? 364 : 68, 0));
    }};


    private static final List<RewardTaskConfig> COLOR_PROGRESS_REWARD = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 3, RewardItemType.SEAT_FRAME, PROD ? 366 : 69, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.GOLD, 0, 1));
        }};
        add(new RewardTaskConfig().setCheckParams(40).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 25, RewardItemType.GOLD, 0, 1));
        }};
        add(new RewardTaskConfig().setCheckParams(100).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 150, RewardItemType.GOLD, 0, 1));
        }};
        add(new RewardTaskConfig().setCheckParams(600).setRewards(rewards4));

        List<RewardInfoData> rewards5 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 375, RewardItemType.GOLD, 0, 1));
        }};
        add(new RewardTaskConfig().setCheckParams(2000).setRewards(rewards5));
    }};


    private static final List<RewardTaskConfig> COLOR_RANK_REWARD = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 230 : 123));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90085 : 90029));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 365 : 83));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 230 : 123));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90086 : 90030));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 365 : 83));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.ENTER_EFFECT, PROD ? 230 : 123));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 365 : 83));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90087 : 90031));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 230 : 123));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 365 : 83));
        }};
        IntStream.rangeClosed(4, 10)
                .forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));
    }};


    private static final List<RewardTaskConfig> RECEIVE_RANK_REWARD = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 230 : 123));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90085 : 90029));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 365 : 83));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 230 : 123));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90086 : 90030));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 365 : 83));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.ENTER_EFFECT, PROD ? 230 : 123));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 365 : 83));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90087 : 90031));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 230 : 123));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 365 : 83));
        }};
        IntStream.rangeClosed(4, 10)
                .forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));
    }};

    private final AppConfigActivityService appConfigActivityService;
    private final AppConfigActivityDao appConfigActivityDao;
    private final ActorMgr actorMgr;
    private final BaseEveryLimitRedis baseEveryLimitRedis;
    private final GiveOutRewardService giveOutRewardService;
    private final EventBaseRankService eventBaseRankService;
    private final BaseHashSaveRedis baseHashSaveRedis;
    private final AwardPoolService awardPoolService;
    private final EventBaseRecordService eventBaseRecordService;
    private final EventReport eventReport;
    private final RoomFloatingImService roomFloatingImService;
    private final HoliPromotionRoomLimitRedis holiPromotionRoomLimitRedis;
    private final MonitorSender monitorSender;
    private final ActivityCommonRedis activityCommonRedis;
    private final MasterUtils masterUtils;
    private final DistributeLockUtils distributeLockUtils;


    /**
     * 用户卡包信息
     */
    private String hashCardPackInfoKey(Integer eventCode) {
        return "hash:event:card_pack:" + eventCode;
    }

    private String limitedCardCountKey(Integer eventCode) {
        return "limited_card_count:" + eventCode;
    }

    /**
     * 卡包变动记录
     */
    private String listCardRecordKey(Integer eventCode, String uid) {
        return "list:event:card_record:" + eventCode + ":" + uid;
    }

    /**
     * 角色击中记录
     */
    private String listShootRecordKey(Integer eventCode, String uid) {
        return "list:event:shoot_record:" + eventCode + ":" + uid;
    }


    /**
     * color日数据key
     */
    private String hashColorDailyKey(Integer eventCode, String dateStr) {
        return "color_daily:" + eventCode + ":" + dateStr;
    }

    /**
     * 榜单key
     */
    private String zsetRankKey(Integer eventCode, Integer rankType) {
        return "zset:event:rank:" + eventCode + ":" + rankType;
    }

    public PageInitVO pageInit(CommonDTO dto) {
        dto.checkParams();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        PageInitVO vo = new PageInitVO()
                .setStartTime(configData.getStartTime())
                .setEndTime(configData.getEndTime())
                .setUid(dto.getUid());
        // 活动未开始
        if (configData.getStartTime() > DateHelper.getCurrTime()) return vo;

        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        if (ObjectUtils.isEmpty(currActor)) return vo;
        String dailyColorKey = hashColorDailyKey(dto.getEventType(), dateHelper.getDateByDeltaDay(0));
        return vo.setRestDyeCount(baseEveryLimitRedis.computeCanClaimCount(dto.getUid(), dto.getEventType(), SEND_GIFT_TASK))
                .setCurrColor(activityCommonRedis.getCommonHashValue(dailyColorKey, dto.getUid()))
                .setCardPack(getCardPack(dto.getUid(), dto.getEventType()))
                .setShareCount(holiPromotionRoomLimitRedis.roomCount(dto.getUid()));
    }


    // 获取收礼榜
    public ModelRankVO<RankRowVO> receiverRank(CommonDTO dto) {
        dto.checkParams();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        // 活动未开始
        if (configData.getStartTime() > DateHelper.getCurrTime()) return new ModelRankVO<>();

        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        if (ObjectUtils.isEmpty(currActor)) return new ModelRankVO<>();

        return eventBaseRankService.rank(currActor, zsetRankKey(dto.getEventType(), RECEIVER_TYPE_RANK), "10+");
    }

    // 获取颜色积分榜
    public ModelRankVO<RankRowVO> colorRank(CommonDTO dto) {
        dto.checkParams();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        // 活动未开始
        if (configData.getStartTime() > DateHelper.getCurrTime()) return new ModelRankVO<>();

        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        if (ObjectUtils.isEmpty(currActor)) return new ModelRankVO<>();

        return eventBaseRankService.rank(currActor, zsetRankKey(dto.getEventType(), COLOR_TYPE_RANK), "10+");
    }

    // 获取用户卡包信息
    private CardPackBO getCardPack(String uid, Integer eventType) {
        CardPackBO cardPackBO = baseHashSaveRedis.getDataByRedis(hashCardPackInfoKey(eventType), uid, CardPackBO.class);
        return ObjectUtils.isEmpty(cardPackBO) ? new CardPackBO() : cardPackBO;
    }

    // 更新用户卡包信息
    private void updateCardPack(String uid, Integer eventType, CardPackBO packBO) {
        baseHashSaveRedis.saveToRedis(hashCardPackInfoKey(eventType), uid, packBO);
    }

    // 更新卡片变动历史
    private void saveSynthesisRecord(String uid, Integer decrease) {
        List<CardChangeVO> voList = Arrays.stream(CardTypeEnum.values())
                .map(cardTypeEnum -> new CardChangeVO()
                        .setChangeCount(-decrease)
                        .setCardName(cardTypeEnum.getName())
                        .setIcon(cardTypeEnum.getIcon())
                        .setTime(dateHelper.getDateByTime(DateHelper.getCurrTime() * 1000)))
                .collect(Collectors.toList());
        eventBaseRecordService.leftPushAll(listCardRecordKey(EVENT_CODE, uid), voList);
    }

    // 更新卡片变动历史
    private void updateCardRecord(CardTypeEnum cardTypeEnum, String uid, int count) {
        CardChangeVO changeVO = new CardChangeVO()
                .setChangeCount(count)
                .setCardName(cardTypeEnum.getName())
                .setIcon(cardTypeEnum.getIcon())
                .setTime(dateHelper.getDateByTime(DateHelper.getCurrTime() * 1000));
        eventBaseRecordService.leftPush(listCardRecordKey(EVENT_CODE, uid), changeVO);
    }

    // 获取卡包变动历史
    public BaseHistoryVO<CardChangeVO> getCardRecord(RecordDTO dto) {
        String cardRecordKey = listCardRecordKey(dto.getEventType(), dto.getUid());
        return eventBaseRecordService.findRecord(dto, cardRecordKey, CardChangeVO.class);
    }


    // 玩游戏，返回剩余可用次数
    public ShootResVO shoot(ShootDTO dto) {
        dto.checkParams();
        ShootResVO shootResVO = new ShootResVO();
        // 活动期内
        AppConfigActivityData configActivityData = appConfigActivityService.getValidOneByEventCodeThrowWebException(EVENT_CODE);
        if (!appConfigActivityService.isWithInTime(configActivityData)) return shootResVO;
        // 渠道支持
        ActorData curActor = actorMgr.getCurrActorData(dto.getUid());
        if (ObjectUtils.isEmpty(curActor) || !appConfigActivityService.isChannelSupported(configActivityData, curActor.getChannel()))
            return shootResVO;
        // 判断剩余粉末数量是否足够
        long currRestDye = baseEveryLimitRedis.computeCanClaimCount(dto.getUid(), dto.getEventType(), SEND_GIFT_TASK);
        if (currRestDye < dto.getTimes()) return shootResVO.setRestDyePack(currRestDye);
        // 使用粉末
        baseEveryLimitRedis.generateCurrCanGetRewards(dto.getUid(), dto.getEventType(), SEND_GIFT_TASK, dto.getTimes());
        // 总榜累计
        eventBaseRankService.rankValueIncrease(zsetRankKey(dto.getEventType(), COLOR_TYPE_RANK), dto.getUid(), dto.computeIncColor());
        // 日计数累计
        String dailyColorKey = hashColorDailyKey(dto.getEventType(), dateHelper.getDateByDeltaDay(0));
        // 累计日分值  发放日奖励
        long beforeDailyColor = activityCommonRedis.getCommonHashValue(dailyColorKey, dto.getUid());
        long afterDailyColor = activityCommonRedis.incCommonHashNum(dailyColorKey, dto.getUid(), (int) dto.computeIncColor());
        dealColorProgressReward(dto.getUid(), beforeDailyColor, afterDailyColor);
        // 记录击中角色记录
        saveShootRecord(dto);
        // 埋点
        if (dto.getTimes() > 0) {
            reportCost(curActor, configActivityData, "0", dto.getTimes(), "4"); // 颜料包
            reportGet(curActor, configActivityData, "2", (int) dto.computeIncColor(), "1", null); // 积分
        }

        return shootResVO.setRestDyePack(currRestDye - dto.getTimes()).setCurrDailyColor(afterDailyColor);
    }

    private void saveShootRecord(ShootDTO dto) {
        dto.checkParams();
        ShootRecordVO vo = new ShootRecordVO()
                .setItemType(dto.shootItemName())
                .setIcon(dto.shootItemIcon())
                .setIncColor(dto.computeIncColor())
                .setTimes(dto.getTimes())
                .setTime(dateHelper.getDateByTime(DateHelper.getCurrTime() * 1000));
        eventBaseRecordService.leftPush(listShootRecordKey(dto.getEventType(), dto.getUid()), vo);
    }

    // 获取角色射中历史
    public BaseHistoryVO<ShootRecordVO> getShootRecord(RecordDTO dto) {
        String shootRecordKey = listShootRecordKey(dto.getEventType(), dto.getUid());
        return eventBaseRecordService.findRecord(dto, shootRecordKey, ShootRecordVO.class);
    }

    private void dealColorProgressReward(String uid, long beforeColor, long currColor) {
        if (beforeColor < 0 || currColor < 0 || beforeColor > currColor || uid == null) return;
        // 获取进度奖励
        List<RewardInfoData> rewardsCanGet = COLOR_PROGRESS_REWARD.stream()
                .filter(rewardTaskConfig -> beforeColor < rewardTaskConfig.getCheckParams()
                        && currColor >= rewardTaskConfig.getCheckParams())
                .map(RewardTaskConfig::getRewards)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(rewardsCanGet)) {
            giveOutRewardService.giveEventReward(uid, rewardsCanGet, EVENT_CODE, "1");
        }
    }

    // 累计活动礼物，换算粉末
    public void giftSendAction(SendGiftSuccessMsgData mqData) {
        if (!HOLI_GIFT_ID_SET.contains(mqData.getGiftId())) {
            return;
        }

        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());

        AppConfigActivityData configActivityData = appConfigActivityService.getValidOneByEventCodeThrowWebException(EVENT_CODE);
        if (!appConfigActivityService.isWithInTimeChannelSet(configActivityData, currActor)) return;

        String lockKey = String.format(SEND_GIFT_COMPUTE_LOCK_KEY, mqData.getUid());
        SendGiftDTO sendGiftDTO = new SendGiftDTO(configActivityData, currActor, mqData);
        distributeLockUtils.distributeMethod(sendGiftDTO, lockKey, this::dealSendGift);
    }

    private int dealSendGift(SendGiftDTO dto) {
        // 累计送礼换取染料
        long restDyeBefore = baseEveryLimitRedis.computeCanClaimCount(dto.getActorData()
                .getUid(), EVENT_CODE, SEND_GIFT_TASK);
        baseEveryLimitRedis.increaseCount(dto.getActorData().getUid(), EVENT_CODE, dto.getMsgData()
                .getCost(), SEND_GIFT_TASK);
        long restDyeAfter = baseEveryLimitRedis.computeCanClaimCount(dto.getActorData()
                .getUid(), EVENT_CODE, SEND_GIFT_TASK);
        // 埋点
        if (restDyeBefore < restDyeAfter) {
            int dyeIncCount = (int) (restDyeAfter - restDyeBefore);
            reportGet(dto.getActorData(), dto.getConfigActivityData(), "0", dyeIncCount, "1", null);
            getDyeFloating(dto.actorData, dyeIncCount);
        }
        // 收礼榜累计
        eventBaseRankService.rankValueIncrease(zsetRankKey(EVENT_CODE, RECEIVER_TYPE_RANK), dto.getMsgData()
                .getToUid(), dto.getMsgData().getCost());
        // 卡片获得
        int canGetCardCount = baseEveryLimitRedis.increaseAndGetRewards(dto.getMsgData().getToUid(),
                EVENT_CODE, dto.getMsgData().getCost(), RECEIVE_GIFT_TASK);
        dealDrawCard(dto.getConfigActivityData(), dto.getMsgData().getToUid(), canGetCardCount);
        return 0;
    }


    public int synthesisCard(CommonDTO dto) {
        dto.checkParams();
        // 活动期内
        AppConfigActivityData configActivityData = appConfigActivityService.getValidOneByEventCodeThrowWebException(EVENT_CODE);
        if (!appConfigActivityService.isWithInTime(configActivityData)) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "not in event time"));
        }
        // 渠道支持
        ActorData curActor = actorMgr.getCurrActorData(dto.getUid());
        if (ObjectUtils.isEmpty(curActor) || !appConfigActivityService.isChannelSupported(configActivityData, curActor.getChannel()))
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "your app not join this event"));
        // 卡片足够
        CardPackBO cardPackBO = getCardPack(dto.getUid(), dto.getEventType());
        if (!cardPackBO.canSynthesize()) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "card not enough"));
        }
        int count = cardPackBO.doSynthesize();
        if (count == 0) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "card not enough"));
        }
        updateCardPack(dto.getUid(), dto.getEventType(), cardPackBO);
        // 更新卡片变动记录
        saveSynthesisRecord(dto.getUid(), count);
        List<RewardInfoData> rewards = SYNTHESIS_REWARD.stream()
                .map(reward -> SpringUtils.copyObj(reward, RewardInfoData.class))
                .peek(reward -> reward.multiNums(count))
                .collect(Collectors.toList());
        // 奖励发放
        giveOutRewardService.giveEventReward(dto.getUid(), rewards, dto.getEventType(), "2");
        // 埋点
        reportCost(curActor, configActivityData, "1", 5 * count, "4");
        return count;
    }

    private void dealDrawCard(AppConfigActivityData activityData, String uid, int count) {
        if (count <= 0) return;
        ActorData currActor = actorMgr.getCurrActorData(uid);
        if (ObjectUtils.isEmpty(currActor)) return;

        CardPackBO cardPackBO = getCardPack(uid, EVENT_CODE);
        List<CardTypeEnum> cards = doDrawCard(count);
        for (CardTypeEnum card : cards) {
            // 更新卡包
            cardPackBO.getCardCountMap().compute(card, (k, v) -> v + 1);
            // 记录卡片变动
            updateCardRecord(card, uid, 1);
            // 埋点
            reportGet(currActor, activityData, "1", 1, "1", card.getName());
            getCardFloating(currActor, 1, card.getIcon(), card.getName());
        }
        updateCardPack(uid, EVENT_CODE, cardPackBO);
    }


    private List<CardTypeEnum> doDrawCard(int count) {
        List<CardTypeEnum> cards = new ArrayList<>();
        int maxAttempts = 10000; // 设置最大运行次数
        int attempts = 0;
        while (cards.size() < count && attempts < maxAttempts) {
            attempts++;
            drawOneCard(cards);
        }
        if (cards.size() < count) {
            log.error("Failed to draw enough cards. Expected: {}, Got: {}, Attempts: {}", count, cards.size(), attempts);
            monitorSender.customMarkdown(WarnName.REPORT, String.format("洒红节卡片抽取结果告警：抽卡数量与期望不符， drawCount=%s, res=%s", count, cards.size()));
        }
        return cards;
    }

    private void drawOneCard(List<CardTypeEnum> cards) {
        List<AwardInfo> drawCards = awardPoolService.drawAwardFromRedisPool(EVENT_CODE, CARD_POOL_TYPE, CARD_POOL_CONFIGS, 10000, 1);
//        if (drawCards.isEmpty()) {
//            throw new WebException(HttpCode.createHttpCode(HttpCode.SERVER_ERROR, true, "draw card is empty to common pool"));
//        }
        CardTypeEnum cardTypeEnum = EnumUtils.fromCode(drawCards.get(0).getAwardId(), CardTypeEnum.class);
        if (ObjectUtils.isEmpty(cardTypeEnum)) {
            monitorSender.customMarkdown(WarnName.REPORT, "洒红节卡片抽取结果告警：卡片类型未定义");
            return;
        }
        // 限制蓝卡发放数量
        if (cardTypeEnum != CardTypeEnum.PINK) {
            cards.add(cardTypeEnum);
            return;
        }
        int currPinkCard = activityCommonRedis.incCommonHashNum(limitedCardCountKey(EVENT_CODE), CardTypeEnum.PINK.getName(), 1);
        if (currPinkCard > LIMITED_CARD_MAX_COUNT) {
            return;
        }
        cards.add(cardTypeEnum);
    }


    // 累计游戏花费，换算粉末
    public void playGameAction(PlayGameMsgData mqData) {
        AppConfigActivityData activityData = appConfigActivityService.getValidOneByEventCodeThrowWebException(EVENT_CODE);
        if (!appConfigActivityService.isWithInTime(activityData)) return;

        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        if (ObjectUtils.isEmpty(currActor) || !appConfigActivityService.isChannelSupported(activityData, currActor.getChannel()))
            return;

        String lockKey = String.format(PLAY_GAME_COMPUTE_LOCK_KEY, mqData.getUid());
        PlayGameDTO playGameDTO = new PlayGameDTO(activityData, currActor, mqData);
        distributeLockUtils.distributeMethod(playGameDTO, lockKey, this::dealPlayGame);
    }

    private int dealPlayGame(PlayGameDTO dto) {
        // 累计送礼换取染料
        long restDyeBefore = baseEveryLimitRedis.computeCanClaimCount(dto.getActorData()
                .getUid(), EVENT_CODE, PLAY_GAME_TASK);
        baseEveryLimitRedis.increaseCount(dto.getActorData()
                .getUid(), EVENT_CODE, dto.msgData.getCoin(), PLAY_GAME_TASK);
        long restDyeAfter = baseEveryLimitRedis.computeCanClaimCount(dto.getActorData()
                .getUid(), EVENT_CODE, PLAY_GAME_TASK);
        // 埋点
        if (restDyeBefore >= restDyeAfter) {
            return 0;
        }
        int dyeIncCount = (int) (restDyeAfter - restDyeBefore);
        reportGet(dto.getActorData(), dto.getConfigActivityData(), "0", dyeIncCount, "2", null);
        getDyeFloating(dto.actorData, dyeIncCount);
        // 统一累计到送礼key中
        long incScore = (restDyeAfter - restDyeBefore) * SEND_GIFT_TASK.getCheckParams();
        baseEveryLimitRedis.increaseCount(dto.getActorData().getUid(), EVENT_CODE, incScore, SEND_GIFT_TASK);
        return 0;
    }

    // 文案分享送染料
    public void roomMsgAction(RoomChatMsgData mqData) {
        // 检测活动文案
        if (!mqData.getMsg().contains(PROMOTION_TEXT)) return;
        // 房间信息，且mqMsg参数齐全
        if (StringUtils.isEmpty(mqData.getUid())) return;
        if (StringUtils.isEmpty(mqData.getRoomId())) return;
        if (StringUtils.isEmpty(mqData.getMsg())) return;
        // 防刷
        if (mqData.getUid().equals(RoomUtils.getRoomOwnerId(mqData.getRoomId()))) return;
        // 活动支持该用户参加，且活动在时间内
        AppConfigActivityData configData = appConfigActivityDao.getValidOneByEventCode(EVENT_CODE);
        if (ObjectUtils.isEmpty(configData)) return;
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        if (ObjectUtils.isEmpty(currActor)) return;
        if (!appConfigActivityService.isWithInTimeChannelSet(configData, currActor)) return;

        log.debug("match PROMOTION_TEXT, roomMsgAction:{}", JSON.toJSONString(mqData));
        // 计算每日领取数量
        int increase = holiPromotionRoomLimitRedis.saveRoomIdAndGetTicket(mqData.getUid(), mqData.getRoomId());
        if (increase < 1) return;
        // 分享次数达到，奖励一个颜料包
        reportGet(currActor, configData, "0", increase, "3", null);
        getDyeFloating(currActor, 1);
        // 统一累计到送礼key中
        baseEveryLimitRedis.increaseCount(mqData.getUid(), EVENT_CODE, increase * SEND_GIFT_TASK.getCheckParams(), SEND_GIFT_TASK);
        log.debug("count++ PROMOTION_TEXT, roomMsgAction:{}", JSON.toJSONString(mqData));
    }

    @Async("asyncPool")
    @Scheduled(cron = "5 0 0 17 3 ?", zone = "GMT+8:00")
    public void settle() {
        // 仅主节点
        if (!masterUtils.isMaster()) return;

        // 活动有效
        AppConfigActivityData activityData = appConfigActivityDao.getValidOneByEventCode(EVENT_CODE);
        if (ObjectUtils.isEmpty(activityData)) return;
        // 官方消息仅安卓
        activityData.setChannel(ChannelEnum.CDE.getName());

        holiRankReward(activityData, RECEIVER_TYPE_RANK);
        holiRankReward(activityData, COLOR_TYPE_RANK);
    }

    private void holiRankReward(AppConfigActivityData configData, Integer rankType) {
        String rankKey = zsetRankKey(configData.getActivityCode(), rankType);
        String rankName = Objects.equals(rankType, RECEIVER_TYPE_RANK) ? "收礼榜" : "积分榜";
        String changeDesc = Objects.equals(rankType, RECEIVER_TYPE_RANK) ? "4" : "3";
        List<RewardTaskConfig> rewardConfigs = Objects.equals(rankType, RECEIVER_TYPE_RANK) ? RECEIVE_RANK_REWARD : COLOR_RANK_REWARD;

        String globalNoticeFormatter = "\uD83D\uDE01 Congratulations to the follows get Top10 in \"holi\" Event \n" +
                "#content" +
                "\uD83D\uDE01 The reward has been issued>>>>";
        eventBaseRankService.rankRewards(configData, rankKey, rewardConfigs, rankName, globalNoticeFormatter, changeDesc,
                NOTICE_IMG, EVENT_URL);
    }

    /**
     * @param itemId   0 （粉末）
     *                 1 （颜色卡片）
     *                 2 （积分）
     * @param fromType 粉末：
     *                 4 打AI (消耗)
     *                 颜色卡片：
     *                 4 合成 （消耗）
     *                 积分：
     *                 1 玩游戏击中（获取）
     */
    private void reportCost(ActorData currActor, AppConfigActivityData configData, String itemId, int itemCount, String fromType) {
        ActivitySpecialItemsEvent logData = new ActivitySpecialItemsEvent();
        logData.setUid(currActor.getUid());
        logData.setCtime(DateHelper.getCurrTime());
        logData.setChannel(currActor.getChannel());
        logData.setEventCode(configData.getActivityCode());
        logData.setActivityName(configData.getName());
        logData.setItemId(itemId);
        logData.setChangeAction(2);
        logData.setFromType(fromType);
        logData.setItemCount(itemCount);
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    /**
     * @param itemId   0 （粉末）
     *                 1 （颜色卡片）
     *                 2 （积分）
     * @param fromType 粉末：
     *                 1 送礼  （获取）
     *                 2 玩游戏 (获取)
     *                 3 宣传活动 (获取)
     *                 颜色卡片：
     *                 1 送礼 （获取）
     *                 积分：
     *                 1 玩游戏击中（获取）
     */
    private void reportGet(ActorData currActor, AppConfigActivityData configData, String itemId, int itemCount, String fromType, String fromTypeDesc) {
        ActivitySpecialItemsEvent logData = new ActivitySpecialItemsEvent();
        logData.setUid(currActor.getUid());
        logData.setCtime(DateHelper.getCurrTime());
        logData.setChannel(currActor.getChannel());
        logData.setEventCode(configData.getActivityCode());
        logData.setActivityName(configData.getName());
        logData.setItemId(itemId);
        logData.setChangeAction(1);
        logData.setFromType(fromType);
        if (!StringUtils.isEmpty(fromTypeDesc)) logData.setFromTypeDesc(fromTypeDesc);
        logData.setItemCount(itemCount);
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private void getDyeFloating(ActorData actorData, int count) {
        roomFloatingImService.sendRoomFloatingIm(actorData, count, DYE_ICON, "dye", "To play", EVENT_CODE, EVENT_URL, false, "Get #ticket*#count #suffix >>>");
    }

    private void getCardFloating(ActorData actorData, int count, String icon, String name) {
        roomFloatingImService.sendRoomFloatingIm(actorData, count, icon, name, "To Redeem rewards", EVENT_CODE, EVENT_URL, false, "Get #ticket*#count #suffix >>>");
    }

    @Data
    @AllArgsConstructor
    static class PlayGameDTO {
        AppConfigActivityData configActivityData;
        ActorData actorData;
        PlayGameMsgData msgData;
    }

    @Data
    @AllArgsConstructor
    static class SendGiftDTO {
        AppConfigActivityData configActivityData;
        ActorData actorData;
        SendGiftSuccessMsgData msgData;
    }

}
