package com.quhong.event.horse.race.v2411;

import com.alibaba.fastjson.JSON;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.LangIdConstant;
import com.quhong.constant.OfficialUserGroup;
import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.WarnName;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.ActorExternalDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.LogPayRecordDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.config.TaskConfig;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.dto.RankingDTO;
import com.quhong.data.dto.RecordDTO;
import com.quhong.data.thData.ActivityTicketsEvent;
import com.quhong.data.thData.DrawPrizesRecordLogData;
import com.quhong.enums.EventCode;
import com.quhong.enums.PayChannelType;
import com.quhong.enums.RewardItemType;
import com.quhong.enums.TopTypeEnum;
import com.quhong.event.horse.race.v2411.dto.DrawDTO;
import com.quhong.event.horse.race.v2411.vo.*;
import com.quhong.exceptions.WebException;
import com.quhong.mq.data.activity.PlayGameMsgData;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.redis.BaseHashSaveRedis;
import com.quhong.redis.BaseListRedis;
import com.quhong.redis.base.currency.task.BaseEveryLimitRedis;
import com.quhong.report.EventReport;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.activity.model.impl.GiftPriceRankActivityService;
import com.quhong.service.common.RewardService;
import com.quhong.utils.StringUtils;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class HorseRaceService {

    private static final boolean PROD = ServerConfiguration.isProduct();


    public static final int MIN_PAY_USD = 100;

    private static final String SEND_GIFT_POOL = "send_gift_pool";
    private static final String PLAY_GAME_POOL = "play_game_pool";
    //    private static final int EVENT_CODE = EventCode.EVENT_HORSE_RACE_2411;
    private static final int EVENT_CODE = EventCode.EVENT_HORSE_RACE_2505;
    /**
     * 默认奖池
     * 重置奖池 del list:event:draw_pool:10054:default_award_pool
     */
    private static final String DEFAULT_AWARD_POOL = "default_award_pool";

    /**
     * 活动链接
     */
    private static final String EVENT_URL = PROD ? "https://videochat.kissu.site/horse_jump_challenge/" : "https://testvideochat.kissu.site/horse_jump_challenge/";
    /**
     * 三方充值用户奖池
     * 重置奖池 del list:event:draw_pool:10054:third_pay_award_pool
     */
    private static final String THIRD_PAY_AWARD_POOL = "third_pay_award_pool";
    private static final Integer RECORD_PAGE_SIZE = 10;
    private static final int LIMIT_INIT_POOL = 50;
    private static final int ZERO_INIT_POOL = 0;

    private static final TaskConfig SEND_GIFT_TASK_CONFIG = new TaskConfig(TaskTypeConstant.SEND_GIFT, 200, 1, -1);
    private static final TaskConfig PLAY_GAME_TASK_CONFIG = new TaskConfig(TaskTypeConstant.PLAY_GAME_COST_GOLD, 10000, 1, -1);

    private static final List<AwardInfo> HORSE_INFOS = new ArrayList<AwardInfo>() {{
        add(new AwardInfo().setAwardId(1).setRate(15).setRate1(15).setAwardName("Gold Medal Horse").setAwardIcon("https://statics.kissu.mobi/Event/horse/202411/1.png"));
        add(new AwardInfo().setAwardId(2).setRate(30).setRate1(25).setAwardName("Silver Medal Horse").setAwardIcon("https://statics.kissu.mobi/Event/horse/202411/2.png"));
        add(new AwardInfo().setAwardId(3).setRate(30).setRate1(30).setAwardName("Bronze Medal Horse").setAwardIcon("https://statics.kissu.mobi/Event/horse/202411/3.png"));
        add(new AwardInfo().setAwardId(4).setRate(25).setRate1(30).setAwardName("Ordinary Horse").setAwardIcon("https://statics.kissu.mobi/Event/horse/202411/4.png"));
    }};
    private static final String NOTICE_IMG = "https://statics.kissu.mobi/event/horse/gaunfangtongzhi.jpg";


    private static final AwardInfo NO_AWARD = new AwardInfo().setAwardId(0).setAwardName("Empty Prize").setAwardIcon("https://statics.kissu.mobi/Event/horse/202411/Empty_Prize.png");
    private static final List<AwardInfo> AWARD_INFOS = new ArrayList<AwardInfo>() {{
        add(new AwardInfo().setRate(1).setRate1(2).setAwardType(RewardItemType.GOLD).setAwardId(5).setAwardName("10000 coins").setNums(250).setUnitPrice(1).setAwardIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png"));
        add(new AwardInfo().setRate(25).setRate1(30).setAwardType(RewardItemType.GOLD).setAwardId(6).setAwardName("1000 coins").setNums(25).setUnitPrice(1).setAwardIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png"));
        add(new AwardInfo().setRate(70).setRate1(70).setAwardType(RewardItemType.GIFT).setAwardId(7).setAwardName("Fencing").setDataId(714).setNums(1).setUnitPrice(15).setAwardIcon("https://statics.kissu.mobi/Event/horse/202411/fencing.png"));
        add(new AwardInfo().setRate(2).setRate1(4).setAwardType(RewardItemType.GIFT).setAwardId(8).setAwardName("Send you flowers").setDataId(2066).setNums(1).setUnitPrice(200).setAwardIcon("https://statics.kissu.mobi/Event/fusion/gift/v1/v2/Send_you_flowers.png"));
        add(new AwardInfo().setRate(140).setRate1(134).setAwardType(RewardItemType.GIFT).setAwardId(9).setAwardName("Thumbs up").setDataId(603).setNums(1).setUnitPrice(1).setAwardIcon("https://statics.kissu.mobi/icon/Welfare/up.png"));
        add(new AwardInfo().setRate(72).setRate1(72).setAwardType(RewardItemType.ENTER_EFFECT).setAwardId(10).setAwardName("Fir Horse*1day").setDataId(PROD ? 299 : 121).setNums(1).setUnitPrice(0).setAwardIcon("https://statics.kissu.mobi/room_item/icon/1732586650547/Fir_Horse.png"));
        add(new AwardInfo().setRate(100).setRate1(100).setAwardType(RewardItemType.ENTER_EFFECT).setAwardId(11).setAwardName("Balloon Car*1day").setDataId(PROD ? 300 : 116).setNums(1).setUnitPrice(0).setAwardIcon("https://statics.kissu.mobi/room_item/icon/1732586797185/Balloon_Car_(2).png"));
        add(new AwardInfo().setRate(110).setRate1(110).setAwardType(RewardItemType.SEAT_FRAME).setAwardId(12).setAwardName("Horse_Racing*1day").setDataId(PROD ? 298 : 114).setNums(1).setUnitPrice(0).setAwardIcon("https://statics.kissu.mobi/room_item/origin/1732586583999/Horse_Racing.webp"));
        add(new AwardInfo().setRate(130).setRate1(130).setAwardType(RewardItemType.SEAT_FRAME).setAwardId(13).setAwardName("Blue Ray*1day").setDataId(PROD ? 297 : 101).setNums(1).setUnitPrice(0).setAwardIcon("https://statics.kissu.mobi/room_item/origin/1732586451913/Blu-ray.webp"));
        add(new AwardInfo().setRate(146).setRate1(143).setAwardType(RewardItemType.BUBBLE_FRAME).setAwardId(14).setAwardName("lucky horse*1day").setDataId(PROD ? 301 : 117).setNums(1).setUnitPrice(0).setAwardIcon("https://statics.kissu.mobi/room_item/icon/1732586877519/lucky_horse2.png"));
        add(new AwardInfo().setRate(100).setRate1(100).setAwardType(RewardItemType.GIFT).setAwardId(15).setAwardName("Tell him/Tell her").setDataId(612).setNums(1).setUnitPrice(0).setAwardIcon("https://statics.kissu.mobi/Event/Speak/speak.png"));
        add(new AwardInfo().setRate(104).setRate1(105).setAwardType(RewardItemType.VIP_DAYS).setAwardId(16).setAwardName("VIP*1day").setNums(1).setUnitPrice(0).setAwardIcon("https://statics.kissu.mobi/icon/checkin/VIP.png"));
    }};
    private static final Set<Integer> PAY_TYPE_SET = new HashSet<Integer>() {{
        add(PayChannelType.MIDDLE_ORDER);
        add(PayChannelType.MIDDLE_SUBSCRIPTION);
    }};

    private static final Set<Integer> STATUS_SET = new HashSet<Integer>() {{
        add(1);
        if (!PROD) add(10);
    }};

    private static final List<RewardTaskConfig> SEND_RANK_REWARD = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 302 : 83));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900054 : 900017));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 277 : 97));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 304 : 55));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 3750, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 302 : 83));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900055 : 90003));
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.ENTER_EFFECT, PROD ? 277 : 97));
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.SEAT_FRAME, PROD ? 304 : 55));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 302 : 83));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900056 : 90001));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 277 : 97));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.SEAT_FRAME, PROD ? 304 : 55));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 302 : 83));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 277 : 97));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 304 : 55));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));
    }};

    private static final List<RewardTaskConfig> RECEIVE_RANK_REWARD = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 302 : 83));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90008 : 900017));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 277 : 97));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 304 : 55));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));
        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 3750, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 302 : 83));
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.ENTER_EFFECT, PROD ? 277 : 97));
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.SEAT_FRAME, PROD ? 304 : 55));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));
        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 302 : 83));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 277 : 97));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.SEAT_FRAME, PROD ? 304 : 55));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));
        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 302 : 83));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 277 : 97));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 304 : 55));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));

    }};
    public static final int HORSE_POOL_SIZE = 100;
    public static final int DRAW_POOL_SIZE = 1000;

    private final AppConfigActivityDao appConfigActivityDao;
    private final ActorMgr actorMgr;
    private final BaseListRedis baseListRedis;
    private final BaseEveryLimitRedis baseEveryLimitRedis;
    private final BaseHashSaveRedis baseHashSaveRedis;
    private final RewardService rewardService;
    private final EventReport eventReport;
    private final GiftPriceRankActivityService giftPriceRankActivityService;
    private final LogPayRecordDao logPayRecordDao;
    private final GiveOutRewardService giveOutRewardService;
    private final MonitorSender monitorSender;
    private final OfficialNoticeService officialNoticeService;
    private final ActorExternalDao actorExternalDao;

    private static String initPoolKey(Integer eventCode, String poolType) {
        return "list:event:draw_pool:" + eventCode + ":" + poolType;
    }

    private static String initCardRecordKey(Integer eventCode, String uid) {
        return "list:event:card_record:" + eventCode + ":" + uid;
    }

    private static String initAwardRecordKey(Integer eventCode, String uid) {
        return "list:event:award_record:" + eventCode + ":" + uid;
    }

    private static String initGlobalAwardRecordKey(Integer eventCode) {
        return "list:event:global_award_record:" + eventCode;
    }

    private static String initUserInfoKey(Integer eventCode) {
        return "hash:event:user_info:" + eventCode + (PROD ? "" : "test");
    }

    private static List<RewardInfoData> fillGetHorses(List<String> awardKeys) {
        List<RewardInfoData> rewards = new ArrayList<>();
        awardKeys.forEach(awardKey -> fillHorseCard(awardKey, rewards));
        return rewards;
    }

    public void sendGiftAction(SendGiftSuccessMsgData mqData) {
        long currTime = DateHelper.getCurrTime();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        if (configData.getStartTime() > currTime || configData.getEndTime() < currTime) {
            return;
        }
        if (!configData.getDataId().contains(mqData.getGiftId().intValue())) {
            return;
        }
        if (actorExternalDao.isTester(mqData.getUid())) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        if (!configData.getChannel().contains(currActor.getChannel())) {
            return;
        }
        int incr = baseEveryLimitRedis.increaseAndGetRewards(currActor.getUid(), configData.getActivityCode(), mqData.getCost(), SEND_GIFT_TASK_CONFIG);
        if (incr < 1) {
            return;
        }
        List<String> awardKeys = drawFromRedisPool(configData.getActivityCode(), SEND_GIFT_POOL, HORSE_INFOS, HORSE_POOL_SIZE, true, incr);
        if (ObjectUtils.isEmpty(awardKeys)) {
            return;
        }
        dealGiveHorseLogic(configData, currActor, awardKeys);
        String fromType = "1";// 来源类型 发送指定活动礼物获得
        String mode = "send gift";
        dealGetCardAfterLogic(currActor, awardKeys, mode, configData, fromType);
    }

    private void dealGiveHorseLogic(AppConfigActivityData configData, ActorData currActor, List<String> awardKeys) {
        String userInfoKey = initUserInfoKey(configData.getActivityCode());
        UserInfo userInfo = findUserInfo(userInfoKey, currActor);
        awardKeys.forEach(awardKey -> {
            Integer num = userInfo.getHorseMap().getOrDefault(awardKey, 0);
            num++;
            userInfo.getHorseMap().put(awardKey, num);
        });
        baseHashSaveRedis.saveToRedis(userInfoKey, currActor.getUid(), userInfo, Duration.ofDays(30));
    }

    private void dealGetCardAfterLogic(ActorData currActor, List<String> awardKeys, String mode, AppConfigActivityData configData, String fromType) {
        // 记录卡片获取
        DateHelper dateHelper = DateHelper.genDateHelper(currActor.getChannel());
        List<CardRecordVO.CardRecord> cardRecords = fillCardRecords(dateHelper, awardKeys, mode);
        String cardRecordKey = initCardRecordKey(configData.getActivityCode(), currActor.getUid());
        baseListRedis.leftPushAll(cardRecordKey, cardRecords);
        // 弹窗提醒
        List<RewardInfoData> rewards = fillGetHorses(awardKeys);
        log.debug("dealGetCardAfterLogic, rewards={}", JSON.toJSONString(rewards));
        if ("1".equals(fromType)) {
            rewardService.sendPersonalActivityPopMsg(currActor.getUid(), rewards, EVENT_URL, configData.getName(), "", "");
        }
        // 上报数数
        reportGetHorseToThinkData(currActor, configData, rewards, fromType);
    }

    private static List<CardRecordVO.CardRecord> fillCardRecords(DateHelper dateHelper, List<String> awardKeys, String mode) {
        String date = dateHelper.getDateByTime(System.currentTimeMillis());
        return awardKeys.stream()
                .map(awardKey -> HORSE_INFOS.stream()
                        .filter(info -> info.getAwardId().toString().equals(awardKey))
                        .findFirst().orElseThrow(() -> new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false,
                                "not found award,id=" + awardKey))))
                .map(info -> new CardRecordVO.CardRecord().setDate(date).setMode(mode).setHorseType(info.getAwardId()))
                .collect(Collectors.toList());
    }

    public void playGameAction(PlayGameMsgData mqData) {
        long currTime = DateHelper.getCurrTime();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        if (configData.getStartTime() > currTime || configData.getEndTime() < currTime) {
            return;
        }
        if (actorExternalDao.isTester(mqData.getUid())) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        if (!configData.getChannel().contains(currActor.getChannel())) {
            return;
        }
        int incr = baseEveryLimitRedis.increaseAndGetRewards(currActor.getUid(), configData.getActivityCode(), mqData.getCoin(), PLAY_GAME_TASK_CONFIG);
        if (incr < 1) {
            return;
        }
        List<String> awardKeys = drawFromRedisPool(configData.getActivityCode(), PLAY_GAME_POOL, HORSE_INFOS, HORSE_POOL_SIZE, false, incr);
        if (ObjectUtils.isEmpty(awardKeys)) {
            return;
        }
        dealGiveHorseLogic(configData, currActor, awardKeys);
        String fromType = "2";// 来源类型 玩游戏获得
        String mode = "play game";
        dealGetCardAfterLogic(currActor, awardKeys, mode, configData, fromType);
    }

    private static void fillHorseCard(String awardKey, List<RewardInfoData> rewards) {
        if (rewards.stream().anyMatch(rewardInfo -> rewardInfo.getDataId().equals(Integer.valueOf(awardKey)))) {
            rewards.stream().filter(rewardInfo -> rewardInfo.getDataId().equals(Integer.valueOf(awardKey)))
                    .findFirst().ifPresent(rewardInfo -> rewardInfo.setNums(rewardInfo.getNums() + 1));
            return;
        }
        AwardInfo horseInfo = HORSE_INFOS.stream().filter(data -> data.getAwardId().equals(Integer.valueOf(awardKey)))
                .findFirst().orElseThrow(() -> new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "not found award,id=" + awardKey)));
        rewards.add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.EVENT_CURRENCY, Integer.parseInt(awardKey))
                .setName(horseInfo.getAwardName()).setIcon(horseInfo.getAwardIcon()));
    }

    private void reportGetHorseToThinkData(ActorData currActor, AppConfigActivityData configData, List<RewardInfoData> rewards, String fromType) {
        rewards.forEach(reward -> {
            ActivityTicketsEvent logData = new ActivityTicketsEvent();
            logData.setUid(currActor.getUid());
            logData.setCtime(DateHelper.getCurrTime());
            logData.setChannel(configData.getChannel());
            logData.setEventCode(configData.getActivityCode());
            logData.setActivityName(configData.getName());
            logData.setTicketType(reward.getDataId());
            logData.setActivityTicketsResource(fromType);
            logData.setActivityTickets(reward.getNums());
            logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
            eventReport.track(logData);
        });
    }

    private UserInfo findUserInfo(String userInfoKey, ActorData currActor) {
        UserInfo userInfo = baseHashSaveRedis.getDataByRedis(userInfoKey, currActor.getUid(), UserInfo.class);
        if (userInfo != null) {
            return userInfo;
        }
        return new UserInfo().setUid(currActor.getUid()).setRid(currActor.getRid());
    }

    private List<String> drawFromRedisPool(Integer eventCode, String poolType, List<AwardInfo> awards, int initPoolSize, boolean isDefaultRate, int times) {
        String poolKey = initPoolKey(eventCode, poolType);
        return IntStream.range(0, times)
                .mapToObj(index -> drawOneFromRedisPool(awards, initPoolSize, isDefaultRate, poolKey))
                .collect(Collectors.toList());
    }


    private String drawOneFromRedisPool(List<AwardInfo> awards, int initPoolSize, boolean isDefaultRate, String poolKey) {
        checkAndInitPool(poolKey, awards, initPoolSize, isDefaultRate);
        String awardKey = baseListRedis.leftPop(poolKey);
        if (StringUtils.isEmpty(awardKey)) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.SERVER_ERROR, true, "drawFromRedisPool error"));
        }
        return awardKey;
    }

    private void checkAndInitPool(String poolKey, List<AwardInfo> awards, int initPoolSize, boolean isDefaultRate) {
        int poolSize = baseListRedis.size(poolKey);
        if (poolSize > ZERO_INIT_POOL && poolSize <= LIMIT_INIT_POOL) {
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    initPool(poolKey, awards, initPoolSize, isDefaultRate);
                }
            });
        } else if (poolSize <= ZERO_INIT_POOL) {
            initPool(poolKey, awards, initPoolSize, isDefaultRate);
        }
    }

    private void initPool(String poolKey, List<AwardInfo> awards, int initPoolSize, boolean isDefaultRate) {
        List<String> poolList = new ArrayList<>(Collections.nCopies(initPoolSize, "-1"));
        List<Integer> indexList = IntStream.rangeClosed(0, initPoolSize - 1).boxed().collect(Collectors.toList());
        Collections.shuffle(indexList);
        awards.forEach(award -> {
            String awardKey = award.getAwardId().toString();
            int awardRate = isDefaultRate ? award.getRate() : award.getRate1();

            if (!isDefaultRate && "6".equals(awardKey)) {
                poolList.set(321, awardKey);
                indexList.remove(Integer.valueOf(321));
                awardRate--;
            }

            if (awardRate == 0) {
                return;
            }
            IntStream.range(0, awardRate).forEach(i -> {
                if (!indexList.isEmpty()) {
                    int index = indexList.remove(0);
                    poolList.set(index, awardKey);
                } else {
                    poolList.add(awardKey);
                }
            });

        });
        poolList.removeAll(Collections.singleton("-1"));
        baseListRedis.rightPushAll(poolKey, poolList);
    }


    /**
     * 页面初始化
     */
    public HorseInitPageVO pageInit(CommonDTO dto) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        HorseInitPageVO vo = new HorseInitPageVO();
        vo.setStartTime(configData.getStartTime());
        vo.setEndTime(configData.getEndTime());
        String userInfoKey = initUserInfoKey(dto.getEventType());
        UserInfo userInfo = findUserInfo(userInfoKey, currActor);
        vo.setHorseMap(userInfo.getHorseMap());
        List<DrawVO.DrawAward> rewards = AWARD_INFOS.stream()
                .map(this::fillDrawAward)
                .collect(Collectors.toList());
        vo.setDrawAwards(rewards);

        long currTime = DateHelper.getCurrTime();
        if (configData.getStartTime() <= currTime
                && configData.getEndTime() >= currTime
                && configData.getChannel().contains(currActor.getChannel())) {
            signThirdPayUser(userInfo);
        }
        return vo;
    }

    private void signThirdPayUser(UserInfo userInfo) {
        if (userInfo.getIsThirdPayUser() != null) {
            return;
        }
        long startTime = DateHelper.getCurrTime() - Duration.ofDays(30).getSeconds();
        String usd = logPayRecordDao.queryUsdPriceSum(userInfo.getUid(), PAY_TYPE_SET, STATUS_SET, startTime, null);
        if (StringUtils.isEmpty(usd)) {
            return;
        }
        if (Double.parseDouble(usd) < MIN_PAY_USD) {
            return;
        }
        userInfo.setIsThirdPayUser(true);
        String userInfoKey = initUserInfoKey(EVENT_CODE);
        baseHashSaveRedis.saveToRedis(userInfoKey, userInfo.getUid(), userInfo, Duration.ofDays(30));
    }

    private DrawVO.DrawAward fillDrawAward(AwardInfo awardInfo) {
        return new DrawVO.DrawAward()
                .setAwardId(awardInfo.getAwardId())
                .setAwardIcon(awardInfo.getAwardIcon())
                .setVideoUrl(awardInfo.getAwardVideoUrl())
                .setAwardName(awardInfo.getAwardName())
                .setAwardNum(1);
    }

    public DrawVO draw(DrawDTO dto) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        long currTime = DateHelper.getCurrTime();
        if (configData.getStartTime() > currTime) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "Event has not started"));
        }
        if (configData.getEndTime() < currTime) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "Event has ended"));
        }

        String userInfoKey = initUserInfoKey(dto.getEventType());
        UserInfo userInfo = findUserInfo(userInfoKey, currActor);
        if (!userInfo.getHorseMap().containsKey(dto.getHorseType().toString())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "Horse not exist"));
        }

        Integer currSum = userInfo.getHorseMap().getOrDefault(dto.getHorseType().toString(), 0);
        if (currSum < dto.getAmount()) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "Horse not enough"));
        }

        DateHelper dateHelper = DateHelper.genDateHelper(currActor.getChannel());
        int afterSum = currSum - dto.getAmount();
        userInfo.getHorseMap().put(dto.getHorseType().toString(), afterSum);
        baseHashSaveRedis.saveToRedis(userInfoKey, userInfo.getUid(), userInfo, Duration.ofDays(30));

        List<DrawVO.DrawAward> drawAwards = doDraw(dto, userInfo, dateHelper, currActor);
        // 数数埋点
        reportDrawInfoToThinkData(dto, currActor, currTime, configData, drawAwards);
        return new DrawVO().setAwards(drawAwards);
    }

    private List<DrawVO.DrawAward> doDraw(DrawDTO dto, UserInfo userInfo, DateHelper dateHelper, ActorData currActor) {
        String awardRecordKey = initAwardRecordKey(dto.getEventType(), dto.getUid());
        String globalAwardRecordKey = initGlobalAwardRecordKey(dto.getEventType());
        // 记录中奖记录
        List<DrawRecordVO.DrawRecord> records = new ArrayList<>(dto.getAmount());
        // 记录中奖广播
        List<BroadcastVO> broadcasts = new ArrayList<>(dto.getAmount());
        // 奖品响应
        List<DrawVO.DrawAward> drawAwards = new ArrayList<>(AWARD_INFOS.size());
        if (dto.getZone()) {
            dealZoneLogic(dto, userInfo, drawAwards, dateHelper, records, currActor, broadcasts, globalAwardRecordKey);
        } else {
            //未中奖
            log.info("draw not in zone,dto={}", JSON.toJSONString(dto));
            dealNotZoneLogic(dto, dateHelper, records, drawAwards);
        }
        baseListRedis.leftPushAll(awardRecordKey, records);
        return drawAwards;
    }

    private void reportDrawInfoToThinkData(DrawDTO dto, ActorData currActor, long currTime, AppConfigActivityData configData, List<DrawVO.DrawAward> drawAwards) {
        DrawPrizesRecordLogData logData = new DrawPrizesRecordLogData();
        logData.setUid(dto.getUid());
        logData.setChannel(currActor.getChannel())
                .setCtime(currTime)
                .setScene(configData.getActivityCode())
                .setCostTicket((long) dto.getAmount())
                .setCostItemId(Long.valueOf(dto.getHorseType()))
                .setDrawNums(dto.getAmount())
                .setDrawSuccessNums(dto.getZone() ? dto.getAmount() : 0)
                .setDrawDesc(dto.getZone() ? "success" : "fail")
                .setDrawResult(JSON.toJSONString(drawAwards));
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private void dealZoneLogic(DrawDTO dto, UserInfo userInfo, List<DrawVO.DrawAward> drawAwards, DateHelper dateHelper, List<DrawRecordVO.DrawRecord> records, ActorData currActor, List<BroadcastVO> broadcasts, String globalAwardRecordKey) {
        boolean isDefaultPool = true;
        String awardPool = DEFAULT_AWARD_POOL;
        if (Boolean.TRUE.equals(userInfo.getIsThirdPayUser())) {
            awardPool = THIRD_PAY_AWARD_POOL;
            isDefaultPool = false;
        }

        List<String> awardKeys = drawFromRedisPool(dto.getEventType(), awardPool,
                AWARD_INFOS, DRAW_POOL_SIZE, isDefaultPool, dto.getAmount());
        if (ObjectUtils.isEmpty(awardKeys)) {
            log.error("draw zone award failed,awardPool={}", awardPool);
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, true, "Award pool is empty"));
        }
        // 中奖逻辑
        List<AwardInfo> awards = fillAwardInfos(awardKeys);
        List<RewardInfoData> rewardInfos = new ArrayList<>(awards.size());
        //需要聚合
        awards.forEach(award -> fillDrawAwards(award, drawAwards));
        awards.forEach(award -> dealAwardMappingInfo(dto, dateHelper, records, currActor, broadcasts, award, rewardInfos));
        if (!ObjectUtils.isEmpty(broadcasts)) {
            baseListRedis.leftPushAll(globalAwardRecordKey, broadcasts);
        }
        if (!ObjectUtils.isEmpty(rewardInfos)) {
            giveOutRewardService.giveEventReward(dto.getUid(), rewardInfos, dto.getEventType(), "draw");
        }
    }

    private void dealAwardMappingInfo(DrawDTO dto, DateHelper dateHelper, List<DrawRecordVO.DrawRecord> records, ActorData currActor, List<BroadcastVO> broadcasts, AwardInfo award, List<RewardInfoData> rewardInfos) {
        // 抽奖记录
        DrawRecordVO.DrawRecord record = fillDrawRecord(dateHelper, award, dto, 1);
        records.add(record);
        // 抽奖广播数据生成
        BroadcastVO broadcast = fillBroadcast(award, currActor);
        broadcasts.add(broadcast);
        //奖品信息
        RewardInfoData rewardInfoData = fillRewardInfoData(award);
        rewardInfos.add(rewardInfoData);

        if ("5".equals(award.getAwardId().toString())) {
//            rewardService.sendUniversalActivityPopMsg(dto.getUid(), Collections.singletonList(rewardInfoData), EVENT_URL, "Summer Horse Racing", null, "");
            rewardService.sendUniversalFullServiceNoticeMsg(dto.getUid(), rewardInfoData, EVENT_URL, "Summer Horse Racing", null, true);
        }
    }

    private List<AwardInfo> fillAwardInfos(List<String> awardKeys) {
        return awardKeys.stream()
                .map(this::findAwardInfoByAwardKey)
                .collect(Collectors.toList());
    }

    private AwardInfo findAwardInfoByAwardKey(String awardKey) {
        return AWARD_INFOS.stream()
                .filter(info -> info.getAwardId().toString().equals(awardKey))
                .findFirst().orElseThrow(() -> new WebException(HttpCode.createHttpCode(
                        HttpCode.ILLEGAL_OPERATION, false, "not found award,id=" + awardKey)));
    }

    private void dealNotZoneLogic(DrawDTO dto, DateHelper dateHelper, List<DrawRecordVO.DrawRecord> records, List<DrawVO.DrawAward> drawAwards) {
        DrawRecordVO.DrawRecord record = fillDrawRecord(dateHelper, NO_AWARD, dto, dto.getAmount());
        records.add(record);
        DrawVO.DrawAward drawAward = fillDrawAward(NO_AWARD);
        drawAward.setAwardNum(dto.getAmount());
        drawAwards.add(drawAward);
    }

    private BroadcastVO fillBroadcast(AwardInfo award, ActorData currActor) {
        return new BroadcastVO()
                .setUsername(currActor.getName())
                .setAwardIcon(award.getAwardIcon())
                .setNums(String.valueOf(award.getNums()));
    }

    private static DrawRecordVO.DrawRecord fillDrawRecord(DateHelper dateHelper, AwardInfo award, DrawDTO dto, int awardNum) {
        return new DrawRecordVO.DrawRecord()
                .setDate(dateHelper.getMinuteTime(System.currentTimeMillis()))
                .setAwardIcon(award.getAwardIcon())
                .setAwardName(award.getAwardName())
                .setHorseType(dto.getHorseType())
                .setAwardNum(awardNum);
    }

    private void fillDrawAwards(AwardInfo award, List<DrawVO.DrawAward> drawAwards) {
        if (ObjectUtils.isEmpty(drawAwards)) {
            drawAwards.add(fillDrawAward(award));
            return;
        }
        if (drawAwards.stream().noneMatch(drawAward -> drawAward.getAwardId().equals(award.getAwardId()))) {
            drawAwards.add(fillDrawAward(award));
            return;
        }
        drawAwards.stream().filter(drawAward -> drawAward.getAwardId().equals(award.getAwardId()))
                .forEach(drawAward -> drawAward.setAwardNum(drawAward.getAwardNum() + 1));
    }

    private RewardInfoData fillRewardInfoData(AwardInfo awardInfo) {
        return new RewardInfoData(EVENT_CODE, awardInfo.getNums(), awardInfo.getAwardType(),
                awardInfo.getDataId(), awardInfo.getUnitPrice())
                .setName(awardInfo.getAwardName())
                .setIcon(awardInfo.getAwardIcon())
                .setUnitCost(awardInfo.getUnitPrice());
    }

    public DrawRecordVO drawRecord(RecordDTO dto) {
        String awardRecordKey = initAwardRecordKey(dto.getEventType(), dto.getUid());
        List<DrawRecordVO.DrawRecord> records = baseListRedis.getListByPage(awardRecordKey,
                DrawRecordVO.DrawRecord.class, dto.getPage(), RECORD_PAGE_SIZE);
        if (ObjectUtils.isEmpty(records)) {
            return new DrawRecordVO();
        }
        int nextPage = checkNextPage(dto, records.size());
        return new DrawRecordVO()
                .setRecords(records)
                .setNextPage(nextPage);
    }


    public CardRecordVO cardRecord(RecordDTO dto) {
        String cardRecordKey = initCardRecordKey(dto.getEventType(), dto.getUid());
        List<CardRecordVO.CardRecord> records = baseListRedis.getListByPage(cardRecordKey,
                CardRecordVO.CardRecord.class, dto.getPage(), RECORD_PAGE_SIZE);
        if (ObjectUtils.isEmpty(records)) {
            return new CardRecordVO();
        }
        int nextPage = checkNextPage(dto, records.size());
        return new CardRecordVO()
                .setRecords(records)
                .setNextPage(nextPage);
    }

    private static int checkNextPage(RecordDTO dto, int size) {
        int nextPage = -1;
        if (size == RECORD_PAGE_SIZE) {
            nextPage = dto.getPage() + 1;
        }
        return nextPage;
    }


    public List<BroadcastVO> broadcast(CommonDTO dto) {
        String awardRecordKey = initGlobalAwardRecordKey(dto.getEventType());
        return baseListRedis.getListByPage(awardRecordKey, BroadcastVO.class, 1, 30);
    }


    public void sendRankRewardAndNotice(int rankType) {
        List<RewardTaskConfig> rewardConfigs;
        String rankName;
        String desc;
        if (rankType == 2) {
            rewardConfigs = RECEIVE_RANK_REWARD;
            rankName = "receiver ranking";
            desc = "rank-receive gift";
        } else {
            rewardConfigs = SEND_RANK_REWARD;
            rankName = "sender ranking";
            desc = "rank-send gift";
        }
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        List<RankingDTO> rankingList = giftPriceRankActivityService.getRankingList(configData, rankType, TopTypeEnum.AWARD);
        if (ObjectUtils.isEmpty(rankingList)) {
            return;
        }

        StringBuilder content = giftPriceRankActivityService.fillNoticeHead(configData, null, rankName);
        StringBuilder globalNotice = new StringBuilder();
        globalNotice.append("😁 Congratulations to the follows get Top 10 in [Summer Horse Racing]").append(rankName).append("\n");
        IntStream.range(0, rankingList.size())
                .forEach(index -> this.giveRewardAndFillNotice(index + 1, rankingList.get(index), content, rewardConfigs, globalNotice, desc));
        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, content.toString());
        globalNotice.append("😁 The reward has been issued~");
        int fixTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        officialNoticeService.sendGlobalNotice("", globalNotice.toString(), NOTICE_IMG, EVENT_URL,
                // 运营要求，只发cde渠道
                ChannelEnum.CDE.getName(),
                fixTime, EVENT_CODE,
                LangIdConstant.ENGLISH, null, OfficialUserGroup.CHANNEL);
    }

    private void giveRewardAndFillNotice(int rankNum, RankingDTO data, StringBuilder content, List<RewardTaskConfig> rewardConfigs, StringBuilder globalNotice, String desc) {
        giftPriceRankActivityService.fillContentBody(rankNum, content, data);
        rewardConfigs.stream().filter(reward -> reward.getCheckParams() == rankNum)
                .findFirst().ifPresent(rewardConfig -> giveReward(rankNum, data, rewardConfig, globalNotice, desc));
    }

    private void giveReward(int rankNum, RankingDTO data, RewardTaskConfig rewardConfig, StringBuilder globalNotice, String desc) {
        globalNotice.append("Top").append(rankNum).append(" ").append(data.getRid()).append("\n");
        giveOutRewardService.giveEventReward(data.getUid(), rewardConfig.getRewards(), EVENT_CODE, desc);
    }

    @Data
    @Accessors(chain = true)
    public static class UserInfo {
        private String uid;
        private Long rid;
        /**
         * 是否是三方充值用户
         * 判定条件为三方充值金额>50美金
         */
        private Boolean isThirdPayUser;
        /**
         * 马匹信息
         */
        private Map<String, Integer> horseMap;

        public UserInfo() {
            this.horseMap = new HashMap<String, Integer>(4) {{
                put("1", 0);
                put("2", 0);
                put("3", 0);
                put("4", 0);
            }};
        }
    }
}
