package com.quhong.event.diwali.v1.data.config;

import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 获得福灯任务配置
 *
 * <AUTHOR>
 * @since 2023/10/23 19:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TaskConfig extends com.quhong.data.config.TaskConfig {

    /**
     * 首充任务
     */
    public static final TaskConfig RECHARGE_TASK = new TaskConfig(TaskTypeConstant.EVENT_FIRST_RECHARGE, 0, 10, -1);
    /**
     * 雅讯时空系列游戏任务
     */
    public static final TaskConfig PLAY_YXSK_GAME_TASK = new TaskConfig(TaskTypeConstant.PLAY_YXSK_GAME, 400, 2, 15);
    /**
     * 宣传任务
     */
    public static final TaskConfig DISSEMINATE_TIMES_TASK = new TaskConfig(TaskTypeConstant.DISSEMINATE_TIMES, 3, 1, 1);
    /**
     * 福灯图标
     */
    public static final String DENG_ICON_URL = "https://statics.kissu.mobi/Event/super/key.png";
    /**
     * 活动地址
     */
    public static final String EVENT_URL = "https://videochat.kissu.site/christmas-2023/";
    public static final String TEST_EVENT_URL = "https://testvideochat.kissu.site/christmas-2023/";
    /**
     * 飘屏背景图
     */
    public static final String FLOATING_SCREEN_BACKGROUND_IMG = "https://statics.kissu.mobi/icon/independence/new/floating_v4.png";
    /**
     * 排灯节活动宣传文案
     */
    public static final String DISSEMINATE_TEXT = "Merry Christmas, participate in the event and win big prizes";
    /**
     * 任务配置
     */
    public static final List<TaskConfig> TASK_CONFIGS = new ArrayList<TaskConfig>() {{
        add(new TaskConfig(TaskTypeConstant.SEND_GIFT, 610, 2, -1));
        add(new TaskConfig(TaskTypeConstant.SEND_GIFT, 613, 6, -1));
//        add(new TaskConfig(TaskTypeConstant.FIRST_RECHARGE, 0, 20, -1));
//        add(new TaskConfig(TaskTypeConstant.PLAY_YXSK_GAME, 200, 4, 10));
//        add(new TaskConfig(TaskTypeConstant.ROOM_STAY_TIME, 120, 2, 1));
    }};

    public TaskConfig(Integer taskType, Integer checkParams, Integer ticket, Integer limit) {
        super(taskType, checkParams, ticket, limit);
    }

}
