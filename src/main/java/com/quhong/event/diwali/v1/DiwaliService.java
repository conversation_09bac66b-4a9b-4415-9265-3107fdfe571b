package com.quhong.event.diwali.v1;

import com.alibaba.fastjson.JSON;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.EventDrawCostTypeConstant;
import com.quhong.constant.diwali.AwardPoolGroupConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.money.MoneyChangeActionConstant;
import com.quhong.core.date.DateSupport;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.enums.GenderTypeEnum;
import com.quhong.core.exceptions.GoldException;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.ActiveRewardRecordDao;
import com.quhong.dao.ActivityDiwaliActorInfoDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.AppConfigAwardDao;
import com.quhong.dao.datas.ActiveRewardRecordData;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.db.ActivityDiwaliActorInfoData;
import com.quhong.dao.datas.db.AppConfigAwardData;
import com.quhong.data.bo.common.award.pool.config.QueryBO;
import com.quhong.data.bo.common.award.pool.config.RecordLastLimitQueryBO;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.dto.money.CurrencyDeductDTO;
import com.quhong.data.thData.LogEventDrawData;
import com.quhong.data.vo.BaseAwardVO;
import com.quhong.data.vo.model.rank.row.ActorAwardVO;
import com.quhong.data.vo.model.rank.row.ActorInfo;
import com.quhong.enums.CurrencyEnum;
import com.quhong.event.diwali.v1.data.config.LuckyGiftPackageConfig;
import com.quhong.event.diwali.v1.data.config.TaskConfig;
import com.quhong.event.diwali.v1.data.dto.PlayAwardPoolDTO;
import com.quhong.event.diwali.v1.data.vo.InitPageVO;
import com.quhong.event.diwali.v1.data.vo.UserAwardHistoryVO;
import com.quhong.event.diwali.v1.data.vo.UserDailyAwardRecordVO;
import com.quhong.event.diwali.v1.redis.DiwaliDailyPlayYXSKGameTaskLimitRedis;
import com.quhong.event.diwali.v1.redis.DiwaliPromotionRoomLimitRedis;
import com.quhong.event.diwali.v1.redis.XmasEveryAwardPoolPlayLimitRedis;
import com.quhong.event.diwali.v1.redis.XmasGlobalAwardPoolPlayLimitDistinctUserRewardRedis;
import com.quhong.exceptions.WebException;
import com.quhong.moderatoin.ModerationService;
import com.quhong.mq.ActivityMQProduct;
import com.quhong.mq.enums.MQConstant;
import com.quhong.players.ActorMgr;
import com.quhong.report.EventReport;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.activity.model.ModelActivityService;
import com.quhong.service.common.BaseAwardPool;
import com.quhong.service.money.CurrencyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2023/10/23 18:13
 */
@Slf4j
//@Service
@RequiredArgsConstructor
public class DiwaliService {
    /**
     * 金币玩一次需要数额
     */
    private static final int ONCE_PLAY_COIN = 100;
    /**
     * 抽奖消费金币，金币流水显示文案
     */
    private static final String DEDUCT_COIN_TEXT = "Event draw consumption";
    private static final List<String> FAKE_UID = new ArrayList<String>() {{
        add("649c0977c27dde0d0f4d055c");
        add("64a221aa237fed28ca891b22");
        add("64c206c009c66d53f29aba24");
        add("64c21df809c66d53f29abfe6");
        add("655476aa4d51da00c956306f");
        add("655478284d51da00c95630b2");
        add("6555895b4d51da00c9566f57");
    }};
    private static final List<Integer> AWARD_ID = new ArrayList<Integer>() {{
        add(38);
        add(38);
        add(38);
        add(38);
        add(36);
        add(36);
        add(38);
        add(38);
        add(38);
        add(38);
    }};
    private final ActorMgr actorMgr;
    private final AppConfigActivityDao appConfigActivityDao;
    private final GiveOutRewardService giveOutRewardService;
    private final ActivityDiwaliActorInfoDao activityDiwaliActorInfoDao;
    private final DiwaliDailyPlayYXSKGameTaskLimitRedis diwaliDailyPlayYXSKGameTaskLimitRedis;
    private final DiwaliPromotionRoomLimitRedis diwaliPromotionRoomLimitRedis;
    private final CurrencyService currencyService;
    private final BaseAwardPool baseAwardPool;
    private final EventReport eventReport;
    private final ActiveRewardRecordDao activeRewardRecordDao;
    private final AppConfigAwardDao appConfigAwardDao;
    private final ModerationService moderationService;
    private final ActivityMQProduct activityMQProduct;
    private final ModelActivityService modelActivityService;
    private final XmasEveryAwardPoolPlayLimitRedis xmasEveryAwardPoolPlayLimitRedis;
    private final XmasGlobalAwardPoolPlayLimitDistinctUserRewardRedis xmasGlobalAwardPoolPlayLimitDistinctUserRewardRedis;

    private static Set<String> getCountryCodeSet(ActorData actor, AppConfigActivityData activityData) {
        Set<String> countryCodeSet = activityData.getUserCountryGroupStr();
        if (GenderTypeEnum.HOST.getType().equals(actor.getGender())) {
            countryCodeSet = activityData.getHostCountryGroupStr();
        }
        return countryCodeSet;
    }

    private static void limitCountryAndChannelAndEventTime(CommonDTO dto, ActorData currActor, AppConfigActivityData configData, long currTime) {
        // 国家限制
        Set<String> countryCodeSet = getCountryCodeSet(currActor, configData);
        if (!ObjectUtils.isEmpty(countryCodeSet) && !countryCodeSet.contains(currActor.getCountryCode())) {
            throw new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Your country cannot participate in this event"));
        }
        // 渠道限制
        if (StringUtils.hasLength(configData.getChannel()) && !configData.getChannel().equals(currActor.getChannel())) {
            throw new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Your app cannot participate in this event"));
        }
        // 活动时间限制
        if (currTime < configData.getStartTime() || currTime > configData.getEndTime()) {
            log.info("not in the event period");
            throw new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("not in the event period"));
        }
    }

    private static BaseAwardVO fillBaseAwardVO(ActiveRewardRecordData recordData, List<AppConfigAwardData> awardInfoList) {
        return awardInfoList.stream().filter(data -> data.getId().equals(recordData.getRewardId()))
                .findFirst()
                .map(data -> new BaseAwardVO(data, recordData.getNums()))
                .orElse(null);
    }

    public Integer doMergeLucky(CommonDTO dto) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        long currTime = DateHelper.getCurrTime();
        limitCountryAndChannelAndEventTime(dto, currActor, configData, currTime);
        ActivityDiwaliActorInfoData actorInfo = activityDiwaliActorInfoDao.getOneByUidAndEventCode(dto.getUid(), dto.getEventType());
        if (actorInfo == null) {
            throw new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("no enough letter"));
        }
        int count = 0;
        List<RewardInfoData> rewards = LuckyGiftPackageConfig.CONFIG.getRewards();
        while (actorInfo.mergeLucky()) {
            count++;
            // 发放奖励
            giveOutRewardService.giveOutReward(dto.getUid(), rewards);
            // 成本消耗异步计算
            rewards.forEach(data -> activityMQProduct.sendObjectMq(MQConstant.EVENT_COST_COUNT_ROUTE, data));
            if (count >= 10) {
                break;
            }
        }
        if (count < 1) {
            throw new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("no enough letter"));
        }
        ActivityDiwaliActorInfoData updateData = ActivityDiwaliActorInfoData.InitFactory.initMergeLucky(actorInfo);
        activityDiwaliActorInfoDao.updateOneSelective(updateData);
        return count;
    }

    public UserAwardHistoryVO getHistoryAwards(CommonDTO dto) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        // 奖励记录查询
        List<ActiveRewardRecordData> recordDataList = queryRewardRecords(dto, configData, currActor.getUid(), null);
        int count = 0;
        if (!ObjectUtils.isEmpty(recordDataList)) {
            count = recordDataList.size();
        }
        // 查找奖品信息
        List<AppConfigAwardData> awardInfoList = queryAwardInfos(recordDataList);
        List<UserDailyAwardRecordVO> voList = new ArrayList<>();
        recordDataList.forEach(data -> fillHistoryVO(data, voList, awardInfoList, configData.getZoneOffset()));
        return new UserAwardHistoryVO().setCount(count).setDetails(voList);
    }

    public List<ActorAwardVO> getScrolling(CommonDTO dto) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        long currTime = DateHelper.getCurrTime();
        limitCountryAndChannelAndEventTime(dto, currActor, configData, currTime);
        // 奖励记录查询 成功的最近30条
        List<ActiveRewardRecordData> recordDataList = queryRewardRecords(dto, configData, null, 30);
        // 查找奖品信息
        List<AppConfigAwardData> awardInfoList = queryAwardInfos(recordDataList);
        // 返回获奖滚屏信息
        List<ActorAwardVO> voList = fillActorAwardVOs(recordDataList, awardInfoList);
        // 随机插入假数据
        if (ServerConfiguration.isProduct()) {
            Integer index = MathUtils.randomSplitInt(0, voList.size());
            ActorAwardVO vo = randomFakeAwardInfo();
            if (vo != null) {
                voList.set(index, vo);
            }
        }
        return voList;
    }

    public InitPageVO initPage(CommonDTO dto) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());

        InitPageVO vo = new InitPageVO();
        vo.setStartTime(configData.getStartTime());
        vo.setEndTime(configData.getEndTime());

        // 国家限制
        Set<String> countryCodeSet = getCountryCodeSet(currActor, configData);
        if (!ObjectUtils.isEmpty(countryCodeSet) && !countryCodeSet.contains(currActor.getCountryCode())) {
//            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Your country cannot participate in this event"));
            return vo;
        }
        // 渠道限制
        if (StringUtils.hasLength(configData.getChannel()) && !configData.getChannel().equals(currActor.getChannel())) {
//            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Your app cannot participate in this event"));
            return vo;
        }
        ActivityDiwaliActorInfoData actorInfo = activityDiwaliActorInfoDao.getOneByUidAndEventCode(currActor.getUid(), configData.getActivityCode());
        if (actorInfo == null) {
            return vo;
        }
        actorInfo.generateLuckyCountMap();
        vo.setIsRecharge(actorInfo.getIsRecharge());
        long dailyPlayGameAwardCount = diwaliDailyPlayYXSKGameTaskLimitRedis.getTimes(currActor.getUid());
        vo.setDailyPlayGameAwardCount(dailyPlayGameAwardCount);
        int dailyDisseminateCount = diwaliPromotionRoomLimitRedis.computeReceiveCount(currActor.getUid());
        vo.setDailyDisseminateCount((long) dailyDisseminateCount);
        vo.setBalance(actorInfo.getDataBalance());
        vo.setLuckyCountMap(actorInfo.getLuckyCountMap());
        return vo;
    }

    public List<BaseAwardVO> playAwardPool(PlayAwardPoolDTO dto) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        long currTime = DateHelper.getCurrTime();
        limitCountryAndChannelAndEventTime(dto, currActor, configData, currTime);

        //扣除货币
        checkAndDeDuctCurrency(dto, currActor, configData);
        //从奖池抽取奖励
        List<RewardInfoData> rewards = IntStream.range(0, dto.getPlayTimes())
                .mapToObj(index -> fillQueryBO(dto))
                .map(baseAwardPool::getOneAwardFromPool)
                .collect(Collectors.toList());
        //发放奖励
        giveOutRewardService.giveOutReward(dto.getUid(), rewards);
        List<BaseAwardVO> voList = new ArrayList<>();
        rewards.forEach(data -> {
            log.info("report Think data,id={}，receiveCount={}", data.getActivityId(), data.getReceiveCount());
            // 上报数数
            reportEventDrawLogFromThinkData(data, dto, configData);
            // 填充响应结构
            fillPlayPoolVO(data, voList);
            // 发布:成本消耗异步计算
            activityMQProduct.sendObjectMq(MQConstant.EVENT_COST_COUNT_ROUTE, data);
        });
        // 计算用户活动期间累计抽奖次数
        BaseTaskFactory.Util.slowTask(dto, this::checkAndReceive1001GiftPackage);
        return voList;
    }

    private void checkAndReceive1001GiftPackage(PlayAwardPoolDTO dto) {
        // 判定是否可领取奖品
//        List<RewardInfoData> rewards = xmasEveryAwardPoolPlayLimitRedis.increaseAndGetRewards(dto.getUid(), dto.getEventCode(), dto.getPlayTimes());
//        if (ObjectUtils.isEmpty(rewards)) {
//            return;
//        }
        List<RewardInfoData> globalRewards = xmasGlobalAwardPoolPlayLimitDistinctUserRewardRedis.checkLimitAndGetRewards(dto.getUid(), dto.getEventType(), dto.getPlayTimes());
//        rewards.addAll(globalRewards);
        if (ObjectUtils.isEmpty(globalRewards)) {
            return;
        }
        giveOutRewardService.giveOutReward(dto.getUid(), globalRewards);
        // 1001大礼包 获奖通知 通知，图片，跳转链接
        String notice = "Merry Christmas and Happy new year, " +
                "the lottery has reached 1001 times, " +
                "Get a New Year gift Gift[Christmas couple] *1+[Christmas medal] permanent," +
                "Pls check in bag, keep on going";
        String img = "https://statics.kissu.mobi/Event/super/1.jpg";
        String url = TaskConfig.TEST_EVENT_URL;
        if (ServerConfiguration.isProduct()) {
            url = TaskConfig.EVENT_URL;
        }
        modelActivityService.sendOfficialNotice(dto.getUid(), notice, img, url, ChannelEnum.CDE.getName(), DateHelper.getCurrentTime() - MathUtils.randomSplitInt(1, 50));
    }

    private void fillPlayPoolVO(RewardInfoData rewardInfo, List<BaseAwardVO> voList) {
        BaseAwardVO vo = voList.stream().filter(data -> rewardInfo.getName().equals(data.getName())).findFirst().orElse(null);
        if (vo == null) {
            vo = new BaseAwardVO(rewardInfo);
            voList.add(vo);
        } else {
            vo.increaseCount(rewardInfo.getNums());
        }
    }

    private void reportEventDrawLogFromThinkData(RewardInfoData data, PlayAwardPoolDTO dto, AppConfigActivityData configData) {
        LogEventDrawData logData = new LogEventDrawData();
        logData.setUid(dto.getUid());
        logData.setEventCode(dto.getEventType());
        logData.setActivityName(configData.getName());
        logData.setEventDrawId(AwardPoolGroupConstant.AWARD_POOL);
        logData.setEventDrawCostType(dto.getPlayType());
        if (dto.getPlayType() == EventDrawCostTypeConstant.COST_COIN) {
            logData.setEventDrawCostNumber(ONCE_PLAY_COIN * dto.getPlayTimes());
        } else {
            logData.setEventDrawCostNumber(dto.getPlayTimes());
        }
        logData.setEventDrawItemType(data.getType());
        logData.setEventDrawItemId(data.getDataId());
        logData.setEventDrawItemName(data.getName());
        logData.setEventDrawItemNumber(data.getNums());
        logData.setCtime(DateHelper.getCurrTime());
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private QueryBO fillQueryBO(PlayAwardPoolDTO dto) {
        return new QueryBO(dto.getEventType(), AwardPoolGroupConstant.AWARD_POOL, dto.getUid());
    }

    private void checkAndDeDuctCurrency(PlayAwardPoolDTO dto, ActorData currActor, AppConfigActivityData configData) {
        switch (dto.getPlayType()) {
            case EventDrawCostTypeConstant.COST_COIN:
                checkAndDeductCoin(dto, currActor, configData);
                break;
            case EventDrawCostTypeConstant.COST_EVENT_CURRENCY:
                checkAndDeductEventCurrency(dto);
                break;
            default:
                throw new WebException(dto, new HttpCode(HttpCode.OTHER_NOT_EXISTS).setMsg("not found play type"));
        }
    }

    private void checkAndDeductEventCurrency(PlayAwardPoolDTO dto) {
        ActivityDiwaliActorInfoData actorInfo = activityDiwaliActorInfoDao.getOneByUidAndEventCode(dto.getUid(), dto.getEventType());
        Optional.ofNullable(actorInfo)
                .filter(data -> data.getDataBalance() >= dto.getPlayTimes())
                .map(data -> deductEventCurrency(data, dto))
                .filter(Boolean.TRUE::equals)
                .orElseThrow(() -> new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("not enough times")));
    }

    private void checkAndDeductCoin(PlayAwardPoolDTO dto, ActorData currActor, AppConfigActivityData configData) {
        BigDecimal balance = currencyService.getRealCurrency1Balance(currActor.getUid());
        if (balance.longValue() >= (long) ONCE_PLAY_COIN * dto.getPlayTimes()) {
            deductCoin(dto, configData);
        } else {
            throw new WebException(dto, new HttpCode(HttpCode.NOT_ENOUGH_TO_SEND_GIFT).setMsg("your coin not enough"));
        }
    }

    private boolean deductEventCurrency(ActivityDiwaliActorInfoData data, PlayAwardPoolDTO dto) {
        boolean enough = data.divDataBalance(dto.getPlayTimes());
        if (enough) {
            ActivityDiwaliActorInfoData updateData = ActivityDiwaliActorInfoData.InitFactory.initDivBalance(data);
            activityDiwaliActorInfoDao.updateOneSelective(updateData);
        }
        return enough;
    }

    private void deductCoin(PlayAwardPoolDTO dto, AppConfigActivityData configData) {
        CurrencyDeductDTO deductDTO = new CurrencyDeductDTO();
        deductDTO.setUid(dto.getUid());
        deductDTO.setActType(configData.getActType());
        deductDTO.setActId(0);
        deductDTO.setActDesc(DEDUCT_COIN_TEXT);
        deductDTO.setChangeNum((long) ONCE_PLAY_COIN * dto.getPlayTimes());
        deductDTO.setSegmentCode(configData.getActivityCode());
        deductDTO.setAction(MoneyChangeActionConstant.ACTION_DEDUCT);
        deductDTO.setCurrencyCode(CurrencyEnum.CURRENCY1.getCurrencyCode());
        deductDTO.setOperator("activity(" + configData.getActivityCode() + ")");
        try {
            currencyService.deduct(deductDTO);
        } catch (GoldException e) {
            throw new WebException(dto, new HttpCode(HttpCode.NOT_ENOUGH_TO_SEND_GIFT).setMsg("your coin not enough"));
        }
    }

    private List<ActorAwardVO> fillActorAwardVOs(List<ActiveRewardRecordData> recordDataList, List<AppConfigAwardData> awardInfoList) {
        return Optional.ofNullable(recordDataList)
                .map(records -> records.stream()
                        .sorted(Comparator.comparingLong(ActiveRewardRecordData::getMtime).reversed())
                        .map(record -> fillActorAwardVO(record, awardInfoList))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());
    }

    private ActorAwardVO fillActorAwardVO(ActiveRewardRecordData record, List<AppConfigAwardData> awardInfoList) {
        ActorData rewardActor = actorMgr.getActorData(record.getUid());
        AppConfigAwardData awardInfo = awardInfoList.stream()
                .filter(data -> data.getId().equals(record.getRewardId()))
                .findFirst().orElse(null);
        if (rewardActor == null || awardInfo == null) {
            return null;
        }
        ActorInfo actorInfo = new ActorInfo();
        actorInfo.setUid(rewardActor.getUid());
        actorInfo.setRid(rewardActor.getRid());
        actorInfo.setHead(moderationService.dealRankHeadModeration(rewardActor));
        actorInfo.setName(rewardActor.getName());

        BaseAwardVO rewardInfo = new BaseAwardVO(awardInfo, record.getNums());
        return new ActorAwardVO(actorInfo, rewardInfo);
    }

    private List<ActiveRewardRecordData> queryRewardRecords(CommonDTO dto, AppConfigActivityData configData, String uid, Integer limit) {
        return activeRewardRecordDao.queryLastLimitBy(RecordLastLimitQueryBO.builder()
                .eventCode(dto.getEventType())
                .status(2)
                .uid(uid)
                .limit(limit)
                .startTime(configData.getStartTime())
                .endTime(configData.getEndTime())
                .build());
    }

    private List<AppConfigAwardData> queryAwardInfos(List<ActiveRewardRecordData> recordDataList) {
        return Optional.ofNullable(recordDataList)
                .map(records -> records.stream()
                        .map(ActiveRewardRecordData::getRewardId)
                        .collect(Collectors.toSet()))
                .map(idSet -> appConfigAwardDao.getListByIdSetAndValid(idSet, 1))
                .orElse(new ArrayList<>());
    }

    private void fillHistoryVO(ActiveRewardRecordData recordData, List<UserDailyAwardRecordVO> voList, List<AppConfigAwardData> awardInfoList, String offsetId) {
        BaseAwardVO awardVO = fillBaseAwardVO(recordData, awardInfoList);
        if (awardVO == null) {
            return;
        }
        UserDailyAwardRecordVO vo = getDailyAwardRecordVO(recordData, voList, offsetId);

        BaseAwardVO listAwardVO = vo.getRewards()
                .parallelStream()
                .filter(data -> data.getName().equals(awardVO.getName()))
                .findFirst().orElse(null);
        if (listAwardVO == null) {
            vo.getRewards().add(awardVO);
        } else {
            listAwardVO.increaseCount(awardVO.getCount());
        }
    }

    private UserDailyAwardRecordVO getDailyAwardRecordVO(ActiveRewardRecordData recordData, List<UserDailyAwardRecordVO> voList, String offsetId) {
        String dateStr = DateSupport.formatDate(offsetId, recordData.getMtime(), DateSupport.yyyy__MM__dd);
        UserDailyAwardRecordVO vo = voList.stream()
                .filter(data -> dateStr.equals(data.getDate()))
                .findFirst().orElse(null);
        if (vo == null) {
            vo = new UserDailyAwardRecordVO(dateStr);
            voList.add(vo);
        }
        return vo;
    }

    private ActorAwardVO randomFakeAwardInfo() {
        // 随机获取测试号
        int index = MathUtils.randomSplitInt(0, FAKE_UID.size());
        Collections.shuffle(FAKE_UID);
        ActorData actorData = actorMgr.getActorData(FAKE_UID.get(index));
        if (actorData == null) {
            return null;
        }
        // 用户信息
        ActorInfo actorInfo = new ActorInfo(actorData);
        // 随机获取奖励信息
        index = MathUtils.randomSplitInt(0, AWARD_ID.size());
        Collections.shuffle(AWARD_ID);
        Integer awardId = AWARD_ID.get(index);
        AppConfigAwardData award = appConfigAwardDao.getOneById(awardId);
        // 填充奖励信息
        BaseAwardVO baseAwardVO = new BaseAwardVO(award, 1);
        return new ActorAwardVO(actorInfo, baseAwardVO);
    }


    public void sendNoticeToDoDraw(int eventCode) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventCode);
        List<ActivityDiwaliActorInfoData> dataList = activityDiwaliActorInfoDao.getListByEventCodeAndGreaterThanBalance(eventCode, 0);
        if (ObjectUtils.isEmpty(dataList)) {
            return;
        }
        dataList.forEach(data -> sendNotice(data, configData));

    }

    private void sendNotice(ActivityDiwaliActorInfoData data, AppConfigActivityData configData) {
        ActorData currActor = actorMgr.getActorData(data.getUid());
        if (currActor == null) {
            return;
        }
        if (!StringUtils.isEmpty(configData.getChannel()) && !configData.getChannel().equals(currActor.getChannel())) {
            return;
        }

        // 活动结束前1小时还有活动货币没消费 提示文案 图片 跳转地址
        String notice = "There is only 1 hour left for event. After the event, all the draws and cards date will be cleared, so be sure to collect them in time.";
        String img = "https://statics.kissu.mobi/Event/super/1.jpg";
        String url = TaskConfig.TEST_EVENT_URL;
        if (ServerConfiguration.isProduct()) {
            url = TaskConfig.EVENT_URL;
        }
        modelActivityService.sendOfficialNotice(data.getUid(), notice, img, url, ChannelEnum.CDE.getName());
    }
}
