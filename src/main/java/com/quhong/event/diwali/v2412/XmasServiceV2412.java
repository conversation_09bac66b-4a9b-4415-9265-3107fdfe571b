package com.quhong.event.diwali.v2412;

import com.alibaba.fastjson.JSON;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.LangIdConstant;
import com.quhong.constant.OfficialUserGroup;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.WarnName;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.enums.GenderTypeEnum;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.ActivityDiwaliActorInfoDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.dao.datas.db.ActivityDiwaliActorInfoData;
import com.quhong.data.bo.anniversary.CakeDivideConfig;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.dto.RankingDTO;
import com.quhong.data.dto.RecordDTO;
import com.quhong.data.thData.ActivitySpecialItemsEvent;
import com.quhong.data.vo.BaseAwardVO;
import com.quhong.data.vo.BaseHistoryVO;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.event.anniversary.RankVO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.ActorInfo;
import com.quhong.enums.EventCode;
import com.quhong.enums.RewardItemType;
import com.quhong.enums.TopTypeEnum;
import com.quhong.event.diwali.v1.redis.DiwaliPromotionRoomLimitRedis;
import com.quhong.event.diwali.v2412.data.bo.GlobalUserInfo;
import com.quhong.event.diwali.v2412.data.dto.ExchangeOrGiftDTO;
import com.quhong.event.diwali.v2412.data.vo.*;
import com.quhong.exceptions.WebException;
import com.quhong.moderatoin.ModerationService;
import com.quhong.players.ActorMgr;
import com.quhong.redis.BaseHashSaveRedis;
import com.quhong.redis.BaseListRedis;
import com.quhong.redis.BaseZSetRedis;
import com.quhong.redis.base.ActivityCommonRedis;
import com.quhong.report.EventReport;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.activity.model.impl.GiftPriceRankActivityService;
import com.quhong.service.common.RewardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.quhong.data.vo.BaseHistoryVO.checkNextPage;

/**
 * <AUTHOR>
 * @since 2023/10/23 18:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class XmasServiceV2412 {
    private static final boolean PROD = ServerConfiguration.isProduct();

    private static final int EVENT_CODE = EventCode.EVENT_XMAS_2024;

    public static final String EVENT_URL = PROD ? "https://videochat.kissu.site/christmas_2024" : "https://testvideochat.kissu.site/christmas_2024";

    private static final String NOTICE_IMG = "https://statics.kissu.mobi/event/2025/Christmas/gftz.jpg";

    /**
     * 蛋糕奖池抽成比例
     */
    public static final String CAKE_POOL_RATE = "0.01";
    /**
     * 日榜榜单展示最多人数
     */
    private static final int DAILY_RANK_SHOW = 100;
    /**
     * 每日蛋糕奖池初始值
     */
    private static final String CAKE_POOL_INIT = "250";
    public static final List<AwardInfo> CARD_INFOS = new ArrayList<AwardInfo>() {{
        add(new AwardInfo().setAwardId(1).setRate(0).setRate1(1).setRate2(0).setRate3(0).setAwardName("Rich").setAwardIcon("https://statics.kissu.mobi/event/2025/Christmas/rich.png"));
        add(new AwardInfo().setAwardId(2).setRate(1).setRate1(2).setRate2(0).setRate3(0).setAwardName("Love").setAwardIcon("https://statics.kissu.mobi/event/2025/Christmas/love.png"));
        add(new AwardInfo().setAwardId(3).setRate(5).setRate1(28).setRate2(0).setRate3(10).setAwardName("Beauty").setAwardIcon("https://statics.kissu.mobi/event/2025/Christmas/beauty.png"));
        add(new AwardInfo().setAwardId(4).setRate(94).setRate1(69).setRate2(100).setRate3(90).setAwardName("Happy").setAwardIcon("https://statics.kissu.mobi/event/2025/Christmas/happy.png"));
    }};

    /**
     * 蛋糕瓜分配置
     */
    public static final List<CakeDivideConfig> CAKE_DIVIDE_CONFIGS = new ArrayList<CakeDivideConfig>() {{
        add(new CakeDivideConfig().setMaxRank(1).setRate("0.15"));
        add(new CakeDivideConfig().setMaxRank(3).setRate("0.1"));
        add(new CakeDivideConfig().setMaxRank(5).setRate("0.05"));
        add(new CakeDivideConfig().setMaxRank(10).setRate("0.02"));
        add(new CakeDivideConfig().setMaxRank(50).setRate("0.01"));
        add(new CakeDivideConfig().setMaxRank(100).setRate("0.001"));
    }};
    /**
     * 礼包配置
     */
    private static final List<GiftPackVO> GIFT_PACKS = new ArrayList<GiftPackVO>() {{
        List<BaseAwardVO> richRewards = new ArrayList<BaseAwardVO>() {{
            BaseAwardVO award1 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/Event/shot/gold-more[1].png")
                    .name("coins").count(250).type(RewardItemType.GOLD).unitCost(1)
                    .dataId(0).build();
            BaseAwardVO award2 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/event/2025/Christmas/10322340515656.png")
                    .name("Get Rich overnight*1").count(1).type(RewardItemType.GIFT).unitCost(500)
                    .dataId(PROD ? 735 : 608).build();
            BaseAwardVO award3 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/room_item/icon/268/elder.png")
                    .name("Christmas car*1Day").count(1).type(RewardItemType.ENTER_EFFECT).unitCost(0)
                    .dataId(PROD ? 107 : 76).build();
            BaseAwardVO award4 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/room_item/icon/1734340703875/1.png")
                    .name("Wealth Up*1Day").count(1).type(RewardItemType.SEAT_FRAME).unitCost(0)
                    .dataId(PROD ? 305 : 103).build();
            BaseAwardVO award5 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/icon/meadl/600.png")
                    .name("2025 Richest*1Day").count(1).type(RewardItemType.MEDAL).unitCost(0)
                    .dataId(PROD ? 900057 : 90001).build();
            BaseAwardVO award6 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/icon/aristocrat/King.png")
                    .name("King*1Day").count(1).type(RewardItemType.LORD_DAYS).unitCost(0)
                    .dataId(6).build();
            add(award1);
            add(award2);
            add(award3);
            add(award4);
            add(award5);
            add(award6);
        }};
        add(new GiftPackVO().setPackId(1).setNeedCardType(1).setPrice(1).setPackName("Rich Gifts").setPackIcon("https://statics.kissu.mobi/event/2025/Christmas/rich_gift.png").setAwards(richRewards));

        List<BaseAwardVO> loveRewards = new ArrayList<BaseAwardVO>() {{
            BaseAwardVO award1 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/Event/shot/gold-more[1].png")
                    .name("1000coins").count(25).type(RewardItemType.GOLD).unitCost(1)
                    .dataId(0).build();
            BaseAwardVO award2 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/Event/super/couple.png")
                    .name("Christmas couple*1").count(1).type(RewardItemType.GIFT).unitCost(200)
                    .dataId(607).build();
            BaseAwardVO award3 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/event/2025/Christmas/Christmas_Hat.png")
                    .name("Lucky Christmas Hat*1").count(1).type(RewardItemType.GIFT).unitCost(3)
                    .dataId(PROD ? 736 : 609).build();
            BaseAwardVO award4 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/Event/super/hello.png")
                    .name("winter bubbles*1Day").count(1).type(RewardItemType.BUBBLE_FRAME).unitCost(0)
                    .dataId(PROD ? 108 : 77).build();
            BaseAwardVO award5 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/room_item/icon/1734350875456/love.png")
                    .name("Love Lucky*1Day").count(1).type(RewardItemType.SEAT_FRAME).unitCost(0)
                    .dataId(PROD ? 307 : 103).build();
            add(award1);
            add(award2);
            add(award3);
            add(award4);
            add(award5);
        }};
        add(new GiftPackVO().setPackId(2).setNeedCardType(2).setPrice(1).setPackName("Love Gifts").setPackIcon("https://statics.kissu.mobi/event/2025/Christmas/Love_gift.png").setAwards(loveRewards));

        List<BaseAwardVO> beautyRewards = new ArrayList<BaseAwardVO>() {{
            BaseAwardVO award1 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/event/2025/Christmas/Elegant&charming.png")
                    .name("Elegant&charming*1").count(1).type(RewardItemType.GIFT).unitCost(100)
                    .dataId(PROD ? 738 : 607).build();
            BaseAwardVO award2 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/Event/super/crystal_apple.png")
                    .name("Crystal apple*1").count(1).type(RewardItemType.GIFT).unitCost(4)
                    .dataId(605).build();
            BaseAwardVO award3 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/icon/meadl/yqmr53xm_pixian_ai.png")
                    .name("Merry Christmas*1Day").count(1).type(RewardItemType.MEDAL).unitCost(0)
                    .dataId(90003).build();
            BaseAwardVO award4 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/room_item/icon/1734350921532/Beauty.png")
                    .name("2025 Beauty Up*1Day").count(1).type(RewardItemType.SEAT_FRAME).unitCost(0)
                    .dataId(PROD ? 308 : 103).build();
            add(award1);
            add(award2);
            add(award3);
            add(award4);
        }};
        add(new GiftPackVO().setPackId(3).setNeedCardType(3).setPrice(2).setPackName("Beauty Gifts").setPackIcon("https://statics.kissu.mobi/event/2025/Christmas/Beauty_gift.png").setAwards(beautyRewards));

        List<BaseAwardVO> happyRewards = new ArrayList<BaseAwardVO>() {{
            BaseAwardVO award1 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/event/2025/Christmas/Lucky_Christmas_Stockings.png")
                    .name("Lucky Christmas Stockings*1").count(1).type(RewardItemType.GIFT).unitCost(3)
                    .dataId(PROD ? 737 : 606).build();
            BaseAwardVO award2 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/room_item/origin/1729739733202/Burn.webp")
                    .name("Burn*1Day").count(1).type(RewardItemType.SEAT_FRAME).unitCost(0)
                    .dataId(PROD ? 275 : 103).build();
            BaseAwardVO award3 = BaseAwardVO.builder()
                    .img("https://statics.kissu.mobi/icon/checkin/VIP.png")
                    .name("VIP*1Day").count(1).type(RewardItemType.VIP_DAYS).unitCost(0)
                    .dataId(0).build();
            add(award1);
            add(award2);
            add(award3);
        }};
        add(new GiftPackVO().setPackId(4).setNeedCardType(4).setPrice(2).setPackName("Happy Gifts").setPackIcon("https://statics.kissu.mobi/event/2025/Christmas/happy_gift.png").setAwards(happyRewards));
    }};
    /**
     * 每日点亮圣诞树奖励
     */
    List<RewardInfoData> DAILY_LIT_UP_TREE_REWARDS = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.PROFILE_CARD, PROD ? 310 : 125, 0));
    }};

    public static final List<RewardTaskConfig> SEND_RANK_REWARDS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 309 : 84, 0));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90066 : 900017, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 311 : 121, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 309 : 84, 0));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90067 : 90003, 0));
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.ENTER_EFFECT, PROD ? 311 : 121, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 309 : 84, 0));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90068 : 90001, 0));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 311 : 121, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 309 : 84, 0));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 311 : 121, 0));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));

    }};

    public static final List<RewardTaskConfig> RECEIVE_RANK_REWARDS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 309 : 84, 0));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90066 : 900017, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 311 : 121, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 309 : 84, 0));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90067 : 90003, 0));
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.ENTER_EFFECT, PROD ? 311 : 121, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 309 : 84, 0));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90068 : 90001, 0));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 311 : 121, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 0, RewardItemType.DESIGNATION, PROD ? 309 : 84, 0));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 311 : 121, 0));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));

    }};

    private final ActorMgr actorMgr;
    private final AppConfigActivityDao appConfigActivityDao;
    private final GiveOutRewardService giveOutRewardService;
    private final ActivityDiwaliActorInfoDao activityDiwaliActorInfoDao;
    private final DiwaliPromotionRoomLimitRedis diwaliPromotionRoomLimitRedis;
    private final EventReport eventReport;
    private final BaseHashSaveRedis baseHashSaveRedis;
    private final BaseListRedis baseListRedis;
    private final ModerationService moderationService;
    private final BaseZSetRedis baseZSetRedis;
    private final ActivityCommonRedis activityCommonRedis;
    private final CdnUtils cdnUtils;
    private final GiftPriceRankActivityService giftPriceRankActivityService;
    private final MonitorSender monitorSender;
    private final OfficialNoticeService officialNoticeService;
    private final RewardService rewardService;

    public String hashGlobalUserInfoKey(Integer eventCode) {
        return "hash:diwali_global_user_info:" + eventCode;
    }

    /**
     * 每日点亮圣诞树礼包状态存储
     */
    public String zsetDailyTreePackKey(String date, Integer eventCode) {
        return "zset:event:daily_tree_pack:" + eventCode + ":" + date;
    }

    /**
     * 获取邀请用户限制任务的key
     */
    public String hashInviteUserLimitKey(int eventCode) {
        return "hash:event:inviter_user_limit:" + eventCode + ":";
    }

    /**
     * 初始化卡片获取记录key
     */
    public String initCardRecordKey(Integer eventCode, String uid) {
        return "list:event:card_record:" + eventCode + ":" + uid;
    }

    /**
     * 收礼日榜key
     */
    public String dailyReceiveRankKey(String date, String activityId) {
        return "zset:activity:receive_rank:" + activityId + ":" + date;
    }

    /**
     * 礼包兑换个人记录
     */
    private String listExchangeOrGiftRecordKey(Integer eventCode, String uid) {
        return "list:event:exchange_or_gift_record:" + eventCode + ":" + uid;
    }

    /**
     * 礼包兑换全局记录
     */
    private String listGlobalsExchangeOrGiftRecordKey(Integer eventCode) {
        return "list:event:globals_exchange_or_gift_record:" + eventCode;
    }


    private String listDailyLightTreeKey(Integer eventCode, String date) {
        return "list:event:daily_light_tree:" + eventCode + ":" + date;
    }

    /**
     * 礼包兑换全局记录
     */

    private static Set<String> getCountryCodeSet(ActorData actor, AppConfigActivityData activityData) {
        Set<String> countryCodeSet = activityData.getUserCountryGroupStr();
        if (GenderTypeEnum.HOST.getType().equals(actor.getGender())) {
            countryCodeSet = activityData.getHostCountryGroupStr();
        }
        return countryCodeSet;
    }

    private static void limitCountryAndChannelAndEventTime(ActorData currActor, AppConfigActivityData configData, long currTime) {
        // 国家限制
        Set<String> countryCodeSet = getCountryCodeSet(currActor, configData);
        if (!ObjectUtils.isEmpty(countryCodeSet) && !countryCodeSet.contains(currActor.getCountryCode())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "Your country cannot participate in this event"));
        }
        // 渠道限制
        if (!ChannelEnum.isTikkoChannel(currActor.getChannel())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "Your app cannot participate in this event"));
        }
        // 活动时间限制
        if (currTime < configData.getStartTime() || currTime > configData.getEndTime()) {
            log.info("not in the event period");
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "not in the event period"));
        }
    }

    /**
     * 兑换或赠送奖品
     */
    public Integer exchangeOrGift(ExchangeOrGiftDTO dto) {
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        ActorData receiverActor = actorMgr.getCurrActorData(dto.getReceiverUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        limitCountryAndChannelAndEventTime(currActor, configData, currTime);
        DateHelper dateHelper = DateHelper.genDateHelper(currActor.getChannel());
        GiftPackVO awardPackInfo = GIFT_PACKS.stream().filter(awardPack -> awardPack.getPackId().equals(dto.getPackId()))
                .findFirst()
                .orElseThrow(() -> new WebException(dto, HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "not found awardPack,id=" + dto.getPackId())));
        if (!dto.getUnitPrice().equals(awardPackInfo.getPrice())) {
            dto.setUnitPrice(awardPackInfo.getPrice());
        }
        String userInfoKey = hashGlobalUserInfoKey(EVENT_CODE);
        GlobalUserInfo userInfo = findGlobalUserInfo(dto.getUid(), userInfoKey);
        //扣除活动货币
        checkAndDeductEventCurrency(dto, userInfo, awardPackInfo, userInfoKey);
        List<RewardInfoData> rewards = awardPackInfo.getAwards().stream().map(awardInfo -> genRewardInfoData(dto, configData, awardInfo))
                .collect(Collectors.toList());
        String desc = dto.getType() == 1 ? "1" : "2";
        giveOutRewardService.giveEventReward(dto.getReceiverUid(), rewards, dto.getEventType(), desc);
        if (awardPackInfo.getPackId() == 1) {
            String tip = dto.getType() == 1 ? "Obtained through redemption in New Year event" : "Obtained through gifts from friends New Year event";
            rewardService.sendUniversalActivityPopMsg(receiverActor, rewards, EVENT_URL, configData.getName(), "", tip);
        }
        // 记录保存
        saveRecord(dto, awardPackInfo, currTime, receiverActor, currActor);
//        String fromType = dto.getType() == 1 ? "4" : "5";
//        reportCostOilLampToThinkData(dto, currActor, configData, fromType, reward, receiverActor);

        // 处理每日收礼包 点亮圣诞树
        dealDailyTreeStatus(receiverActor, currActor, dateHelper, userInfoKey, configData);
        // 数数上报 礼包兑换
        dealCardUseReportToThinkData(dto, currActor, configData, awardPackInfo, receiverActor);
        return userInfo.getCardMap().getOrDefault(awardPackInfo.getNeedCardType().toString(), 0);
    }

    private void dealCardUseReportToThinkData(ExchangeOrGiftDTO dto, ActorData currActor, AppConfigActivityData configData, GiftPackVO awardPackInfo, ActorData receiverActor) {
        String fromType = dto.getType() == 1 ? "5" : "6";
        ActivitySpecialItemsEvent logData = new ActivitySpecialItemsEvent();
        logData.setUid(currActor.getUid());
        logData.setCtime(DateHelper.getCurrTime());
        logData.setChannel(currActor.getChannel());
        logData.setEventCode(configData.getActivityCode());
        logData.setActivityName(configData.getName());
        logData.setItemId(awardPackInfo.getNeedCardType().toString());
        logData.setChangeAction(2);
        logData.setFromType(fromType);
        logData.setFromTypeDesc(awardPackInfo.getPackName());
        logData.setExchangeAmount(dto.getAmount());
        logData.setToUid(receiverActor.getUid());
        logData.setToRid(receiverActor.getRid());
        logData.setItemCount(dto.getAmount() * dto.getUnitPrice());
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private void dealDailyTreeStatus(ActorData receiverActor, ActorData currActor, DateHelper dateHelper, String userInfoKey, AppConfigActivityData configData) {
        if (receiverActor.getUid().equals(currActor.getUid())) {
            return;
        }
        String today = dateHelper.getToday();
        String treePackStatusKey = zsetDailyTreePackKey(today, EVENT_CODE);
        CountVO one = baseZSetRedis.getOne(treePackStatusKey, receiverActor.getUid());
        if (one.getCount() != 0) {
            return;
        }
        //礼包设置为可领取状态
        baseZSetRedis.addToZSet(treePackStatusKey, receiverActor.getUid(), 1, Duration.ofDays(30));
        GlobalUserInfo receiveUserInfo = findGlobalUserInfo(receiverActor.getUid(), userInfoKey);
        if (receiveUserInfo.getLightDateSet().contains(today)) {
            return;
        }
        //点亮当日圣诞树
        receiveUserInfo.getLightDateSet().add(today);
        baseHashSaveRedis.saveToRedis(userInfoKey, receiveUserInfo.getUid(), receiveUserInfo, Duration.ofDays(30));
        String dailyLightTreeKey = listDailyLightTreeKey(configData.getActivityCode(), dateHelper.getToday());
        baseListRedis.leftPush(dailyLightTreeKey, new UserInfoVO(receiverActor));
    }

    private void reportCostOilLampToThinkData(ExchangeOrGiftDTO dto, ActorData currActor, AppConfigActivityData configData, String fromType, RewardInfoData reward, ActorData receiverActor) {
        ActivitySpecialItemsEvent logData = new ActivitySpecialItemsEvent();
        logData.setUid(currActor.getUid());
        logData.setCtime(DateHelper.getCurrTime());
        logData.setChannel(currActor.getChannel());
        logData.setEventCode(configData.getActivityCode());
        logData.setActivityName(configData.getName());
        logData.setItemId("1");
        logData.setChangeAction(2);
        logData.setFromType(fromType);
        logData.setFromTypeDesc(reward.getName());
        logData.setExchangeAmount(dto.getAmount());
        logData.setToUid(receiverActor.getUid());
        logData.setToRid(receiverActor.getRid());
        logData.setItemCount(dto.getAmount() * dto.getUnitPrice());
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private void saveRecord(ExchangeOrGiftDTO dto, GiftPackVO awardInfo, long currTime, ActorData receiverActor, ActorData currActor) {
        String selfKey = listExchangeOrGiftRecordKey(dto.getEventType(), dto.getUid());
        String globalKey = listGlobalsExchangeOrGiftRecordKey(dto.getEventType());
        RecordVO raw = fillRecordVO(dto, awardInfo, currTime, receiverActor, currActor);
        baseListRedis.leftPushAll(selfKey, Collections.singletonList(raw));
        baseListRedis.leftPushAll(globalKey, Collections.singletonList(raw));
    }

    public GlobalUserInfo findGlobalUserInfo(String uid, String userInfoKey) {
        GlobalUserInfo globalUserInfo = baseHashSaveRedis.getDataByRedis(userInfoKey, uid, GlobalUserInfo.class);
        if (globalUserInfo != null) {
            return globalUserInfo;
        }
        return new GlobalUserInfo().setUid(uid);
    }

    private RecordVO fillRecordVO(ExchangeOrGiftDTO dto, GiftPackVO awardInfo, long currTime, ActorData receiverActor, ActorData currActor) {
        return new RecordVO()
                .setIcon(awardInfo.getPackIcon())
                .setRewardName(awardInfo.getPackName())
                .setCount(dto.getAmount().toString())
                .setType(dto.getType() == 1 ? "me" : "friend")
                .setCtime(currTime)
                .setRid(receiverActor.getRid().toString())
                .setReceiverHead(moderationService.dealRankHeadModeration(receiverActor))
                .setSenderHead(moderationService.dealRankHeadModeration(currActor))
                .setSenderName(currActor.getName());
    }

    private static RewardInfoData genRewardInfoData(ExchangeOrGiftDTO dto, AppConfigActivityData configData, BaseAwardVO awardInfo) {
        return new RewardInfoData()
                .setActivityType(configData.getActivityCode())
                .setType(awardInfo.getType())
                .setNums(dto.getAmount() * awardInfo.getCount())
                .setUnitCost(awardInfo.getUnitCost())
                .setDataId(awardInfo.getDataId())
                .setName(awardInfo.getName())
                .setIcon(awardInfo.getImg());
    }

    /**
     * 兑换或赠送记录
     */
    public BaseHistoryVO<RecordVO> exchangeOrGiftRecord(RecordDTO dto) {
        if (dto.getPage() == null || dto.getPage() <= 0) {
            dto.setPage(1);
        }
        String selfKey = listExchangeOrGiftRecordKey(dto.getEventType(), dto.getUid());
        List<RecordVO> records = baseListRedis.getListByPage(selfKey, RecordVO.class, dto.getPage(), BaseHistoryVO.RECORD_PAGE_SIZE);
        if (ObjectUtils.isEmpty(records)) {
            return new BaseHistoryVO<>();
        }

        return new BaseHistoryVO<>(records, checkNextPage(dto.getPage(), records.size()));
    }

    public boolean receiveTreeLightAwards(String date, CommonDTO dto) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        long currTime = DateHelper.getCurrTime();
        limitCountryAndChannelAndEventTime(currActor, configData, currTime);
        String treeStatusKey = zsetDailyTreePackKey(date, dto.getEventType());
        CountVO one = baseZSetRedis.getOne(treeStatusKey, currActor.getUid());
        if (one.getCount() > 1 || one.getCount() < -1) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "illegal status"));
        } else if (one.getCount() == 0) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "current date's xmas tree is not lit up"));
        } else if (one.getCount() == -1) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "current date's xmas tree has been claimed"));
        }
        baseZSetRedis.addToZSet(treeStatusKey, currActor.getUid(), -1, Duration.ofDays(30));
        //奖励发放
        giveOutRewardService.giveEventReward(currActor.getUid(), DAILY_LIT_UP_TREE_REWARDS, EVENT_CODE, "3");
        return true;
    }

    public InitPageVO initPage(CommonDTO dto) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        long currTime = DateHelper.getCurrTime();

        InitPageVO vo = new InitPageVO();
        vo.setStartTime(configData.getStartTime());
        vo.setEndTime(configData.getEndTime());
        vo.setGiftPackList(GIFT_PACKS);

        // 国家限制
        Set<String> countryCodeSet = getCountryCodeSet(currActor, configData);
        if (!ObjectUtils.isEmpty(countryCodeSet) && !countryCodeSet.contains(currActor.getCountryCode())) {
//            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Your country cannot participate in this event"));
            return vo;
        }
        // 渠道限制
        if (!ChannelEnum.isTikkoChannel(currActor.getChannel())) {
//            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Your app cannot participate in this event"));
            return vo;
        }
        String userInfoKey = hashGlobalUserInfoKey(dto.getEventType());
        GlobalUserInfo userInfo = findGlobalUserInfo(currActor.getUid(), userInfoKey);
        vo.setCardMap(userInfo.getCardMap());

        int roomCount = diwaliPromotionRoomLimitRedis.roomCount(currActor.getUid());
        vo.setDailyDisseminateCount((long) roomCount);

        String lastRecordKey = listGlobalsExchangeOrGiftRecordKey(dto.getEventType());
        List<RecordVO> lastRecords = baseListRedis.getListByPage(lastRecordKey, RecordVO.class, 1, BaseHistoryVO.RECORD_PAGE_SIZE);
        vo.setLastRecords(lastRecords);
        //填充日榜数据
        DateHelper dateHelper = DateHelper.genDateHelper(currActor.getChannel());
        long endTime = Math.min(currTime, configData.getEndTime());
        List<String> dayList = dateHelper.getDateListByTime(configData.getStartTime() * 1000L, endTime * 1000L);
        if (!ObjectUtils.isEmpty(dayList)) {
            dayList.forEach(dayStr -> fillDailyRankMap(dayStr, configData, currActor, currTime, vo, userInfo));
        }
        return vo;
    }

    private void fillDailyRankMap(String dayStr, AppConfigActivityData configData, ActorData currActor, long currTime, InitPageVO vo, GlobalUserInfo userInfo) {
        //蛋糕
        String cakeGold = findCakeGold(configData, dayStr);
        boolean treeStatus = userInfo.getLightDateSet().contains(dayStr);
        String packStatusKey = zsetDailyTreePackKey(dayStr, configData.getActivityCode());
        CountVO packStatus = baseZSetRedis.getOne(packStatusKey, currActor.getUid());
        String rankKey = dailyReceiveRankKey(dayStr, configData.getActivityCode().toString());
        List<CountVO> dataList = baseZSetRedis.getRange(rankKey, 1, DAILY_RANK_SHOW);
        RankVO self = new RankVO();
        List<RankVO> rankList = new ArrayList<>(100);
        if (!ObjectUtils.isEmpty(dataList)) {
            rankList = IntStream.range(0, dataList.size())
                    .mapToObj(index -> this.fillRankVO(index + 1, dataList.get(index), self, currActor, cakeGold, dayStr))
                    .collect(Collectors.toList());
        }
        fillSelfRankData(self, currActor, rankKey);
        if (currTime < configData.getStartTime()) {
            self.setScore(0L);
            self.setWillGet(0);
            rankList = new ArrayList<>(0);
        }
        String top1Head = "";
        if (!ObjectUtils.isEmpty(rankList)) {
            top1Head = rankList.get(0).getActorInfo().getHead();
        }
        vo.getDailyRankMap().put(dayStr,
                new DailyEventInfoVO()
                        .setDate(dayStr)
                        .setCakeGold(cakeGold)
                        .setTop1Head(top1Head)
                        .setTreeStatus(treeStatus)
                        .setTreePackStatus((int) packStatus.getCount())
                        .setRankInfo(new ModelRankVO<>(self, rankList)));
    }

    private void fillSelfRankData(RankVO self, ActorData currActor, String rankKey) {
        if (self.getScore() == 0L) {
            CountVO selfCount = baseZSetRedis.getOne(rankKey, currActor.getUid());
            self.setScore(selfCount.getCount());
            ActorInfo actorInfo = new ActorInfo(currActor);
            String head = moderationService.dealRankHeadModeration(currActor);
            actorInfo.setHead(cdnUtils.replaceUrlDomain(currActor.getChannel(), head, 0));
            self.setActorInfo(actorInfo);
        }
    }

    private RankVO fillRankVO(Integer rankNum, CountVO data, RankVO self, ActorData currActor, String cakeGold, String dayStr) {
        RankVO vo = new RankVO();
        vo.setRankNum(rankNum.toString());
        vo.setScore(data.getCount());
        ActorInfo actorInfo = new ActorInfo();
        ActorData rankActor = actorMgr.getActorData(data.getUid());
        if (rankActor != null) {
            actorInfo = new ActorInfo(rankActor);
            String head = moderationService.dealRankHeadModeration(rankActor);
            actorInfo.setHead(cdnUtils.replaceUrlDomain(currActor.getChannel(), head, 0));
        }
        vo.setActorInfo(actorInfo);
        // 计算奖池分成
        GlobalUserInfo rankUserInfo = findGlobalUserInfo(data.getUid(), hashGlobalUserInfoKey(EVENT_CODE));
        if (rankUserInfo.getLightDateSet().contains(dayStr)) {
            int willGetGold = computeWillGet(rankNum, cakeGold);
            vo.setWillGet(willGetGold);
        }
        if (currActor.getUid().equals(data.getUid())) {
            self.copy(vo);
        }
        return vo;
    }

    private static int computeWillGet(Integer rankNum, String cakeGold) {
        int willGetGold = 0;
        CakeDivideConfig cakeDivideConfig = CAKE_DIVIDE_CONFIGS.stream().filter(config -> config.getMaxRank() >= rankNum)
                .min(Comparator.comparing(CakeDivideConfig::getMaxRank))
                .orElse(null);
        if (cakeDivideConfig == null) {
            return willGetGold;
        }
        BigDecimal willGet = new BigDecimal(cakeGold).multiply(new BigDecimal(cakeDivideConfig.getRate()));
        if (willGet.compareTo(new BigDecimal("1")) >= 0) {
            willGetGold = willGet.setScale(0, RoundingMode.HALF_UP).intValue();
        } else {
            willGetGold = 1;
        }
        return willGetGold;
    }


    private String findCakeGold(AppConfigActivityData configData, String date) {
        String cakeGold = activityCommonRedis.getCommonStrValue(configData.getActivityCode() + date);
        if (com.quhong.utils.StringUtils.isEmpty(cakeGold)) {
            cakeGold = CAKE_POOL_INIT;
        } else {
            cakeGold = new BigDecimal(CAKE_POOL_INIT).add(new BigDecimal(cakeGold)).toString();
        }
        return cakeGold;
    }


    private void checkAndDeductEventCurrency(ExchangeOrGiftDTO dto, GlobalUserInfo userInfo, GiftPackVO awardPackInfo, String userInfoKey) {
        Integer balance = userInfo.getCardMap().getOrDefault(awardPackInfo.getNeedCardType().toString(), 0);
        int needUse = dto.getAmount() * dto.getUnitPrice();
        if (balance < needUse) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "not enough Lucky Bag"));
        }
        userInfo.getCardMap().put(awardPackInfo.getNeedCardType().toString(), balance - needUse);
        baseHashSaveRedis.saveToRedis(userInfoKey, userInfo.getUid(), userInfo, Duration.ofDays(30));
    }


    private boolean deductEventCurrency(ActivityDiwaliActorInfoData data, ExchangeOrGiftDTO dto) {
        boolean enough = data.divDataBalance((long) dto.getAmount() * dto.getUnitPrice());
        if (enough) {
            ActivityDiwaliActorInfoData updateData = ActivityDiwaliActorInfoData.InitFactory.initDivBalance(data);
            activityDiwaliActorInfoDao.updateOneSelective(updateData);
        }
        return enough;
    }

    public CardRecordVO cardRecord(RecordDTO dto) {
        if (dto.getPage() == null || dto.getPage() < 0) {
            dto.setPage(1);
        }
        String cardRecordKey = initCardRecordKey(dto.getEventType(), dto.getUid());
        List<CardRecordVO.CardRecord> records = baseListRedis.getListByPage(cardRecordKey,
                CardRecordVO.CardRecord.class, dto.getPage(), BaseHistoryVO.RECORD_PAGE_SIZE);
        if (ObjectUtils.isEmpty(records)) {
            return new CardRecordVO();
        }
        int nextPage = checkNextPage(dto.getPage(), records.size());
        return new CardRecordVO().setRecords(records).setNextPage(nextPage);
    }

    public void sendRankRewardAndNotice(int rankType) {
        List<RewardTaskConfig> rewardConfigs;
        String rankName;
        String desc;
        if (rankType == 2) {
            rewardConfigs = RECEIVE_RANK_REWARDS;
            rankName = "receiver ranking";
            desc = "6";
        } else {
            rewardConfigs = SEND_RANK_REWARDS;
            rankName = "sender ranking";
            desc = "5";
        }
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        List<RankingDTO> rankingList = giftPriceRankActivityService.getRankingList(configData, rankType, TopTypeEnum.AWARD);
        if (ObjectUtils.isEmpty(rankingList)) {
            return;
        }

        StringBuilder content = giftPriceRankActivityService.fillNoticeHead(configData, null, rankName);
        StringBuilder globalNotice = new StringBuilder();
        globalNotice.append("😁 Congratulations to the follows get Top 10 in [").append(configData.getName()).append("] ").append(rankName).append("\n");
        IntStream.range(0, rankingList.size())
                .forEach(index -> this.giveRewardAndFillNotice(index + 1, rankingList.get(index), content, rewardConfigs, globalNotice, desc));
        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, content.toString());
        globalNotice.append("😁 The reward has been issued~");
        int fixTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        officialNoticeService.sendGlobalNotice("", globalNotice.toString(), NOTICE_IMG, EVENT_URL, configData.getChannel(), fixTime, EVENT_CODE,
                LangIdConstant.ENGLISH, null, OfficialUserGroup.CHANNEL);
    }

    private void giveRewardAndFillNotice(int rankNum, RankingDTO data, StringBuilder content, List<RewardTaskConfig> rewardConfigs, StringBuilder globalNotice, String desc) {
        giftPriceRankActivityService.fillContentBody(rankNum, content, data);
        rewardConfigs.stream().filter(reward -> reward.getCheckParams() == rankNum)
                .findFirst().ifPresent(rewardConfig -> giveReward(rankNum, data, rewardConfig, globalNotice, desc));
    }

    private void giveReward(int rankNum, RankingDTO data, RewardTaskConfig rewardConfig, StringBuilder globalNotice, String desc) {
        globalNotice.append("Top").append(rankNum).append(" ").append(data.getRid()).append("\n");
        giveOutRewardService.giveEventReward(data.getUid(), rewardConfig.getRewards(), EVENT_CODE, desc);
    }

    public void sendDailyRankReward() {
        AppConfigActivityData configData = appConfigActivityDao.getOneByCodeAndValid(EVENT_CODE, 1);
        LocalDateTime now = LocalDateTime.now(ZoneOffset.of(configData.getZoneOffset())).minusHours(23);
        long dateSec = now.toEpochSecond(ZoneOffset.of(configData.getZoneOffset()));
        if (dateSec < configData.getStartTime() || dateSec > configData.getEndTime()) {
            return;
        }
        String localDateStr = now.toLocalDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
        dealRankReward(localDateStr, configData);
    }

    private void dealRankReward(String localDateStr, AppConfigActivityData configData) {
        String rankKey = dailyReceiveRankKey(localDateStr, configData.getActivityCode().toString());
        List<CountVO> dataList = baseZSetRedis.getRange(rankKey, 1, 100);
        if (ObjectUtils.isEmpty(dataList)) {
            return;
        }
        log.info("total rank reward top.size:{}", dataList.size());
        String cakeGold = findCakeGold(configData, localDateStr);
        StringBuilder content = new StringBuilder();
        content.append("## 圣诞节 日榜(").append(configData.getActivityCode()).append(")\n")
                .append("排名\t\t统计数\t\tget后台金币\t\tuid\t\t\t\t\t\t\t\t\t\trid\n");
        IntStream.range(0, dataList.size())
                .forEach(index -> this.sendRankRewardAndNotice(index + 1, dataList.get(index), configData, cakeGold, content, localDateStr));
        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, content.toString());

    }


    private void sendRankRewardAndNotice(int rankNum, CountVO countData, AppConfigActivityData configData, String cakeGold, StringBuilder content, String localDateStr) {
        try {
            ActorData currActor = actorMgr.getActorData(countData.getUid());
            if (currActor == null) return;
            String treeStatusKey = zsetDailyTreePackKey(localDateStr, EVENT_CODE);
            Double one = baseZSetRedis.getScore(treeStatusKey, currActor.getUid());
            log.info("sendRankRewardAndNotice uid={} treeStatusKey={} treeStatus={}",currActor.getUid(),treeStatusKey,one);
            if (one == null || one == 0) {
                return;
            }
            int willGet = computeWillGet(rankNum, cakeGold);
            content.append(rankNum).append("\t\t\t").append(countData.getCount()).append("\t\t\t").append(willGet).append("\t\t\t\t").append(countData.getUid()).append("\t\t");
            if (willGet <= 0) return;
            RewardInfoData reward = new RewardInfoData(configData.getActivityCode(), willGet, RewardItemType.GOLD, 0, 1);
            giveOutRewardService.giveEventReward(currActor.getUid(), Collections.singletonList(reward), configData.getActivityCode(), "4");
            content.append(currActor.getRid());
            // 总榜官方通知
            CakeDivideConfig cakeDivideConfig = CAKE_DIVIDE_CONFIGS.stream().filter(config -> config.getMaxRank() >= rankNum)
                    .min(Comparator.comparing(CakeDivideConfig::getMaxRank))
                    .orElse(null);
            if (cakeDivideConfig == null) return;
            String notice = fillRankNotice(rankNum, cakeDivideConfig, willGet);
            officialNoticeService.sendOfficialNotice(currActor.getUid(), configData.getName(),
                    notice, NOTICE_IMG, EVENT_URL, currActor.getChannel(),
                    DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50),
                    configData.getActivityCode());
        } finally {
            content.append("\n");
        }
    }

    /**
     * daily notice
     */
    private static String fillRankNotice(int rankNum, CakeDivideConfig cakeDivideConfig, int willGet) {
        double rate = new BigDecimal(cakeDivideConfig.getRate()).movePointRight(2)
                .setScale(2, RoundingMode.HALF_UP).doubleValue();
        String notice = "\uD83C\uDF89\uD83D\uDC4F\uD83C\uDF8AMerry Christmas and Happy New Year\n" +
                "\uD83C\uDF89\uD83D\uDC4F\uD83C\uDF8ACongratulations on geting Top.#top in the\"2025, what's your luck? \" Event daily receiver ranking\n" +
                "\uD83C\uDF89\uD83D\uDC4F\uD83C\uDF8AYou've received #rate% of Christmas Prize Pool coins：#count coins";
        notice = notice.replace("#top", String.valueOf(rankNum))
                .replace("#rate", String.valueOf(rate))
                .replace("#count", String.valueOf(willGet * 40));
        return notice;
    }

    public List<UserInfoVO> dailyLastLightTree(String date, CommonDTO dto) {
        String dailyLightTreeKey = listDailyLightTreeKey(dto.getEventType(), date);
        return baseListRedis.getListByPage(dailyLightTreeKey, UserInfoVO.class, 1, 30);
    }
}
