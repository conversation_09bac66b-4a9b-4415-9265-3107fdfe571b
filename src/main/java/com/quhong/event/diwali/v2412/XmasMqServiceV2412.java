package com.quhong.event.diwali.v2412;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.ActType;
import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.constant.money.MoneyChangeActionConstant;
import com.quhong.core.enums.ActorType;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.enums.GenderTypeEnum;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.MoneyDetailData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.data.bo.diwali.SendGiftMqActionBO;
import com.quhong.data.config.TaskConfig;
import com.quhong.data.thData.ActivitySpecialItemsEvent;
import com.quhong.data.thData.LogEventCurrencyAddData;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.CurrencyEnum;
import com.quhong.enums.RewardItemType;
import com.quhong.event.diwali.v1.redis.DiwaliPromotionRoomLimitRedis;
import com.quhong.event.diwali.v2412.data.bo.GlobalUserInfo;
import com.quhong.event.diwali.v2412.data.config.DiwaliTaskConfig;
import com.quhong.event.diwali.v2412.data.vo.CardRecordVO;
import com.quhong.exceptions.WebException;
import com.quhong.game.data.dto.yxsk.UpdateCoinDTO;
import com.quhong.mq.data.activity.InviteUserSuccessMsgData;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.mq.data.room.RoomChatMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.redis.BaseHashSaveRedis;
import com.quhong.redis.BaseListRedis;
import com.quhong.redis.BaseZSetRedis;
import com.quhong.redis.base.ActivityCommonRedis;
import com.quhong.redis.base.currency.task.BaseDailyLimitRedis;
import com.quhong.redis.base.currency.task.BaseEveryLimitRedis;
import com.quhong.report.EventReport;
import com.quhong.service.common.AwardPoolService;
import com.quhong.service.common.RewardService;
import com.quhong.service.common.RoomFloatingImService;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2023/10/23 18:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class XmasMqServiceV2412 {

    //    private static final ActivityTypeEnum ACTIVITY_TYPE = ActivityTypeEnum.DIWALI_ACTIVITY;
//    private static final ActivityTypeEnum ACTIVITY_TYPE = ActivityTypeEnum.EVENT_DIWALI_2410 ;
    private static final ActivityTypeEnum ACTIVITY_TYPE = ActivityTypeEnum.EVENT_XMAS_2024;

    public static final TaskConfig PROMOTION_DAILY_TASK = new TaskConfig(TaskTypeConstant.DISSEMINATE_TIMES, 5, 1, 1);
    private static final TaskConfig SEND_GIFT_TASK = new TaskConfig(TaskTypeConstant.SEND_GIFT, 200, 1, -1);
    private static final TaskConfig PLAY_GAME_TASK = new TaskConfig(TaskTypeConstant.PLAY_GAME_COST_GOLD, 10000, 1, -1);
    private static final TaskConfig INVITE_USER_DAILY_TASK = new TaskConfig(TaskTypeConstant.INVITE_USER_COUNT, 1, 1, 1);

    private static final String SEND_GIFT_MODE = "https://statics.kissu.mobi/event/2025/Christmas/history_01.png";
    private static final String PLAY_GAME_MODE = "https://statics.kissu.mobi/event/2025/Christmas/02.png";
    private static final String PROMOTION_MODE = "https://statics.kissu.mobi/event/2025/Christmas/03.png";
    private static final String INVITE_USER_MODE = "https://statics.kissu.mobi/event/2025/Christmas/04.png";

    private static final String PROMOTION_TEXT = "Merry Christmas and Happy New Year, Get Good Luck during Event~";
    //    private final DistributeLockUtils distributeLockUtils;
//    private final ActivityDiwaliActorInfoDao activityDiwaliActorInfoDao;
    private final DiwaliPromotionRoomLimitRedis diwaliPromotionRoomLimitRedis;
    private final ActorMgr actorMgr;
    private final AppConfigActivityDao appConfigActivityDao;

    private final EventReport eventReport;
    private final BaseEveryLimitRedis baseEveryLimitRedis;
    private final BaseDailyLimitRedis baseDailyLimitRedis;
    private final XmasServiceV2412 xmasServiceV2412;
    private final AwardPoolService awardPoolService;
    private final BaseHashSaveRedis baseHashSaveRedis;
    private final BaseListRedis baseListRedis;
    private final RewardService rewardService;
    private final ActivityCommonRedis activityCommonRedis;
    private final BaseZSetRedis baseZSetRedis;
    private final RoomFloatingImService roomFloatingImService;


    public void inviteUserSuccessAction(InviteUserSuccessMsgData mqData) {
        checkParams(mqData);
        ActorData currActor = actorMgr.getCurrActorData(mqData.getInviterUid());
        ActorData invitedActor = actorMgr.getCurrActorData(mqData.getInvitedUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(ACTIVITY_TYPE.getCode());
        checkLimit(currActor, configData, DateHelper.getCurrTime());
        String keyPre = xmasServiceV2412.hashInviteUserLimitKey(configData.getActivityCode());
        long incr = baseDailyLimitRedis.getTicket(keyPre, currActor.getUid(), 1, configData.getZoneOffset(), INVITE_USER_DAILY_TASK);
        if (incr < 1) {
            return;
        }
        giveDeng(currActor, configData, (int) incr, "4", INVITE_USER_MODE);
    }


    /**
     * 校验限制
     *
     * @param currActor  当前用户
     * @param configData 活动配置
     * @param currTime   当前时间
     */
    private static void checkLimit(ActorData currActor, AppConfigActivityData configData, long currTime) {
        if (!ActorType.REAL_PERSON_USER_TYPE_SET.contains(currActor.getUserType())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("robot data not count"));
        }
        if (!ChannelEnum.isTikkoChannel(currActor.getChannel())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Your channel cannot not join this event"));
        }

        // 国家限制
        Set<String> countryCodeSet = getCountryCodeSet(currActor, configData);
        if (!ObjectUtils.isEmpty(countryCodeSet) && !countryCodeSet.contains(currActor.getCountryCode())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Your country cannot participate in this event"));
        }

        // 活动时间限制
        if (currTime < configData.getStartTime() || currTime > configData.getEndTime()) {
            log.info("not in the event period");
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("not in the event period"));
        }
    }

    private static Set<String> getCountryCodeSet(ActorData actor, AppConfigActivityData activityData) {
        Set<String> countryCodeSet = activityData.getUserCountryGroupStr();
        if (GenderTypeEnum.HOST.getType().equals(actor.getGender())) {
            countryCodeSet = activityData.getHostCountryGroupStr();
        }
        return countryCodeSet;
    }

    /**
     * 送礼mq处理
     *
     * @param mqData mq
     */
    public void sendGiftMqAction(SendGiftSuccessMsgData mqData) {
        //校验参数
        checkParams(mqData);
        //校验活动配置
        ActorData sendActor = actorMgr.getCurrActorData(mqData.getUid());
        ActorData receiveActor = actorMgr.getCurrActorData(mqData.getToUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(ACTIVITY_TYPE.getCode());
        //发放福灯
        try {
            sendGiftAction(new SendGiftMqActionBO(sendActor, configData, mqData));
        } catch (WebException e) {
            log.info("warn to action send gift, httpCode={}", JSON.toJSONString(e.getHttpCode()));
        } catch (Exception e) {
            log.error("error to action send gift, msg={}", e.getMessage(), e);
        }
        //记录收礼价值
        try {
            receiveGiftAction(new SendGiftMqActionBO(receiveActor, configData, mqData));
        } catch (WebException e) {
            log.info("warn to action send gift, httpCode={}", JSON.toJSONString(e.getHttpCode()));
        } catch (Exception e) {
            log.error("error to action send gift, msg={}", e.getMessage(), e);
        }
    }

    private void increaseCake(SendGiftSuccessMsgData mqData, String key) {
        BigDecimal incrPool = new BigDecimal(mqData.getCost().toString()).multiply(new BigDecimal(XmasServiceV2412.CAKE_POOL_RATE));
        String poolBalance = activityCommonRedis.getCommonStrValue(key);
        if (StringUtils.isEmpty(poolBalance)) {
            poolBalance = "0";
        }
        BigDecimal finalPool = incrPool.add(new BigDecimal(poolBalance));
        activityCommonRedis.setCommonStrValue(key, finalPool.toString());
    }

    public void moneyDetailAction(MoneyDetailData mqData) {
        checkParams(mqData);
        if (ActType.HKYS_GAME_GOLD != mqData.getActType()) {
            return;
        }
        playGameLockFunc(mqData);

    }

    private Boolean playGameLockFunc(MoneyDetailData mqData) {
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUserid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(ACTIVITY_TYPE.getCode());
        checkLimit(currActor, configData, DateHelper.getCurrTime());
        int incr = baseEveryLimitRedis.increaseAndGetRewards(mqData.getUserid(), ACTIVITY_TYPE.getCode(),
                Double.parseDouble(mqData.getRealSingleChange()), PLAY_GAME_TASK);
        if (incr < 1) return false;
        giveDeng(currActor, configData, incr, "2", PLAY_GAME_MODE);
        return true;
    }


    /**
     * 房间消息mq处理
     *
     * @param mqData mq
     */
    public void roomMsgAction(RoomChatMsgData mqData) {
        //校验参数
        checkParams(mqData);
        roomMsgLockFunc(mqData);
    }

    private Boolean roomMsgLockFunc(RoomChatMsgData mqData) {
        //校验活动配置
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(ACTIVITY_TYPE.getCode());
        checkLimit(currActor, configData, DateHelper.getCurrTime());

        if (!mqData.getMsg().contains(PROMOTION_TEXT)) {
            return false;
        }
        //计算每日领取数量
        int increase = diwaliPromotionRoomLimitRedis.saveRoomIdAndGetTicket(mqData.getUid(), mqData.getRoomId());
        if (increase < 1) {
            return false;
        }
        //发福灯
        giveDeng(currActor, configData, increase, "3", PROMOTION_MODE);
        return true;
    }


    private Boolean sendGiftAction(SendGiftMqActionBO bo) {
        checkLimit(bo.getActorData(), bo.getConfigData(), bo.getMqData().getTime());
        if (!bo.getConfigData().getDataId().contains(bo.getMqData().getGiftId().intValue())) {
            return false;
        }

        int incr = baseEveryLimitRedis.increaseAndGetRewards(bo.getActorData().getUid(), bo.getConfigData().getActivityCode(),
                Double.parseDouble(bo.getMqData().getRealCost()), SEND_GIFT_TASK);
        if (incr < 1) {
            return false;
        }
        //发福灯
        giveDeng(bo.getActorData(), bo.getConfigData(), incr, "1", SEND_GIFT_MODE);
        return true;
    }

    /**
     * 数据转移到 redis 多类型货币
     */
    private void giveDeng(ActorData currActor, AppConfigActivityData configData, Integer increase, String fromType, String mode) {
        List<String> awardKeys = awardPoolService.drawFromRedisPool(configData.getActivityCode(), fromType,
                XmasServiceV2412.CARD_INFOS, 100, increase, XmasMqServiceV2412::fillPool);
        String hashGlobalUserInfoKey = xmasServiceV2412.hashGlobalUserInfoKey(configData.getActivityCode());
        GlobalUserInfo userInfo = xmasServiceV2412.findGlobalUserInfo(currActor.getUid(), hashGlobalUserInfoKey);
        awardKeys.forEach(awardKey -> {
            Integer num = userInfo.getCardMap().getOrDefault(awardKey, 0);
            num++;
            userInfo.getCardMap().put(awardKey, num);
            // 上报数数
            reportGetCardToThinkData(currActor, configData, 1, fromType, awardKey);
        });
        baseHashSaveRedis.saveToRedis(hashGlobalUserInfoKey, userInfo.getUid(), userInfo, Duration.ofDays(30));
        DateHelper dateHelper = DateHelper.genDateHelper(currActor.getChannel());
        //写入卡片获取记录
        List<CardRecordVO.CardRecord> cardRecords = fillCardRecords(dateHelper, awardKeys, mode);
        String cardRecordKey = xmasServiceV2412.initCardRecordKey(configData.getActivityCode(), currActor.getUid());
        baseListRedis.leftPushAll(cardRecordKey, cardRecords);
        //弹窗提醒
        List<RewardInfoData> rewards = fillGetCards(awardKeys);
        rewards.forEach(card -> roomFloatingImService.sendRoomFloatingIm(currActor, card.getNums(), card.getIcon(), card.getName(),
                "To Join Event", ACTIVITY_TYPE.getCode(), XmasServiceV2412.EVENT_URL, false, "Get #ticket*#count #suffix >>>"));
//        rewardService.sendPersonalActivityPopMsg(currActor.getUid(), rewards, XmasServiceV2412.EVENT_URL, configData.getName(), "", "");

    }

    public static void fillPool(AwardPoolService.FillPoolDTO dto) {
        AwardInfo award = dto.getAward();
        List<Integer> indexList = dto.getIndexList();
        List<String> poolList = dto.getPoolList();
        String awardKey = award.getAwardId().toString();

        int awardRate;
        switch (dto.getPoolType()) {
            case "1":
                awardRate = award.getRate();
                break;
            case "2":
                awardRate = award.getRate1();
                break;
            case "3":
                awardRate = award.getRate2();
                break;
            case "4":
                awardRate = award.getRate3();
                break;
            default:
                awardRate = 0;
                break;
        }
        if (awardRate == 0) {
            return;
        }
        IntStream.range(0, awardRate).forEach(i -> {
            if (!indexList.isEmpty()) {
                int index = indexList.remove(0);
                poolList.set(index, awardKey);
            } else {
                poolList.add(awardKey);
            }
        });
    }


    private static List<RewardInfoData> fillGetCards(List<String> awardKeys) {
        List<RewardInfoData> rewards = new ArrayList<>();
        awardKeys.forEach(awardKey -> {
            fillCard(awardKey, rewards);
        });
        return rewards;
    }

    private static void fillCard(String awardKey, List<RewardInfoData> rewards) {
        if (rewards.stream().anyMatch(rewardInfo -> rewardInfo.getDataId().equals(Integer.valueOf(awardKey)))) {
            rewards.stream().filter(rewardInfo -> rewardInfo.getDataId().equals(Integer.valueOf(awardKey)))
                    .findFirst().ifPresent(rewardInfo -> rewardInfo.setNums(rewardInfo.getNums() + 1));
            return;
        }
        AwardInfo horseInfo = XmasServiceV2412.CARD_INFOS.stream().filter(data -> data.getAwardId().equals(Integer.valueOf(awardKey)))
                .findFirst().orElseThrow(() -> new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "not found award,id=" + awardKey)));
        rewards.add(new RewardInfoData(ACTIVITY_TYPE.getCode(), 1, RewardItemType.EVENT_CURRENCY, Integer.parseInt(awardKey))
                .setName(horseInfo.getAwardName()).setIcon(horseInfo.getAwardIcon()));
    }

    private static List<CardRecordVO.CardRecord> fillCardRecords(DateHelper dateHelper, List<String> awardKeys, String mode) {
        String date = dateHelper.getDateByTime(System.currentTimeMillis());
        return awardKeys.stream()
                .map(awardKey -> XmasServiceV2412.CARD_INFOS.stream()
                        .filter(info -> info.getAwardId().toString().equals(awardKey))
                        .findFirst().orElseThrow(() -> new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false,
                                "not found award,id=" + awardKey))))
                .map(info -> new CardRecordVO.CardRecord().setDate(date).setMode(mode).setCardType(info.getAwardId()))
                .collect(Collectors.toList());
    }

    private void reportGetCardToThinkData(ActorData currActor, AppConfigActivityData configData, Integer increase, String fromType, String cardType) {
        ActivitySpecialItemsEvent logData = new ActivitySpecialItemsEvent();
        logData.setUid(currActor.getUid());
        logData.setCtime(DateHelper.getCurrTime());
        logData.setChannel(currActor.getChannel());
        logData.setEventCode(configData.getActivityCode());
        logData.setActivityName(configData.getName());
        logData.setItemId(cardType);
        logData.setChangeAction(1);
        logData.setFromType(fromType);
        logData.setItemCount(increase);
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    @Deprecated
    private void reportEventCurrencyAdd(ActorData currActor, AppConfigActivityData configData, Integer increase, DiwaliTaskConfig diwaliTaskConfig) {
        LogEventCurrencyAddData logData = new LogEventCurrencyAddData();
        logData.setUid(currActor.getUid());
        logData.setEventCode(configData.getActivityCode());
        logData.setActivityName(configData.getName());
        logData.setEventTaskType(diwaliTaskConfig.getTaskType());
        logData.setEventTaskParams(diwaliTaskConfig.getCheckParams());
        logData.setEventCurrencyChanged(increase);
        logData.setCtime(DateHelper.getCurrTime());
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private Boolean receiveGiftAction(SendGiftMqActionBO bo) {
        ActorData receiveActor = bo.getActorData();
        AppConfigActivityData configData = bo.getConfigData();
        SendGiftSuccessMsgData mqData = bo.getMqData();
        checkLimit(receiveActor, configData, mqData.getTime());
        if (!configData.getDataId().contains(mqData.getGiftId().intValue())) {
            return false;
        }
        DateHelper dateHelper = DateHelper.genDateHelper(receiveActor.getChannel());

        increaseCake(mqData, configData.getActivityCode() + dateHelper.getToday());
        // 日榜榜单数据统计
        String dailyRankKey = xmasServiceV2412.dailyReceiveRankKey(dateHelper.getToday(), configData.getActivityCode().toString());
        baseZSetRedis.increaseToZSet(dailyRankKey, receiveActor.getUid(), mqData.getCost(), Duration.ofDays(30));
        return true;
    }

    private void checkParams(MoneyDetailData mqData) {
        if (mqData.getSingleAction() != MoneyChangeActionConstant.ACTION_DEDUCT) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("this record not join event, single action must is 2"));
        }
        if (!Objects.equals(mqData.getCurrencyCode(), CurrencyEnum.CURRENCY1.getCurrencyCode())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("this record not join event, currency code must is 1"));
        }

    }


    private void checkParams(SendGiftSuccessMsgData mqData) {
        checkSendGiftParams(mqData);
        if (mqData.getCost() == null || mqData.fetchRealCost() <= 0) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("cost is empty"));
        }
    }

    private void checkSendGiftParams(SendGiftSuccessMsgData mqData) {
        if (mqData.getGiftId() == null || mqData.getGiftId() <= 0) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("gift is empty"));
        }
        if (mqData.getNum() == null || mqData.getNum() <= 0) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("num is empty"));
        }
    }

    private void checkParams(UpdateCoinDTO mqData) {
        mqData.checkPlayGameUpdateCoinParams();
    }

    private void checkParams(RoomChatMsgData mqData) {
        if (StringUtils.isEmpty(mqData.getUid())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("uid is empty"));
        }
        if (StringUtils.isEmpty(mqData.getRoomId())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("roomId is empty"));
        }
        if (StringUtils.isEmpty(mqData.getMsg())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("msg is empty"));
        }
    }

    public void checkParams(InviteUserSuccessMsgData mqData) {
        if (StringUtils.isEmpty(mqData.getInviterUid())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "inviterUid is empty"));
        }
        if (StringUtils.isEmpty(mqData.getInvitedUid())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "invitedUid is empty"));
        }
    }
}
