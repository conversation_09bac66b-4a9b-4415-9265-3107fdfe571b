package com.quhong.event.diwali.v2410.data.config;

import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.data.config.TaskConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获得福灯任务配置
 *
 * <AUTHOR>
 * @since 2023/10/23 19:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DiwaliTaskConfig extends TaskConfig {
    private static final boolean PROD = ServerConfiguration.isProduct();
    public static final TaskConfig SEND_GIFT_PRICE_TASK = new TaskConfig(TaskTypeConstant.SEND_GIFT, 25, 1, -1);

//    /**
//     * 首充任务
//     */
//    public static final TaskConfig RECHARGE_TASK = new TaskConfig(TaskTypeConstant.EVENT_FIRST_RECHARGE, 0, 10, -1);
//    /**
//     * 雅讯时空系列游戏任务
//     */
//    public static final TaskConfig PLAY_YXSK_GAME_TASK = new TaskConfig(TaskTypeConstant.PLAY_GAME_COST_GOLD, 400, 2, 15);
    /**
     * 宣传任务
     */
    public static final TaskConfig DISSEMINATE_TIMES_TASK = new TaskConfig(TaskTypeConstant.DISSEMINATE_TIMES, 5, 1, 1);
    /**
     * 福灯图标
     */
    public static final String DENG_ICON_URL = "https://statics.kissu.mobi/Event/diwali/202410/-s-Diya_Lamp_Emoji.png";
    /**
     * 活动地址
     */
    public static final String EVENT_URL = PROD ? "https://videochat.kissu.site/diwali_2024_10/" : "https://testvideochat.kissu.site/diwali_2024_10/";
    /**
     * 飘屏背景图
     */
    public static final String FLOATING_SCREEN_BACKGROUND_IMG = "https://statics.kissu.mobi/icon/independence/new/floating_v4.png";
    /**
     * 排灯节活动宣传文案
     */
    public static final String DISSEMINATE_TEXT = "Happy Diwali, Join Event";

    /**
     * 任务配置
     */
//    public static final List<TaskConfig> TASK_CONFIGS = new ArrayList<TaskConfig>() {{
//        add(new TaskConfig(TaskTypeConstant.SEND_GIFT, 610, 2, -1));
//        add(new TaskConfig(TaskTypeConstant.SEND_GIFT, 613, 6, -1));
//    }};
    public DiwaliTaskConfig(Integer taskType, Integer checkParams, Integer ticket, Integer limit) {
        super(taskType, checkParams, ticket, limit);
    }

}
