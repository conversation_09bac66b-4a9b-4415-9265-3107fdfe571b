package com.quhong.event.training.service;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.LangIdConstant;
import com.quhong.constant.OfficialUserGroup;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.WarnName;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MasterUtils;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.data.enums.ActivityHttpCode;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.enums.EventCode;
import com.quhong.enums.RewardItemType;
import com.quhong.event.training.data.bo.WarriorBO;
import com.quhong.event.training.data.bo.WarriorTeamBO;
import com.quhong.event.training.data.dto.DoTrainingDTO;
import com.quhong.event.training.data.vo.PageInitVO;
import com.quhong.event.training.data.vo.ReceiveRewardVO;
import com.quhong.event.training.redis.TrainingRedisOps;
import com.quhong.exceptions.WebException;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.activity.AppConfigActivityService;
import com.quhong.service.common.ActivityConfigService;
import com.quhong.service.common.AwardPoolService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class TrainingService {

    private static final boolean PROD = ServerConfiguration.isProduct();
    private static final int EVENT_CODE = EventCode.EVENT_DRAGON_TRAINING_2502;
    private static final long PER_TRAIN_SCORE = 1; // 驯服一次增加的进度

    // 团队进度得奖励
    private static final long TEAM_PROGRESS_CHECKPOINT_L1 = PER_TRAIN_SCORE * 3;
    private static final long TEAM_PROGRESS_CHECKPOINT_L2 = PER_TRAIN_SCORE * 15;
    private static final long TEAM_PROGRESS_CHECKPOINT_L3 = PER_TRAIN_SCORE * 30;
    private static final long TEAM_PROGRESS_CHECKPOINT_L4 = PER_TRAIN_SCORE * 400;
    private static final long TEAM_PROGRESS_CHECKPOINT_L5 = PER_TRAIN_SCORE * 1000;

    // 轮播最近七条驯龙中奖记录
    private static final int ROTATION_SIZE = 7;

    private static final String NOTICE_IMG = "https://statics.kissu.mobi/Event/drgon/dragon_notice.png";
    private static final String EVENT_URL = PROD ? "https://videochat.kissu.site/train_dragon" : "https://testvideochat.kissu.site/train_dragon";


    /**
     * 仅赠送活动礼物计入统计
     */
    private static final Set<Long> TRAINING_GIFT_ID_SET = Collections.unmodifiableSet(
            new HashSet<>(Arrays.asList(2035L, 2036L, 2037L))
    );

    // 送礼榜奖励
    private static final Map<Integer, List<RewardInfoData>> SENDER_RANK_REWARDS = new LinkedHashMap<Integer, List<RewardInfoData>>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90066 : 900017, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 247 : 97, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 251 : 55, 0));
        }};
        put(1, rewards1);

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90067 : 90003, 0));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 247 : 97, 0));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.SEAT_FRAME, PROD ? 251 : 55, 0));
        }};
        put(2, rewards2);

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900068 : 90001, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 247 : 97, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 251 : 55, 0));
        }};
        put(3, rewards3);
    }};

    // 收礼榜奖励
    private static final Map<Integer, List<RewardInfoData>> RECEIVER_RANK_REWARDS = new LinkedHashMap<Integer, List<RewardInfoData>>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900050 : 900018, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 247 : 97, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 252 : 65, 0));
        }};
        put(1, rewards1);

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900050 : 900018, 0));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 247 : 97, 0));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.SEAT_FRAME, PROD ? 252 : 65, 0));
        }};
        put(2, rewards2);

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900050 : 900018, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 247 : 97, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 252 : 65, 0));
        }};
        put(3, rewards3);
    }};

    /**
     * 组队成功奖励
     */
    private static final List<RewardInfoData> REWARD_JOIN_TEAM = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.DESIGNATION, PROD ? 357 : 90, 0));
        add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 900084 : 900031, 0));
        add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.SEAT_FRAME, PROD ? 27 : 98, 0));
    }};

    /**
     * 最高团队奖励
     */
    private static final List<RewardInfoData> TOP1_TEAM_REWARD = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 50, RewardItemType.GOLD, 0, 1));
        add(new RewardInfoData(EVENT_CODE, 3, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97, 0));
        add(new RewardInfoData(EVENT_CODE, 3, RewardItemType.LORD_DAYS, 6, 0));
        add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, PROD ? 628 : 23, 50));
        add(new RewardInfoData(EVENT_CODE, 50000, RewardItemType.LEVEL_SCORE, 0, 0));
    }};

    public static final List<AwardInfo> AWARD_POOL_CONFIGS_LEVEL_1 = new ArrayList<AwardInfo>() {{
        add(new AwardInfo().setRate(PROD ? 1 : 15)
                .setAwardType(RewardItemType.GOLD)
                .setAwardId(1)
                .setAwardName("Gold")
                .setDataId(0)
                .setNums(250)
                .setUnitPrice(1)
                .setShowNums("10000")
                .setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png"));
        add(new AwardInfo().setRate(PROD ? 1 : 10)
                .setAwardType(RewardItemType.GOLD)
                .setAwardId(2)
                .setAwardName("Gold")
                .setDataId(0)
                .setNums(25)
                .setUnitPrice(1)
                .setShowNums("1000")
                .setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png"));
        add(new AwardInfo().setRate(PROD ? 1 : 200)
                .setAwardType(RewardItemType.GOLD)
                .setAwardId(3)
                .setAwardName("Gold")
                .setDataId(0)
                .setNums(5)
                .setUnitPrice(1)
                .setShowNums("200")
                .setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png"));
        add(new AwardInfo().setRate(400)
                .setAwardType(RewardItemType.VIP_DAYS)
                .setAwardId(4)
                .setAwardName("VIP")
                .setDataId(0)
                .setNums(1)
                .setUnitPrice(0)
                .setShowNums("1day")
                .setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/icon/checkin/VIP.png"));
        add(new AwardInfo().setRate(540)
                .setAwardType(RewardItemType.GIFT)
                .setAwardId(5)
                .setAwardName("Tell Him")
                .setDataId(612)
                .setNums(1)
                .setUnitPrice(0)
                .setShowNums("1")
                .setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/Event/Speak/speak.png"));
        add(new AwardInfo().setRate(57)
                .setAwardType(RewardItemType.SEAT_FRAME)
                .setAwardId(6)
                .setAwardName("Dragon Rider")
                .setDataId(PROD ? 358 : 101)
                .setUnitPrice(0)
                .setNums(1)
                .setShowNums("1day")
                .setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/room_item/origin/1739947212042/q.webp"));
    }};

    public static final List<AwardInfo> AWARD_POOL_CONFIGS_LEVEL_2 = new ArrayList<AwardInfo>() {{
        add(new AwardInfo().setRate1(PROD ? 1 : 20)
                .setAwardType(RewardItemType.GOLD)
                .setAwardId(1)
                .setAwardName("Gold")
                .setDataId(0)
                .setNums(250)
                .setUnitPrice(1)
                .setShowNums("10000")
                .setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png"));
        add(new AwardInfo().setRate1(PROD ? 1 : 50)
                .setAwardType(RewardItemType.GOLD)
                .setAwardId(2)
                .setAwardName("Gold")
                .setDataId(0)
                .setNums(25)
                .setUnitPrice(1)
                .setShowNums("1000")
                .setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png"));
        add(new AwardInfo().setRate1(PROD ? 1 : 70)
                .setAwardType(RewardItemType.GIFT)
                .setAwardId(3)
                .setAwardName("Lucky champion")
                .setDataId(634)
                .setNums(1)
                .setUnitPrice(15)
                .setShowNums("600")
                .setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/Event/shot/icon.png"));
        add(new AwardInfo().setRate1(PROD ? 1 : 3)
                .setAwardType(RewardItemType.GIFT)
                .setAwardId(4)
                .setAwardName("Sweet hit")
                .setDataId(628)
                .setNums(1)
                .setUnitPrice(500)
                .setShowNums("1")
                .setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/icon/lottery/Sweet_crit.png"));
        add(new AwardInfo().setRate1(PROD ? 104 : 250)
                .setAwardType(RewardItemType.GIFT)
                .setAwardId(5)
                .setAwardName("Thumbs up")
                .setDataId(603)
                .setNums(1)
                .setUnitPrice(1)
                .setShowNums("1")
                .setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/icon/Welfare/up.png"));
        add(new AwardInfo().setRate1(265)
                .setAwardType(RewardItemType.ENTER_EFFECT)
                .setAwardId(6)
                .setAwardName("Racing car")
                .setDataId(PROD ? 350 : 116)
                .setNums(1)
                .setUnitPrice(0)
                .setShowNums("1")
                .setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/room_item/icon/437/Racing_car.png"));
        add(new AwardInfo().setRate1(560)
                .setAwardType(RewardItemType.SEAT_FRAME)
                .setAwardId(7)
                .setAwardName("Advanced Dragon")
                .setDataId(PROD ? 359 : 101)
                .setNums(1)
                .setUnitPrice(0)
                .setShowNums("1")
                .setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/room_item/origin/1739948904911/3.webp"));
        add(new AwardInfo().setRate1(67)
                .setAwardType(RewardItemType.LEVEL_SCORE)
                .setAwardId(8)
                .setAwardName("1000 level points")
                .setDataId(0)
                .setNums(1000)
                .setUnitPrice(0)
                .setShowNums("1000")
                .setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/Event/Welfare/Battle/Planting/EXP2.png"));
    }};

    /**
     * 组队档位奖励
     */
    private static final List<RewardInfoData> TEAM_REWARD_LEVEL_1 = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GOLD, 0, 1));
    }};
    private static final List<RewardInfoData> TEAM_REWARD_LEVEL_2 = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 5, RewardItemType.GOLD, 0, 1));
    }};
    private static final List<RewardInfoData> TEAM_REWARD_LEVEL_3 = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.GOLD, 0, 1));
    }};
    private static final List<RewardInfoData> TEAM_REWARD_LEVEL_4 = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 250, RewardItemType.GOLD, 0, 1));
    }};
    private static final List<RewardInfoData> TEAM_REWARD_LEVEL_5 = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 500, RewardItemType.GOLD, 0, 1));
    }};

    private static final Map<Long, List<RewardInfoData>> TEAM_REWARD = new LinkedHashMap<Long, List<RewardInfoData>>() {{
        put(TEAM_PROGRESS_CHECKPOINT_L1, TEAM_REWARD_LEVEL_1);
        put(TEAM_PROGRESS_CHECKPOINT_L2, TEAM_REWARD_LEVEL_2);
        put(TEAM_PROGRESS_CHECKPOINT_L3, TEAM_REWARD_LEVEL_3);
        put(TEAM_PROGRESS_CHECKPOINT_L4, TEAM_REWARD_LEVEL_4);
        put(TEAM_PROGRESS_CHECKPOINT_L5, TEAM_REWARD_LEVEL_5);
    }};


    private final AppConfigActivityService appConfigActivityService;
    private final ActivityConfigService activityConfigService;
    private final GiveOutRewardService giveOutRewardService;
    private final TrainingRedisOps trainingRedisOps;
    private final MasterUtils masterUtils;
    private final TrainReportService reportService;
    private final ActorMgr actorMgr;
    private final OfficialNoticeService officialNoticeService;
    private final AwardPoolService awardPoolService;
    private final MonitorSender monitorSender;


    /**
     * 页面初始化
     */
    public PageInitVO pageInit(HttpEnvData dto) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData activityData = appConfigActivityService.getValidOneByEventCodeThrowWebException(EVENT_CODE);
        Long now = DateHelper.getCurrTime();
        if (!appConfigActivityService.isChannelSupported(activityData, currActor.getChannel()) || activityData.getStartTime() > now) {
            return PageInitVO.builder()
                    .startTime(activityData.getStartTime())
                    .endTime(activityData.getEndTime())
                    .build();
        }

        long score = trainingRedisOps.getTrainedCount(dto.getUid()) * PER_TRAIN_SCORE;
        long nextLevelScore = getNextPoolLevelScore(score);
        return PageInitVO.builder()
                .trainingScore(score)
                .nextLevelScore(nextLevelScore)
                .currLevel(getCurrLevelScore(score))
                .restTrainingCount(trainingRedisOps.restTrainCount(dto.getUid()))
                .myTeam(myTeam(dto.getUid()))
                .rotationRewardHistory(trainingRedisOps.getRotationRewardHistory(ROTATION_SIZE))
                .startTime(activityData.getStartTime())
                .endTime(activityData.getEndTime())
                .build();
    }

    private WarriorTeamBO myTeam(String uid) {
        String captainUid = trainingRedisOps.getCaptainUid(uid);
        if (!ObjectUtils.isEmpty(captainUid)) {
            WarriorTeamBO warriorTeamBO = trainingRedisOps.getTeamInfo(captainUid);
            fillTeamMemberScore(warriorTeamBO);
            return warriorTeamBO;
        }
        // 未组队成功则显示只有自己在队伍中
        ActorData actorData = actorMgr.getActorData(uid);
        WarriorTeamBO warriorTeamBO = WarriorTeamBO.builder()
                .warriors(new ArrayList<>())
                .name(actorData.getName() + "'s team")
                .captainUid(captainUid)
                .build();
        WarriorBO warriorBO = new WarriorBO(actorData);
        warriorTeamBO.getWarriors().add(warriorBO);
        return warriorTeamBO;
    }

    private void fillTeamMemberScore(WarriorTeamBO warriorTeamBO) {
        if (!ObjectUtils.isEmpty(warriorTeamBO) && !ObjectUtils.isEmpty(warriorTeamBO.getWarriors())) {
            warriorTeamBO.getWarriors().forEach(warrior -> {
                warrior.setScore(trainingRedisOps.getTrainedCount(warrior.getActorInfo().getUid()));
            });
        }
    }

    public ModelRankVO<WarriorTeamBO> teamRank(HttpEnvData dto) {
        activityConfigService.checkWithInTimeChannel(EVENT_CODE, dto.getUid());
        return trainingRedisOps.getTeamRank(dto.getUid(), PER_TRAIN_SCORE);
    }

    public ModelRankVO<WarriorBO> senderRank(HttpEnvData dto) {
        activityConfigService.checkWithInTimeChannel(EVENT_CODE, dto.getUid());
        return trainingRedisOps.getSenderRank(dto.getUid());
    }

    public ModelRankVO<WarriorBO> receiverRank(HttpEnvData dto) {
        activityConfigService.checkWithInTimeChannel(EVENT_CODE, dto.getUid());
        return trainingRedisOps.getReceiverRank(dto.getUid());
    }

    public List<ReceiveRewardVO> rewardHistory(HttpEnvData dto) {
        activityConfigService.checkWithInTimeChannel(EVENT_CODE, dto.getUid());
        return trainingRedisOps.getRewardHistory(dto.getUid());
    }


    /**
     * 邀请加入
     */
    public void inviteJoinTeam(HttpEnvData dto, String invitedUid) {
        AppConfigActivityData activityData = appConfigActivityService.getValidOneByEventCodeThrowWebException(EVENT_CODE);
        if (!appConfigActivityService.isWithInTime(activityData)) {
            throw new WebException(ActivityHttpCode.NOT_IN_ACTIVITY_DURATION);
        }
        if (!appConfigActivityService.isChannelSupported(activityData, dto.getChannel())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "event not support captain"));
        }
        ActorData invited = actorMgr.getActorData(invitedUid);
        if (ObjectUtils.isEmpty(invited)) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "invited not found"));
        }
        if (!appConfigActivityService.isChannelSupported(activityData, invited.getChannel())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "event not support invited"));
        }
        if (trainingRedisOps.hasBeenInvited(dto.getUid(), invitedUid)) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "already invited"));
        }
        String captainUid = trainingRedisOps.getCaptainUid(dto.getUid());
        if (!ObjectUtils.isEmpty(captainUid) && !captainUid.equals(dto.getUid())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "you are not captain"));
        }
        trainingRedisOps.saveInviteInfo(dto.getUid(), invitedUid);
    }

    /**
     * 加入队伍
     */
    public boolean joinTeam(HttpEnvData dto, String captainUid) {
        activityConfigService.checkWithInTimeChannel(EVENT_CODE, dto.getUid());
        if (!trainingRedisOps.hasBeenInvited(captainUid, dto.getUid())) {
            throw new WebException(null, HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "not been invited"));
        }
        List<WarriorBO> joinedWarriors = trainingRedisOps.addIntoTeam(captainUid, dto.getUid());
        ActorData captain = actorMgr.getActorData(captainUid);
        joinedWarriors.forEach(warrior -> {
            reportService.reportJoinTeam(warrior.getActorInfo().getUid(), dto.getChannel(), captain);
            giveOutRewardService.giveEventReward(warrior.getActorInfo()
                    .getUid(), REWARD_JOIN_TEAM, EVENT_CODE, TrainReportService.TrainItemChangeType.JOIN_TEAM);
            joinTeamNotice(warrior.getActorInfo().getUid());
        });
        log.info("join team, uid={} captain={}, joinedWarriors={}", dto.getUid(), JSON.toJSONString(captain), JSON.toJSONString(joinedWarriors));
        return !joinedWarriors.isEmpty();
    }

    /**
     * 驯龙
     */
    public List<RewardInfoData> train(DoTrainingDTO trainingDTO) {
        AppConfigActivityData activityData = appConfigActivityService.getValidOneByEventCodeThrowWebException(EVENT_CODE);
        if (!appConfigActivityService.isWithInTimeChannelSet(activityData, trainingDTO.getUid())) {
            throw new WebException(ActivityHttpCode.NOT_IN_ACTIVITY_DURATION);
        }
        if (trainingRedisOps.restTrainCount(trainingDTO.getUid()) < trainingDTO.getTrainCount()) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "rest train count not enough"));
        }
        // 训龙进度&抽奖
        long trainedCount = trainingRedisOps.getTrainedCount(trainingDTO.getUid());
        TrainingRedisOps.TrainCountRes trainCount = trainingRedisOps.train(trainingDTO.getUid(), trainingDTO.getTrainCount());
        long totalCount = trainCount.getAdditionalCount() + trainCount.getScoreCount();
        List<RewardInfoData> rewardInfoDataList = drawRewards(trainedCount, totalCount);
        log.info("trainedCount={}, reqTrainCount={}, realTrainCount={}, rewardInfoDataList={}", trainedCount, trainingDTO.getTrainCount(), trainCount, rewardInfoDataList);
        reportService.reportDraw(trainingDTO.getUid(), trainingDTO.getChannel(), (int) totalCount, rewardInfoDataList);

        // 抽取奖池礼物数量异常 告警
        if (rewardInfoDataList.size() > 10) {
            monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, String.format("驯龙抽取奖池礼物数量异常,请求抽奖次数: %s, 实际抽奖次数: %s, 发送奖励数量: %s", trainingDTO.getTrainCount(), trainCount, rewardInfoDataList.size()));
            return new ArrayList<>();
        }

        // 礼品下发&记录
        if (!ObjectUtils.isEmpty(rewardInfoDataList)) {
            ActorData actorData = actorMgr.getActorData(trainingDTO.getUid());
            List<ReceiveRewardVO> receiveRewardVOList = rewardInfoDataList.stream()
                    .map(rewardInfoData -> {
                        ReceiveRewardVO receiveRewardVO = new ReceiveRewardVO(rewardInfoData, actorData);
                        if (rewardInfoData.getType().equals(RewardItemType.GOLD)) {
                            receiveRewardVO.setNums(receiveRewardVO.getNums() * 40);
                        }
                        return receiveRewardVO;
                    })
                    .collect(Collectors.toList());
            giveOutRewardService.giveEventReward(trainingDTO.getUid(), rewardInfoDataList, EVENT_CODE, TrainReportService.TrainItemChangeType.DRAW); // 下发礼物
            trainingRedisOps.updateRotationRewardHistory(receiveRewardVOList); // 轮播更新
            trainingRedisOps.saveRewardHistory(trainingDTO.getUid(), receiveRewardVOList); // 个人收礼记录
//            List<RewardInfoData> bigReward = rewardInfoDataList.stream()
//                    .filter(rewardInfoData -> rewardInfoData.getType()
//                            .equals(RewardItemType.GIFT) && rewardInfoData.getDataId() == 666)
//                    .collect(Collectors.toList());
//            if (!ObjectUtils.isEmpty(bigReward)) {

//                rewardService.sendUniversalActivityPopMsg(trainingDTO.getUid(), bigReward, EVENT_URL, ActivityTypeEnum.EVENT_DRAGON_TRAINING_2502.getName(), "", "");
//            }
        }

        // 团队仅记录积分累计抽奖次数
        if (trainCount.getScoreCount() > 0) {
            // 团队进度
            String captainUid = trainingRedisOps.getCaptainUid(trainingDTO.getUid());
            if (!ObjectUtils.isEmpty(captainUid)) {
                // 团队积分
                trainingRedisOps.incTeamTrainedCount(captainUid, (int) trainCount.getScoreCount());
                // 进度奖励
                long teamTrainedCount = trainingRedisOps.getTeamTrainedCount(captainUid);
                long teamScoreBefore = teamTrainedCount * PER_TRAIN_SCORE;
                long teamScoreAfter = teamScoreBefore + trainCount.getScoreCount() * PER_TRAIN_SCORE;
                List<RewardInfoData> progressRewardList = getProgressReward(teamScoreBefore, teamScoreAfter);
                if (!ObjectUtils.isEmpty(progressRewardList)) {
                    log.info("teamScoreBefore={}, teamScoreAfter={}, progressRewardList={}", teamScoreBefore, teamScoreAfter, JSON.toJSONString(progressRewardList));
                    WarriorTeamBO warriorTeamBO = trainingRedisOps.getTeamInfo(captainUid);
                    warriorTeamBO.getWarriors().forEach(warriorBO -> {
                        giveOutRewardService.giveEventReward(warriorBO.getActorInfo()
                                .getUid(), progressRewardList, EVENT_CODE, TrainReportService.TrainItemChangeType.TEAM_CHECKPOINT); // 礼物下发
                        // 更新个人收礼历史
                        List<ReceiveRewardVO> receiveRewardVOList = rewardInfoDataList.stream()
                                .map(rewardInfoData -> {
                                    ReceiveRewardVO receiveRewardVO = new ReceiveRewardVO(rewardInfoData, actorMgr.getActorData(warriorBO.getActorInfo()
                                            .getUid()));
                                    if (rewardInfoData.getType().equals(RewardItemType.GOLD)) {
                                        receiveRewardVO.setNums(receiveRewardVO.getNums() * 40);
                                    }
                                    return receiveRewardVO;
                                })
                                .collect(Collectors.toList());
                        trainingRedisOps.saveRewardHistory(warriorBO.getActorInfo().getUid(), receiveRewardVOList);
                    });
                }
            }
        }
        rewardInfoDataList.forEach(rewardInfoData -> {
            if (rewardInfoData.getType().equals(RewardItemType.GOLD))
                rewardInfoData.setNums(rewardInfoData.getNums() * 40);
        });

        return rewardInfoDataList;
    }

    /**
     * 榜单结算
     */
//    @Async("asyncPool")
//    @Scheduled(cron = "0 0 0 4 3 ?", zone = "GMT+8:00")
    public void settle() {
        if (!masterUtils.isMaster()) {
            return;
        }
        settleRank();
    }

    private void settleRank() {
        AtomicInteger curRank = new AtomicInteger(0);
        // 送礼榜
        Map<String, Integer> topSender = trainingRedisOps.getSenderRank(3);
        topSender.forEach((key, value) -> giveOutRewardService.giveEventReward(key, getRewardByRank(curRank.incrementAndGet(), SENDER_RANK_REWARDS), EVENT_CODE, TrainReportService.TrainItemChangeType.SENDER_RANK));
        topSenderRankNotice(new ArrayList<>(topSender.keySet()));
        // 收礼榜
        curRank.set(0);
        Map<String, Integer> topReceiver = trainingRedisOps.getReceiverRank(3);
        topReceiver.forEach((key, value) -> giveOutRewardService.giveEventReward(key, getRewardByRank(curRank.incrementAndGet(), RECEIVER_RANK_REWARDS), EVENT_CODE, TrainReportService.TrainItemChangeType.RECEiVER_RANK));
        topReceiverRankNotice(new ArrayList<>(topReceiver.keySet()));
        // top1队伍
        WarriorTeamBO topTeam = trainingRedisOps.getTop1Team();
        if (ObjectUtils.isEmpty(topTeam) || ObjectUtils.isEmpty(topTeam.getWarriors())) {
            log.info("null topTeam={}", JSON.toJSONString(topTeam));
            return;
        }
        topTeam.getWarriors().forEach(warriorBO -> {
            giveOutRewardService.giveEventReward(warriorBO.getActorInfo()
                    .getUid(), TOP1_TEAM_REWARD, EVENT_CODE, TrainReportService.TrainItemChangeType.TEAM_RANK);
        });
        top1TeamSettle(topTeam.getName());
    }


    /**
     * 累计活动礼物, 增加可驯服次数
     */
    public void giftSendAction(SendGiftSuccessMsgData mqData) {
        if (appConfigActivityService.isWithInTimeChannelSet(EVENT_CODE, mqData.getUid()) && TRAINING_GIFT_ID_SET.contains(mqData.getGiftId())) {
            trainingRedisOps.saveTrainGiftCost(mqData.getUid(), mqData.getToUid(), mqData.getCost().intValue());
        }
    }

    public void topReceiverRankNotice(List<String> uidList) {
        StringBuilder globalNotice = new StringBuilder();
        globalNotice.append("\uD83D\uDE01 Congratulations to the follows get Top 3 in Dragon Training sender ranking");
        IntStream.range(0, uidList.size()).forEach(i -> {
            String rid = Optional.ofNullable(actorMgr.getActorData(uidList.get(i)))
                    .map(ActorData::getRid)
                    .map(Objects::toString)
                    .orElse("xxxxx");
            globalNotice.append("\tTop").append(i + 1).append(" ").append(rid);
        });
        globalNotice.append("\n\uD83D\uDE01 The reward has been issued~");
        activityGlobalNotice(globalNotice.toString());
    }

    public void topSenderRankNotice(List<String> uidList) {
        StringBuilder globalNotice = new StringBuilder();
        globalNotice.append("\uD83D\uDE01 Congratulations to the follows get Top 3 in Dragon Training receiver ranking");
        IntStream.range(0, uidList.size()).forEach(i -> {
            String rid = Optional.ofNullable(actorMgr.getActorData(uidList.get(i)))
                    .map(ActorData::getRid)
                    .map(Objects::toString)
                    .orElse("xxxxx");
            globalNotice.append("\tTop").append(i + 1).append(" ").append(rid);
        });
        globalNotice.append("\n\uD83D\uDE01 The reward has been issued~");
        activityGlobalNotice(globalNotice.toString());
    }

    public void top1TeamSettle(String name) {
        activityGlobalNotice("\n\uD83D\uDE01 Congratulations to the " + name + " Gain the highest dragon taming team " +
                "honor successful goals\n\uD83D\uDE01Team members received the highest honor award of the team" +
                "\n\uD83D\uDE01 The reward has been issued~\"");
    }

    public void joinTeamNotice(String uid) {
        activityOfficialNotice(uid, "Successfully formed a team and received a team reward，Fight the dragon with your teammates！");
    }

    private void activityOfficialNotice(String uid, String content) {
        officialNoticeService.sendOfficialNotice(uid, "Congratulations", content, NOTICE_IMG, EVENT_URL, "cde", DateHelper.getCurrentTime(), EVENT_CODE);
    }

    private void activityGlobalNotice(String content) {
        officialNoticeService.sendGlobalNotice("", content, NOTICE_IMG, EVENT_URL, "cde", DateHelper.getCurrentTime(), EVENT_CODE,
                LangIdConstant.ENGLISH, null, OfficialUserGroup.CHANNEL);
    }

    private List<RewardInfoData> getRewardByRank(int rank, Map<Integer, List<RewardInfoData>> rewardMap) {
        return rewardMap.entrySet()
                .stream()
                .filter(integerListEntry -> integerListEntry.getKey() >= rank)
                .findFirst()
                .map(Map.Entry::getValue)
                .orElse(null);
    }

    private List<RewardInfoData> getProgressReward(long scoreBefore, long scoreAfter) {
        if (scoreAfter == scoreBefore) {
            return Collections.emptyList();
        }
        return TEAM_REWARD.entrySet()
                .stream()
                .filter(entry -> entry.getKey() > scoreBefore && entry.getKey() <= scoreAfter)
                .flatMap(entry -> entry.getValue().stream())
                .collect(Collectors.toList());
    }

    private Long getNextPoolLevelScore(long score) {
        if (score < 100) {
            return 100L;
        }
        return Long.MAX_VALUE;
    }

    private int getCurrLevelScore(long score) {
        if (score < 100) {
            return 1;
        }
        return 2;
    }

    private List<RewardInfoData> drawRewards(Long trainedCount, Long trainCount) {
        List<AwardInfo> hitrewardList = new ArrayList<>();
        draw(trainedCount, trainCount, hitrewardList);

        return hitrewardList.stream()
                .map(data -> new RewardInfoData()
                        .setDataId(data.getDataId())
                        .setIcon(data.getAwardIcon())
                        .setName(data.getAwardName())
                        .setNums(data.getNums())
                        .setType(data.getAwardType())
                        .setUnitCost(data.getUnitPrice())
                        .setActivityId(EVENT_CODE)
                        .setActivityType(EVENT_CODE)
                        .setAwardDesc("1")
                )
                .collect(Collectors.toList());
    }

    private void draw(Long trainedCount, Long trainCount, List<AwardInfo> hitrewardList) {
        if (trainedCount >= 100) {
            hitrewardList.addAll(awardPoolService.drawAwardFromRedisPool(EVENT_CODE, "1", AWARD_POOL_CONFIGS_LEVEL_2, 1000, trainCount.intValue()));
            return;
        }
        if (trainedCount + trainCount <= 100) {
            hitrewardList.addAll(awardPoolService.drawAwardFromRedisPool(EVENT_CODE, "0", AWARD_POOL_CONFIGS_LEVEL_1, 1000, trainCount.intValue()));
            return;
        }
        int level1 = 100 - trainedCount.intValue();
        int level2 = trainedCount.intValue() + trainCount.intValue() - 100;
        hitrewardList.addAll(awardPoolService.drawAwardFromRedisPool(EVENT_CODE, "0", AWARD_POOL_CONFIGS_LEVEL_1, 1000, level1));
        hitrewardList.addAll(awardPoolService.drawAwardFromRedisPool(EVENT_CODE, "1", AWARD_POOL_CONFIGS_LEVEL_2, 1000, level2));
    }

}
