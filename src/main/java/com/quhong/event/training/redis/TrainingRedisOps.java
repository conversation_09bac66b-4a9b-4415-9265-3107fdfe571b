package com.quhong.event.training.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.dao.datas.ActorData;
import com.quhong.data.config.TaskConfig;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.enums.EventCode;
import com.quhong.event.training.data.bo.WarriorBO;
import com.quhong.event.training.data.bo.WarriorTeamBO;
import com.quhong.event.training.data.vo.ReceiveRewardVO;
import com.quhong.event.training.service.TrainReportService;
import com.quhong.players.ActorMgr;
import com.quhong.redis.base.ActivityCommonRedis;
import com.quhong.redis.base.currency.task.BaseEveryLimitRedis;
import com.quhong.utils.StringUtils;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class TrainingRedisOps {
    private static final int RANK_SIZE = 10;
    private static final int PER_TRAIN_COST = 8000 / 40; // 活动礼物每消耗8000得到一次训龙次数(客户端金币)
    private static final TaskConfig TRAIN_GIFT_COST_TASK_CONFIG = TaskConfig.builder()
            .checkParams(PER_TRAIN_COST)
            .ticket(1)
            .taskType(TaskTypeConstant.SEND_GIFT)
            .limit(-1)
            .build();


    private static final boolean PROD = ServerConfiguration.isProduct();
    private static final int EVENT_CODE = EventCode.EVENT_DRAGON_TRAINING_2502;
    private static final String TEST_ENV_KEY_SUFFIX = ":test3";

    private final ActivityCommonRedis commonRedis;
    private final BaseEveryLimitRedis baseEveryLimitRedis;
    private final ActivityCommonRedis activityCommonRedis;
    private final TrainReportService reportService;
    private final ActorMgr actorMgr;

    /**
     * 返还抽奖数量
     */
    private String getAdditionalTrainCountHashKey() {
        String key = EVENT_CODE + ":additional:count";
        return PROD ? key : key + TEST_ENV_KEY_SUFFIX;
    }

    /**
     * 获得奖励记录
     */
    private String getReceiveRewardHashKey() {
        String key = EVENT_CODE + ":receive_reward";
        return PROD ? key : key + TEST_ENV_KEY_SUFFIX;
    }

    /**
     * 收礼榜&送礼榜
     */
    private String getReceiverScoreZsetKey() {
        String key = EVENT_CODE + ":receiver_score";
        return PROD ? key : key + TEST_ENV_KEY_SUFFIX;
    }

    private String getSenderScoreZsetKey() {
        String key = EVENT_CODE + ":sender_score";
        return PROD ? key : key + TEST_ENV_KEY_SUFFIX;
    }

    /**
     * 组队关系Map，uid -> captainUid
     */
    private String getCaptainHashKey() {
        String key = EVENT_CODE + ":captain";
        return PROD ? key : key + TEST_ENV_KEY_SUFFIX;
    }

    /**
     * 团队信息Map, captainUid -> teamBO
     */
    private String getTeamInfoHashKey() {
        String key = EVENT_CODE + ":team_info";
        return PROD ? key : key + TEST_ENV_KEY_SUFFIX;
    }

    /**
     * 团队排名
     */
    private String getTeamRankZsetKey() {
        String key = EVENT_CODE + ":team_rank";
        return PROD ? key : key + TEST_ENV_KEY_SUFFIX;
    }

    /**
     * 邀请信息
     */
    private String getTeamInviteHashKey() {
        String key = EVENT_CODE + ":team_invite";
        return PROD ? key : key + TEST_ENV_KEY_SUFFIX;
    }

    /**
     * 轮播信息
     */
    private String getRotationRewardListKey() {
        String key = EVENT_CODE + ":rotation_reward";
        return PROD ? key : key + TEST_ENV_KEY_SUFFIX;
    }

    /**
     * 更新最近中奖轮播信息，中多个奖只插入价值最高的
     */
    public void updateRotationRewardHistory(List<ReceiveRewardVO> rewardList) {
        // 每个uid只取获得礼物单价最高进入轮播
        Map<String, List<ReceiveRewardVO>> rewardsByUid = rewardList.stream()
                .collect(Collectors.groupingBy(ReceiveRewardVO::getUid));

        List<String> bestRewards = rewardsByUid.values().stream()
                .map(rewards -> {
                    return rewards.stream()
                            .max(Comparator.comparingLong(ReceiveRewardVO::getUnitCost)
                                    .thenComparing(ReceiveRewardVO::getGiveOutTime))
                            .orElse(null);
                })
                .filter(Objects::nonNull)
                .map(JSON::toJSONString)
                .collect(Collectors.toList());

        // 轮播信息不需全部存储
        commonRedis.leftPushAllCommonListWithOverflow(getRotationRewardListKey(), bestRewards);
    }

    public List<ReceiveRewardVO> getRotationRewardHistory(int size) {
        if (size <= 0) return Collections.emptyList();
        List<String> dataStrList = commonRedis.getCommonListRecord(getRotationRewardListKey(), size);
        return dataStrList.stream()
                .map(dataStr -> JSON.parseObject(dataStr, ReceiveRewardVO.class))
                .collect(Collectors.toList());
    }

    /**
     * 奖励历史
     */
    public void saveRewardHistory(String uid, List<ReceiveRewardVO> rewardList) {
        String historyStr = commonRedis.getCommonHashStrValue(getReceiveRewardHashKey(), uid);
        if (!StringUtils.isEmpty(historyStr)) {
            rewardList.addAll(JSON.parseArray(historyStr, ReceiveRewardVO.class));
        }
        commonRedis.setCommonHashData(getReceiveRewardHashKey(), uid, JSON.toJSONString(rewardList));
    }

    public List<ReceiveRewardVO> getRewardHistory(String uid) {
        String historyStr = commonRedis.getCommonHashStrValue(getReceiveRewardHashKey(), uid);
        if (!StringUtils.isEmpty(historyStr)) {
            List<ReceiveRewardVO> history = JSON.parseArray(historyStr, ReceiveRewardVO.class);
            history.sort(Comparator.comparingLong(ReceiveRewardVO::getGiveOutTime).reversed());
            return history;
        }
        return Collections.emptyList();
    }

    /**
     * 是否被队长邀请
     */
    public boolean hasBeenInvited(String captainUid, String invitedUid) {
        if (StringUtils.isEmpty(captainUid) || StringUtils.isEmpty(invitedUid)) return false;
        String invitedListStr = commonRedis.getCommonHashStrValue(getTeamInviteHashKey(), captainUid);
        return invitedListStr.contains(invitedUid);
    }

    /**
     * 添加邀请记录
     */
    public void saveInviteInfo(String captainUid, String uid) {
        String invitedListStr = commonRedis.getCommonHashStrValue(getTeamInviteHashKey(), captainUid);
        commonRedis.setCommonHashData(getTeamInviteHashKey(), captainUid, invitedListStr + "-" + uid);
        log.info("saveInviteInfo captainUid={}, nowInviteStrList={}", captainUid, invitedListStr + "-" + uid);
    }

    /**
     * 队长id
     */
    public String getCaptainUid(String uid) {
        String captainUid = commonRedis.getCommonHashStrValue(getCaptainHashKey(), uid);
        if (ObjectUtils.isEmpty(captainUid)) return null;
        return captainUid;
    }

    public void setCaptainUid(String uid, String captainUid) {
        commonRedis.setCommonHashData(getCaptainHashKey(), uid, captainUid);
    }

    /**
     * 团队信息
     */
    public WarriorTeamBO getTeamInfo(String captainUid) {
        String teamInfoStr = commonRedis.getCommonHashStrValue(getTeamInfoHashKey(), captainUid);
        if (StringUtils.isEmpty(teamInfoStr)) return null;
        return JSON.parseObject(teamInfoStr, WarriorTeamBO.class);
    }

    /**
     * 加入队伍
     */
    public List<WarriorBO> addIntoTeam(String captainUid, String uid) {
        WarriorTeamBO warriorTeamBO = getTeamInfo(captainUid);
        List<WarriorBO> needAddList = new ArrayList<>();

        // 队伍建立
        if (StringUtils.isEmpty(warriorTeamBO)) {
            ActorData captain = actorMgr.getActorData(captainUid);
            if (ObjectUtils.isEmpty(captain)) {
                log.error("ActorData not exist, uid=" + captainUid);
                return Collections.emptyList();
            }
            warriorTeamBO = WarriorTeamBO.builder()
                    .captainUid(captainUid)
                    .name(captain.getName() + "'s team")
                    .warriors(new ArrayList<>())
                    .build();
            needAddList.add(new WarriorBO(captain));
        } else if (warriorTeamBO.getWarriors().size() >= 3) {
            log.error("Team full, captainUid={}, uid={}", captainUid, uid);
            return Collections.emptyList();
        }

        // 加入已存在队伍
        ActorData invitedActor = actorMgr.getActorData(uid);
        if (ObjectUtils.isEmpty(invitedActor)) {
            log.error("ActorData not exist, uid=" + uid);
            return Collections.emptyList();
        }

        // 禁止重复加入
        if (warriorTeamBO.getWarriors().stream().anyMatch(warriorBO -> warriorBO.getActorInfo().getUid().equals(uid))) {
            log.error("Already in team, captainUid={}, uid={}", captainUid, uid);
            return Collections.emptyList();
        }

        needAddList.add(new WarriorBO(invitedActor));
        warriorTeamBO.getWarriors().addAll(needAddList);
        log.info("join part={}, now team={}", JSON.toJSONString(captainUid), JSON.toJSONString(warriorTeamBO));
        commonRedis.setCommonHashData(getTeamInfoHashKey(), captainUid, JSON.toJSONString(warriorTeamBO));
        needAddList.forEach(warriorBO -> {
            setCaptainUid(warriorBO.getActorInfo().getUid(), captainUid);
        });
        return needAddList;
    }

    /**
     * 收礼榜&送礼榜
     */
    private void increaseReceiverScore(String uid, int score) {
        activityCommonRedis.incrCommonZSetRankingScoreNoTimeStamp(getReceiverScoreZsetKey(), uid, score);
    }

    private void increaseSenderScore(String uid, int score) {
        activityCommonRedis.incrCommonZSetRankingScoreNoTimeStamp(getSenderScoreZsetKey(), uid, score);
    }

    /**
     * 获取已驯服次数
     */
    public long getTrainedCount(String uid) {
        return baseEveryLimitRedis.claimedCount(uid, EVENT_CODE, TRAIN_GIFT_COST_TASK_CONFIG);
    }

    /**
     * 可驯服次数
     */
    public long restTrainCount(String uid) {
        long count = baseEveryLimitRedis.computeCanClaimCount(uid, EVENT_CODE, TRAIN_GIFT_COST_TASK_CONFIG);
        long additional = activityCommonRedis.getCommonHashValue(getAdditionalTrainCountHashKey(), uid);
        return count + additional;
    }

    /**
     * 驯服
     */
    public TrainCountRes train(String uid, int count) {
        if (count != 1 && count != 10) return new TrainCountRes();

        long additional = activityCommonRedis.getCommonHashValue(getAdditionalTrainCountHashKey(), uid);
        // 无返回次数则正常计算
        if (additional <= 0) {
            long trainCount = baseEveryLimitRedis.generateCurrCanGetRewards(uid, EVENT_CODE, TRAIN_GIFT_COST_TASK_CONFIG, count);
            return new TrainCountRes().setScoreCount(trainCount);
        }
        // 优先消耗返还次数
        if (count <= additional) { // 全部扣返还数量
            activityCommonRedis.incCommonHashNum(getAdditionalTrainCountHashKey(), uid, -count);
            return new TrainCountRes().setAdditionalCount(count);
        }
        // 部分扣返还次数 不够再从得分累计的计数扣
        activityCommonRedis.incCommonHashNum(getAdditionalTrainCountHashKey(), uid, -(int) additional);
        long scoreTrainCount = baseEveryLimitRedis.generateCurrCanGetRewards(uid, EVENT_CODE, TRAIN_GIFT_COST_TASK_CONFIG, (int) (count - additional));
        return new TrainCountRes().setScoreCount(scoreTrainCount).setAdditionalCount(additional);
    }

    @Data
    @Accessors(chain = true)
    public static class TrainCountRes {
        long scoreCount = 0;
        long additionalCount = 0;
    }

    /**
     * 团队榜添加驯服数
     */
    public void incTeamTrainedCount(String captainUid, int incCount) {
        activityCommonRedis.incrCommonZSetRankingScoreNoTimeStamp(getTeamRankZsetKey(), captainUid, incCount);
    }

    /**
     * 团队驯服次数
     */
    public long getTeamTrainedCount(String captainUid) {
        return activityCommonRedis.getCommonZSetRankingScore(getTeamRankZsetKey(), captainUid);
    }

    /**
     * 团队榜
     */
    public ModelRankVO<WarriorTeamBO> getTeamRank(String uid, long perTrainScore) {
        Map<String, Integer> captainUidHashRankNumMap = activityCommonRedis.getCommonRankingMap(getTeamRankZsetKey(), RANK_SIZE);

        ModelRankVO<WarriorTeamBO> modelRankVO = new ModelRankVO<>();
        modelRankVO.setSelf(null);
        modelRankVO.setRankList(new ArrayList<>());
        String captainUid = getCaptainUid(uid);

        AtomicInteger rankNum = new AtomicInteger();
        AtomicLong lastRankScore = new AtomicLong(0L);
        captainUidHashRankNumMap.forEach((key, score) -> {
            WarriorTeamBO teamWarrior = getTeamInfo(key);
            // 距上一名的差距
            long rankUpReqScore = lastRankScore.get() - score * perTrainScore;
            if (rankUpReqScore < 0) rankUpReqScore = 0;
            lastRankScore.set(score * perTrainScore);
            teamWarrior.setRankUpReqScore(rankUpReqScore);
            teamWarrior.setScore(score.longValue() * perTrainScore);
            teamWarrior.setRankNum(String.valueOf(rankNum.incrementAndGet()));
            if (!ObjectUtils.isEmpty(captainUid) && key.equals(captainUid)) {
                modelRankVO.setSelf(teamWarrior);
                modelRankVO.getSelf().getWarriors().forEach(warrior -> {
                    warrior.setScore(getTrainedCount(warrior.getActorInfo().getUid()));
                });
            }
            modelRankVO.getRankList().add(teamWarrior);
        });

        if (ObjectUtils.isEmpty(modelRankVO.getSelf())) {
            if (!ObjectUtils.isEmpty(captainUid)) {
                int selfRankNum = activityCommonRedis.getCommonZSetRank(getTeamRankZsetKey(), captainUid);
                WarriorTeamBO teamWarrior = getTeamInfo(captainUid);
                if (selfRankNum > 0) {
                    Map<String, Integer> rangeRank = activityCommonRedis.getCommonRankingMapInRankRange(getTeamRankZsetKey(), selfRankNum - 1, selfRankNum);
                    AtomicReference<Long> lastScore = new AtomicReference<>(0L);
                    rangeRank.forEach((key, score) -> {
                        lastScore.set(score.longValue());
                        if (key.equals(captainUid)) {
                            teamWarrior.setScore(score * perTrainScore);
                            teamWarrior.setRankUpReqScore(lastScore.get() - score);
                            teamWarrior.setRankNum(String.valueOf(selfRankNum));
                        }
                    });
                } else {
                    teamWarrior.setScore(0L);
                    teamWarrior.setRankUpReqScore(0L);
                    teamWarrior.setWarriors(new ArrayList<>());
                    teamWarrior.setRankNum("999+");
                }
                modelRankVO.setSelf(teamWarrior);
                modelRankVO.getSelf().getWarriors().forEach(warrior -> {
                    warrior.setScore(getTrainedCount(warrior.getActorInfo().getUid()));
                });
            }
        }

        return modelRankVO;
    }

    public WarriorTeamBO getTop1Team() {
        Map<String, Integer> captainUidHashRankNumMap = activityCommonRedis.getCommonRankingMap(getTeamRankZsetKey(), 1);
        if (ObjectUtils.isEmpty(captainUidHashRankNumMap)) return null;
        String topCaptainUid = captainUidHashRankNumMap.entrySet().stream().findFirst().get().getKey();
        return getTeamInfo(topCaptainUid);
    }

    /**
     * 收礼榜
     */
    public ModelRankVO<WarriorBO> getReceiverRank(String uid) {
        return getWarriorRank(uid, getReceiverScoreZsetKey());
    }

    public Map<String, Integer> getReceiverRank(int size) {
        return getWarriorRank(size, getReceiverScoreZsetKey());
    }

    /**
     * 送礼榜
     */
    public ModelRankVO<WarriorBO> getSenderRank(String uid) {
        return getWarriorRank(uid, getSenderScoreZsetKey());
    }

    public Map<String, Integer> getSenderRank(int size) {
        return getWarriorRank(size, getSenderScoreZsetKey());
    }

    private ModelRankVO<WarriorBO> getWarriorRank(String uid, String redisKey) {
        Map<String, Integer> uidHashScoreMap = activityCommonRedis.getCommonRankingMap(redisKey, RANK_SIZE);

        ModelRankVO<WarriorBO> rankVO = new ModelRankVO<>();
        rankVO.setRankList(new ArrayList<>());
        rankVO.setSelf(null);
        AtomicInteger rankNum = new AtomicInteger();
        log.info("redisKey={}, uidHashScoreMap={}", redisKey, JSON.toJSONString(uidHashScoreMap));
        uidHashScoreMap.forEach((key, score) -> {
            ActorData actorData = actorMgr.getActorData(key);
            WarriorBO warriorBO = new WarriorBO(actorData);
            warriorBO.setScore(score.longValue());
            warriorBO.setRankNum(String.valueOf(rankNum.incrementAndGet()));
            rankVO.getRankList().add(warriorBO);
            if (key.equals(uid)) {
                rankVO.setSelf(warriorBO);
            }
        });

        if (ObjectUtils.isEmpty(rankVO.getSelf())) {
            int selfRankNum = activityCommonRedis.getCommonZSetRank(redisKey, uid);
            ActorData selfActorData = actorMgr.getActorData(uid);
            WarriorBO warriorBO = new WarriorBO(selfActorData);
            if (selfRankNum != 0) {
                int selfScore = activityCommonRedis.getCommonZSetRankingScore(redisKey, uid);
                warriorBO.setScore((long) selfScore);
                warriorBO.setRankNum(String.valueOf(selfRankNum));
            } else {
                warriorBO.setRankNum("999+");
                warriorBO.setScore(0L);
            }
            rankVO.setSelf(warriorBO);
        }

        return rankVO;
    }

    private Map<String, Integer> getWarriorRank(int size, String redisKey) {
        return activityCommonRedis.getCommonRankingMap(redisKey, size);
    }

    /**
     * 赠送活动礼物更新数据
     * 1.累计送礼&收礼榜
     * 2.换算可驯服次数
     */
    public void saveTrainGiftCost(String fromUid, String toUid, int cost) {
        if (cost > 0) {
            increaseSenderScore(fromUid, cost); // 送礼榜
            increaseReceiverScore(toUid, cost); // 收礼榜
            long beforeCount = restTrainCount(fromUid);
            baseEveryLimitRedis.increaseCount(fromUid, EVENT_CODE, cost, TRAIN_GIFT_COST_TASK_CONFIG); // 更新可驯服次数
            long afterCount = restTrainCount(fromUid);
            if (afterCount - beforeCount > 0) {
                reportService.reportGetDrawCount(fromUid, "cde", (int) (afterCount - beforeCount));
            }
        }
    }

}
