package com.quhong.event.magic.lamp.v2506;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpRes;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.dto.QueryRankDTO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * V2506/神灯挑战活动
 * <AUTHOR>
 * @since 2025-06-10 09:49
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/activity/magic_lamp/v2506")
public class EventMagicLampController {
    private final EventMagicLampService eventMagicLampService;

    /**
     * 神灯活动页面初始化
     */
    @GetMapping("/pageInit")
    public HttpRes<EventMagicLampService.PageInitVO> pageInit(CommonDTO dto) {
        log.info("pre magic lamp pageInit,dto={}", JSON.toJSONString(dto));
        dto.checkParams();
        EventMagicLampService.PageInitVO vo = eventMagicLampService.pageInit(dto);
        log.debug("end magic lamp pageInit result:{}", JSON.toJSONString(vo));
        return HttpRes.success(vo);
    }

    /**
     * 神灯活动排行榜 两个榜单都为金币数量
     */
    @GetMapping("/rank")
    public HttpRes<ModelRankVO<RankRowVO>> rank(QueryRankDTO dto) {
        log.info("pre magic lamp rank get, dto={}", JSON.toJSONString(dto));
        dto.checkParams();
        ModelRankVO<RankRowVO> vo = eventMagicLampService.queryRank(dto);
        log.debug("end magic lamp rank get, vo={}", JSON.toJSONString(vo));
        return HttpRes.success(vo);
    }

    /**
     * 神灯活动排行榜测试结算
     * @param rankType 排行榜类型 1:送礼排行榜 2:收礼排行榜
     */
    @GetMapping("/settlement")
    public HttpRes<Boolean> settlement(Integer rankType) {
        log.info("pre magic lamp settlement, rankType={}", rankType);
        if (ServerConfiguration.isProduct()) {
            return HttpRes.success(false);
        }
        String rankName = "Sender ranking";
        List<RewardTaskConfig> rewards = EventMagicLampService.SENDER_RANK_AWARDS;
        if (new Integer(2).equals(rankType)) {
            rankName = "Receiver ranking";
            rewards = EventMagicLampService.RECEIVER_RANK_AWARDS;
        }
        eventMagicLampService.rankRewards(EventMagicLampService.EVENT_CODE, rankType, rankName, rewards);
        return HttpRes.success(true);
    }
}
