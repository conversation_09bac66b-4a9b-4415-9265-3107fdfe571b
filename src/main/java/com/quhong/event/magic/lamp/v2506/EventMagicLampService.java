package com.quhong.event.magic.lamp.v2506;

import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.ActorExternalDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.config.TaskConfig;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.dto.QueryRankDTO;
import com.quhong.data.vo.BasePageInitVO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.enums.EventCode;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.redis.base.reward.BaseNodeRewardRedis;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.common.EventBaseRankService;
import com.quhong.service.common.RewardService;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * V2506/神灯活动
 *
 * <AUTHOR>
 * @since 2025-06-10 09:49
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EventMagicLampService {
    /**
     * 事件码
     */
    public static final Integer EVENT_CODE = EventCode.EVENT_MAGIC_LAMP_2506;

    /**
     * 送礼榜排行榜类型
     */
    public static final Integer SEND_RANK_TYPE = 1;

    /**
     * 收礼榜排行榜类型
     */
    public static final Integer RECEIVE_RANK_TYPE = 2;


    /**
     * 是否生产环境
     */
    public static final boolean PROD = ServerConfiguration.isProduct();

    /**
     * 活动URL
     */
    public static final String EVENT_URL = PROD ? "https://videochat.kissu.site/Magic_Lamp_Challenge" : "https://testvideochat.kissu.site/Magic_Lamp_Challenge";

    /**
     * 通知图片
     */
    public static final String NOTICE_IMG = "https://statics.kissu.mobi/event/lamp/guanfangtongzhi.jpg";

    public static final List<RewardTaskConfig> SENDER_RANK_AWARDS = new ArrayList<RewardTaskConfig>() {{
        // 送礼榜第1名
        RewardTaskConfig top1Config = new RewardTaskConfig().setCheckParams(1).setAwardsKey("Magic_Lamp_Challenge_Top1");
        add(top1Config);

        // 送礼榜第2名
        RewardTaskConfig top2Config = new RewardTaskConfig().setCheckParams(2).setAwardsKey("Magic_Lamp_Challenge_Top2");
        add(top2Config);

        // 送礼榜第3名
        RewardTaskConfig top3Config = new RewardTaskConfig().setCheckParams(3).setAwardsKey("Magic_Lamp_Challenge_Top3");
        add(top3Config);

        // 送礼榜第4~10名
        RewardTaskConfig top4Config = new RewardTaskConfig().setCheckParams(4).setAwardsKey("Magic_Lamp_Challenge_Top4_10");
        add(top4Config);

    }};

    public static final List<RewardTaskConfig> RECEIVER_RANK_AWARDS = new ArrayList<RewardTaskConfig>() {{
        // 收礼榜第1名
        RewardTaskConfig top1Config = new RewardTaskConfig().setCheckParams(1).setAwardsKey("Magic_Lamp_Challenge_Receiver_Top1");
        add(top1Config);
        // 收礼榜第2名
        RewardTaskConfig top2Config = new RewardTaskConfig().setCheckParams(2).setAwardsKey("Magic_Lamp_Challenge_Receiver_Top2");
        add(top2Config);
        // 收礼榜第3名
        RewardTaskConfig top3Config = new RewardTaskConfig().setCheckParams(3).setAwardsKey("Magic_Lamp_Challenge_Receiver_Top3");
        add(top3Config);
        // 收礼榜第4~10名
        RewardTaskConfig top4Config = new RewardTaskConfig().setCheckParams(4).setAwardsKey("Magic_Lamp_Challenge_Receiver_Top4_10");
        add(top4Config);
    }};

    /**
     * 节点任务奖励配置
     */
    public static final List<RewardTaskConfig> NODE_TASK_CONFIGS = new ArrayList<RewardTaskConfig>() {{
        // 赠送1个神灯节点
        add(new RewardTaskConfig()
                .setTaskName("1")
                .setTaskType(TaskConfig.TaskTypeConstant.SEND_GIFT)
                .setCheckParams(1)
                .setAwardsKey("Magic_Lamp_Challenge_Pack1")
                .setNotice("\uD83D\uDCCC 1 Magic Lamp Sent: \uD83C\uDF89 Great start! You’ve gifted 1 Magic Lamp and earned your first reward. " +
                        "Keep shining — more treasures await you ahead! ✨")
        );

        // 赠送5个神灯节点
        add(new RewardTaskConfig()
                .setTaskName("2")
                .setTaskType(TaskConfig.TaskTypeConstant.SEND_GIFT)
                .setCheckParams(2)
                .setAwardsKey("Magic_Lamp_Challenge_Pack2")
                .setNotice("\uD83D\uDCCC 2 Magic Lamps Sent: \uD83C\uDF8A Well done! You’ve sent 2 Magic Lamps and unlocked a special bonus! " +
                        "Stay in the game — the next reward is within reach! \uD83D\uDD25")
        );

        // 赠送10个神灯节点
        add(new RewardTaskConfig()
                .setTaskName("3")
                .setTaskType(TaskConfig.TaskTypeConstant.SEND_GIFT)
                .setCheckParams(50)
                .setAwardsKey("Magic_Lamp_Challenge_Pack3")
                .setNotice("\uD83D\uDCCC 50 Magic Lamps Sent: \uD83E\uDD73 Impressive! 50 Magic Lamps gifted and you’ve claimed a Lucky Gift! " +
                        "Your generosity is lighting up the leaderboard — aim higher for greater fortune! \uD83C\uDF1F")
        );

        // 赠送25个神灯节点
        add(new RewardTaskConfig()
                .setTaskName("4")
                .setTaskType(TaskConfig.TaskTypeConstant.SEND_GIFT)
                .setCheckParams(100)
                .setAwardsKey("Magic_Lamp_Challenge_Pack4")
                .setNotice("\uD83D\uDCCC 100 Magic Lamps Sent: \uD83D\uDC51 Magic Lamp Master! " +
                        "You’ve gifted 100 Magic Lamps and secured an epic reward! Glory and fortune await — check your leaderboard rank now! \uD83D\uDCA5")
        );

    }};

    private final AppConfigActivityDao appConfigActivityDao;
    private final EventBaseRankService eventBaseRankService;
    private final RewardService rewardService;
    private final ActorMgr actorMgr;
    private final BaseNodeRewardRedis baseNodeRewardRedis;
    private final ActorExternalDao actorExternalDao;
    private final OfficialNoticeService officialNoticeService;

    /**
     * 神灯活动页面初始化VO
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    public static class PageInitVO extends BasePageInitVO {
        /**
         * 用户累计送出神灯数量
         */
        private Long sentGiftCount = 0L;
    }

    public PageInitVO pageInit(CommonDTO dto) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());

        PageInitVO vo = new PageInitVO();
        vo.setStartSec(configData.getStartTime());
        vo.setEndSec(configData.getEndTime());

        // 获取用户累计送出神灯数量
        fillSentGiftCount(dto, vo);

        return vo;
    }

    private void fillSentGiftCount(CommonDTO dto, PageInitVO vo) {
        String uid = dto.getUid();
        if (ObjectUtils.isEmpty(uid)) {
            return;
        }
        ActorData actorData = actorMgr.getActorData(uid);
        if (actorData == null) {
            return;
        }
        // 获取用户在该活动中累计送出的神灯数量
        String giftCountKey = String.valueOf(EVENT_CODE);
        Long sentCount = baseNodeRewardRedis.getCount(dto.getUid(),giftCountKey).longValue();
        vo.setSentGiftCount(sentCount);
    }

    public ModelRankVO<RankRowVO> queryRank(QueryRankDTO dto) {
        ActorData currActor = actorMgr.getActorData(dto.getUid());
        String rankKey = eventBaseRankService.zsetRankKey(dto.getEventType(), dto.getRankType());
        return eventBaseRankService.rank(currActor, rankKey);
    }

    public void rankRewards(Integer eventCode, Integer rankType, String rankName, List<RewardTaskConfig> rankRewards) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventCode);
        String rankKey = eventBaseRankService.zsetRankKey(configData.getActivityCode(), rankType);

        // 直接实现排行榜奖励发放逻辑
        rankRewardsWithAwardsKey(configData, rankKey, rankName, rankRewards);
    }

    /**
     * 使用礼包key的方式发放排行榜奖励
     */
    private void rankRewardsWithAwardsKey(AppConfigActivityData configData, String rankKey, String rankName, List<RewardTaskConfig> rankRewards) {
        String noticeFormatter = "\uD83D\uDCE3 Ranking Magic Lamp #rankName Results Are Out!\n" +
                "A huge congratulations to this week’s shining stars \uD83C\uDF1F — the TOP 10 Magic Lamp Masters!\n" +
                "#content" +
                "\n" +
                "\uD83C\uDF81 Rewards include:\n" +
                "✨ Massive Coins\n" +
                "✨ Magic Carpet Ride\n" +
                "✨ Golden Lamp Avatar Frame\n" +
                "✨ Exclusive 2025 Top Badge\n" +
                "\n" +
                "\uD83C\uDF89 Rewards have been sent to your accounts.\n" +
                "A new week, a fresh challenge — keep gifting, rise to the top and claim your glory! \uD83D\uDE80";
        EventBaseRankService.RankRewardsDTO rankRewardsDTO = new EventBaseRankService.RankRewardsDTO(configData, rankKey, rankRewards, rankName, "",
                NOTICE_IMG, EVENT_URL, true, noticeFormatter);
        eventBaseRankService.rankRewards(rankRewardsDTO);
    }

    /**
     * 处理送礼MQ消息
     */
    public void sendGiftAction(SendGiftSuccessMsgData msgData) {
        // 检查活动状态
        AppConfigActivityData activityData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        long now = DateHelper.getCurrTime();
        if (now < activityData.getStartTime() || now > activityData.getEndTime()) {
            log.info("Magic Lamp activity not in progress, not in event time");
            return;
        }
        // 检查是否是神灯礼物
        if (!activityData.getDataId().contains(msgData.getGiftId().intValue())) {
            return;
        }

        String senderUid = msgData.getUid();
        String receiverUid = msgData.getToUid();
        Integer giftNum = msgData.getNum();
        double cost = msgData.fetchRealCost();

        // 更新送礼榜
        dealSenderLogic(senderUid, activityData, cost, giftNum);

        // 更新收礼榜
        dealReceiverLogic(senderUid, activityData, receiverUid, cost);
    }

    private void dealReceiverLogic(String senderUid, AppConfigActivityData activityData, String receiverUid, double cost) {
        if (checkUserLimit(senderUid, activityData)) return;
        String receiveRankKey = eventBaseRankService.zsetRankKey(activityData.getActivityCode(), RECEIVE_RANK_TYPE);
        eventBaseRankService.rankValueIncrease(receiveRankKey, receiverUid, cost);
    }

    private void dealSenderLogic(String senderUid, AppConfigActivityData activityData, double cost, Integer giftNum) {
        if (checkUserLimit(senderUid, activityData)) return;
        String sendRankKey = eventBaseRankService.zsetRankKey(activityData.getActivityCode(), SEND_RANK_TYPE);
        eventBaseRankService.rankValueIncrease(sendRankKey, senderUid, cost);
        // 处理发送者的节点任务奖励
        handleNodeTaskRewards(senderUid, giftNum, activityData);
    }

    private boolean checkUserLimit(String senderUid, AppConfigActivityData activityData) {
        ActorData currActor = actorMgr.getActorData(senderUid);
        if (currActor == null) {
            return true;
        }
        if (!activityData.getChannel().contains(currActor.getChannel())) {
            return true;
        }
        if (actorExternalDao.isTester(senderUid)) {
            return true;
        }
        return false;
    }

    /**
     * 处理节点任务奖励
     */
    private void handleNodeTaskRewards(String uid, Integer giftNum, AppConfigActivityData activityData) {
        ActorData currActor = actorMgr.getActorData(uid);
        if (currActor == null) {
            return;
        }

        // 更新累计送礼数量并获取可以领取的任务配置
        List<RewardTaskConfig> configs = baseNodeRewardRedis.increaseAndGetCanGetTaskConfig(
                activityData.getActivityCode().toString(), uid, giftNum, NODE_TASK_CONFIGS);

        if (ObjectUtils.isEmpty(configs)) {
            return;
        }

        // 发放节点任务奖励
        configs.forEach(config -> sendNodeTaskReward(currActor, config, activityData));
    }

    /**
     * 发送节点任务奖励
     */
    private void sendNodeTaskReward(ActorData currActor, RewardTaskConfig config, AppConfigActivityData activityData) {
        String uid = currActor.getUid();
        // 构造礼包key
        String awardsKey = config.getAwardsKey();

        // 使用RewardService发放奖励
        RewardService.GiveAwardsKeyDTO dto = new RewardService.GiveAwardsKeyDTO()
                .setUid(uid)
                .setAwardsKey(awardsKey)
                .setEventCode(activityData.getActivityCode())
                .setChangeDesc(config.getTaskName())
                .setOperator("event_task_auto_reward")
                .setEventUrl(EVENT_URL);
        rewardService.giveAwardsKeyReward(dto);
        //官方消息
        int fixTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0,50);
        officialNoticeService.sendOfficialNotice(uid, "Congratulations", config.getNotice(), NOTICE_IMG, EVENT_URL,
                currActor.getChannel(), fixTime, activityData.getActivityCode());


    }
}
