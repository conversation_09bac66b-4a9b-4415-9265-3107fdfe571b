package com.quhong.event.recharge.node.task.v2505;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 充值节点任务页面初始化VO
 *
 * <AUTHOR>
 * @since 2025-05-12 16:21
 */
@Data
@Accessors(chain = true)
public class PageInitVO {
    /**
     * 活动开始时间（秒）
     */
    private Long startTime;

    /**
     * 活动结束时间（秒）
     */
    private Long endTime;

    /**
     * 已充值金币数
     */
    private Long rechargeCoins = 0L;
    /**
     * 最近30条领奖记录
     */
    private List<RewardRecordVO> last30History;

}
