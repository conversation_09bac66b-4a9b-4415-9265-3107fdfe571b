package com.quhong.event.recharge.node.task.v2505;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpRes;
import com.quhong.data.dto.CommonDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * V2505/充值节点任务活动
 *
 * <AUTHOR>
 * @since 2025-05-12 16:15
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("${server.baseurl}/recharge/node_task/v2505")
public class EventRechargeNodeTaskController {
    private final EventRechargeNodeTaskService eventRechargeNodeTaskService;

    /**
     * 页面初始化接口
     *
     * @param dto 请求参数
     * @return 页面初始化数据
     */
    @GetMapping("/page_init")
    public HttpRes<PageInitVO> pageInit(CommonDTO dto) {
        log.info("pre page_init, dto={}", JSON.toJSONString(dto));
        dto.checkParams();
        PageInitVO vo = eventRechargeNodeTaskService.pageInit(dto);
        log.info("end page_init, vo={}", JSON.toJSONString(vo));
        return HttpRes.success(vo);
    }
}
