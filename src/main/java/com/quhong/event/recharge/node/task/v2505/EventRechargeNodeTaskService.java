package com.quhong.event.recharge.node.task.v2505;

import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.money.MoneyChangeActionConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.MoneyDetailData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.dto.CommonDTO;
import com.quhong.enums.CurrencyEnum;
import com.quhong.enums.EventCode;
import com.quhong.enums.RewardItemType;
import com.quhong.moderatoin.ModerationService;
import com.quhong.players.ActorMgr;
import com.quhong.redis.base.reward.BaseNodeRewardRedis;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.activity.AppConfigActivityService;
import com.quhong.service.common.EventBaseRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import static com.quhong.service.activity.recharge.draw.RechargeDrawService.ACT_TYPE_SET;

/**
 * 充值节点任务服务
 *
 * <AUTHOR>
 * @since 2025-05-12 16:19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EventRechargeNodeTaskService {
    /**
     * 活动编码
     */
    public static final int EVENT_CODE = EventCode.EVENT_RECHARGE_NODE_TASK_2505;

    /**
     * 是否生产环境
     */
    public static final boolean PROD = ServerConfiguration.isProduct();

    /**
     * 通知图片
     */
    public static final String NOTICE_IMG = "https://statics.kissu.mobi/Event/car/513/QHbanner.jpg";

    /**
     * 活动URL
     */
    public static final String EVENT_URL = PROD ? "https://videochat.kissu.site/online_recharge_202505" : "https://testvideochat.kissu.site/online_recharge_202505";

    /**
     * 节点奖励配置
     */
    public static final List<RewardTaskConfig> NODE_REWARD_CONFIGS = new ArrayList<RewardTaskConfig>() {{
        // 充值10000金币节点
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, 612, 0));
        }};
        add(new RewardTaskConfig()
                .setTaskName("Reward Lv1")
                .setTaskType(TaskTypeConstant.REAL_RECHARGE_GOLD)
                .setCheckParams(250)
                .setRewards(rewards1)
                .setNotice("Congratulations!\n" +
                        "In the  [Online Recharge Bonus]  event, you’ve reached 10,000 coins and unlocked:\n" +
                        "\uD83C\uDF81400 Coins\n" +
                        "\uD83C\uDF81 [Tell him] ×1\n" +
                        "Claim your rewards now and express yourself in style!")
        );

        // 充值50000金币节点
        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 25, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 205 : 60, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.BUBBLE_FRAME, PROD ? 392 : 117, 0));
        }};
        add(new RewardTaskConfig()
                .setTaskName("Reward Lv2")
                .setTaskType(TaskTypeConstant.REAL_RECHARGE_GOLD)
                .setCheckParams(1250)
                .setRewards(rewards2)
                .setNotice("Awesome job!\n" +
                        "In the [Online Recharge Bonus] event, you’ve accumulated 50,000 coins and earned:\n" +
                        "\uD83C\uDF81 1,000 Coins\n" +
                        "\uD83C\uDF81 [Super Gamer] Avatar Frame ×7 days\n" +
                        "\uD83C\uDF81 [Confession Pop] Bubble Frame ×7 days\n" +
                        "Show off your elite status and keep shining!")
        );

        // 充值300000金币节点
        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 300, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 5, RewardItemType.GIFT, PROD ? 2077 : 200, 3));
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.LEVEL_SCORE, 3, 0));
        }};
        add(new RewardTaskConfig()
                .setTaskName("Reward Lv3")
                .setTaskType(TaskTypeConstant.REAL_RECHARGE_GOLD)
                .setCheckParams(7500)
                .setRewards(rewards3)
                .setNotice("Well done, legend!\n" +
                        "Through the [Online Recharge Bonus] event, you reached 300,000 coins and won:\n" +
                        "\uD83C\uDF81 12,000 Coins\n" +
                        "\uD83C\uDF81 [Lucky Jackpot] Gift ×5\n" +
                        "\uD83C\uDF81 5,000 Points\n" +
                        "Your luck’s on fire — spin and level up now!")
        );

        // 充值800000金币节点
        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 253 : 66, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.LORD_DAYS, 5, 0));
        }};
        add(new RewardTaskConfig()
                .setTaskName("Reward Lv4")
                .setTaskType(TaskTypeConstant.REAL_RECHARGE_GOLD)
                .setCheckParams(20000)
                .setRewards(rewards4)
                .setNotice("Superstar status unlocked!\n" +
                        "In the [Online Recharge Bonus] event, you’ve crossed 800,000 coins and received:\n" +
                        "\uD83C\uDF81 40,000 Coins\n" +
                        "\uD83C\uDF81 [Meet Romance] car ×7 days\n" +
                        "\uD83C\uDF81 [Duke]×7 days\n" +
                        "Romance and prestige await you — ride in style!")
        );

        // 充值1600000金币节点
        List<RewardInfoData> rewards5 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2400, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 391 : 67, 0));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.LORD_DAYS, 6, 0));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90089 : 90003, 0));
            add(new RewardInfoData(EVENT_CODE, 2, RewardItemType.GIFT, 2063, 200));
        }};
        add(new RewardTaskConfig()
                .setTaskName("Reward Lv5")
                .setTaskType(TaskTypeConstant.REAL_RECHARGE_GOLD)
                .setCheckParams(40000)
                .setRewards(rewards5)
                .setNotice("Champion of champions!\n" +
                        "By achieving 1,600,000 coins in the Online Recharge Bonus event, you are rewarded with:\n" +
                        "\uD83C\uDF81 96,000 Coins\n" +
                        "\uD83C\uDF81 [Fire Dragon] car ×15days\n" +
                        "\uD83C\uDF81 [King]×15 days\n" +
                        "\uD83C\uDF81 [Monopoly] Medal (Permanent)\n" +
                        "\uD83C\uDF81 [Love You (8K)] Gift ×2\n" +
                        "Claim your crown and rule the stage!")
        );


    }};


    private final ActorMgr actorMgr;
    private final AppConfigActivityService appConfigActivityService;
    private final BaseNodeRewardRedis baseNodeRewardRedis;
    private final GiveOutRewardService giveOutRewardService;
    private final OfficialNoticeService officialNoticeService;
    private final EventBaseRecordService eventBaseRecordService;
    private final ModerationService moderationService;

    private String lastHistoryKey(Integer eventCode) {
        return eventBaseRecordService.listRecordKey(eventCode.toString());
    }

    /**
     * 页面初始化
     *
     * @return 页面初始化数据
     */
    public PageInitVO pageInit(CommonDTO dto) {
        // 获取用户数据
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());

        // 获取活动配置
        AppConfigActivityData activityData = appConfigActivityService.getValidOneByEventCodeThrowWebException(EVENT_CODE);

        // 创建返回对象
        PageInitVO vo = new PageInitVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());

        // 获取最近30条领奖记录
        String lastHistoryKey = lastHistoryKey(dto.getEventType());
        List<RewardRecordVO> histories = eventBaseRecordService.lastHistory(lastHistoryKey, RewardRecordVO.class, 30);
        histories.sort(Comparator.comparingLong(RewardRecordVO::getCtime));
        vo.setLast30History(histories);

        // 检查活动是否在进行中
        long now = DateHelper.getCurrTime();
        if (now < activityData.getStartTime() || now > activityData.getEndTime() ||
                !appConfigActivityService.isChannelSupported(activityData, currActor.getChannel())) {
            // 活动未开始或已结束或渠道不支持，返回基本信息
            return vo;
        }

        // 获取用户累计充值金额
        BigDecimal totalRechargeCoins = baseNodeRewardRedis.getCount(dto.getUid(), String.valueOf(EVENT_CODE));
        vo.setRechargeCoins(totalRechargeCoins.longValue());

        return vo;
    }

    /**
     * 处理金币变动消息
     *
     * @param mqData 金币变动数据
     */
    public void moneyDetailAction(MoneyDetailData mqData) {
        if (mqData.getSingleAction() != MoneyChangeActionConstant.ACTION_ADD) {
            return;
        }
        if (!CurrencyEnum.CURRENCY1.getCurrencyCode().equals(mqData.getCurrencyCode())) {
            return;
        }
        if (mqData.getSingleChange() == null || mqData.getSingleChange() < 1) {
            return;
        }
        if (!ACT_TYPE_SET.contains(mqData.getActType())) {
            return;
        }
        handleRechargeEvent(mqData.getUserid(), mqData.fetchRealSingleChange());
    }

    /**
     * 处理充值事件
     *
     * @param uid    用户ID
     * @param amount 充值金额(金币)
     */
    public void handleRechargeEvent(String uid, double amount) {
        // 检查活动状态
        ActorData currActor = actorMgr.getCurrActorData(uid);
        AppConfigActivityData activityData = appConfigActivityService.getValidOneByEventCodeThrowWebException(EVENT_CODE);
        if (!appConfigActivityService.isChannelSupported(activityData, currActor.getChannel())) {
            return;
        }
        long now = DateHelper.getCurrTime();
        if (now < activityData.getStartTime() || now > activityData.getEndTime()) {
            log.info("Activity not in progress, ignore recharge event");
            return;
        }

        // 更新累计充值金额并处理节点奖励
        updateTotalRechargeAmount(currActor, amount);
    }

    /**
     * 更新累计充值金额并处理节点奖励
     */
    private void updateTotalRechargeAmount(ActorData currActor, double amount) {
        List<RewardTaskConfig> nodeConfigs = getNodeRewardConfigs();
        List<RewardTaskConfig> configs = baseNodeRewardRedis.increaseAndGetCanGetTaskConfig(String.valueOf(EVENT_CODE),
                currActor.getUid(), amount, nodeConfigs);
        if (ObjectUtils.isEmpty(configs)) {
            return;
        }
        configs.forEach(config -> sendRewardAndNotice(currActor, config));
    }

    private void sendRewardAndNotice(ActorData currActor, RewardTaskConfig config) {
        String uid = currActor.getUid();
        // 发放节点奖励
        giveOutRewardService.giveEventReward(uid, config.getRewards(), EVENT_CODE, config.getTaskName());

        // 发送官方通知
        officialNoticeService.sendOfficialNotice(uid, "", config.getNotice(), NOTICE_IMG, EVENT_URL,
                actorMgr.getCurrActorData(uid).getChannel(),
                DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50), EVENT_CODE);

        // 记录历史
        saveHistory(currActor, config, uid);
    }

    private void saveHistory(ActorData currActor, RewardTaskConfig config, String uid) {
        RewardRecordVO record = new RewardRecordVO()
                .setUid(uid)
                .setName(currActor.getName())
                .setHead(moderationService.dealRankHeadModeration(currActor))
                .setTaskName(config.getTaskName())
                .setCtime(DateHelper.getCurrTime());
        eventBaseRecordService.leftPush(lastHistoryKey(EVENT_CODE), record);
    }

    /**
     * 获取节点奖励配置
     */
    private List<RewardTaskConfig> getNodeRewardConfigs() {
        return NODE_REWARD_CONFIGS;
    }
}
