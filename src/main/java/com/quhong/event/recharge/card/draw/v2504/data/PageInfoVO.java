package com.quhong.event.recharge.card.draw.v2504.data;

import com.quhong.dao.datas.common.AwardInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 页面初始化数据VO
 */
@Data
@Accessors(chain = true)
public class PageInfoVO {
    /**
     * 活动开始时间戳(秒)
     */
    private Long startTime;

    /**
     * 活动结束时间戳(秒)
     */
    private Long endTime;

    /**
     * 用户剩余抽奖次数
     */
    private Long drawCount = 0L;

    /**
     * 用户累计充值金币数
     */
    private Integer totalRechargeCoins = 0;

    /**
     * 是否可领取每日奖励 true：可领取，false：不可领取
     */
    private Boolean canClaimDailyReward = false;

    /**
     * 今日奖励是否已领取 true：已领取，false：未领取
     */
    private Boolean dailyRewardClaimed = false;


    /**
     * 奖池奖品列表
     */
    private List<AwardInfo> awardPool;

    /**
     * 最近30条抽奖轮播
     */
    private List<String> lastHistory;
}
