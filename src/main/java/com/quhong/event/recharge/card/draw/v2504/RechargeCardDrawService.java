package com.quhong.event.recharge.card.draw.v2504;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.money.MoneyChangeActionConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.MoneyDetailData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.config.TaskConfig;
import com.quhong.data.dto.BaseDrawDTO;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.dto.RecordDTO;
import com.quhong.data.thData.ActivitySpecialItemsEvent;
import com.quhong.data.thData.DrawPrizesRecordLogData;
import com.quhong.data.vo.BaseHistoryVO;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.CurrencyEnum;
import com.quhong.enums.RewardItemType;
import com.quhong.event.recharge.card.draw.v2504.constant.RechargeCardDrawConstant;
import com.quhong.event.recharge.card.draw.v2504.data.DrawRecordVO;
import com.quhong.event.recharge.card.draw.v2504.data.DrawResultVO;
import com.quhong.event.recharge.card.draw.v2504.data.PageInfoVO;
import com.quhong.exceptions.WebException;
import com.quhong.players.ActorMgr;
import com.quhong.redis.BaseHashSaveRedis;
import com.quhong.redis.base.currency.task.BaseEveryLimitRedis;
import com.quhong.redis.base.reward.BaseNodeRewardRedis;
import com.quhong.redis.base.reward.BaseOneLimitRewardRedis;
import com.quhong.report.EventReport;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.activity.AppConfigActivityService;
import com.quhong.service.common.AwardPoolService;
import com.quhong.service.common.EventBaseRecordService;
import com.quhong.service.common.RewardService;
import com.quhong.utils.DistributeLockUtils;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.quhong.redis.base.reward.BaseOneLimitRewardRedis.CAN_CLAIM;
import static com.quhong.redis.base.reward.BaseOneLimitRewardRedis.CLAIMED;
import static com.quhong.service.activity.recharge.draw.RechargeDrawService.ACT_TYPE_SET;

/**
 * Tikko线上充值活动2025-04服务类
 *
 * <AUTHOR>
 * @since 2025/4/7 11:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RechargeCardDrawService {
    public static final boolean PROD = ServerConfiguration.isProduct();
    private static final String DRAW_LOCK_KEY_PREFIX = "lock:event:card_draw_2504:draw:";
    private static final String DAILY_REWARD_LOCK_KEY_PREFIX = "lock:event:card_draw_2504:daily_reward:";

    public static final String NOTICE_IMG = "https://statics.kissu.mobi/event/rich/party/tongzhi.jpg";

    public static final String EVENT_URL = PROD ? "https://videochat.kissu.site/online_recharge" : "https://testvideochat.kissu.site/online_recharge";

    public static final TaskConfig DRAW_TIMES_TASK = new TaskConfig(TaskTypeConstant.REAL_RECHARGE_GOLD, 200, 1, -1);

    // 每日充值任意金币即可领取奖励
    public static final List<RewardInfoData> DAILY_REWARDS = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(RechargeCardDrawConstant.EVENT_CODE, 1, RewardItemType.BUBBLE_FRAME, PROD ? 377 : 64, 0));
        add(new RewardInfoData(RechargeCardDrawConstant.EVENT_CODE, 1, RewardItemType.SEAT_FRAME, PROD ? 353 : 63, 0));
        add(new RewardInfoData(RechargeCardDrawConstant.EVENT_CODE, 1, RewardItemType.GIFT, 517, 1));
    }};

    public static final RewardTaskConfig DAILY_REWARDS_CONFIG = new RewardTaskConfig()
            .setTaskName("Daily recharge reward")
            .setTaskType(TaskTypeConstant.FIRST_RECHARGE)
            .setCheckParams(RechargeCardDrawConstant.DAILY_REWARD_CHECK_PARAM)
            .setRewards(DAILY_REWARDS);

    public static final List<RewardTaskConfig> NODE_REWARD_CONFIGS = new ArrayList<RewardTaskConfig>() {{
        // 充值10000金币节点
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(RechargeCardDrawConstant.EVENT_CODE, 5, RewardItemType.GOLD, 0, 1).setName("Coin"));
        }};
        add(new RewardTaskConfig()
                .setTaskName("New")
                .setTaskType(TaskTypeConstant.REAL_RECHARGE_GOLD)
                .setCheckParams(250)
                .setRewards(rewards1)
                .setNotice("Dear players,\n" +
                        "Recharge 10,000 coins online during the event, 200 coins automatically credited to your account.\n" +
                        "Great job! Don’t stop now—move on to the next task to earn even more rewards!")
        );

        // 充值180000金币节点
        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(RechargeCardDrawConstant.EVENT_CODE, 90, RewardItemType.GOLD, 0, 1).setName("Coin"));
        }};
        add(new RewardTaskConfig()
                .setTaskName("Familiar")
                .setTaskType(TaskTypeConstant.REAL_RECHARGE_GOLD)
                .setCheckParams(4500)
                .setRewards(rewards2)
                .setNotice("Dear players,\n" +
                        "Recharge 180000 coins online during the event, 3600 coins automatically credited to your account.\n" +
                        "Great job! Don’t stop now—move on to the next task to earn even more rewards!")
        );
        // 充值500000金币节点
        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(RechargeCardDrawConstant.EVENT_CODE, 250, RewardItemType.GOLD, 0, 1).setName("Coin"));
        }};
        add(new RewardTaskConfig()
                .setTaskName("Super")
                .setTaskType(TaskTypeConstant.REAL_RECHARGE_GOLD)
                .setCheckParams(12500)
                .setRewards(rewards3)
                .setNotice("Dear players,\n" +
                        "Recharge 500000 coins online during the event 10000 coins automatically credited to your account.\n" +
                        "Great job! Don’t stop now—move on to the next task to earn even more rewards!")
        );
    }};

    public static final List<AwardInfo> POOL_CONFIG_LIST = new ArrayList<AwardInfo>() {{
        add(new AwardInfo().setAwardId(1).setAwardType(RewardItemType.GOLD).setAwardName("10000 coins").setShowPrice("100000")
                .setAwardIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png").setNums(2500).setDataId(0).setUnitPrice(1).setRate(2));
        add(new AwardInfo().setAwardId(2).setAwardType(RewardItemType.GOLD).setAwardName("1000 coins").setShowPrice("1000")
                .setAwardIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png").setNums(25).setDataId(0).setUnitPrice(1).setRate(24));
        add(new AwardInfo().setAwardId(3).setAwardType(RewardItemType.GIFT).setAwardName("Crystal Love").setShowPrice("600")
                .setAwardIcon("https://statics.kissu.mobi/Event/horse/202411/fencing.png").setNums(1).setDataId(PROD ? 2060 : 714).setUnitPrice(15).setRate(80));
        add(new AwardInfo().setAwardId(4).setAwardType(RewardItemType.GIFT).setAwardName("Fire Warrior").setShowPrice("20000")
                .setAwardIcon("https://statics.kissu.mobi/Event/horse/202411/Fire_Warrior.png").setNums(1).setDataId(PROD ? 715 : 715).setUnitPrice(500).setRate(4));
        add(new AwardInfo().setAwardId(5).setAwardType(RewardItemType.GIFT).setAwardName("Thumbs up").setShowPrice("40")
                .setAwardIcon("https://statics.kissu.mobi/icon/Welfare/up.png").setNums(1).setDataId(PROD ? 603 : 603).setUnitPrice(1).setRate(220));
        add(new AwardInfo().setAwardId(6).setAwardType(RewardItemType.ENTER_EFFECT).setAwardName("Elf Car*1Day").setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/room_item/icon/1722845930434/Elf_Car.png").setNums(1).setDataId(PROD ? 240 : 121).setUnitPrice(0).setRate(100));
        add(new AwardInfo().setAwardId(7).setAwardType(RewardItemType.SEAT_FRAME).setAwardName("Full coins*1Day").setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/room_item/origin/1732586583999/Horse_Racing.webp").setNums(1).setDataId(PROD ? 378 : 114).setUnitPrice(0).setRate(200));
        add(new AwardInfo().setAwardId(8).setAwardType(RewardItemType.BUBBLE_FRAME).setAwardName("lucky horse*1Day").setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/room_item/icon/1732586877519/lucky_horse2.png").setNums(1).setDataId(PROD ? 301 : 117).setUnitPrice(0).setRate(200));
        add(new AwardInfo().setAwardId(9).setAwardType(RewardItemType.GIFT).setAwardName("Tell him/Tell her").setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/Event/Speak/speak.png").setNums(1).setDataId(PROD ? 612 : 612).setUnitPrice(0).setRate(100));
        add(new AwardInfo().setAwardId(10).setAwardType(RewardItemType.LORD_DAYS).setAwardName("King*1Day").setShowPrice("")
                .setAwardIcon("https://statics.kissu.mobi/icon/checkin/VIP.png").setNums(1).setDataId(6).setUnitPrice(0).setRate(70));

    }};

    private final ActorMgr actorMgr;
    /**
     * 活动配置DAO
     */
    private final AppConfigActivityDao appConfigActivityDao;

    private final AppConfigActivityService appConfigActivityService;

    /**
     * 每日限制奖励组件
     */
    private final BaseOneLimitRewardRedis baseOneLimitRewardRedis;

    /**
     * 节点奖励组件
     */
    private final BaseNodeRewardRedis baseNodeRewardRedis;

    /**
     * 奖池服务
     */
    private final AwardPoolService awardPoolService;

    /**
     * 奖励发放服务
     */
    private final GiveOutRewardService giveOutRewardService;

    /**
     * 记录服务
     */
    private final EventBaseRecordService eventBaseRecordService;

    /**
     * 分布式锁工具
     */
    private final DistributeLockUtils distributeLockUtils;

    /**
     * Hash操作组件
     */
    private final BaseHashSaveRedis baseHashSaveRedis;

    private final OfficialNoticeService officialNoticeService;

    private final BaseEveryLimitRedis baseEveryLimitRedis;

    private final EventReport eventReport;

    private final RewardService rewardService;


    private String fillLastHistoryStr(String awardName, String username) {
        return "Congratulations to #username for winning #awardName".replace("#username", username)
                .replace("#awardName", awardName);
    }

    /**
     * 页面初始化，获取活动基本信息和用户状态
     *
     * @param dto 用户ID
     * @return 页面初始化数据
     */
    public PageInfoVO pageInit(CommonDTO dto) {
        dto.checkParams();
        // 获取活动配置
        AppConfigActivityData activityData = getActivityConfig(dto.getEventType());

        // 获取用户抽奖次数
        long drawCount = getUserDrawCount(dto.getUid());

        // 获取用户每日奖励状态
        String canGetRewardStatus = baseOneLimitRewardRedis.canGetRewardStatus(dto.getUid(), dto.getEventType());
        boolean canClaimDailyReward = CAN_CLAIM.equals(canGetRewardStatus);//-1可领取
        boolean dailyRewardClaimed = CLAIMED.equals(canGetRewardStatus);//-2已领取

        // 获取用户累计充值金额
        BigDecimal totalRechargeCoins = baseNodeRewardRedis.getCount(dto.getUid(), String.valueOf(RechargeCardDrawConstant.EVENT_CODE));
        // 获取奖池配置
        List<AwardInfo> awardPool = getAwardPoolConfig();

        String globalRecords = eventBaseRecordService.listRecordKey(dto.getEventType().toString());
        log.debug("globalRecords={}", globalRecords);
        List<String> lastHistory = eventBaseRecordService.lastHistory(globalRecords, 30);
        log.debug("lastHistory={}", lastHistory);

        // 构建返回数据
        return new PageInfoVO()
                .setStartTime(activityData.getStartTime())
                .setEndTime(activityData.getEndTime())
                .setDrawCount(drawCount)
                .setTotalRechargeCoins(totalRechargeCoins.intValue())
                .setCanClaimDailyReward(canClaimDailyReward)
                .setDailyRewardClaimed(dailyRewardClaimed)
                .setAwardPool(awardPool)
                .setLastHistory(lastHistory);
    }

    /**
     * 用户抽奖
     *
     * @param dto 请求体
     * @return 抽奖结果
     */
    public DrawResultVO draw(BaseDrawDTO dto) {
        dto.checkParams();
        // 活动检查
        AppConfigActivityData configData = checkActivity(dto);
        // 抽奖加锁
        String lockKey = DRAW_LOCK_KEY_PREFIX + dto.getUid();
        return distributeLockUtils.distributeMethod(dto, lockKey, (d) -> this.doDraw(d, configData));
    }

    private DrawResultVO doDraw(BaseDrawDTO dto, AppConfigActivityData configData) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        if (!appConfigActivityService.isWithInChannelSet(configData, currActor)) {
            throw new WebException(HttpCode.CHANNEL_NOT_JOIN_EVENT);
        }
        long afterCount = deductDrawTimes(dto, currActor);

        // 从奖池抽奖
        AwardPoolService.DrawDTO drawDTO = new AwardPoolService.DrawDTO()
                .setEventCode(RechargeCardDrawConstant.EVENT_CODE)
                .setPoolType(RechargeCardDrawConstant.POOL_TYPE)
                .setTimes(dto.getAmount())
                .setAwards(getAwardPoolConfig());
        List<AwardInfo> awardInfos = awardPoolService.drawAwardFromRedisPool(drawDTO);
        if (ObjectUtils.isEmpty(awardInfos)) {
            log.error("draw zone award failed,awardPool={}", RechargeCardDrawConstant.POOL_TYPE);
            throw new WebException(new HttpEnvData(), HttpCode.createHttpCode(HttpCode.SERVER_ERROR, true, "The prize pool lottery failed"));
        }
        //抽奖历史
        saveDrawRecords(dto, awardInfos);
        // 全局最近三十条记录
        saveLastHistories(dto, awardInfos, currActor);

        // 发放奖励
        List<RewardInfoData> rewards = awardInfos.stream()
                .map(RechargeCardDrawService::generateReward)
                .collect(Collectors.toList());
        giveOutRewardService.giveEventReward(dto.getUid(), rewards, drawDTO.getEventCode(), "3");
        //抽奖结果上报数数
        reportDrawResult(dto, configData, currActor, awardInfos);
        //大奖全服弹窗
        dealBigRewardUniversalPopMsg(dto, configData, awardInfos);
        // 构建返回结果
        return new DrawResultVO()
                .setAward(awardInfos)
                .setRemainDrawCount(afterCount);
    }

    private void dealBigRewardUniversalPopMsg(BaseDrawDTO dto, AppConfigActivityData configData, List<AwardInfo> awardInfos) {
        if (awardInfos.stream().noneMatch(awardInfo -> 1 == awardInfo.getAwardId())) {
            return;
        }
        List<AwardInfo> bigAwards = awardInfos.stream().filter(awardInfo -> 1 == awardInfo.getAwardId())
                .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(bigAwards)) {
            return;
        }
        List<RewardInfoData> bigRewards = bigAwards.stream()
                .map(RechargeCardDrawService::generateReward)
                .collect(Collectors.toList());
        rewardService.sendUniversalActivityPopMsg(dto.getUid(), bigRewards, EVENT_URL, configData.getName(), null, "");

    }

    private void reportDrawResult(BaseDrawDTO dto, AppConfigActivityData configData, ActorData currActor, List<AwardInfo> awardInfos) {
        DrawPrizesRecordLogData logData = new DrawPrizesRecordLogData();
        logData.setUid(dto.getUid());
        logData.setChannel(currActor.getChannel())
                .setCtime(DateHelper.getCurrTime())
                .setScene(configData.getActivityCode())
                .setSceneDetail(1)
                .setCostTicket((long) dto.getAmount())
                .setCostItemId(0L)
                .setDrawNums(dto.getAmount())
                .setDrawSuccessNums(dto.getAmount())
                .setDrawDesc("success")
                .setDrawResult(JSON.toJSONString(awardInfos));
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private long deductDrawTimes(BaseDrawDTO dto, ActorData currActor) {
        // 获取抽奖次数
        long drawCount = getUserDrawCount(dto.getUid());
        if (drawCount < dto.getAmount()) {
            throw new WebException(new HttpEnvData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "Insufficient number of lucky draws"));
        }
        // 减少抽奖次数
        long afterCount = decreaseDrawCount(dto.getUid(), dto.getAmount());
        //上报数数
        reportDrawTimes(currActor, dto.getEventType(), 2, dto.getAmount());
        return afterCount;
    }

    private void reportDrawTimes(ActorData currActor, Integer eventCode, int changeAction, Integer change) {
        ActivitySpecialItemsEvent logData = new ActivitySpecialItemsEvent();
        logData.setUid(currActor.getUid());
        logData.setCtime(DateHelper.getCurrTime());
        logData.setChannel(currActor.getChannel());
        logData.setEventCode(eventCode);
        logData.setActivityName(ActivityTypeEnum.EVENT_CARD_DRAW_2504.getName());
        logData.setItemId(String.valueOf(0));
        logData.setChangeAction(changeAction);
        logData.setFromType(String.valueOf(changeAction));
        logData.setItemCount(change);
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private void saveDrawRecords(BaseDrawDTO dto, List<AwardInfo> awardInfos) {
        List<DrawRecordVO> records = awardInfos.stream()
                .map(RechargeCardDrawService::generateDrawRecord)
                .collect(Collectors.toList());

        String recordKey = getDrawRecordKey(dto.getUid());
        log.debug("save draw records,uid={},key={},records={}", dto.getUid(), recordKey, JSON.toJSONString(records));
        eventBaseRecordService.leftPushAll(recordKey, records);
    }

    private void saveLastHistories(BaseDrawDTO dto, List<AwardInfo> awardInfos, ActorData currActor) {
        String globalRecords = eventBaseRecordService.listRecordKey(dto.getEventType().toString());
        log.debug("save last histories,uid={},key={},records={}", dto.getUid(), globalRecords, JSON.toJSONString(awardInfos));
        List<String> lastHistoryBars = awardInfos.stream()
                .map(awardInfo -> fillLastHistoryStr(awardInfo.getAwardName(), currActor.getName()))
                .collect(Collectors.toList());
        log.debug("save last histories,uid={},key={},records={}", dto.getUid(), globalRecords, JSON.toJSONString(lastHistoryBars));
        eventBaseRecordService.leftPushStrAll(globalRecords, lastHistoryBars);
    }

    private static RewardInfoData generateReward(AwardInfo awardInfo) {
        return new RewardInfoData(RechargeCardDrawConstant.EVENT_CODE,
                awardInfo.getNums(),
                awardInfo.getAwardType(),
                awardInfo.getDataId(),
                awardInfo.getUnitPrice()
        ).setName(awardInfo.getAwardName());
    }

    private static DrawRecordVO generateDrawRecord(AwardInfo awardInfo) {
        return new DrawRecordVO()
                .setTime(DateHelper.getCurrTime())
                .setAwardId(Long.valueOf(awardInfo.getAwardId()))
                .setAwardType(awardInfo.getAwardType())
                .setAwardName(awardInfo.getAwardName())
                .setAwardImg(awardInfo.getAwardIcon())
                .setAwardNum(awardInfo.getNums())
                .setDataId(awardInfo.getDataId());
    }

    /**
     * 获取用户抽奖记录
     *
     * @param dto 记录查询参数
     * @return 抽奖记录列表
     */
    public BaseHistoryVO<DrawRecordVO> drawRecords(RecordDTO dto) {
        String recordKey = getDrawRecordKey(dto.getUid());
        log.debug("get draw records,uid={},key={}", dto.getUid(), recordKey);
        return eventBaseRecordService.findRecord(dto, recordKey, DrawRecordVO.class);
    }

    /**
     * 领取每日充值奖励
     *
     * @param dto 用户ID
     * @return 奖励领取结果
     */
    public boolean claimDailyRechargeAward(CommonDTO dto) {
        // 活动检查
        AppConfigActivityData configData = checkActivity(dto);
        // 领取奖励加锁
        String lockKey = DAILY_REWARD_LOCK_KEY_PREFIX + dto.getUid();
        return distributeLockUtils.distributeMethod(dto, lockKey, (d) -> this.doDailyClaim(d, configData));
    }

    private boolean doDailyClaim(CommonDTO dto, AppConfigActivityData configData) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        if (!appConfigActivityService.isWithInChannelSet(configData, currActor)) {
            throw new WebException(HttpCode.CHANNEL_NOT_JOIN_EVENT);
        }
        // 检查是否可领取
        RewardTaskConfig taskConfig = getDailyRewardConfig();
        List<RewardInfoData> rewards = baseOneLimitRewardRedis.checkAndGetRewards(dto.getUid(), RechargeCardDrawConstant.EVENT_CODE, taskConfig);

        if (ObjectUtils.isEmpty(rewards)) {
            // 检查当前状态
            String status = baseOneLimitRewardRedis.canGetRewardStatus(dto.getUid(), dto.getEventType());
            if (CAN_CLAIM.equals(status)) {
                throw new WebException(new HttpEnvData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "Received today"));
            } else {
                throw new WebException(new HttpEnvData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "Not meeting the eligibility criteria for collection"));
            }
        }

        // 发放奖励
        giveOutRewardService.giveEventReward(dto.getUid(), rewards, dto.getEventType(), "1");

        return true;
    }

    public void moneyDetailAction(MoneyDetailData mqData) {
        if (mqData.getSingleAction() != MoneyChangeActionConstant.ACTION_ADD) {
            return;
        }
        if (!CurrencyEnum.CURRENCY1.getCurrencyCode().equals(mqData.getCurrencyCode())) {
            return;
        }
        if (mqData.getSingleChange() == null || mqData.getSingleChange() < 1) {
            return;
        }
        if (!ACT_TYPE_SET.contains(mqData.getActType())) {
            return;
        }
        handleRechargeEvent(mqData.getUserid(), mqData.getSingleChange());
    }

    /**
     * 处理充值消息
     * 该方法由MQ消费者调用，处理用户充值事件
     *
     * @param uid    用户ID
     * @param amount 充值金额(金币)
     */
    public void handleRechargeEvent(String uid, int amount) {
        // 检查活动状态
        ActorData currActor = actorMgr.getCurrActorData(uid);
        AppConfigActivityData activityData = getActivityConfig(RechargeCardDrawConstant.EVENT_CODE);
        if (!appConfigActivityService.isWithInChannelSet(activityData, currActor)) {
            return;
        }
        long now = DateHelper.getCurrTime();
        if (now < activityData.getStartTime() || now > activityData.getEndTime()) {
            log.info("Activity not in progress, ignore recharge event");
            return;
        }

        // 更新每日充值状态
        updateDailyRechargeStatus(uid, amount);

        // 更新累计充值金额并处理节点奖励
        updateTotalRechargeAmount(uid, amount);

        // 增加抽奖次数
        increaseDrawCount(currActor, amount);
    }

    // 以下是私有辅助方法

    /**
     * 获取活动配置
     */
    private AppConfigActivityData getActivityConfig(Integer eventType) {
        return appConfigActivityDao.getOneByEventCodeThrowWebException(eventType);
    }

    /**
     * 检查活动状态
     *
     * @param activityData 活动配置数据
     * @return 活动状态：0-未开始，1-进行中，2-已结束
     */
    private int checkActivityStatus(AppConfigActivityData activityData) {
        long now = DateHelper.getCurrTime();
        if (now < activityData.getStartTime()) {
            return 0; // 未开始
        } else if (now > activityData.getEndTime()) {
            return 2; // 已结束
        } else {
            return 1; // 进行中
        }
    }

    /**
     * 检查活动是否进行中
     */
    private AppConfigActivityData checkActivity(CommonDTO dto) {
        AppConfigActivityData activityData = getActivityConfig(dto.getEventType());
        int status = checkActivityStatus(activityData);

        if (status == 0) {
            throw new WebException(new HttpEnvData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "The activity has not started yet"));
        } else if (status == 2) {
            throw new WebException(new HttpEnvData(), HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "The activity has ended"));
        }
        return activityData;
    }

    /**
     * 获取用户抽奖次数
     */
    private long getUserDrawCount(String uid) {
        String countStr = baseHashSaveRedis.getStrByRedis(RechargeCardDrawConstant.REDIS_DRAW_COUNT_KEY, uid);
        if (StringUtils.isEmpty(countStr)) {
            return 0;
        }
        return Long.parseLong(countStr);
    }

    /**
     * 增加用户抽奖次数
     */
    private void increaseDrawCount(ActorData currActor, int amount) {
        // 计算增加的抽奖次数
        int incr = baseEveryLimitRedis.increaseAndGetRewards(currActor.getUid(), RechargeCardDrawConstant.EVENT_CODE, amount, DRAW_TIMES_TASK);
        if (incr <= 0) {
            return;
        }
        baseHashSaveRedis.increaseCount(RechargeCardDrawConstant.REDIS_DRAW_COUNT_KEY, currActor.getUid(), incr);
        //上报数数
        reportGetDrawTimes(currActor, incr);
    }

    private void reportGetDrawTimes(ActorData currActor, int incr) {
        reportDrawTimes(currActor, RechargeCardDrawConstant.EVENT_CODE, 1, incr);
    }

    /**
     * 减少用户抽奖次数
     */
    private long decreaseDrawCount(String uid, int decr) {
        return baseHashSaveRedis.decreaseCount(RechargeCardDrawConstant.REDIS_DRAW_COUNT_KEY, uid, decr);
    }

    /**
     * 更新每日充值状态
     */
    private void updateDailyRechargeStatus(String uid, int amount) {
        RewardTaskConfig taskConfig = getDailyRewardConfig();
        baseOneLimitRewardRedis.incrAndCheckCanGetReward(uid, RechargeCardDrawConstant.EVENT_CODE, taskConfig, amount);
    }

    /**
     * 更新累计充值金额并处理节点奖励
     */
    private void updateTotalRechargeAmount(String uid, int amount) {
        List<RewardTaskConfig> nodeConfigs = getNodeRewardConfigs();
        List<RewardTaskConfig> configs = baseNodeRewardRedis.increaseAndGetCanGetTaskConfig(String.valueOf(RechargeCardDrawConstant.EVENT_CODE),
                uid, amount, nodeConfigs);
        if (ObjectUtils.isEmpty(configs)) {
            return;
        }
        configs.forEach(config -> {
            // 发放节点奖励
            giveOutRewardService.giveEventReward(uid, config.getRewards(), RechargeCardDrawConstant.EVENT_CODE, "2");

            officialNoticeService.sendOfficialNotice(uid, "Congratulations", config.getNotice(), NOTICE_IMG, EVENT_URL, actorMgr.getCurrActorData(uid).getChannel(),
                    DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50), RechargeCardDrawConstant.EVENT_CODE);
        });

    }

    /**
     * 获取每日奖励配置
     */
    private RewardTaskConfig getDailyRewardConfig() {
        return DAILY_REWARDS_CONFIG;
    }

    /**
     * 获取节点奖励配置
     */
    private List<RewardTaskConfig> getNodeRewardConfigs() {
        return NODE_REWARD_CONFIGS;
    }

    /**
     * 获取奖池配置
     */
    private List<AwardInfo> getAwardPoolConfig() {
        return POOL_CONFIG_LIST;
    }

    /**
     * 获取抽奖记录键
     */
    private String getDrawRecordKey(String uid) {
        return RechargeCardDrawConstant.REDIS_DRAW_RECORD_KEY_PREFIX + uid;
    }

}
