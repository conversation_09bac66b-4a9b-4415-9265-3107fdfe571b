package com.quhong.event.speed.v2505;

import com.quhong.constant.SafeStrategyTypeConstant;
import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.ActorExternalDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.config.TaskConfig;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.enums.EventCode;
import com.quhong.enums.RewardItemType;
import com.quhong.event.speed.data.vo.PageInitVO;
import com.quhong.exceptions.WebException;
import com.quhong.mq.data.activity.PlayGameMsgData;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.redis.base.currency.task.BaseEveryLimitRedis;
import com.quhong.redis.base.reward.BaseNodeRewardRedis;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.SafeStrategyService;
import com.quhong.service.common.EventBaseRankService;
import com.quhong.service.common.RoomFloatingImService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2025-05-09 11:01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpeedServiceV2505 {
    /**
     * 积分榜
     */
    public static final int SCORE_RANK = 1;
    /**
     * 幸运礼物钻石分成榜
     */
    public static final int RECEIVE_RANK = 2;
    private static final boolean PROD = ServerConfiguration.isProduct();
    private static final int EVENT_CODE = EventCode.EVENT_SPEED_2505;
    private static final String POINT_IMG = "https://statics.kissu.mobi/Event/car/point.png";
    private static final String NOTICE_IMG = "https://statics.kissu.mobi/Event/car/513/notice.jpg";
    private static final String EVENT_URL = PROD ? "https://videochat.kissu.site/car_party_2505/" : "https://testvideochat.kissu.site/car_party_2505/";
    private static final TaskConfig PLAY_GAME_TASK = new TaskConfig(TaskTypeConstant.PLAY_GAME_COST_GOLD, 2500, 1, -1);
    private static final TaskConfig SEND_LUCKY_GIFT_TASK = new TaskConfig(TaskTypeConstant.SEND_GIFT, 100, 1, -1);

    private static final List<RewardTaskConfig> NODE_TASKS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 330 : 115));
        }};
        add(new RewardTaskConfig().setCheckParams(50).setRewards(rewards1).setTaskName("Lv1"));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 331 : 111));
        }};
        add(new RewardTaskConfig().setCheckParams(200).setRewards(rewards2).setTaskName("Lv2"));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 332 : 29));
        }};
        add(new RewardTaskConfig().setCheckParams(500).setRewards(rewards3).setTaskName("Lv3"));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 333 : 69));
        }};
        add(new RewardTaskConfig().setCheckParams(1500).setRewards(rewards4).setTaskName("Lv4"));

        List<RewardInfoData> rewards5 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 334 : 89));
        }};
        add(new RewardTaskConfig().setCheckParams(5500).setRewards(rewards5).setTaskName("Lv5"));
    }};

    public static final List<RewardTaskConfig> SCORE_RANK_REWARDS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90069 : 90001, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 335 : 98, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.BUBBLE_FRAME, PROD ? 343 : 71, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90070 : 90003, 0));
            add(new RewardInfoData(EVENT_CODE, 11, RewardItemType.SEAT_FRAME, PROD ? 336 : 101, 0));
            add(new RewardInfoData(EVENT_CODE, 11, RewardItemType.BUBBLE_FRAME, PROD ? 343 : 71, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90071 : 900017, 0));
            add(new RewardInfoData(EVENT_CODE, 9, RewardItemType.SEAT_FRAME, PROD ? 337 : 102, 0));
            add(new RewardInfoData(EVENT_CODE, 9, RewardItemType.BUBBLE_FRAME, PROD ? 343 : 71, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 5, RewardItemType.SEAT_FRAME, PROD ? 338 : 65, 0));
            add(new RewardInfoData(EVENT_CODE, 5, RewardItemType.BUBBLE_FRAME, PROD ? 343 : 71, 0));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));

    }};

    public static final List<RewardTaskConfig> RECEIVE_RANK_REWARDS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90072 : 900018, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 339 : 74, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.BUBBLE_FRAME, PROD ? 344 : 27, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90073 : 900029, 0));
            add(new RewardInfoData(EVENT_CODE, 11, RewardItemType.SEAT_FRAME, PROD ? 340 : 33, 0));
            add(new RewardInfoData(EVENT_CODE, 11, RewardItemType.BUBBLE_FRAME, PROD ? 344 : 27, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90074 : 900030, 0));
            add(new RewardInfoData(EVENT_CODE, 9, RewardItemType.SEAT_FRAME, PROD ? 341 : 78, 0));
            add(new RewardInfoData(EVENT_CODE, 9, RewardItemType.BUBBLE_FRAME, PROD ? 344 : 27, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 5, RewardItemType.SEAT_FRAME, PROD ? 342 : 18, 0));
            add(new RewardInfoData(EVENT_CODE, 5, RewardItemType.BUBBLE_FRAME, PROD ? 344 : 27, 0));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));

    }};
    private final AppConfigActivityDao appConfigActivityDao;
    private final ActorMgr actorMgr;
    private final BaseNodeRewardRedis baseNodeRewardRedis;
    private final BaseEveryLimitRedis baseEveryLimitRedis;
    private final GiveOutRewardService giveOutRewardService;
    private final OfficialNoticeService officialNoticeService;
    private final ActorExternalDao actorExternalDao;
    private final EventBaseRankService eventBaseRankService;
    private final RoomFloatingImService roomFloatingImService;
    private final SafeStrategyService safeStrategyService;

    /**
     * 榜单key
     */
    private String zsetRankKey(Integer eventCode, Integer rankType) {
        return "zset:event:speed_rank:" + eventCode + ":" + rankType;
    }


    public PageInitVO pageInit(CommonDTO dto) {
        dto.checkParams();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        String scoreRankKey = zsetRankKey(EVENT_CODE, SCORE_RANK);
        CountVO data = eventBaseRankService.getOne(scoreRankKey, currActor.getUid());
        boolean safeStrategySwitch = safeStrategyService.getSafeStrategySwitch(currActor, SafeStrategyTypeConstant.GAME_SHOW_STRATEGY);
        return new PageInitVO()
                .setStartTime(configData.getStartTime())
                .setEndTime(configData.getEndTime())
                .setScore(data.getCount())
                .setGameSafeSwitch(safeStrategySwitch);
    }

    public ModelRankVO<RankRowVO> rank(String uid, Integer eventType, Integer rankType) {
        ActorData currActor = actorMgr.getCurrActorData(uid);
        String rankKey = zsetRankKey(eventType, rankType);
        return eventBaseRankService.rank(currActor, rankKey);
    }

    public void playGameAction(PlayGameMsgData mqData) {
        long currTime = DateHelper.getCurrTime();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        if (configData.getStartTime() > currTime || configData.getEndTime() < currTime) {
            return;
        }
        boolean tester = actorExternalDao.isTester(mqData.getUid());
        if (tester) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        if (!configData.getChannel().contains(currActor.getChannel())) {
            return;
        }

        //计算积分
        double finalCoin = Double.parseDouble(mqData.fetchCoin());
        int incr = baseEveryLimitRedis.increaseAndGetRewards(mqData.getUid(), EVENT_CODE, finalCoin, PLAY_GAME_TASK);
        if (incr < 1) {
            return;
        }
        dealScoreLogic(currActor, incr, configData);
    }

    private void dealScoreLogic(ActorData currActor, int incr, AppConfigActivityData configData) {
        //积分榜单统计
        String scoreRankKey = zsetRankKey(EVENT_CODE, SCORE_RANK);
        eventBaseRankService.rankValueIncrease(scoreRankKey, currActor.getUid(), incr);
        //获得积分飘屏
        roomFloatingImService.sendRoomFloatingIm(currActor, incr, POINT_IMG, "points", "to see", EVENT_CODE, EVENT_URL, false, "Get #ticket*#count #suffix>>>");
        //节点奖励计算
        List<RewardInfoData> rewards = baseNodeRewardRedis.increaseAndGetRewards(String.valueOf(EVENT_CODE), currActor.getUid(), incr, NODE_TASKS);
        if (ObjectUtils.isEmpty(rewards)) {
            return;
        }
        giveOutRewardService.giveEventReward(currActor.getUid(), rewards, EVENT_CODE, "node task reward");
        // 发官方消息
        String notice = "\uD83C\uDF89\uD83D\uDC4F\uD83C\uDF8ACongratulations on getting Event Super Car in the\"Luxury car club\" Event~\n" +
                "\uD83C\uDF89\uD83D\uDC4F\uD83C\uDF8AThe reward has been sent to the store backpack, remember to wear it~";
        int fixTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        officialNoticeService.sendOfficialNotice(currActor.getUid(), configData.getName(), notice, NOTICE_IMG, EVENT_URL, currActor.getChannel(), fixTime, EVENT_CODE);
    }

    public void sendGiftAction(SendGiftSuccessMsgData mqData) {
        if (mqData.getIsLuck() == 0) {
            return;
        }
        long currTime = DateHelper.getCurrTime();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        if (configData.getStartTime() > currTime || configData.getEndTime() < currTime) {
            return;
        }
        try {
            dealSendLogic(mqData, configData);
        } catch (WebException e) {
            log.error("send gift error, msg={}", e.getHttpCode());
        } catch (Exception e) {
            log.error("send gift error, uid={}, giftId={}", mqData.getUid(), mqData.getGiftId(), e);
        }

        try {
            dealReceiveLogic(mqData, configData);
        } catch (WebException e) {
            log.error("receive gift error, msg={}", e.getHttpCode());
        } catch (Exception e) {
            log.error("receive gift error, uid={}, giftId={}", mqData.getUid(), mqData.getGiftId(), e);
        }

    }

    private void dealReceiveLogic(SendGiftSuccessMsgData mqData, AppConfigActivityData configData) {
        if (actorExternalDao.isTester(mqData.getToUid())) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getToUid());
        if (!configData.getChannel().contains(currActor.getChannel())) {
            return;
        }
        double finalGain = mqData.fetchRealGain();
        String receiveRankKey = zsetRankKey(EVENT_CODE, RECEIVE_RANK);
        eventBaseRankService.rankValueIncrease(receiveRankKey, mqData.getToUid(), finalGain);
    }

    private void dealSendLogic(SendGiftSuccessMsgData mqData, AppConfigActivityData configData) {
        boolean tester = actorExternalDao.isTester(mqData.getUid());
        if (tester) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        if (!configData.getChannel().contains(currActor.getChannel())) {
            return;
        }
        //计算积分
        double finalCoin = mqData.fetchRealCost();
        int incr = baseEveryLimitRedis.increaseAndGetRewards(mqData.getUid(), EVENT_CODE, finalCoin, SEND_LUCKY_GIFT_TASK);
        if (incr < 1) {
            return;
        }
        //榜单统计
        dealScoreLogic(currActor, incr, configData);
    }

    public void rankRewards(Integer eventCode, Integer rankType) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventCode);
        String rankKey = zsetRankKey(eventCode, rankType);

        // 全局消息
        List<RewardTaskConfig> rewardConfigs = RECEIVE_RANK_REWARDS;
        String rankName = "Lucky gift receiver ranking";
        if (rankType == SCORE_RANK) {
            rewardConfigs = SCORE_RANK_REWARDS;
            rankName = "Points ranking";
        }
        String changeDesc = rankName;
        String globalNoticeFormatter = "\uD83D\uDE01 Congratulations to the follows get Top3 in \"Luxury car Club\" Event " +  rankName + "\n"+
                "#content" +
                "\uD83D\uDE01 The reward has been issued>>>>";
        eventBaseRankService.rankRewards(configData, rankKey, rewardConfigs, rankName,
                globalNoticeFormatter, changeDesc, NOTICE_IMG, EVENT_URL);
    }
}
