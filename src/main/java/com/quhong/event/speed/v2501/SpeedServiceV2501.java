package com.quhong.event.speed.v2501;

import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.ActorExternalDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.config.TaskConfig;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.enums.EventCode;
import com.quhong.enums.RewardItemType;
import com.quhong.event.speed.data.vo.PageInitVO;
import com.quhong.exceptions.WebException;
import com.quhong.mq.data.activity.PlayGameMsgData;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.redis.base.currency.task.BaseEveryLimitRedis;
import com.quhong.redis.base.reward.BaseNodeRewardRedis;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.common.EventBaseRankService;
import com.quhong.service.common.RoomFloatingImService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2025/1/16 10:26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpeedServiceV2501 {
    private static final boolean PROD = ServerConfiguration.isProduct();
//    private static final int EVENT_CODE = EventCode.EVENT_SPEED_2501;
    private static final int EVENT_CODE = EventCode.EVENT_SPEED_2503;

    private static final String POINT_IMG = "https://statics.kissu.mobi/Event/car/point.png";

    private static final String NOTICE_IMG = "https://statics.kissu.mobi/Event/Lucky/2025/banner_17417473064486.png";
    private static final String EVENT_URL = PROD ? "https://videochat.kissu.site/car_party_2503/" : "https://testvideochat.kissu.site/car_party_2503/";
    /**
     * 积分榜
     */
    public static final int SCORE_RANK = 1;
    /**
     * 幸运礼物钻石分成榜
     */
    public static final int RECEIVE_RANK = 2;

    private static final TaskConfig PLAY_GAME_TASK = new TaskConfig(TaskTypeConstant.PLAY_GAME_COST_GOLD, 2500, 1, -1);
    private static final TaskConfig SEND_LUCKY_GIFT_TASK = new TaskConfig(TaskTypeConstant.SEND_GIFT, 100, 1, -1);

    private static final List<RewardTaskConfig> NODE_TASKS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 371 : 115));
        }};
        add(new RewardTaskConfig().setCheckParams(50).setRewards(rewards1).setTaskName("Lv1"));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 372 : 111));
        }};
        add(new RewardTaskConfig().setCheckParams(200).setRewards(rewards2).setTaskName("Lv2"));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 374 : 29));
        }};
        add(new RewardTaskConfig().setCheckParams(500).setRewards(rewards3).setTaskName("Lv3"));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 311 : 99));
        }};
        add(new RewardTaskConfig().setCheckParams(1500).setRewards(rewards4).setTaskName("Lv4"));

        List<RewardInfoData> rewards5 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 373 : 89));
        }};
        add(new RewardTaskConfig().setCheckParams(5500).setRewards(rewards5).setTaskName("Lv5"));
    }};

    public static final List<RewardTaskConfig> SCORE_RANK_REWARDS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 368 : 98, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.BUBBLE_FRAME, PROD ? 370 : 71, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.SEAT_FRAME, PROD ? 368 : 98, 0));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.BUBBLE_FRAME, PROD ? 370 : 71, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.SEAT_FRAME, PROD ? 368 : 98, 0));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.BUBBLE_FRAME, PROD ? 370 : 71, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 368 : 98, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.BUBBLE_FRAME, PROD ? 370 : 71, 0));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));

    }};

    public static final List<RewardTaskConfig> RECEIVE_RANK_REWARDS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 369 : 74, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.BUBBLE_FRAME, PROD ? 370 : 27, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.SEAT_FRAME, PROD ? 369 : 74, 0));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.BUBBLE_FRAME, PROD ? 370 : 27, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.SEAT_FRAME, PROD ? 369 : 74, 0));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.BUBBLE_FRAME, PROD ? 370 : 27, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 369 : 74, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.BUBBLE_FRAME, PROD ? 370 : 27, 0));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));

    }};

    private final AppConfigActivityDao appConfigActivityDao;
    private final ActorMgr actorMgr;
    private final BaseNodeRewardRedis baseNodeRewardRedis;
    private final BaseEveryLimitRedis baseEveryLimitRedis;
    private final GiveOutRewardService giveOutRewardService;
    private final OfficialNoticeService officialNoticeService;
    private final ActorExternalDao actorExternalDao;
    private final EventBaseRankService eventBaseRankService;
    private final RoomFloatingImService roomFloatingImService;

    /**
     * 榜单key
     */
    private String zsetRankKey(Integer eventCode, Integer rankType) {
        return "zset:event:speed_rank:" + eventCode + ":" + rankType;
    }


    public PageInitVO pageInit(CommonDTO dto) {
        dto.checkParams();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        String scoreRankKey = zsetRankKey(EVENT_CODE, SCORE_RANK);
        CountVO data = eventBaseRankService.getOne(scoreRankKey, currActor.getUid());
        return new PageInitVO()
                .setStartTime(configData.getStartTime())
                .setEndTime(configData.getEndTime())
                .setScore(data.getCount());
    }

    public ModelRankVO<RankRowVO> rank(String uid, Integer eventType, Integer rankType) {
        ActorData currActor = actorMgr.getCurrActorData(uid);
        String rankKey = zsetRankKey(eventType, rankType);
        return eventBaseRankService.rank(currActor, rankKey);
    }

    public void playGameAction(PlayGameMsgData mqData) {
        long currTime = DateHelper.getCurrTime();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        if (configData.getStartTime() > currTime || configData.getEndTime() < currTime) {
            return;
        }
        boolean tester = actorExternalDao.isTester(mqData.getUid());
        if (tester) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        if (!configData.getChannel().contains(currActor.getChannel())) {
            return;
        }

        //计算积分
        double finalCoin = Double.parseDouble(mqData.fetchCoin());
        int incr = baseEveryLimitRedis.increaseAndGetRewards(mqData.getUid(), EVENT_CODE, finalCoin, PLAY_GAME_TASK);
        if (incr < 1) {
            return;
        }
        dealScoreLogic(currActor, incr, configData);
    }

    private void dealScoreLogic(ActorData currActor, int incr, AppConfigActivityData configData) {
        //积分榜单统计
        String scoreRankKey = zsetRankKey(EVENT_CODE, SCORE_RANK);
        eventBaseRankService.rankValueIncrease(scoreRankKey, currActor.getUid(), incr);
        //获得积分飘屏
        roomFloatingImService.sendRoomFloatingIm(currActor, incr, POINT_IMG, "points", "to see", EVENT_CODE, EVENT_URL, false, "Get #ticket*#count #suffix>>>");
        //节点奖励计算
        List<RewardInfoData> rewards = baseNodeRewardRedis.increaseAndGetRewards(String.valueOf(EVENT_CODE), currActor.getUid(), incr, NODE_TASKS);
        if (ObjectUtils.isEmpty(rewards)) {
            return;
        }
        giveOutRewardService.giveEventReward(currActor.getUid(), rewards, EVENT_CODE, "node task reward");
        // 发官方消息
        String notice = "\uD83D\uDE01\uD83D\uDE01Congratulations on getting Event super Car in the\"Luxury Moto club\"Event\n" +
                "\uD83D\uDE01\uD83D\uDE01The reward has been sent to the store backpack,remember to wear it~";
        int fixTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        officialNoticeService.sendOfficialNotice(currActor.getUid(), configData.getName(), notice, NOTICE_IMG, EVENT_URL, currActor.getChannel(), fixTime, EVENT_CODE);
    }

    public void sendGiftAction(SendGiftSuccessMsgData mqData) {
        if (mqData.getIsLuck() == 0) {
            return;
        }
        long currTime = DateHelper.getCurrTime();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        if (configData.getStartTime() > currTime || configData.getEndTime() < currTime) {
            return;
        }
        try {
            dealSendLogic(mqData, configData);
        } catch (WebException e) {
            log.error("send gift error, msg={}", e.getHttpCode());
        } catch (Exception e) {
            log.error("send gift error, uid={}, giftId={}", mqData.getUid(), mqData.getGiftId(), e);
        }

        try {
            dealReceiveLogic(mqData, configData);
        } catch (WebException e) {
            log.error("receive gift error, msg={}", e.getHttpCode());
        } catch (Exception e) {
            log.error("receive gift error, uid={}, giftId={}", mqData.getUid(), mqData.getGiftId(), e);
        }

    }

    private void dealReceiveLogic(SendGiftSuccessMsgData mqData, AppConfigActivityData configData) {
        if (actorExternalDao.isTester(mqData.getToUid())) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getToUid());
        if (!configData.getChannel().contains(currActor.getChannel())) {
            return;
        }
        double finalGain = mqData.fetchRealGain();
        String receiveRankKey = zsetRankKey(EVENT_CODE, RECEIVE_RANK);
        eventBaseRankService.rankValueIncrease(receiveRankKey, mqData.getToUid(), finalGain);
    }

    private void dealSendLogic(SendGiftSuccessMsgData mqData, AppConfigActivityData configData) {
        boolean tester = actorExternalDao.isTester(mqData.getUid());
        if (tester) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        if (!configData.getChannel().contains(currActor.getChannel())) {
            return;
        }
        //计算积分
        double finalCoin = mqData.fetchRealCost();
        int incr = baseEveryLimitRedis.increaseAndGetRewards(mqData.getUid(), EVENT_CODE, finalCoin, SEND_LUCKY_GIFT_TASK);
        if (incr < 1) {
            return;
        }
        //榜单统计
        dealScoreLogic(currActor, incr, configData);
    }

    public void rankRewards(Integer eventCode, Integer rankType) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventCode);
        String rankKey = zsetRankKey(eventCode, rankType);

        // 全局消息
        List<RewardTaskConfig> rewardConfigs = RECEIVE_RANK_REWARDS;
        String rankName = "Lucky gift receiver ranking";
        if (rankType == SCORE_RANK) {
            rewardConfigs = SCORE_RANK_REWARDS;
            rankName = "points ranking";
        }
        String changeDesc = rankName;
        String globalNoticeFormatter = "\uD83D\uDE01 Congratulations to the follows get Top3 in \"Luxury Moto club\" Event " + rankName + "\n" +
                "#content" +
                "\uD83D\uDE01 The reward has been issued>>>>";
        configData.setChannel(ChannelEnum.CDE.getName());
        eventBaseRankService.rankRewards(configData, rankKey, rewardConfigs, rankName,
                globalNoticeFormatter, changeDesc, NOTICE_IMG, EVENT_URL);
    }
}
