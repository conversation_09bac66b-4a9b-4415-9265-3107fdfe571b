package com.quhong.data.dto.event.unit;

import com.quhong.common.data.BaseHttpData;
import com.quhong.common.enums.HttpCode;
import com.quhong.exceptions.WebException;
import com.quhong.utils.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-06-04 15:27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class BaseDTO extends BaseHttpData {
    private String uid;

    private Integer eventCode;

    public void checkParams() {
        if (StringUtils.isEmpty(this.uid)) {
            throw new WebException(this, HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "uid is empty"));
        }
        if (this.eventCode == null) {
            throw new WebException(this, HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "eventType is empty"));
        }
    }
}
