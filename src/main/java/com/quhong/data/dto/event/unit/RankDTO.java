package com.quhong.data.dto.event.unit;

import com.quhong.common.enums.HttpCode;
import com.quhong.exceptions.WebException;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025-06-04 19:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RankDTO extends BaseDTO{
    /**
     * 榜单组件id
     */
    private Integer unitId;

    public void checkParams() {
        super.checkParams();
        if (unitId == null) {
            throw new WebException(this, HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "unitId is empty"));
        }
    }
}
