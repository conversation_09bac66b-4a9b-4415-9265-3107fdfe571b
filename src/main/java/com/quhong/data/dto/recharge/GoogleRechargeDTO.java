package com.quhong.data.dto.recharge;

import com.quhong.data.vo.GoogleProductPurchaseVO;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GoogleRechargeDTO extends RechargeDTO {
    private String purchaseTime;
    private String purchaseToken;
    private Integer purchaseState; // 0。已购买 1. 已取消 2. 待处理
    /**
     * 用于通知验证，已经有这个对象，就不用再请求验证了
     */
    private GoogleProductPurchaseVO purchaseVO;
    /**
     * 从通知过来
     */
    private boolean fromNotification;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        // do nothing
    }

    @Override
    protected byte[] doToBody() throws Exception {
        return new byte[0];
    }
}
