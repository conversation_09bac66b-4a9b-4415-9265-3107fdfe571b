package com.quhong.data.dto.privateActor;

import com.quhong.common.data.ProtoDTO;
import com.quhong.server.protobuf.UserOperationProtobuf;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/10 10:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DelBannerListDTO extends ProtoDTO {
    private List<Long> ids;
    /**
     * @param bytes
     * @throws Exception
     */
    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        UserOperationProtobuf.BatchDelBannerReq msg = UserOperationProtobuf.BatchDelBannerReq.parseFrom(bytes);
        if (!ObjectUtils.isEmpty(msg.getIdsList())) {
            this.ids = msg.getIdsList();
        }
    }

    /**
     * @return
     * @throws Exception
     */
    @Override
    protected byte[] doToBody() throws Exception {
        return new byte[0];
    }
}
