package com.quhong.data.protobuf;

import com.quhong.server.protobuf.UserMessageProtobuf;
import org.springframework.beans.BeanUtils;

public class GreetingToHostResp {

    private int left_times;

    public byte[] doToBody(){
        UserMessageProtobuf.GreetingToHostResp.Builder builder = UserMessageProtobuf.GreetingToHostResp.newBuilder();
        BeanUtils.copyProperties(this,builder);
        return builder.build().toByteArray();
    }

    public int getLeft_times() {
        return left_times;
    }

    public void setLeft_times(int left_times) {
        this.left_times = left_times;
    }
}
