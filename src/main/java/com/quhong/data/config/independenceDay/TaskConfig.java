package com.quhong.data.config.independenceDay;

import com.quhong.core.config.ServerConfiguration;
import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 获得门票任务配置
 *
 * <AUTHOR>
 * @since 2023/7/28 17:01
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TaskConfig extends com.quhong.data.config.TaskConfig {

    /**
     * 首充任务
     */
    public static final TaskConfig FIRST_RECHARGE_TASK = new TaskConfig(TaskTypeConstant.FIRST_RECHARGE, 0, 0, -1);
    /**
     * 雅讯时空系列游戏任务
     */
    public static final TaskConfig PLAY_YXSK_GAME_TASK = new TaskConfig(TaskTypeConstant.PLAY_YXSK_GAME, 200, 0, 10);
    /**
     * 房间停留任务
     */
    public static final TaskConfig ROOM_STAY_TIME_TASK = new TaskConfig(TaskTypeConstant.ROOM_STAY_TIME, 120, 0, 1);
    /**
     * 星动嘉年华消耗金币数每达标，获得奖励
     */
    public static final TaskConfig CARNIVAL_LUCKY_DRAW_COST_GOLD_TASK = new TaskConfig(TaskTypeConstant.CARNIVAL_LUCKY_DRAW_COST_GOLD, 50, 1, -1);
    /**
     * 玩游戏消耗金币数每达标，获得奖励
     */
    public static final TaskConfig PLAY_GAME_COST_GOLD_TASK = new TaskConfig(TaskTypeConstant.PLAY_GAME_COST_GOLD, 5000, 1, -1);
    /**
     * 分享任务
     */
    public static final TaskConfig DISSEMINATE_TIMES_TASK = new TaskConfig(TaskTypeConstant.DISSEMINATE_TIMES, 5, 1, 1);
    /**
     * 门票图标地址
     */
    public static final String TICKET_ICON_URL = "https://statics.kissu.mobi/icon/independence/new/ticket_v4.png";
    /**
     * 飘屏背景图
     */
    public static final String FLOATING_SCREEN_BACKGROUND_IMG = "https://statics.kissu.mobi/icon/independence/new/floating_v4.png";
    /**
     * 活动地址
     */
    public static final String EVENT_URL = ServerConfiguration.isProduct() ? "https://videochat.kissu.site/independence_day" : "https://testvideochat.kissu.site/independence_day";
    /**
     * 礼物任务配置
     */
    public static final List<TaskConfig> TASK_CONFIGS = new ArrayList<TaskConfig>() {{
        add(new TaskConfig(TaskTypeConstant.SEND_GIFT, 661, 4, -1));
        add(new TaskConfig(TaskTypeConstant.SEND_GIFT, 663, 12, -1));
//        add(new TaskConfig(TaskTypeConstant.FIRST_RECHARGE, 0, 20, -1));
//        add(new TaskConfig(TaskTypeConstant.PLAY_YXSK_GAME, 200, 4, 10));
//        add(new TaskConfig(TaskTypeConstant.ROOM_STAY_TIME, 120, 2, 1));
    }};

    public TaskConfig(Integer taskType, Integer checkParams, Integer ticket, Integer limit) {
        super(taskType, checkParams, ticket, limit);
    }
}
