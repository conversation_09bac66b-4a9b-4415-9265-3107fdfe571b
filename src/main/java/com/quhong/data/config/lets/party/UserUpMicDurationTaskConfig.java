package com.quhong.data.config.lets.party;

import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.data.config.DailyRewardTaskConfig;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.RewardItemType;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/28 14:42
 */
public class UserUpMicDurationTaskConfig extends DailyRewardTaskConfig {
    public static final UserUpMicDurationTaskConfig CONFIG = new UserUpMicDurationTaskConfig(TaskTypeConstant.MIC_STAY_TIME,
            (int) Duration.ofMinutes(5).getSeconds(),
            1,
            new ArrayList<RewardInfoData>() {{
                if (ServerConfiguration.isProduct()) {
                    add(new RewardInfoData(ActivityTypeEnum.HOTCHAT_CHAT_ROOM_ACTIVITY.getCode(), 1, RewardItemType.SEAT_FRAME, 27));
                    add(new RewardInfoData(ActivityTypeEnum.HOTCHAT_CHAT_ROOM_ACTIVITY.getCode(), 1, RewardItemType.GIFT, 562));
                } else {
                    // 测试服id
                    add(new RewardInfoData(ActivityTypeEnum.HOTCHAT_CHAT_ROOM_ACTIVITY.getCode(), 1, RewardItemType.SEAT_FRAME, 18));
                    add(new RewardInfoData(ActivityTypeEnum.HOTCHAT_CHAT_ROOM_ACTIVITY.getCode(), 1, RewardItemType.GIFT, 561));
                }
            }},
            1);

    public UserUpMicDurationTaskConfig(Integer taskType, Integer checkParams, Integer limit, List<RewardInfoData> rewards, Integer dailyLimit) {
        super(taskType, checkParams, limit, rewards, dailyLimit);
    }
}
