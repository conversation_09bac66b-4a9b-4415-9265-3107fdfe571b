package com.quhong.data.config.lets.party;

import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.data.config.DailyRewardTaskConfig;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.RewardItemType;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/28 15:22
 */
public class ChatRoomGiftPriceTaskConfig extends DailyRewardTaskConfig {

    public static final ChatRoomGiftPriceTaskConfig CONFIG = new ChatRoomGiftPriceTaskConfig(
            TaskTypeConstant.PARTY_ROOM_GIFT_PRICE,
            50000 / 40,
            1,
            new ArrayList<RewardInfoData>() {{
                add(new RewardInfoData(ActivityTypeEnum.HOTCHAT_CHAT_ROOM_ACTIVITY.getCode(), 1, RewardItemType.MATCH_CARD, 0));
                if (ServerConfiguration.isProduct()) {
                    add(new RewardInfoData(ActivityTypeEnum.HOTCHAT_CHAT_ROOM_ACTIVITY.getCode(), 1, RewardItemType.GIFT, 231));
                } else {
                    add(new RewardInfoData(ActivityTypeEnum.HOTCHAT_CHAT_ROOM_ACTIVITY.getCode(), 1, RewardItemType.GIFT, 556));
                }
            }},
            1
    );

    public ChatRoomGiftPriceTaskConfig(Integer taskType, Integer checkParams, Integer limit, List<RewardInfoData> rewards, Integer dailyLimit) {
        super(taskType, checkParams, limit, rewards, dailyLimit);
    }
}
