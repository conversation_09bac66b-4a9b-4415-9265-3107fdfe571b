package com.quhong.data.config.lets.party;

import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.data.config.DailyRewardTaskConfig;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.RewardItemType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/28 14:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlayGameTimesTaskConfig extends DailyRewardTaskConfig {

    public static final List<Integer> GAME_ID_SET = new ArrayList<Integer>() {{
        add(111);
        add(112);
        add(202);
    }};
    public static final List<PlayGameTimesTaskConfig> CONFIGS = new ArrayList<PlayGameTimesTaskConfig>() {{
        int code = ActivityTypeEnum.HOTCHAT_CHAT_ROOM_ACTIVITY.getCode();
        int taskType = TaskTypeConstant.GAME_PLAY_TIMES;
        // 第一阶段
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            if (ServerConfiguration.isProduct()) {
                add(new RewardInfoData(code, 1, RewardItemType.BUBBLE_FRAME, 82));
                add(new RewardInfoData(code, 3, RewardItemType.GIFT, 562));
            } else {
                // 测试服id
                add(new RewardInfoData(code, 1, RewardItemType.BUBBLE_FRAME, 71));
                add(new RewardInfoData(code, 3, RewardItemType.GIFT, 561));
            }
        }};
        add(new PlayGameTimesTaskConfig(taskType, 10, 1, rewards1, 1, GAME_ID_SET));

        // 第二阶段
        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(code, 3, RewardItemType.VIP_DAYS, 0));
            if (ServerConfiguration.isProduct()) {
                add(new RewardInfoData(code, 10, RewardItemType.GIFT, 562));
            } else {
                // 测试服id
                add(new RewardInfoData(code, 10, RewardItemType.GIFT, 561));
            }
        }};
        add(new PlayGameTimesTaskConfig(taskType, 30, 1, rewards2, 1, GAME_ID_SET));
    }};
    private List<Integer> gameIdSet;

    public PlayGameTimesTaskConfig(Integer taskType, Integer checkParams, Integer limit, List<RewardInfoData> rewards, Integer dailyLimit, List<Integer> gameIdSet) {
        super(taskType, checkParams, limit, rewards, dailyLimit);
        this.gameIdSet = gameIdSet;
    }
}
