package com.quhong.data.config.lets.party;

import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.data.config.DailyRewardTaskConfig;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.RewardItemType;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/28 14:04
 */
public class HostPartyRoomDurationDailyTaskConfig extends DailyRewardTaskConfig {
    public static final List<HostPartyRoomDurationDailyTaskConfig> CONFIGS = new ArrayList<HostPartyRoomDurationDailyTaskConfig>() {{
        int code = ActivityTypeEnum.HOTCHAT_CHAT_ROOM_ACTIVITY.getCode();
        int taskType = TaskTypeConstant.HOST_PARTY_ROOM_STAY_TIME;
        // 第一阶段奖励
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            if (ServerConfiguration.isProduct()) {
                add(new RewardInfoData(code, 1, RewardItemType.BUBBLE_FRAME, 35));
            } else {
                // 测试服id
                add(new RewardInfoData(code, 1, RewardItemType.BUBBLE_FRAME, 17));
            }
        }};
        add(new HostPartyRoomDurationDailyTaskConfig(taskType, (int) Duration.ofMinutes(5).getSeconds(), 1, rewards1, 1));
        // 第二阶段奖励
        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            if (ServerConfiguration.isProduct()) {
                add(new RewardInfoData(code, 1, RewardItemType.SEAT_FRAME, 94));
            } else {
                // 测试服id
                add(new RewardInfoData(code, 1, RewardItemType.SEAT_FRAME, 69));
            }
        }};
        add(new HostPartyRoomDurationDailyTaskConfig(taskType, (int) Duration.ofMinutes(15).getSeconds(), 1, rewards2, 1));
    }};

    public HostPartyRoomDurationDailyTaskConfig(Integer taskType, Integer checkParams, Integer limit, List<RewardInfoData> rewards, Integer dailyLimit) {
        super(taskType, checkParams, limit, rewards, dailyLimit);
    }
}
