package com.quhong.data.config.cupid;

import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/8/27 11:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TaskConfig extends com.quhong.data.config.TaskConfig {
    /**
     * 玩游戏消耗金币数每达标，获得奖励
     */
    public static final TaskConfig SEND_GIFT_PRICE_TASK = new TaskConfig(TaskTypeConstant.SEND_GIFT, 200, 1, -1);

    public TaskConfig(Integer taskType, Integer checkParams, Integer ticket, Integer limit) {
        super(taskType, checkParams, ticket, limit);
    }
}
