package com.quhong.data;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @ClassName MsgBodyData
 * <AUTHOR>
 * @date 2022/3/3 17:24
 */
@Data
public class MsgBodyListData {
    @JSONField(name = "from_platform")
    private Integer fromPlatform;

    @JSONField(name = "to_platform")
    private Integer toPlatform;

    @JSONField(name = "msg_body")
    private String msgBody;

    @JSONField(name = "msg_type")
    private Integer msgType;

    @JSONField(name = "is_robot")
    public Integer isRobot;

    @JSONField(name = "msg_info")
    public ChatMsgInfoData msgInfo;
}
