package com.quhong.data.vo.model.rank.row;

import com.quhong.core.utils.SpringUtils;
import com.quhong.dao.datas.ActorData;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/7/12 13:51
 */
@Data
public class ActorInfo {
    /**
     * 用户uid
     */
    private String uid;
    /**
     * 用户rid
     */
    private Long rid;
    /**
     * 用户头像
     */
    private String head;
    /**
     * 用户名
     */
    private String name;
    /**
     * 直播中
     */
    private Boolean living;

    private String roomId;
    /**
     * 房间类型
     *
     * @see com.quhong.enums.RoomType
     */
    private Integer roomType;

    public ActorInfo() {
        this.rid = 8888888888L;
        this.head = "https://statics.kissu.mobi/icon/tikko/<EMAIL>";
        this.name = "XXXXXXXX";
        this.living = false;
    }

    public ActorInfo(ActorData actorData) {
        this.rid = actorData.getRid();
        this.uid = actorData.getUid();
        this.head = actorData.getHeadIcon();
        this.name = actorData.getName();
    }

    public ActorInfo copy() {
        ActorInfo target = new ActorInfo();
        SpringUtils.copyPropertiesIgnoreNull(this, target);
        return target;
    }
}
