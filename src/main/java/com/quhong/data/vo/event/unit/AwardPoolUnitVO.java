package com.quhong.data.vo.event.unit;

import com.quhong.core.utils.SpringUtils;
import com.quhong.dao.datas.app.config.activity.unit.AwardPoolUnit;
import com.quhong.dao.datas.app.config.activity.unit.EventUnit;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-06-04 18:41
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class AwardPoolUnitVO {
    /**
     * 活动组件id
     */
    private Integer unitId;
    /**
     * 组件类型
     * @see com.quhong.constant.activity.unit.EventUnitType 组件类型
     */
    private Integer unitType;
    /**
     * 组件名称
     */
    private String unitName;
    /**
     * 组件别名
     */
    private String unitAka;
    /**
     * 描述
     */
    private String unitDesc;
    /**
     * 通用奖池组件
     * unitType==2时必填
     */
    private AwardPoolUnit awardPoolUnit;


    public AwardPoolUnitVO(EventUnit unit){
        SpringUtils.copyPropertiesIgnoreNull(unit, this);
    }
}
