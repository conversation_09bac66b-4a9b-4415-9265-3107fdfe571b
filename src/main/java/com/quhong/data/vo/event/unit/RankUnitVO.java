package com.quhong.data.vo.event.unit;

import com.quhong.core.utils.SpringUtils;
import com.quhong.dao.datas.app.config.activity.unit.EventUnit;
import com.quhong.dao.datas.app.config.activity.unit.RankUnit;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-06-04 18:52
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class RankUnitVO {
    /**
     * 活动组件id
     */
    private Integer unitId;
    /**
     * 组件类型
     * @see com.quhong.constant.activity.unit.EventUnitType 组件类型
     */
    private Integer unitType;
    /**
     * 组件名称
     */
    private String unitName;
    /**
     * 组件别名
     */
    private String unitAka;
    /**
     * 描述
     */
    private String unitDesc;
    /**
     * 排行组件
     * unitType==4时必填
     */
    private RankUnit rankUnit;

    public RankUnitVO(EventUnit unit) {
        SpringUtils.copyPropertiesIgnoreNull(unit, this);
        this.rankUnit = unit.getRankUnit();
    }
}
