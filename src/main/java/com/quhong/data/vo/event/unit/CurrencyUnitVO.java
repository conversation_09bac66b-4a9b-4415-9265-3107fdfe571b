package com.quhong.data.vo.event.unit;

import com.quhong.core.utils.SpringUtils;
import com.quhong.dao.datas.app.config.activity.unit.CurrencyUnit;
import com.quhong.dao.datas.app.config.activity.unit.EventUnit;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-06-04 15:21
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class CurrencyUnitVO {
    /**
     * 活动组件id
     */
    private Integer unitId;
    /**
     * 组件类型
     * @see com.quhong.constant.activity.unit.EventUnitType 组件类型
     */
    private Integer unitType;
    /**
     * 组件名称
     */
    private String unitName;
    /**
     * 组件别名
     */
    private String unitAka;
    /**
     * 描述
     */
    private String unitDesc;
    /**
     * 货币组件
     */
    private CurrencyUnit currencyUnit;
    /**
     * 余额
     */
    private Long balance;

    public CurrencyUnitVO(EventUnit unit, Long balance) {
        SpringUtils.copyPropertiesIgnoreNull(unit, this);
        this.currencyUnit = unit.getCurrencyUnit();
        this.balance = balance;
    }
}
