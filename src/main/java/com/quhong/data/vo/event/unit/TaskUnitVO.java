package com.quhong.data.vo.event.unit;

import com.quhong.core.utils.SpringUtils;
import com.quhong.dao.datas.app.config.activity.unit.EventUnit;
import com.quhong.dao.datas.app.config.activity.unit.TaskUnit;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-06-04 16:04
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class TaskUnitVO {
    /**
     * 活动组件id
     */
    private Integer unitId;
    /**
     * 组件类型
     * @see com.quhong.constant.activity.unit.EventUnitType 组件类型
     */
    private Integer unitType;
    /**
     * 组件名称
     */
    private String unitName;
    /**
     * 组件别名
     */
    private String unitAka;
    /**
     * 描述
     */
    private String unitDesc;
    /**
     * 任务组件
     * unitType==3时必填
     */
    private TaskUnit taskUnit;
    /**
     * 任务积分
     */
    private Long score;

    public TaskUnitVO(EventUnit unit, Long score) {
        SpringUtils.copyPropertiesIgnoreNull(unit, this);
        this.taskUnit = unit.getTaskUnit();
        this.score = score;
    }
}
