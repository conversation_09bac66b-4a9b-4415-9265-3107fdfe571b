package com.quhong.data.vo.event.unit;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-06-04 15:19
 */
@Data
@Accessors(chain = true)
public class UnitPageInitVO {
    /**
     * 活动码
     */
    private Integer eventCode;
    /**
     * 用户uid
     */
    private String uid;
    /**
     * 活动开始时间
     */
    private Long startTime;
    /**
     * 活动结束时间
     */
    private Long endTime;
    /**
     * 组件货币列表
     */
    private List<CurrencyUnitVO> balances;
    /**
     * 任务列表
     */
    private List<TaskUnitVO> tasks;
    /**
     * 奖池配置
     */
    private List<AwardPoolUnitVO> awardPools;
    /**
     * 排行榜配置
     */
    private List<RankUnitVO> rankConfigs;


}
