package com.quhong.data.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.common.data.ProtoVO;
import com.quhong.server.protobuf.UserOperationProtobuf;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023-02-21  11:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ActorEditorVO extends ProtoVO {

    @JSONField(name = "live_host_rights")
    private Integer liveHostRights;
    @JSONField(name = "accept_video")
    private Integer acceptVideo;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        UserOperationProtobuf.ActorEditorResp msg = UserOperationProtobuf.ActorEditorResp.parseFrom(bytes);
        this.liveHostRights = msg.getLiveHostRights();
        this.acceptVideo = msg.getAcceptVideo();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        UserOperationProtobuf.ActorEditorResp.Builder builder = UserOperationProtobuf.ActorEditorResp.newBuilder();
        builder.setLiveHostRights(this.liveHostRights);
        builder.setAcceptVideo(this.acceptVideo);
        return builder.build().toByteArray();
    }
}
