package com.quhong.data.vo;

import com.quhong.common.data.ProtoVO;
import com.quhong.data.http.RspUserListData;
import com.quhong.data.http.SuggestedData;
import com.quhong.server.protobuf.UserMessageProtobuf;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class SuggestListVO extends ProtoVO {
    private List<SuggestedData> list;
    private int list_sign;
    private boolean next_page;
    private int left_times;

    public SuggestListVO(){

    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        UserMessageProtobuf.SuggestedListResp msg = UserMessageProtobuf.SuggestedListResp.parseFrom(bytes);
        list = new ArrayList<>();
        for (UserMessageProtobuf.SuggestedData userdata : msg.getUserDataList()) {
            SuggestedData listData = new SuggestedData();
            listData.doFromBody(userdata);
            list.add(listData);
        }
        this.list_sign = msg.getListSign();
        this.next_page = msg.getNextPage();
        this.left_times = msg.getLeftTimes();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        UserMessageProtobuf.SuggestedListResp.Builder builder =  UserMessageProtobuf.SuggestedListResp.newBuilder();
        if(this.list != null){
            for(SuggestedData listData : this.list){
                builder.addUserData(listData.doToBody());
            }
        }
        builder.setListSign(list_sign);
        builder.setNextPage(next_page);
        builder.setLeftTimes(left_times);
        return builder.build().toByteArray();
    }
}
