package com.quhong.data.vo.activity.model;

import com.quhong.dao.datas.app.config.activity.model.page.ModelPageInfo;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 页面初始化响应体
 *
 * <AUTHOR>
 * @since 2024/1/29 14:51
 */
@Data
@Accessors(chain = true)
public class PageInitVO {
    /**
     * 活动码
     */
    private Integer eventCode;
    /**
     * 活动开始时间
     */
    private Long startTime;
    /**
     * 活动结束时间
     */
    private Long endTime;
    /**
     * 活动页面信息
     */
    private ModelPageInfo pageInfo;
}
