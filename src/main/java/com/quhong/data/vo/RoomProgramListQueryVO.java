package com.quhong.data.vo;

import com.quhong.common.data.ProtoVO;
import com.quhong.data.RoomProgramList;
import com.quhong.server.protobuf.RoomMessageProtobuf;
import lombok.Data;

/**
 * @ClassName RoomProgramListQueryVO
 * <AUTHOR>
 * @date 2023/10/23 14:34
 */
@Data
public class RoomProgramListQueryVO extends ProtoVO {
    private RoomProgramList roomProgramList;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {

    }

    @Override
    protected byte[] doToBody() throws Exception {
        RoomMessageProtobuf.RoomProgramListResp.Builder builder = RoomMessageProtobuf.RoomProgramListResp.newBuilder();
        if (this.roomProgramList != null) {
            builder.setData(this.roomProgramList.doToBody());
        }
        return builder.build().toByteArray();
    }
}
