package com.quhong.data.vo;

import com.quhong.common.data.ProtoVO;
import com.quhong.data.object.RoomStoreGoodsObject;
import com.quhong.server.protobuf.ItemsMessageProtobuf;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName RoomStoreGoodsListVO
 * <AUTHOR>
 * @date 2022/12/3 11:30
 */
@Setter
@Getter
@ToString(callSuper = true)
public class RoomStoreGoodsListVO extends ProtoVO {
    private List<RoomStoreGoodsObject> entranceEffectList;
    private List<RoomStoreGoodsObject> framesList;
    private List<RoomStoreGoodsObject> textBubbleList;
    private List<RoomStoreGoodsObject> entryEffectList;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        ItemsMessageProtobuf.RoomStoreGoodsListResp msg = ItemsMessageProtobuf.RoomStoreGoodsListResp.parseFrom(bytes);
        this.entranceEffectList = new ArrayList<>();
        for (ItemsMessageProtobuf.RoomStoreGoodsData roomStoreGoodsData : msg.getEntranceEffectListList()) {
            RoomStoreGoodsObject data = new RoomStoreGoodsObject();
            data.doFromBody(roomStoreGoodsData);
            this.entranceEffectList.add(data);
        }
        this.framesList = new ArrayList<>();
        for (ItemsMessageProtobuf.RoomStoreGoodsData roomStoreGoodsData : msg.getFramesListList()) {
            RoomStoreGoodsObject data = new RoomStoreGoodsObject();
            data.doFromBody(roomStoreGoodsData);
            this.framesList.add(data);
        }
        this.textBubbleList = new ArrayList<>();
        for (ItemsMessageProtobuf.RoomStoreGoodsData roomStoreGoodsData : msg.getTextBubbleListList()) {
            RoomStoreGoodsObject data = new RoomStoreGoodsObject();
            data.doFromBody(roomStoreGoodsData);
            this.textBubbleList.add(data);
        }
    }

    @Override
    protected byte[] doToBody() throws Exception {
        ItemsMessageProtobuf.RoomStoreGoodsListResp.Builder builder = ItemsMessageProtobuf.RoomStoreGoodsListResp.newBuilder();
        if (this.entranceEffectList != null) {
            for (RoomStoreGoodsObject data : this.entranceEffectList) {
                builder.addEntranceEffectList(data.doToBody());
            }
        }
        if (this.framesList != null) {
            for (RoomStoreGoodsObject data : this.framesList) {
                builder.addFramesList(data.doToBody());
            }
        }
        if (this.textBubbleList != null) {
            for (RoomStoreGoodsObject data : this.textBubbleList) {
                builder.addTextBubbleList(data.doToBody());
            }
        }
        if (this.entryEffectList != null) {
            for (RoomStoreGoodsObject data : this.entryEffectList) {
                builder.addEntryEffectList(data.doToBody());
            }
        }
        return builder.build().toByteArray();
    }
}
