package com.quhong.data.common;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/12/9 18:15
 */
@Data
@Accessors(chain = true)
public class AwardInfo {
    private Integer awardId;
    private Integer awardType;
    private Integer dataId;
    private Integer nums;
    private String awardName;
    private String awardIcon;
    private String awardVideoUrl;
    private Integer unitPrice;
    /**
     * 显示的价值
     */
    private String showPrice;
    /**
     * 显示数量
     */
    private String showNums;
    /**
     * 默认抽奖概率
     */
    private Integer rate;
    /**
     * 三方充值>50美金用户抽奖概率
     */
    private Integer rate1;
    private Integer rate2;
    private Integer rate3;
    /**
     * 全局限制数量
     * -1 不限制 >=0 限制  0直接不入奖池
     */
    private Integer limit = -1;
}
