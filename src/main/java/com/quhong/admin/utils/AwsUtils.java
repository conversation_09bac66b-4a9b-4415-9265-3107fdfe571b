package com.quhong.admin.utils;

import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.quhong.admin.data.AwsFileInfoData;
import com.quhong.aws.clients.AWSUploader;
import com.quhong.aws.clients.buckets.BucketOption;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class AwsUtils {
    private static final Logger logger = LoggerFactory.getLogger(AwsUtils.class);

    public static final String IMG_TYPE_PNG = "PNG";
    public static final String IMG_TYPE_JPG = "JPG";
    public static final String IMG_TYPE_JPEG = "JPEG";
    public static final String VIDEO_TYPE_MP4 = "MP4";
    public static final String VIDEO_TYPE_MOV = "mov";

    public static final String BUCKET_NAME = "kissubucket";

    private static final String VIRTUAL_HOST_AWS_PATH = "systemHost/data/";

    public static final String AWS_IMG_CDN = "statics.kissu.mobi";

    public static final String AWS_UPLOAD_URL = "kissubucket.s3.ap-southeast-1.amazonaws.com";

    public static final String VIDEO_CDN = "video.kissu.mobi";
    @Resource
    private AWSUploader awsUploader;
    @Resource
    private BucketOption bucketOption;
    /**
     * Description: 通过目录获取文件信息
     * @return java.util.List<com.quhong.admin.data.vo.dev.AwsFileInfoData>
     *
     */
    public List<AwsFileInfoData> getFileByDir(String dir) {
        List<AwsFileInfoData> infoDataList = new ArrayList<>();

        ListObjectsV2Result kissUBucket = bucketOption.listObject(BUCKET_NAME, dir);
        for (S3ObjectSummary summary : kissUBucket.getObjectSummaries()) {
            String key = summary.getKey();
            String[] infos = key.split("/");
            String fileName = infos[infos.length - 1];
            String url = "https://" + AWS_IMG_CDN + "/" + key;
            AwsFileInfoData data = new AwsFileInfoData();
//            data.setDir(dir);
//            data.setFileName(fileName);
            data.setUrl(url);
            infoDataList.add(data);
        }
        return infoDataList;
    }
}
