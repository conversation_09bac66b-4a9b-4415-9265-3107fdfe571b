package com.quhong.admin.service.official.starry;

import com.alibaba.fastjson.JSONObject;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.StarryCarnivalRewardTypeConstant;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.*;
import com.quhong.dao.datas.GiftListConfigData;
import com.quhong.dao.datas.db.LordConfigData;
import com.quhong.dao.datas.db.RoomItemsConfigData;
import com.quhong.dao.datas.db.StarryCarnivalChapterConfigData;
import com.quhong.dao.datas.db.StarryCarnivalRewardPoolData;
import com.quhong.dbService.CarnivalDBService;
import com.quhong.enums.RewardItemType;
import com.quhong.exceptions.WebException;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName StarryConfigService
 * <AUTHOR>
 * @date 2024/5/27 11:37
 */
@Component
@Lazy
public class StarryConfigService {
    @Resource
    private StarryCarnivalRewardPoolDao rewardPoolDao;
    @Resource
    private StarryCarnivalChapterConfigDao configDao;
    @Resource
    private RoomItemsConfigDao roomItemsConfigDao;
    @Resource
    private GiftListConfigDao giftListConfigDao;
    @Resource
    private LordConfigDao lordConfigDao;
    @Resource
    private CarnivalDBService dbService;


    public StarryCarnivalChapterConfigData queryConfig(Integer chapterId) {
        StarryCarnivalChapterConfigData data = configDao.getConfigByChapter(chapterId);
        if (data == null) {
            return null;
        }
        data.copyLotteryFeesConfig(data.getLotteryFeesConfig());
        List<StarryCarnivalRewardPoolData> rewardPoolList = rewardPoolDao.getRewardPoolByChapter(chapterId);
        if (!CollectionUtils.isEmpty(rewardPoolList)) {
            for (StarryCarnivalRewardPoolData poolData : rewardPoolList) {
                poolData.copyRewardDetailList(poolData.getRewardDetail());
                dealIconAndName(poolData);
                poolData.setRate(new BigDecimal(poolData.getRate()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_DOWN).toString());
            }
        }
        data.setUpperLimitRate(new BigDecimal(data.getUpperLimitRate()).divide(new BigDecimal("10000"), 2, RoundingMode.HALF_DOWN).toString());
        data.setLowLimitRate(new BigDecimal(data.getLowLimitRate()).divide(new BigDecimal("10000"), 2, RoundingMode.HALF_DOWN).toString());
        if (!StringUtils.isEmpty(data.getSecLowLimitRate())) {
            data.setSecLowLimitRate(new BigDecimal(data.getSecLowLimitRate()).divide(new BigDecimal("10000"), 2, RoundingMode.HALF_DOWN).toString());
        }
        if (!StringUtils.isEmpty(data.getSecUpperLimitRate())) {
            data.setSecUpperLimitRate(new BigDecimal(data.getSecUpperLimitRate()).divide(new BigDecimal("10000"), 2, RoundingMode.HALF_DOWN).toString());
        }
        data.setRewardPoolList(rewardPoolList);
        return data;
    }

    private void dealIconAndName(StarryCarnivalRewardPoolData poolData) {
        List<StarryCarnivalRewardPoolData.RewardDetailData> rewardDetailList = poolData.getRewardDetailList();
        for (StarryCarnivalRewardPoolData.RewardDetailData rewardDetailData : rewardDetailList) {
            switch (rewardDetailData.getType()) {
                case StarryCarnivalRewardTypeConstant.SEAT_FRAME:
                case StarryCarnivalRewardTypeConstant.BUBBLE_FRAME:
                case StarryCarnivalRewardTypeConstant.ENTER_EFFECT:
                case StarryCarnivalRewardTypeConstant.ENTRY_EFFECT:
                    RoomItemsConfigData roomItemsConfigData = roomItemsConfigDao.getDataByItemIdAndItemType(rewardDetailData.getRewardRelationId(), rewardDetailData.getType());
                    rewardDetailData.setRewardIcon(roomItemsConfigData.getIconUrl());
                    rewardDetailData.setRewardName(roomItemsConfigData.getRoomItemName());
                    break;
                case StarryCarnivalRewardTypeConstant.GIFT:
                    GiftListConfigData giftConfigByGiftId = giftListConfigDao.getGiftConfigByGiftIdAndChannel(rewardDetailData.getRewardRelationId(), ChannelEnum.CDE.getName());
                    rewardDetailData.setRewardIcon(giftConfigByGiftId.getIconUrl());
                    rewardDetailData.setRewardName(giftConfigByGiftId.getShowName());
                    break;
                case StarryCarnivalRewardTypeConstant.VIP:
                    rewardDetailData.setRewardIcon(StarryCarnivalRewardPoolDao.VIP_ICON);
                    rewardDetailData.setRewardName("VIP");
                    break;
                case StarryCarnivalRewardTypeConstant.LORD:
                    LordConfigData lordConfigByLevel = lordConfigDao.getLordConfigByLevel(rewardDetailData.getRewardRelationId());
                    rewardDetailData.setRewardIcon(lordConfigByLevel.getLevelIcon());
                    rewardDetailData.setRewardName(lordConfigByLevel.getLevelName());
                    break;
            }
        }
        switch (poolData.getRewardType()) {
            case StarryCarnivalRewardTypeConstant.GIFT_BOX_1:
                poolData.setRewardIcon(StarryCarnivalRewardPoolDao.GIFT_BOX_1);
                poolData.setRewardName(StarryCarnivalRewardPoolDao.GIFT_BOX_1_NAME);
                break;
            case StarryCarnivalRewardTypeConstant.GIFT_BOX_2:
                poolData.setRewardIcon(StarryCarnivalRewardPoolDao.GIFT_BOX_2);
                poolData.setRewardName(StarryCarnivalRewardPoolDao.GIFT_BOX_2_NAME);
                break;
            case StarryCarnivalRewardTypeConstant.GIFT_BOX_3:
                poolData.setRewardIcon(StarryCarnivalRewardPoolDao.GIFT_BOX_3);
                poolData.setRewardName(StarryCarnivalRewardPoolDao.GIFT_BOX_3_NAME);
                break;
            default:
                poolData.setRewardIcon(poolData.getRewardDetailList().get(0).getRewardIcon());
                poolData.setRewardName(poolData.getRewardDetailList().get(0).getRewardName());
                break;
        }
    }

    public void changeConfig(StarryCarnivalChapterConfigData dto, String username) {
        //将奖池置为无效 重新插入新的奖池
        rewardPoolDao.invalidPoolList(dto.getChapter(), username);
        List<StarryCarnivalRewardPoolData> rewardPoolList = dto.getRewardPoolList();
        saveRewardPoolList(dto.getChapter(), rewardPoolList, username);
        StarryCarnivalChapterConfigData data = configDao.getConfigByChapter(dto.getChapter());
        if (data == null) {
            StarryCarnivalChapterConfigData configData = getConfigData(dto, username);
            configData.setCtime(configData.getMtime());
            configData.setGameTotalPrice("0");
            configData.setLuckyTotalPrice("0");
            configDao.save(configData);
        } else {
            dto.setId(data.getId());
            dto.setMtime(DateHelper.getCurrTime());
            dto.setGameTotalPrice(null);
            dto.setLuckyTotalPrice(null);
            dto.setLotteryFeesConfig(JSONObject.toJSONString(dto.getLotteryFeesConfigList()));
            dto.setUpperLimitRate(new BigDecimal(dto.getUpperLimitRate()).multiply(new BigDecimal("10000")).setScale(0, RoundingMode.HALF_DOWN).toString());
            dto.setLowLimitRate(new BigDecimal(dto.getLowLimitRate()).multiply(new BigDecimal("10000")).setScale(0, RoundingMode.HALF_DOWN).toString());
            if (!StringUtils.isEmpty(dto.getSecUpperLimitRate())) {
                dto.setSecUpperLimitRate(new BigDecimal(dto.getSecUpperLimitRate()).multiply(new BigDecimal("10000")).setScale(0, RoundingMode.HALF_DOWN).toString());
            } else {
                dto.setSecUpperLimitRate(null);
            }
            if (!StringUtils.isEmpty(dto.getSecLowLimitRate())) {
                dto.setSecLowLimitRate(new BigDecimal(dto.getSecLowLimitRate()).multiply(new BigDecimal("10000")).setScale(0, RoundingMode.HALF_DOWN).toString());
            } else {
                dto.setSecLowLimitRate(null);
            }
            dto.setOperator(username);
            configDao.update(dto);
        }
        dbService.clearGiftBoxRewardList();
    }

    private StarryCarnivalChapterConfigData getConfigData(StarryCarnivalChapterConfigData dto, String username) {
        StarryCarnivalChapterConfigData configData = new StarryCarnivalChapterConfigData();
        configData.setChapter(dto.getChapter());
        configData.setUpperLimit(dto.getUpperLimit());
        configData.setUpperLimitRate(new BigDecimal(dto.getUpperLimitRate()).multiply(new BigDecimal("10000")).setScale(0, RoundingMode.HALF_DOWN).toString());
        configData.setLowLimit(dto.getLowLimit());
        configData.setLowLimitRate(new BigDecimal(dto.getLowLimitRate()).multiply(new BigDecimal("10000")).setScale(0, RoundingMode.HALF_DOWN).toString());
        configData.setLuckyValue(dto.getLuckyValue());
        configData.setGameRate(dto.getGameRate());
        configData.setLuckyRate(dto.getLuckyRate());
        configData.setSystemRate(dto.getSystemRate());
        configData.setTicketType(dto.getTicketType());
        configData.setLotteryFeesConfig(JSONObject.toJSONString(dto.getLotteryFeesConfigList()));
        configData.setOperator(username);
        configData.setMtime(DateHelper.getCurrTime());
        return configData;
    }

    private void saveRewardPoolList(Integer chapter, List<StarryCarnivalRewardPoolData> rewardPoolList, String username) {
        for (StarryCarnivalRewardPoolData poolData : rewardPoolList) {
            poolData.setId(null);
            poolData.setChapter(chapter);
            poolData.setOperator(username);
            poolData.setCtime(DateHelper.getCurrTime());
            poolData.setMtime(DateHelper.getCurrTime());
            poolData.setLuckySurplusCount(poolData.getLuckyCount());
            poolData.setNormalSurplusCount(poolData.getNormalCount());
            if (poolData.getShowPrice() == null || poolData.getShowPrice() == 0) {
                poolData.setShowPrice(poolData.getPrice());
            }
            if (StarryCarnivalRewardTypeConstant.GIFT_BOX_SET.contains(poolData.getRewardType())) {
                poolData.setRewardDetail(JSONObject.toJSONString(poolData.getRewardDetailList()));
            } else {
                List<StarryCarnivalRewardPoolData.RewardDetailData> rewardList = getRewardDetailData(poolData);
                poolData.setRewardDetail(JSONObject.toJSONString(rewardList));
            }
            poolData.setRate(new BigDecimal(poolData.getRate()).multiply(new BigDecimal("100")).setScale(0, RoundingMode.HALF_DOWN).toString());
            rewardPoolDao.save(poolData);
        }
    }

    private List<StarryCarnivalRewardPoolData.RewardDetailData> getRewardDetailData(StarryCarnivalRewardPoolData poolData) {
        List<StarryCarnivalRewardPoolData.RewardDetailData> rewardList = new ArrayList<>();
        StarryCarnivalRewardPoolData.RewardDetailData rewardDetailData = new StarryCarnivalRewardPoolData.RewardDetailData();
        rewardDetailData.setRewardRelationId(poolData.getRewardId());
        rewardDetailData.setType(poolData.getRewardType());
        rewardDetailData.setMark(poolData.getMark());
        rewardDetailData.setExpire(poolData.getExpire());
        rewardDetailData.setPrice(poolData.getPrice());
        rewardDetailData.setOrderId(poolData.getOrderId());
        rewardDetailData.setShowPrice(poolData.getShowPrice());
        rewardList.add(rewardDetailData);
        return rewardList;
    }

    public StarryCarnivalRewardPoolData queryRewardInfo(Integer rewardId, Integer type) {
        StarryCarnivalRewardPoolData data = new StarryCarnivalRewardPoolData();
        switch (type) {
            case StarryCarnivalRewardTypeConstant.SEAT_FRAME:
            case StarryCarnivalRewardTypeConstant.BUBBLE_FRAME:
            case StarryCarnivalRewardTypeConstant.ENTER_EFFECT:
            case StarryCarnivalRewardTypeConstant.ENTRY_EFFECT:
                RoomItemsConfigData roomItemsConfigData = roomItemsConfigDao.getDataByItemIdAndItemType(rewardId, type);
                if (roomItemsConfigData == null) {
                    throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("room items is not exist"));
                }
                data.setRewardIcon(roomItemsConfigData.getIconUrl());
                data.setRewardName(roomItemsConfigData.getRoomItemName());
                break;
            case StarryCarnivalRewardTypeConstant.GIFT:
                GiftListConfigData giftConfigByGiftId = giftListConfigDao.getGiftConfigByGiftIdAndChannel(rewardId, ChannelEnum.CDE.getName());
                if (giftConfigByGiftId == null) {
                    throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("gift is not exist"));
                }
                data.setRewardIcon(giftConfigByGiftId.getIconUrl());
                data.setRewardName(giftConfigByGiftId.getShowName());
                break;
            case StarryCarnivalRewardTypeConstant.VIP:
                data.setRewardIcon(StarryCarnivalRewardPoolDao.VIP_ICON);
                data.setRewardName("VIP");
                break;
            case StarryCarnivalRewardTypeConstant.DIAMOND:
                data.setRewardName("diamond");
                data.setRewardIcon(RewardItemType.DIAMOND_ICON);
                break;
            case StarryCarnivalRewardTypeConstant.COIN:
                data.setRewardName("coins");
                data.setRewardIcon(RewardItemType.GOLD_ICON);
                break;
            case StarryCarnivalRewardTypeConstant.LORD:
                LordConfigData lordConfigByLevel = lordConfigDao.getLordConfigByLevel(rewardId);
                if (lordConfigByLevel == null) {
                    throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("lord is not exist"));
                }
                data.setRewardIcon(lordConfigByLevel.getLevelIcon());
                data.setRewardName(lordConfigByLevel.getLevelName());
                break;
            case StarryCarnivalRewardTypeConstant.GIFT_BOX_1:
                data.setRewardIcon(StarryCarnivalRewardPoolDao.GIFT_BOX_1);
                data.setRewardName(StarryCarnivalRewardPoolDao.GIFT_BOX_1_NAME);
                break;
            case StarryCarnivalRewardTypeConstant.GIFT_BOX_2:
                data.setRewardIcon(StarryCarnivalRewardPoolDao.GIFT_BOX_2);
                data.setRewardName(StarryCarnivalRewardPoolDao.GIFT_BOX_2_NAME);
                break;
            case StarryCarnivalRewardTypeConstant.GIFT_BOX_3:
                data.setRewardIcon(StarryCarnivalRewardPoolDao.GIFT_BOX_3);
                data.setRewardName(StarryCarnivalRewardPoolDao.GIFT_BOX_3_NAME);
                break;
        }
        return data;
    }
}
