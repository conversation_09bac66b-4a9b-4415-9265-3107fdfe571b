package com.quhong.admin.service.official.event.unit;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.quhong.admin.constant.PageConsts;
import com.quhong.admin.data.dto.official.activity.unit.CreateEventDTO;
import com.quhong.admin.data.dto.official.activity.unit.UpdateEventDTO;
import com.quhong.admin.data.vo.official.event.model.RankRowVO;
import com.quhong.admin.data.vo.official.event.unit.CopyUnitEventVO;
import com.quhong.admin.data.vo.official.event.unit.UnitEventVO;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.ActType;
import com.quhong.constant.activity.EventGroupConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.date.DayTimeSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.AppEventDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.db.AppEventData;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.PageResultVo;
import com.quhong.exceptions.WebException;
import com.quhong.players.ActorMgr;
import com.quhong.redis.event.EventRankUnitService;
import com.quhong.utils.StringUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 组件化活动配置服务
 * <AUTHOR>
 * @since 2025-06-03 15:48
 */
@Slf4j
@Service
public class UnitEventOptService {

    @Resource
    private AppConfigActivityDao appConfigActivityDao;
    @Resource
    private AppEventDao appEventDao;
    @Resource
    private EventRankUnitService eventRankUnitService;
    @Resource
    private ActorMgr actorMgr;

    private final String url;

    public UnitEventOptService() {
        if (ServerConfiguration.isProduct()) {
            url = "https://www.baidu.com/?eventType=";
        } else {
            url = "http://www.baidu.com/?eventType=";
        }
    }

    public List<RankRowVO> queryRankData(Integer eventCode, Integer unitId) {
        List<CountVO> rankList = eventRankUnitService.getRankList(eventCode, unitId, 1, 100);
        if (ObjectUtils.isEmpty(rankList)) {
            return new ArrayList<>();
        }
        return IntStream.range(0, rankList.size())
                .mapToObj(index -> this.fillRankRowVO(index, rankList.get(index)))
                .collect(Collectors.toList());
    }

    private RankRowVO fillRankRowVO(int index, CountVO countVO) {
        ActorData actorDataFromCache = actorMgr.getActorDataFromCache(countVO.getUid());
        long rid = 0;
        if (actorDataFromCache != null) {
            rid = actorDataFromCache.getRid();
        }
        return new RankRowVO()
                .setRankNum(index + 1)
                .setRid(rid)
                .setUid(countVO.getUid())
                .setRankCount(new Double(countVO.fetchRealCount()).longValue())
                .setDoubleCount(countVO.fetchRealCount());
    }

    /**
     * 活动状态过滤器
     */
    @Getter
    private static class ActivityStatusFilter {
        private final Long startGreaterThan;
        private final Long startLessThanAndEqualTo;
        private final Long endGreaterThan;
        private final Long endLessThan;
        private final String statusDescription;

        private ActivityStatusFilter(Long startGreaterThan, Long startLessThanAndEqualTo,
                                   Long endGreaterThan, Long endLessThan, String statusDescription) {
            this.startGreaterThan = startGreaterThan;
            this.startLessThanAndEqualTo = startLessThanAndEqualTo;
            this.endGreaterThan = endGreaterThan;
            this.endLessThan = endLessThan;
            this.statusDescription = statusDescription;
        }

        public static ActivityStatusFilter create(int status, long currentTime) {
            switch (status) {
                case 1: // 未开始
                    return new ActivityStatusFilter(currentTime, null, null, null, "未开始");
                case 2: // 进行中
                    return new ActivityStatusFilter(null, currentTime, currentTime, null, "进行中");
                case 3: // 已结束
                    return new ActivityStatusFilter(null, null, null, currentTime, "已结束");
                case 0: // ALL
                default:
                    return new ActivityStatusFilter(null, null, null, null, "");
            }
        }
    }

    /**
     * 根据当前时间和活动时间判断活动状态
     */
    private String determineActivityStatus(long currentTime, long startTime, long endTime) {
        if (currentTime < startTime) {
            return "未开始";
        } else if (currentTime < endTime) {
            return "进行中";
        } else {
            return "已结束";
        }
    }

    /**
     * 查询组件化活动列表
     */
    public PageResultVo<List<UnitEventVO>> queryList(Integer status, String zoneOffset, Integer page, Integer valid, Integer eventGroup) {
        // 设置默认页码
        if (page == null || page < 1) {
            page = 1;
        }

        long currTime = DateHelper.getCurrTime();

        // 使用过滤器来处理状态逻辑
        ActivityStatusFilter filter = ActivityStatusFilter.create(status, currTime);

        // 分页查询
        PageHelper.startPage(page, PageConsts.PAGE_SIZE_10);
        List<AppConfigActivityData> dataList = appConfigActivityDao.queryListBy(
                zoneOffset, valid, eventGroup,
                filter.getStartGreaterThan(), filter.getStartLessThanAndEqualTo(),
                filter.getEndGreaterThan(), filter.getEndLessThan());

        // 构建分页结果
        PageResultVo<List<UnitEventVO>> vo = buildPageResult(dataList);

        // 转换为VO对象
        List<UnitEventVO> voList = dataList.stream()
                .map(data -> convertToVO(data, filter.getStatusDescription(), currTime))
                .collect(Collectors.toList());

        vo.setData(voList);
        return vo;
    }

    /**
     * 构建分页结果
     */
    private PageResultVo<List<UnitEventVO>> buildPageResult(List<AppConfigActivityData> dataList) {
        PageInfo<AppConfigActivityData> pageInfo = new PageInfo<>(dataList);
        return new PageResultVo<>((int) pageInfo.getTotal(), new ArrayList<>());
    }

    /**
     * 创建组件化活动
     */
    public boolean createEvent(CreateEventDTO dto) {
        checkCreateParams(dto);
        long currTime = DateHelper.getCurrTime();
        AppEventData eventData = saveAppEventData(dto, currTime);
        saveAppConfigActivityData(dto, eventData, currTime);
        return true;
    }

    /**
     * 更新组件化活动
     */
    public boolean updateEvent(UpdateEventDTO dto) {
        checkUpdateParams(dto);
        return updateAppConfigActivityData(dto);
    }

    /**
     * 逻辑删除组件化活动
     */
    public boolean logicDelActivity(Integer id, String username, Integer eventCode) {
        if (id == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "id is empty"), true);
        }
        return logicDelAppConfigActivity(id, username, eventCode);
    }

    /**
     * 拷贝组件化活动
     */
    public CopyUnitEventVO copyEvent(Integer eventCode) {
        if (eventCode == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "eventCode is empty"), true);
        }
        AppConfigActivityData configData = appConfigActivityDao.getOneByCodeAndValid(eventCode, -1);
        if (configData == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "not found this event,eventCode=" + eventCode), true);
        }
        return new CopyUnitEventVO()
                .setName(configData.getName())
                .setEventDesc(configData.getActivityDesc())
                .setZoneOffset(configData.getZoneOffset())
                .setDateFormat(configData.getDateFormat())
                .setStartTime(DayTimeSupport.secFormatStr(configData.getStartTime(), configData.getZoneOffset(), configData.getDateFormat()))
                .setEndTime(DayTimeSupport.secFormatStr(configData.getEndTime(), configData.getZoneOffset(), configData.getDateFormat()))
                .setChannelSet(configData.getChannelSet())
                .setCountryCodeSet(configData.getCountryCodeSet())
                .setJoinModeInfo(configData.getJoinModeInfo())
                .setNotJoinRidSet(configData.getNotJoinRidSet())
                .setUnits(configData.getUnits())
                .setNoticeImg(configData.getNoticeImg())
                .setValid(configData.getValid());
    }

    /**
     * 转换为VO对象
     */
    private UnitEventVO convertToVO(AppConfigActivityData data, String activeStatus, long currTime) {
        // 如果没有预设状态，则根据时间动态计算
        if (StringUtils.isEmpty(activeStatus)) {
            activeStatus = determineActivityStatus(currTime, data.getStartTime(), data.getEndTime());
        }

        return new UnitEventVO().setId(data.getId())
                .setEventCode(data.getActivityCode())
                .setEventGroup(data.getEventGroup())
                .setName(data.getName())
                .setEventDesc(data.getActivityDesc())
                .setZoneOffset(data.getZoneOffset())
                .setDateFormat(data.getDateFormat())
                .setStartTime(DayTimeSupport.secFormatStr(data.getStartTime(), data.getZoneOffset(), data.getDateFormat()))
                .setEndTime(DayTimeSupport.secFormatStr(data.getEndTime(), data.getZoneOffset(), data.getDateFormat()))
                .setChannelSet(data.getChannelSet())
                .setCountryCodeSet(data.getCountryCodeSet())
                .setUrl(data.getUrl())
                .setJoinModeInfo(data.getJoinModeInfo())
                .setNotJoinRidSet(data.getNotJoinRidSet())
                .setUnits(data.getUnits())
                .setNoticeImg(data.getNoticeImg())
                .setValid(data.getValid())
                .setCtime(DayTimeSupport.secFormatStr(data.getCtime(), data.getZoneOffset(), data.getDateFormat()))
                .setMtime(DayTimeSupport.secFormatStr(data.getMtime(), data.getZoneOffset(), data.getDateFormat()))
                .setOperator(data.getOperator())
                .setActiveStatus(activeStatus);
    }

    /**
     * 逻辑删除活动配置
     */
    private boolean logicDelAppConfigActivity(Integer id, String username, Integer eventCode) {
        AppConfigActivityData delData = new AppConfigActivityData();
        delData.setId(id);
        delData.setActivityCode(eventCode);
        delData.setMtime(DateHelper.getCurrTime());
        delData.setValid(0);
        delData.setOperator(username);
        return appConfigActivityDao.updateOneSelective(delData);
    }

    /**
     * 更新活动配置数据
     */
    private boolean updateAppConfigActivityData(UpdateEventDTO dto) {
        AppConfigActivityData updateData = new AppConfigActivityData();
        updateData.setId(dto.getId().intValue());
        updateData.setActivityCode(dto.getEventCode());
        updateData.setName(dto.getName());
        updateData.setActivityDesc(dto.getEventDesc());
        updateData.setZoneOffset(dto.getZoneOffset());
        updateData.setDateFormat(dto.getDateFormat());
        updateData.setStartTime(DayTimeSupport.strParseToSec(dto.getStartTime(), dto.getZoneOffset(), dto.getDateFormat()));
        updateData.setEndTime(DayTimeSupport.strParseToSec(dto.getEndTime(), dto.getZoneOffset(), dto.getDateFormat()));
        updateData.setChannelSet(dto.getChannelSet());
        updateData.setCountryCodeSet(dto.getCountryCodeSet());
        updateData.setJoinModeInfo(dto.getJoinModeInfo());
        updateData.setNotJoinRidSet(dto.getNotJoinRidSet());
        updateData.setUnits(dto.getUnits());
        updateData.setNoticeImg(dto.getNoticeImg());
        updateData.setValid(dto.getValid());
        updateData.setMtime(DateHelper.getCurrTime());
        updateData.setOperator(dto.getOperator());
        return appConfigActivityDao.updateOneSelective(updateData);
    }

    /**
     * 保存活动配置数据
     */
    private void saveAppConfigActivityData(CreateEventDTO dto, AppEventData eventData, long currTime) {
        AppConfigActivityData configData = new AppConfigActivityData();
        configData.setActivityCode(eventData.getEventCode().intValue());
        configData.setEventGroup(EventGroupConstant.UNIT_EVENT);
        configData.setName(dto.getName());
        configData.setActivityDesc(dto.getEventDesc());
        configData.setZoneOffset(dto.getZoneOffset());
        configData.setDateFormat(dto.getDateFormat());
        configData.setStartTime(DayTimeSupport.strParseToSec(dto.getStartTime(), dto.getZoneOffset(), dto.getDateFormat()));
        configData.setEndTime(DayTimeSupport.strParseToSec(dto.getEndTime(), dto.getZoneOffset(), dto.getDateFormat()));
        configData.setActType((int) ActType.ACTIVITY_REWARD);
        configData.setChannelSet(dto.getChannelSet());
        configData.setCountryCodeSet(dto.getCountryCodeSet());
        configData.setJoinModeInfo(dto.getJoinModeInfo());
        configData.setNotJoinRidSet(dto.getNotJoinRidSet());
        configData.setUnits(dto.getUnits());
        configData.setNoticeImg(dto.getNoticeImg());
        configData.setValid(dto.getValid());
        configData.setCtime(currTime);
        configData.setMtime(currTime);
        configData.setOperator(dto.getOperator());
        configData.setUrl(url + eventData.getEventCode());
        appConfigActivityDao.insertOneSelective(configData);
    }

    /**
     * 保存活动事件数据
     */
    private AppEventData saveAppEventData(CreateEventDTO dto, long currTime) {
        AppEventData eventData = new AppEventData();
        eventData.setName(dto.getName());
        eventData.setEventDesc(dto.getEventDesc());
        eventData.setEventGroup(EventGroupConstant.UNIT_EVENT);
        eventData.setCtime(currTime);
        eventData.setMtime(currTime);
        eventData.setOperator(dto.getOperator());
        appEventDao.insertOneSelective(eventData);
        return eventData;
    }

    /**
     * 检查创建参数
     */
    private void checkCreateParams(CreateEventDTO dto) {
        if (StringUtils.isEmpty(dto.getName())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "name is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getZoneOffset())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "zoneOffset is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getDateFormat())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "dateFormat is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getStartTime())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "startTime is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getEndTime())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "endTime is empty"), true);
        }
        if (dto.getValid() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "valid is empty"), true);
        }
    }

    /**
     * 检查更新参数
     */
    private void checkUpdateParams(UpdateEventDTO dto) {
        if (dto.getId() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "id is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getName())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "name is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getZoneOffset())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "zoneOffset is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getDateFormat())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "dateFormat is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getStartTime())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "startTime is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getEndTime())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "endTime is empty"), true);
        }
        if (dto.getValid() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "valid is empty"), true);
        }
    }
}
