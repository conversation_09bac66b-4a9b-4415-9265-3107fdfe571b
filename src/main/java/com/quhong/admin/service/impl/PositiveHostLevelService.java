package com.quhong.admin.service.impl;

import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.constant.AppraiseScoreType;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.ActorDao;
import com.quhong.dao.HostAppraiseScoreLogDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.HostAppraiseScoreLogData;
import com.quhong.data.appConfig.HostAppraiseLevelCalculateConfig;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.data.host.HostQualityLevelUpdateMqData;
import com.quhong.monitor.CmdCodeEnum;
import com.quhong.mq.enums.MQConstant;
import com.quhong.mq.service.BaseMqProducer;
import com.quhong.redis.HostAppraiseRedis;
import com.quhong.service.ConfigApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName PositiveHostLevelService
 * <AUTHOR>
 * @date 2022/3/31 20:12
 */
@Service
public class PositiveHostLevelService {
    private static final Logger logger = LoggerFactory.getLogger(PositiveHostLevelService.class);

    @Autowired
    private ActorDao actorDao;
    @Autowired
    private MonitorSender monitorSender;
    @Autowired
    private HostAppraiseScoreLogDao hostAppraiseScoreLogDao;
    @Autowired
    private HostAppraiseRedis hostAppraiseRedis;
    @Autowired
    private ConfigApi configApi;
    @Resource
    private BaseMqProducer baseMqProducer;

    public void updateHostLevel(String username, Integer rid, Integer level, String operatorDesc, boolean sendMonitor) {
        updateHostLevel(username, rid, level, operatorDesc, AppraiseScoreType.NORMAL_SCORE_TYPE, null, sendMonitor);
    }

    public void updateHostLevel(String username, Integer rid, Integer level, String operatorDesc, int hostType, Integer hour, boolean sendMonitor) {
        ActorData actorRid = actorDao.getActorRid(rid);
        if (actorRid == null) {
            return;
        }
        int levelScore = getLevelScore(level, actorRid.getUid(), hostType);
        logger.info("update host level uid={} level={} levelScore={}", actorRid.getUid(), level, levelScore);
        String today = DateHelper.UTC.getToday();
        HostAppraiseScoreLogData data = getHostAppraiseScoreLogData(actorRid, levelScore, today, hostType);
        hostAppraiseScoreLogDao.insertOne(data);
        // 发布mq：发送host质量等级更新mq
        baseMqProducer.sendMq(MQConstant.USER_INFO_EXCHANGE, MQConstant.HOST_QUALITY_LEVEL_UPDATE_ROUTE,
                new HostQualityLevelUpdateMqData().setUid(actorRid.getUid())
                        .setLastDayLevelScore(0L)
                        .setCurrScore((long) levelScore));
        hostAppraiseRedis.addAppraiseHostScore(actorRid.getUid(), today, levelScore);
        if (hour != null) {
            hostAppraiseRedis.saveOperateHostScoreRecord(actorRid.getUid(), levelScore, hour);
        }
        if (sendMonitor) {
            sendMonitor(username, actorRid, operatorDesc, hour);
        }
    }

    private void sendMonitor(String username, ActorData actorRid, String operatorDesc, Integer hour) {
        String detail = "\n" + "rid: " + actorRid.getRid() + "\noperator: " + username + "\ndesc: " + operatorDesc + "\nhour: " + hour;
        String desc = "主播等级修改通知";
        monitorSender.info(CmdCodeEnum.PROBATION_NOTICE.getWarnName(), desc, detail);
    }

    private HostAppraiseScoreLogData getHostAppraiseScoreLogData(ActorData actorRid, int levelScore, String today, Integer hostType) {
        HostAppraiseScoreLogData data = new HostAppraiseScoreLogData();
        data.setUid(actorRid.getUid());
        data.setDate(today);
        data.setCtime(DateHelper.getCurrentTime());
        data.setHostType(hostType);
        data.setScore(levelScore);
        return data;
    }

    private int getLevelScore(Integer level, String uid, Integer hostType) {
        ConfigDTO dto = new ConfigDTO();
        dto.setKey(AppConfigKeyConstant.HOST_APPRAISE_LEVEL_CALCULATE_CONFIG);
        dto.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        dto.setUid(uid);
        HostAppraiseLevelCalculateConfig config = configApi.getJavaBeanVal(dto, HostAppraiseLevelCalculateConfig.class);
        int appraiseHostType = hostAppraiseScoreLogDao.getAppraiseHostType(uid, DateHelper.UTC.getToday());
        if (config == null || appraiseHostType == AppraiseScoreType.EXAMINE_HOST_SCORE_TYPE || hostType == AppraiseScoreType.EXAMINE_HOST_SCORE_TYPE) {
            config = new HostAppraiseLevelCalculateConfig(80, 70, 25);
        }
        int levelScore = 0;
        switch (level) {
            case 0:
                levelScore = 0;
                break;
            case 1:
                levelScore = config.getConfigL1MinScore();
                break;
            case 2:
                levelScore = config.getConfigL2MinScore();
                break;
            case 3:
                levelScore = config.getConfigL3MinScore();
                break;
            default:
                break;
        }
        return levelScore;
    }

}
