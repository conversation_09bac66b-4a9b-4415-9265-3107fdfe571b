package com.quhong.admin.service.sys;

import com.quhong.core.constant.RedisCacheManagerConstant;
import com.quhong.dao.data.AdminAuthData;
import com.quhong.dao.mapper.db.AdminAuthMapper;
import com.quhong.dao.slave.mapper.db.AdminAuthSlaveMapper;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * admin用户权限(admin_auth)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-07-21 14:05:38
 */
@Service
public class AdminAuthService {
    @Resource
    private AdminAuthSlaveMapper slaveMapper;
    @Resource
    private AdminAuthMapper mapper;

    /**
     * 通过ID查询单条数据
     *
     * @param authId 主键
     * @return 实例对象
     */

    public AdminAuthData queryById(Integer authId) {
        return this.slaveMapper.queryById(authId);
    }

    public List<AdminAuthData> queryAll(AdminAuthData data) {
        return slaveMapper.queryAll(data);
    }

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */

    public List<AdminAuthData> queryAllByLimit(int offset, int limit) {
        return this.slaveMapper.queryAllByLimit(offset, limit);
    }

    /**
     * 通过角色查询权限
     *
     * @param roleId 角色id
     * @return List<AdminAuth>
     * <AUTHOR>
     * @date 2021/07/25 20:42
     **/

    public List<AdminAuthData> queryByRoleId(Integer roleId) {
        return slaveMapper.queryByRoleId(roleId);
    }

    /**
     * 通过角色idset 获取权限列表
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2021/07/25 20:49
     **/

    public List<AdminAuthData> queryByRoleIdSet(Set<Integer> ridSet) {
        if (CollectionUtils.isEmpty(ridSet)) {
            return new ArrayList<>();
        }
        return slaveMapper.queryByRoleIdSet(ridSet);
    }

    /**
     * 获取所有有效权限（带缓存）
     *
     * @return 所有有效权限列表
     */
    @Cacheable(value = "adminAuth", key = "'allValidAuths'",
               cacheManager = RedisCacheManagerConstant.CAFFEINE_EXPIRE_5MIN_AFTER_WRITE_MANAGER)
    public List<AdminAuthData> getAllValidAuths() {
        AdminAuthData condition = new AdminAuthData();
        condition.setStatus(1); // 只获取有效权限
        return slaveMapper.queryAll(condition);
    }

    /**
     * 获取权限树（优化版本，避免多次数据库查询）
     *
     * @param userAuths 用户拥有的权限列表
     * @return 权限树
     * <AUTHOR>
     * @date 2021/07/25 22:14
     **/
    public List<AdminAuthData> getAuthTree(List<AdminAuthData> userAuths) {
        if (userAuths == null) {
            return null;
        }

        // 检查是否是查询所有权限的情况（管理界面使用）
        List<AdminAuthData> allValidAuths = getAllValidAuths();
        if (userAuths.size() == allValidAuths.size()) {
            // 如果传入的是所有权限，直接构建完整权限树
            return buildCompleteAuthTree(allValidAuths);
        }

        // 将用户权限转换为Set，便于快速查找
        Set<Integer> userAuthIds = userAuths.stream()
                .map(AdminAuthData::getAuthId)
                .collect(Collectors.toSet());

        // 构建权限树，只包含用户有权限的节点及其必要的父节点
        return buildAuthTree(allValidAuths, userAuthIds);
    }

    /**
     * 构建完整权限树（用于管理界面显示所有权限）
     *
     * @param allValidAuths 所有有效权限
     * @return 完整权限树
     */
    private List<AdminAuthData> buildCompleteAuthTree(List<AdminAuthData> allValidAuths) {
        // 创建权限ID到权限对象的映射
        Map<Integer, AdminAuthData> authMap = allValidAuths.stream()
                .collect(Collectors.toMap(AdminAuthData::getAuthId, auth -> {
                    // 创建新的对象避免修改原始缓存数据
                    AdminAuthData newAuth = new AdminAuthData();
                    newAuth.setAuthId(auth.getAuthId());
                    newAuth.setAuthName(auth.getAuthName());
                    newAuth.setParentId(auth.getParentId());
                    newAuth.setOrderNum(auth.getOrderNum());
                    newAuth.setPath(auth.getPath());
                    newAuth.setComponent(auth.getComponent());
                    newAuth.setPerms(auth.getPerms());
                    newAuth.setAuthType(auth.getAuthType());
                    newAuth.setIcon(auth.getIcon());
                    newAuth.setStatus(auth.getStatus());
                    return newAuth;
                }));

        // 找出根节点
        List<AdminAuthData> rootNodes = allValidAuths.stream()
                .filter(auth -> auth.getParentId() == 0)
                .map(auth -> authMap.get(auth.getAuthId()))
                .collect(Collectors.toList());

        // 为每个根节点构建子树
        for (AdminAuthData rootNode : rootNodes) {
            buildCompleteChildren(rootNode, authMap);
        }

        // 按order_num排序
        rootNodes.sort(Comparator.comparing(AdminAuthData::getOrderNum, Comparator.nullsLast(Comparator.naturalOrder())));

        return rootNodes;
    }

    /**
     * 递归构建完整子节点
     *
     * @param parentNode 父节点
     * @param authMap 权限映射
     */
    private void buildCompleteChildren(AdminAuthData parentNode, Map<Integer, AdminAuthData> authMap) {
        List<AdminAuthData> children = authMap.values().stream()
                .filter(auth -> parentNode.getAuthId().equals(auth.getParentId()))
                .collect(Collectors.toList());

        for (AdminAuthData child : children) {
            buildCompleteChildren(child, authMap);
        }

        // 按order_num排序
        children.sort(Comparator.comparing(AdminAuthData::getOrderNum, Comparator.nullsLast(Comparator.naturalOrder())));
        parentNode.setSonAuths(children);
    }

    /**
     * 构建权限树（优化版本）
     *
     * @param allValidAuths 所有有效权限
     * @param userAuthIds 用户拥有的权限ID集合
     * @return 权限树
     */
    private List<AdminAuthData> buildAuthTree(List<AdminAuthData> allValidAuths, Set<Integer> userAuthIds) {
        // 创建权限ID到权限对象的映射
        Map<Integer, AdminAuthData> authMap = allValidAuths.stream()
                .collect(Collectors.toMap(AdminAuthData::getAuthId, auth -> {
                    // 创建新的对象避免修改原始数据
                    AdminAuthData newAuth = new AdminAuthData();
                    newAuth.setAuthId(auth.getAuthId());
                    newAuth.setAuthName(auth.getAuthName());
                    newAuth.setParentId(auth.getParentId());
                    newAuth.setOrderNum(auth.getOrderNum());
                    newAuth.setPath(auth.getPath());
                    newAuth.setComponent(auth.getComponent());
                    newAuth.setPerms(auth.getPerms());
                    newAuth.setAuthType(auth.getAuthType());
                    newAuth.setIcon(auth.getIcon());
                    newAuth.setStatus(auth.getStatus());
                    return newAuth;
                }));

        // 找出需要显示的权限ID（根据权限继承逻辑）
        Set<Integer> requiredAuthIds = new HashSet<>(userAuthIds);

        // 添加父节点
        for (Integer authId : userAuthIds) {
            addParentAuthIds(authId, authMap, requiredAuthIds);
        }

        // 根据权限继承逻辑添加子节点
        for (Integer authId : new HashSet<>(userAuthIds)) {
            addChildAuthIdsWithInheritance(authId, authMap, userAuthIds, requiredAuthIds);
        }

        // 构建树结构
        List<AdminAuthData> rootNodes = new ArrayList<>();
        for (AdminAuthData auth : allValidAuths) {
            if (requiredAuthIds.contains(auth.getAuthId())) {
                AdminAuthData authNode = authMap.get(auth.getAuthId());
                if (authNode.getParentId() == 0) {
                    rootNodes.add(authNode);
                }
            }
        }

        // 为每个节点构建子节点
        for (AdminAuthData rootNode : rootNodes) {
            buildChildren(rootNode, authMap, requiredAuthIds);
        }

        // 按order_num排序
        rootNodes.sort(Comparator.comparing(AdminAuthData::getOrderNum, Comparator.nullsLast(Comparator.naturalOrder())));

        return rootNodes;
    }

    /**
     * 递归添加父节点ID
     *
     * @param authId 权限ID
     * @param authMap 权限映射
     * @param requiredAuthIds 需要的权限ID集合
     */
    private void addParentAuthIds(Integer authId, Map<Integer, AdminAuthData> authMap, Set<Integer> requiredAuthIds) {
        AdminAuthData auth = authMap.get(authId);
        if (auth != null && auth.getParentId() != 0 && !requiredAuthIds.contains(auth.getParentId())) {
            requiredAuthIds.add(auth.getParentId());
            addParentAuthIds(auth.getParentId(), authMap, requiredAuthIds);
        }
    }

    /**
     * 根据权限继承逻辑添加子节点ID
     * 逻辑：
     * 1. 如果同时拥有父权限和子权限：只展示父权限和用户实际拥有的子权限
     * 2. 如果只有父权限：展示父权限下的所有子权限（继承逻辑）
     *
     * @param authId 权限ID
     * @param authMap 权限映射
     * @param userAuthIds 用户拥有的权限ID集合
     * @param requiredAuthIds 需要的权限ID集合
     */
    private void addChildAuthIdsWithInheritance(Integer authId, Map<Integer, AdminAuthData> authMap,
                                               Set<Integer> userAuthIds, Set<Integer> requiredAuthIds) {
        // 获取当前权限的所有直接子权限
        List<AdminAuthData> directChildren = authMap.values().stream()
                .filter(auth -> authId.equals(auth.getParentId()))
                .collect(Collectors.toList());

        if (directChildren.isEmpty()) {
            return; // 没有子权限，直接返回
        }

        // 检查用户是否拥有任何子权限
        boolean hasAnyChildAuth = directChildren.stream()
                .anyMatch(child -> userAuthIds.contains(child.getAuthId()));

        if (hasAnyChildAuth) {
            // 情况1：同时拥有父权限和子权限，只添加用户实际拥有的子权限
            for (AdminAuthData child : directChildren) {
                if (userAuthIds.contains(child.getAuthId())) {
                    requiredAuthIds.add(child.getAuthId());
                    // 递归处理子权限的子权限
                    addChildAuthIdsWithInheritance(child.getAuthId(), authMap, userAuthIds, requiredAuthIds);
                }
            }
        } else {
            // 情况2：只有父权限，添加所有子权限（继承逻辑）
            for (AdminAuthData child : directChildren) {
                requiredAuthIds.add(child.getAuthId());
                // 递归添加所有子权限的子权限
                addAllChildAuthIds(child.getAuthId(), authMap, requiredAuthIds);
            }
        }
    }

    /**
     * 递归添加所有子权限（用于权限继承）
     *
     * @param authId 权限ID
     * @param authMap 权限映射
     * @param requiredAuthIds 需要的权限ID集合
     */
    private void addAllChildAuthIds(Integer authId, Map<Integer, AdminAuthData> authMap, Set<Integer> requiredAuthIds) {
        for (AdminAuthData auth : authMap.values()) {
            if (authId.equals(auth.getParentId()) && !requiredAuthIds.contains(auth.getAuthId())) {
                requiredAuthIds.add(auth.getAuthId());
                // 递归添加子节点的子节点
                addAllChildAuthIds(auth.getAuthId(), authMap, requiredAuthIds);
            }
        }
    }

    /**
     * 递归构建子节点
     *
     * @param parentNode 父节点
     * @param authMap 权限映射
     * @param requiredAuthIds 需要的权限ID集合
     */
    private void buildChildren(AdminAuthData parentNode, Map<Integer, AdminAuthData> authMap, Set<Integer> requiredAuthIds) {
        List<AdminAuthData> children = new ArrayList<>();
        for (AdminAuthData auth : authMap.values()) {
            if (requiredAuthIds.contains(auth.getAuthId()) &&
                parentNode.getAuthId().equals(auth.getParentId())) {
                children.add(auth);
                buildChildren(auth, authMap, requiredAuthIds);
            }
        }
        // 按order_num排序
        children.sort(Comparator.comparing(AdminAuthData::getOrderNum, Comparator.nullsLast(Comparator.naturalOrder())));
        parentNode.setSonAuths(children);
    }

    /**
     * 新增数据
     *
     * @param adminAuth 实例对象
     * @return 实例对象
     */
    @CacheEvict(value = "adminAuth", key = "'allValidAuths'",
                cacheManager = RedisCacheManagerConstant.CAFFEINE_EXPIRE_5MIN_AFTER_WRITE_MANAGER)
    public AdminAuthData insert(AdminAuthData adminAuth) {
        mapper.insertSelective(adminAuth);
        return adminAuth;
    }

    /**
     * 修改数据
     *
     * @param adminAuth 实例对象
     * @return 实例对象
     */
    @CacheEvict(value = "adminAuth", key = "'allValidAuths'",
                cacheManager = RedisCacheManagerConstant.CAFFEINE_EXPIRE_5MIN_AFTER_WRITE_MANAGER)
    public AdminAuthData update(AdminAuthData adminAuth) {
        mapper.update(adminAuth);
        return this.queryById(adminAuth.getAuthId());
    }

    /**
     * 通过主键删除数据
     *
     * @param authId 主键
     * @return 是否成功
     */
    @CacheEvict(value = "adminAuth", key = "'allValidAuths'",
                cacheManager = RedisCacheManagerConstant.CAFFEINE_EXPIRE_5MIN_AFTER_WRITE_MANAGER)
    public boolean deleteById(Integer authId) {
        return mapper.deleteById(authId) > 0;
    }
}
