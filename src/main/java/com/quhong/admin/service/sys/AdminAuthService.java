package com.quhong.admin.service.sys;

import com.quhong.dao.data.AdminAuthData;
import com.quhong.dao.mapper.db.AdminAuthMapper;
import com.quhong.dao.slave.mapper.db.AdminAuthSlaveMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * admin用户权限(admin_auth)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-07-21 14:05:38
 */
@Service
public class AdminAuthService {
    @Resource
    private AdminAuthSlaveMapper slaveMapper;
    @Resource
    private AdminAuthMapper mapper;

    /**
     * 通过ID查询单条数据
     *
     * @param authId 主键
     * @return 实例对象
     */

    public AdminAuthData queryById(Integer authId) {
        return this.slaveMapper.queryById(authId);
    }

    public List<AdminAuthData> queryAll(AdminAuthData data) {
        return slaveMapper.queryAll(data);
    }

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */

    public List<AdminAuthData> queryAllByLimit(int offset, int limit) {
        return this.slaveMapper.queryAllByLimit(offset, limit);
    }

    /**
     * 通过角色查询权限
     *
     * @param roleId 角色id
     * @return List<AdminAuth>
     * <AUTHOR>
     * @date 2021/07/25 20:42
     **/

    public List<AdminAuthData> queryByRoleId(Integer roleId) {
        List<AdminAuthData> auths = slaveMapper.queryByRoleId(roleId);
        return auths;
    }

    /**
     * 通过角色idset 获取权限列表
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2021/07/25 20:49
     **/

    public List<AdminAuthData> queryByRoleIdSet(Set<Integer> ridSet) {
        if (CollectionUtils.isEmpty(ridSet)) {
            return new ArrayList<>();
        }
        return slaveMapper.queryByRoleIdSet(ridSet);
    }

    /**
     * 获取权限树
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2021/07/25 22:14
     **/

    public List<AdminAuthData> getAuthTree(List<AdminAuthData> auths) {
        if (auths == null) {
            return null;
        }
        List<AdminAuthData> result = new ArrayList<>();
        for (AdminAuthData adModule : auths) {
            // 根节点
            if (new Integer(0).equals(adModule.getParentId())) {
                result.add(getChildless(adModule, auths));
            }
        }
        return result;
    }

    /**
     * 获取子节点
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2021/07/25 23:21
     **/
    private AdminAuthData getChildless(AdminAuthData module, List<AdminAuthData> list) {
        List<AdminAuthData> childNodes = new ArrayList<>();
        for (AdminAuthData node : list) {
            //将赋予了子权限设置为父权限的子节点
            if (node.getParentId().equals(module.getAuthId())) {
                childNodes.add(getChildless(node, list));
            }
        }
        if (childNodes.isEmpty()) {
            //查询当前权限的所有子权限
            AdminAuthData auth = new AdminAuthData();
            auth.setParentId(module.getAuthId());
            auth.setStatus(1);
            childNodes = slaveMapper.queryAll(auth);
            if (childNodes != null && !childNodes.isEmpty()) {
                for (int i = 0; i < childNodes.size(); i++) {
                    // ** 有针对每个页面的对应按钮时，需要进行调整
                    childNodes.set(i, getChildless(childNodes.get(i), list));
                }
            }
        }
        module.setSonAuths(childNodes);
        return module;
    }


    /**
     * 新增数据
     *
     * @param adminAuth 实例对象
     * @return 实例对象
     */

    public AdminAuthData insert(AdminAuthData adminAuth) {
        mapper.insertSelective(adminAuth);
        return adminAuth;
    }

    /**
     * 修改数据
     *
     * @param adminAuth 实例对象
     * @return 实例对象
     */

    public AdminAuthData update(AdminAuthData adminAuth) {
        mapper.update(adminAuth);
        return this.queryById(adminAuth.getAuthId());
    }

    /**
     * 通过主键删除数据
     *
     * @param authId 主键
     * @return 是否成功
     */

    public boolean deleteById(Integer authId) {
        return mapper.deleteById(authId) > 0;
    }
}
