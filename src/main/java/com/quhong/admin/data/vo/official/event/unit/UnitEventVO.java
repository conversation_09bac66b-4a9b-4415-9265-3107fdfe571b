package com.quhong.admin.data.vo.official.event.unit;

import com.quhong.constant.activity.EventGroupConstant;
import com.quhong.dao.datas.app.config.activity.model.JoinModeInfo;
import com.quhong.dao.datas.app.config.activity.unit.EventUnit;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025-06-03 15:59
 */
@Data
@Accessors(chain = true)
public class UnitEventVO {
    /**
     * 唯一键（只用作运营平台增删改，不展示）
     */
    private Integer id;
    /**
     * 活动code
     */
    private Integer eventCode;
    /**
     * 事件分组 1:PK活动 2活动模板分组 3组件化活动
     * @see EventGroupConstant
     */
    private Integer eventGroup;
    /**
     * 名字
     */
    private String name;
    /**
     * 描述
     */
    private String eventDesc;
    /**
     * 活动使用时区
     * 常量接口 com.quhong.constant.activity.model.event.mode.ZoneOffsetConstant
     * @see com.quhong.constant.activity.model.event.mode.ZoneOffsetConstant 时区偏移常量
     */
    private String zoneOffset;
    /**
     * 活动格式化日期格式
     * 常量接口 com.quhong.constant.activity.model.event.mode.DateFormatPatternConstant
     * @see com.quhong.constant.activity.model.event.mode.DateFormatPatternConstant 日期格式常量
     */
    private String dateFormat;
    /**
     * 活动开始时间
     */
    private String startTime;
    /**
     * 活动结束时间
     */
    private String endTime;
    /**
     * 渠道列表（为空则不限制）
     */
    private Set<String> channelSet;
    /**
     * 国家列表（为空则不限制）
     */
    private Set<String> countryCodeSet;

    private String url;
    /**
     * 参与方式信息
     */
    private JoinModeInfo joinModeInfo;
    /**
     * 禁止参与用户rid列表
     */
    private Set<Integer> notJoinRidSet;

    /**
     * 活动组件列表
     */
    private List<EventUnit> units;

    /**
     * 通知图片
     */
    private String noticeImg;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer valid;

    private String ctime;

    private String mtime;

    private String operator;
    /**
     * 活动状态 1:未开始 2:进行中 3:已结束
     */
    private String activeStatus;
}
