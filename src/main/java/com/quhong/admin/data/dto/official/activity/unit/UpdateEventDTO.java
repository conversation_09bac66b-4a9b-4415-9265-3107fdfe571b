package com.quhong.admin.data.dto.official.activity.unit;

import com.quhong.dao.datas.app.config.activity.model.JoinModeInfo;
import com.quhong.dao.datas.app.config.activity.unit.EventUnit;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/2/3 09:55
 */
@Data
public class UpdateEventDTO {
    /**
     * 唯一键（非eventCode）
     */
    private Long id;
    /**
     * 名字
     */
    private String name;
    /**
     * 活动码
     */
    private Integer eventCode;
    /**
     * 描述
     */
    private String eventDesc;
    /**
     * 活动使用时区
     * 常量接口 com.quhong.constant.activity.model.event.mode.ZoneOffsetConstant
     * @see com.quhong.constant.activity.model.event.mode.ZoneOffsetConstant 时区偏移常量
     */
    private String zoneOffset;
    /**
     * 活动格式化日期格式 默认选中不可不选
     * 常量接口 com.quhong.constant.activity.model.event.mode.DateFormatPatternConstant
     * @see com.quhong.constant.activity.model.event.mode.DateFormatPatternConstant 日期格式常量
     */
    private String dateFormat;
    /**
     * 活动开始时间 yyyy-MM-dd HH:mm:ss
     */
    private String startTime;
    /**
     * 活动结束时间 yyyy-MM-dd HH:mm:ss
     */
    private String endTime;
    /**
     * 渠道列表（为空列表则不限制）
     */
    private Set<String> channelSet;
    /**
     * 国家列表（为空列表则不限制）
     */
    private Set<String> countryCodeSet;
    /**
     * 参与方式信息
     */
    private JoinModeInfo joinModeInfo;
    /**
     * 禁止参与用户rid列表
     */
    private Set<Integer> notJoinRidSet;

    /**
     * 活动组件列表
     */
    private List<EventUnit> units;

    /**
     * 通知图片
     */
    private String noticeImg;

    /**
     * 奖励key列表
     */
    private Set<String> awardsKeys;

    /**
     * 成本限制预警 持续时间类型 目前只支持一下4种
     * "" 活动期间
     * "0" 每天
     * "5" 每周
     * "6" 每月
     * @see com.quhong.constant.DurationTypeConstant
     */
    private String costLimitDurationType;

    /**
     * 1倍最大成本限制
     */
    private Long maxCostLimit;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer valid;
    /**
     * 操作人
     */
    private String operator;

}
