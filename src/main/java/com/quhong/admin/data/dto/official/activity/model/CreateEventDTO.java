package com.quhong.admin.data.dto.official.activity.model;

import com.quhong.dao.datas.app.config.activity.model.EventModeInfo;
import com.quhong.dao.datas.app.config.activity.model.JoinModeInfo;
import com.quhong.dao.datas.app.config.activity.model.page.ModelPageInfo;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/2/3 09:40
 */
@Data
public class CreateEventDTO {
    /**
     * 名字
     */
    private String name;
    /**
     * 描述
     */
    private String eventDesc;
    /**
     * 活动使用时区
     * 常量接口 com.quhong.constant.activity.model.event.mode.ZoneOffsetConstant
     * @see com.quhong.constant.activity.model.event.mode.ZoneOffsetConstant 时区偏移常量
     */
    private String zoneOffset;
    /**
     * 活动格式化日期格式 默认选中不可不选
     * 常量接口 com.quhong.constant.activity.model.event.mode.DateFormatPatternConstant
     * @see com.quhong.constant.activity.model.event.mode.DateFormatPatternConstant 日期格式常量
     */
    private String dateFormat;
    /**
     * 活动开始时间 yyyy-MM-dd HH:mm:ss
     */
    private String startTime;
    /**
     * 活动结束时间 yyyy-MM-dd HH:mm:ss
     */
    private String endTime;
    /**
     * 渠道列表（为空列表则不限制）
     */
    private Set<String> channelSet;
    /**
     * 国家列表（为空列表则不限制）
     */
    private Set<String> countryCodeSet;
    /**
     * 参与方式信息
     */
    private JoinModeInfo joinModeInfo;
    /**
     * 禁止参与用户rid列表
     */
    private Set<Integer> notJoinRidSet;
    /**
     * 活动方式信息
     */
    private EventModeInfo eventModeInfo;
    /**
     * 榜单类型 列表
     * 常量接口 com.quhong.core.annotation.ActivityRankType
     *
     * @see com.quhong.core.annotation.ActivityRankType 活动榜单类型
     */
    private Set<Integer> rankType;
    /**
     * 页面信息
     */
    private ModelPageInfo pageInfo;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer valid;
    /**
     * 操作人
     */
    private String operator;


}
