package com.quhong.admin.scheduled;

import com.quhong.admin.enums.AdminChannelEnum;
import com.quhong.admin.service.impl.CallingStatService;
import com.quhong.admin.service.impl.PayRecordStatService;
import com.quhong.admin.utils.DateUtils;
import com.quhong.core.concurrency.queues.TaskQueue;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.datas.DayTimeData;
import com.quhong.core.utils.MasterUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 统计 模块定时缓存任务
 */
@Component
@Async
public class StatServiceScheduled {

    private static final Logger logger = LoggerFactory.getLogger(StatServiceScheduled.class);

    @Autowired
    private PayRecordStatService payRecordStatDao;

    @Autowired
    private CallingStatService callingStatDao;

    @Resource
    private MasterUtils masterUtils;

    private static final Long PERIOD = 8 * 60 * 60 * 1000L;

    /**
     *  1、存储所有用户充值数据
     *  2、日期：凌晨8点42（北京时间）、正式服utc时间是00:42
     */
    @Scheduled(cron = "0 42 0 * * ?")
    public void savePayRecord(){
        if (!masterUtils.isMaster()) {
            return;
        }
        try {
            //获取前一天的时间戳
            Date date = new Date(new Date().getTime() - PERIOD);
            doPayRecordSave(date);
        }catch(Exception e){
            logger.error(e.getMessage(),e);
        }
    }

    /**
     *  1、存储通话数据
     *  2、日期：凌晨8点55（北京时间）、正式服utc时间是前一天00:55
     */
//    @Scheduled(cron = "0 55 0 * * ?")
    public void saveCallingStat(){
//        if (!masterUtils.isMaster()) {
//            return;
//        }
//        try {
//            //获取前一天的时间戳
//            Date date = new Date(new Date().getTime() - PERIOD);
//            doCallingStatSave(date);
//        }catch(Exception e){
//            logger.error(e.getMessage(),e);
//        }
    }

    /**
     * 用户充值内容存储
     */
    private void doPayRecordSave(Date date){
        String start = DateUtils.DateToStr(date, DateUtils.DATENORM_DAY);
        DayTimeData dayTimeData = DateHelper.UTC.getContinuesDays(start);
        AdminChannelEnum[] values = AdminChannelEnum.values();
        TaskQueue taskQueue = new TaskQueue();
        for (AdminChannelEnum adminChannelEnum : values) {
            String channel = adminChannelEnum.getValue();
            if (channel != null && channel.equals("total")){
                channel = null;
            }
            String finalChannel = channel;
            taskQueue.add(new Task() {
                @Override
                protected void execute() {
                    logger.info("set PayStat service cache set date = {},channel = {},start = {},end = {}",start,finalChannel,dayTimeData.getTime(),dayTimeData.getEndTime());
                    payRecordStatDao.getPayStatData(dayTimeData,finalChannel,-1,null);
                }
            });
        }
    }

    /**
     * 存储通话数据
     * @param date
     */
    private void doCallingStatSave(Date date){
//        String start = DateUtils.DateToStr(date, DateUtils.DATENORM_DAY);
//        DayTimeData dayTimeData = DateHelper.UTC.getContinuesDays(start);
//        AdminChannelEnum[] values = AdminChannelEnum.values();
//        TaskQueue taskQueue = new TaskQueue();
//        for (AdminChannelEnum adminChannelEnum : values) {
//            String channel = adminChannelEnum.getValue();
//            if (channel != null && channel.equals("total")){
//                channel = null;
//            }
//            String finalChannel = channel;
//            taskQueue.add(new Task() {
//                @Override
//                protected void execute() {
//                    logger.info("set CallingStat service cache set date = {},channel = {},start = {},end = {}",start,finalChannel,dayTimeData.getTime(),dayTimeData.getEndTime());
//                    callingStatDao.getCallingData(dayTimeData,finalChannel,-1,-1);
//                }
//            });
//        }
    }
}
