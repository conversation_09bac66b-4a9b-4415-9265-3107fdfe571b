package com.quhong.admin.controller.callcount;

import com.quhong.admin.constant.DeferredResultConstant;
import com.quhong.admin.data.stats.CallingReasonGroup;
import com.quhong.admin.enums.HttpResultEnum;
import com.quhong.admin.service.impl.CallingReasonStatService;
import com.quhong.admin.controller.BaseHandel;
import com.quhong.common.utils.RequestUtils;
import com.quhong.core.concurrency.queues.SlowTaskQueue;
import com.quhong.core.concurrency.tasks.AbstractTask;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.datas.DayTimeData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import javax.servlet.http.HttpServletRequest;

/**
 * create by maoyule on 2020/2/14
 */
@RestController
@RequestMapping("${server.baseurl}statistics/")
public class CallReasonController extends BaseHandel {
    private static final Logger logger = LoggerFactory.getLogger(CallReasonController.class);

    private SlowTaskQueue taskQueue = new SlowTaskQueue();

    private CallingReasonStatService callingReasonStatService;

    @Autowired
    public CallReasonController(CallingReasonStatService callingReasonStatService) {
        //super(LimitPrivilegeData.allowStat());
        this.callingReasonStatService = callingReasonStatService;
    }

    /**
     * 统计-通话挂断原因 查询
     *
     * @param request
     * @return
     */
    @RequestMapping("call_reasons")
    public DeferredResult<String> queryData(HttpServletRequest request) {
//        DeferredResult<String> result = new DeferredResult<>(DeferredResultConstant.DEFERRED_RESULT_TIMEOUT);
//        // 权限检查
//        //String retStr = checkAllowOrGetResult(request);
//        //if (retStr != null) {
//        //    result.setResult(retStr);
//        //    return result;
//        //}
//        taskQueue.add(new AbstractTask() {
//            @Override
//            protected void execute() {
//                result.setResult(doQueryData(request));
//            }
//        });
//        return result;
        return null;
    }

    private String doQueryData(HttpServletRequest request) {
        String startDate = RequestUtils.getParam(request, "start");
        String channel = RequestUtils.getParam(request, "channel");
        int fromType = RequestUtils.getParamInt(request, "from_type");
        String toChannel = RequestUtils.getParam(request, "to_channel");
        logger.info("query call reasons. start={} channel={} fromType={} userName={},toChannel = {}", startDate, channel, fromType, getUserName(request), toChannel);
        if (channel != null && channel.equals("all")) {
            channel = null;
        }
        if (toChannel != null && toChannel.equals("all")) {
            toChannel = null;
        }
        DayTimeData timeData = DateHelper.UTC.getContinuesDays(startDate);
        CallingReasonGroup groupData;
        if (timeData.isMoreThenToday()) {
            groupData = new CallingReasonGroup();
            return createResult(HttpResultEnum.SUCCESS, groupData);
        } else {
            groupData = callingReasonStatService.getData(timeData, channel, fromType, toChannel);
        }
        return createResult(HttpResultEnum.SUCCESS, groupData);
    }
}
