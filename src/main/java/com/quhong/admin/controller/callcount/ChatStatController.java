package com.quhong.admin.controller.callcount;

import com.quhong.admin.constant.DeferredResultConstant;
import com.quhong.admin.controller.BaseHandel;
import com.quhong.admin.data.dto.CallRequestByChannelDTO;
import com.quhong.admin.data.dto.CallSuccessByChannelDTO;
import com.quhong.admin.data.stats.CallingStatData;
import com.quhong.admin.data.vo.UserCallSuccessRateVO;
import com.quhong.admin.enums.HttpResultEnum;
import com.quhong.admin.service.impl.CallingStatService;
import com.quhong.admin.service.sys.AdminChannelService;
import com.quhong.common.utils.RequestUtils;
import com.quhong.core.concurrency.queues.TaskQueue;
import com.quhong.core.concurrency.tree.TreeTask;
import com.quhong.core.datas.DayTimeData;
import com.quhong.core.enums.CallFromType;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.data.AdminChannelData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;
import org.thymeleaf.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@RestController
@RequestMapping("${server.baseurl}statistics/")
public class ChatStatController extends BaseHandel {
    private static final Logger logger = LoggerFactory.getLogger(ChatStatController.class);

    private final CallingStatService callingStatService;

    private final AdminChannelService adminChannelService;

    private final Map<String, TaskQueue> cacheMap = new ConcurrentHashMap<>();

    @Autowired
    public ChatStatController(CallingStatService callingStatService, AdminChannelService adminChannelService) {
        this.callingStatService = callingStatService;
        this.adminChannelService = adminChannelService;
    }

    /**
     * 通话成功率接口
     *
     * @param request 请求体
     * @param channel 渠道
     * @param start   日期
     * @return 结果json
     */
    @RequestMapping("chat_successRate")
    public DeferredResult<String> chatSuccessRate(HttpServletRequest request, @RequestParam(value = "channel", defaultValue = "all") String channel, String start) {
        DeferredResult<String> result = new DeferredResult<>(DeferredResultConstant.DEFERRED_RESULT_TIMEOUT);
        //结果集
        List<UserCallSuccessRateVO> successRateVOList = new ArrayList<>();

        //校验参数
        if (StringUtils.isEmpty(start) || StringUtils.isEmpty(channel)) {
            result.setResult(createResult(HttpResultEnum.PARAM_ERROR, null));
            return result;
        }

        //前端提交表单内的channel
        List<String> channelList;
        if ("".equals(channel) || "all".equals(channel) || "total".equals(channel)) {
            channelList = null;
        } else {
            channelList = new ArrayList<>(Arrays.asList(channel.split(",")));
        }

        //日期类生成
        DayTimeData timeData = DateHelper.UTC.getContinuesDays(start);
        if (timeData.isMoreThenToday()) {
            //返回null
            result.setResult(createResult(HttpResultEnum.SUCCESS));
            return result;
        }

        List<Integer> statusList = new ArrayList<>();
        statusList.add(-1);
        statusList.add(-22);

        //获取通话请求数
        List<CallRequestByChannelDTO> callRequestData = callingStatService.getChatRequestCountByChannel(timeData,
                channelList, CallFromType.FROM_USER, statusList);

        //排除status=-1, -4,-5,-22
        statusList.add(-4);
        statusList.add(-5);
        List<CallRequestByChannelDTO> callRequestData2 = callingStatService.getChatRequestCountByChannel(timeData,
                channelList, CallFromType.FROM_USER, statusList);
        Map<String, Integer> callRequest2Map = new LinkedHashMap<>();

        for (CallRequestByChannelDTO data : callRequestData2) {
            if (!StringUtils.isEmpty(data.getChannel())) {
                callRequest2Map.put(data.getChannel(), data.getRequestCount());
            }
        }

        //渠道名字替换
        List<AdminChannelData> channels = adminChannelService.queryAllChannel();
        Map<String, String> channelMap = new LinkedHashMap<>();
        channels.forEach(data -> channelMap.put(data.getValue(), data.getName()));

        //获取通话成功数
        List<CallSuccessByChannelDTO> callSuccessData = callingStatService.getChatRecordCountByChannel(timeData, channelList, CallFromType.FROM_USER, 0);

        for (CallRequestByChannelDTO dto : callRequestData) {
            String channelTemp = dto.getChannel();
            logger.info("channel:" + channelTemp);
            UserCallSuccessRateVO userCallSuccessRateVO = new UserCallSuccessRateVO();
            if (!"".equals(channelTemp) && !"null".equals(channelTemp) && channelTemp != null) {
                userCallSuccessRateVO.setChannel(channelTemp);
                logger.info(dto.toString());
                userCallSuccessRateVO.setRequestCount(dto.getRequestCount());
                successRateVOList.add(userCallSuccessRateVO);
            }
        }

        for (CallSuccessByChannelDTO data : callSuccessData) {
            for (UserCallSuccessRateVO vo : successRateVOList) {
                if (vo.getChannel().equalsIgnoreCase(data.getChannel())) {
                    vo.setSuccessCount(data.getCallSuccessCount());
                    vo.setSuccessRate(MathUtils.getRate(data.getCallSuccessCount(), vo.getRequestCount()));
                }
            }
        }

        for (UserCallSuccessRateVO vo : successRateVOList) {
            if (callRequest2Map.containsKey(vo.getChannel())) {
                vo.setRequestCount2(callRequest2Map.get(vo.getChannel()));
                vo.setSuccessRate2(MathUtils.getRate(vo.getSuccessCount(), vo.getRequestCount2()));
            }
            if (!StringUtils.isEmpty(channelMap.get(vo.getChannel().toLowerCase()))) {
                vo.setChannel(channelMap.get(vo.getChannel().toLowerCase()));
            }
        }

        //计算并加入当前列表总成功率
        getTotalSuccessRate(successRateVOList);

        result.setResult(createResult(HttpResultEnum.SUCCESS, successRateVOList));
        return result;
    }

    /**
     * 计算当前列表的总成功率
     *
     * @param successRateVOList 数据集合
     * <AUTHOR>
     * @date 2021/07/27 22:42
     **/
    private void getTotalSuccessRate(List<UserCallSuccessRateVO> successRateVOList) {
        UserCallSuccessRateVO total = new UserCallSuccessRateVO();
        total.setChannel("total");
        successRateVOList.forEach(vo -> {
            total.setRequestCount(total.getRequestCount() + vo.getRequestCount());
            total.setSuccessCount(total.getSuccessCount() + vo.getSuccessCount());
            total.setRequestCount2(total.getRequestCount2() + vo.getRequestCount2());
        });

        total.setSuccessRate(MathUtils.getRate(total.getSuccessCount(), total.getRequestCount()));
        total.setSuccessRate2(MathUtils.getRate(total.getSuccessCount(), total.getRequestCount2()));
        successRateVOList.add(total);
    }


    /**
     * 统计-通话 查询
     *
     * @param request 请求体
     * @return json
     */
    @RequestMapping("chat_video")
    public DeferredResult<String> videoChat(HttpServletRequest request) {
//        DeferredResult<String> result = new DeferredResult<>(DeferredResultConstant.DEFERRED_RESULT_TIMEOUT);
//        doCalling(request, result);
        return null;
    }

    private void doCalling(HttpServletRequest request, DeferredResult<String> result) {
        String startDate = RequestUtils.getParam(request, "start");
        String endDate = RequestUtils.getParam(request, "end");
        String channel = RequestUtils.getParam(request, "channel");
        int callType = RequestUtils.getParamInt(request, "call_type");
        int fromType = RequestUtils.getParamInt(request, "from_type");
        logger.info("query calling. start={} end={} channel={} callType={} fromType={} userName={}", startDate, endDate, channel, callType, fromType, getUserName(request));
        if (channel != null && channel.equals("all")) {
            channel = null;
        }
        List<DayTimeData> list = DateHelper.UTC.getContinuesDays(startDate, endDate);
        if (list.size() == 0) {
            result.setResult(createResult(HttpResultEnum.SUCCESS, new Object()));
            return;
        }
        String paramChannel = channel;
        TreeTask parentTask = new TreeTask() {
            @Override
            protected void doComplete(Object data) {
                logger.info("all complete. channel={}", paramChannel);
                if (getData() != null) {
                    List<CallingStatData> list = (List<CallingStatData>) getData();
                    list.sort(DATE_COMPARATOR);
                    result.setResult(createResult(HttpResultEnum.SUCCESS, list));
                }
            }

            @Override
            protected void execute() {
                //do nothing
            }
        };
        List<CallingStatData> retList = Collections.synchronizedList(new ArrayList<>());
        parentTask.setData(retList);
        for (DayTimeData timeData : list) {
            TreeTask subTask = new TreeTask(parentTask) {
                @Override
                protected void execute() {
                    if (timeData.isMoreThenToday()) {
                        CallingStatData statData = new CallingStatData();
                        statData.setDate(timeData.getDate());
                        retList.add(statData);
                    } else {
                        CallingStatData statData = callingStatService.getCallingData(timeData, paramChannel, fromType, callType);
                        retList.add(statData);
                    }
                }

                @Override
                protected void doComplete(Object data) {

                }
            };
            addTask(timeData, paramChannel, subTask);
        }
        parentTask.start();
    }

    private void addTask(DayTimeData timeData, String channel, TreeTask subTask) {
        String key = getKey(timeData, channel);
        TaskQueue queue = cacheMap.get(key);
        if (queue == null) {
            queue = new TaskQueue();
            cacheMap.put(key, queue);
        }
        queue.add(subTask);
    }

    private String getKey(DayTimeData dayTimeData, String channel) {
        return dayTimeData.getDate() + channel;
    }
}
