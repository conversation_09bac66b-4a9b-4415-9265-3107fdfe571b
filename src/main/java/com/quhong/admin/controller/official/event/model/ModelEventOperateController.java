package com.quhong.admin.controller.official.event.model;

import com.alibaba.fastjson.JSON;
import com.quhong.admin.data.dto.official.activity.model.CreateEventDTO;
import com.quhong.admin.data.dto.official.activity.model.UpdateEventDTO;
import com.quhong.admin.data.vo.official.event.model.CopyModelVO;
import com.quhong.admin.data.vo.official.event.model.EventModelVO;
import com.quhong.admin.data.vo.official.event.model.RankRowVO;
import com.quhong.admin.service.official.event.model.ModelEventOperateService;
import com.quhong.admin.utils.ExcelUtils;
import com.quhong.common.data.ApiResult;
import com.quhong.constant.activity.EventGroupConstant;
import com.quhong.data.vo.PageResultVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 活动模板配置管理页
 * <AUTHOR>
 * @since 2024/2/2 11:06
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/java_admin/official/event/model")
public class ModelEventOperateController {
    private final ModelEventOperateService modelEventOperateService;
    private final Lock lock = new ReentrantLock();

    /**
     * 活动配置列表
     * @param status 状态 0ALL(默认) 1未开始  2进行中  3已结束
     * @param zoneOffset 时区 默认选中第一个
     *                   使用常量接口 com.quhong.constant.activity.model.event.mode.ZoneOffsetConstant
     * @param valid 是否有效 -1ALL 0无效 1有效(默认)
     * @param page 页码 每页10条
     * @return 活动列表
     */
    @GetMapping("/list")
    public ApiResult<PageResultVo<List<EventModelVO>>> list(Integer status, String zoneOffset, Integer valid, Integer page){
        log.info("per event model list,status={}, zoneOffset={}", status, zoneOffset);
        Integer eventGroup = EventGroupConstant.MODEL_EVENT;
        PageResultVo<List<EventModelVO>> vo = modelEventOperateService.queryList(status, zoneOffset, page, valid, eventGroup);
        log.info("per event model list result:{}", JSON.toJSONString(vo));
        return ApiResult.getOk(vo);
    }

    /**
     * 创建活动
     * @param dto 活动创建DTO
     * @return 是否创建成功
     */
    @PostMapping("/create")
    public ApiResult<Boolean> createEvent(@RequestBody CreateEventDTO dto){
        log.info("per event model create event, dto:{}", JSON.toJSONString(dto));
        boolean result = modelEventOperateService.createEvent(dto);
        log.info("per event model create event result:{}", result);
        return ApiResult.getOk(result);
    }

    /**
     * 修改活动
     * @param dto 请求体
     * @return 是否修改成功
     */
    @PostMapping("/update")
    public ApiResult<Boolean> updateEvent(@RequestBody UpdateEventDTO dto){
        log.info("per event model update event, dto:{}", JSON.toJSONString(dto));
        boolean result = modelEventOperateService.updateEvent(dto);
        log.info("per event model update event result:{}", result);
        return ApiResult.getOk(result);
    }

    /**
     * 删除活动
     * @param id 活动id
     * @param username 操作人
     * @return 是否删除成功
     */
    @GetMapping("/delete")
    public ApiResult<Boolean> deleteEvent(Integer id, String username, Integer eventCode){
        log.info("per event model delete event, id:{}, username:{}, eventCode={}", id, username, eventCode);
        boolean result = modelEventOperateService.logicDelActivity(id, username, eventCode);
        log.info("per event model delete event result:{}", result);
        return ApiResult.getOk(result);
    }

    /**
     * 查询排行榜数据
     * @param eventCode 活动code
     * @return 排行榜数据
     */
    @GetMapping("/rank/data")
    public ApiResult<List<RankRowVO>> queryRankData(Integer eventCode){
        log.info("per event model query rank data, eventCode:{}", eventCode);
        List<RankRowVO> vo = modelEventOperateService.queryRankData(eventCode);
        log.info("per event model query rank data result:{}", JSON.toJSONString(vo));
        return ApiResult.getOk(vo);
    }

    /**
     * 查询排行榜数据（导出excel）
     * @param eventCode 活动code
     */
    @GetMapping("/rank/data/download")
    public void queryRankDataExcel(Integer eventCode, HttpServletResponse response){
        lock.lock();
        try {
            log.info("per event model query rank data excel, eventCode:{}", eventCode);
            List<RankRowVO> vo = modelEventOperateService.queryRankData(eventCode);
            log.info("per event model query rank data excel result:{}", JSON.toJSONString(vo));
            ExcelUtils.reportExcel(response, "活动模板排行榜数据", "排行榜数据", RankRowVO.class, vo);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 拷贝活动去创建
     * @param eventCode 活动code
     * @return 拷贝的数据
     */
    @GetMapping("/copy_to_create")
    public ApiResult<CopyModelVO> copyEvent(Integer eventCode){
        log.info("per event model copy event, eventCode:{}", eventCode);
        CopyModelVO vo = modelEventOperateService.copyEvent(eventCode);
        log.info("per event model copy event result:{}", vo);
        return ApiResult.getOk(vo);
    }



}
