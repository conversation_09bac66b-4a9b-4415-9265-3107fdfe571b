package com.quhong.admin.controller.official.event.unit;

import com.alibaba.fastjson.JSON;
import com.quhong.admin.data.dto.official.activity.unit.CreateEventDTO;
import com.quhong.admin.data.dto.official.activity.unit.UpdateEventDTO;
import com.quhong.admin.data.vo.official.event.model.RankRowVO;
import com.quhong.admin.data.vo.official.event.unit.CopyUnitEventVO;
import com.quhong.admin.data.vo.official.event.unit.UnitEventVO;
import com.quhong.admin.service.official.event.unit.UnitEventOptService;
import com.quhong.admin.utils.ExcelUtils;
import com.quhong.common.data.ApiResult;
import com.quhong.constant.activity.EventGroupConstant;
import com.quhong.data.vo.PageResultVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 官方运营操作/组件化活动配置
 *
 * <AUTHOR>
 * @since 2025-06-03 15:50
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/java_admin/official/event/unit")
public class UnitEventOptController {
    private final UnitEventOptService unitEventOptService;
    private final Lock lock = new ReentrantLock();

    /**
     * 组件化活动配置列表
     *
     * @param status     状态 0ALL(默认) 1未开始  2进行中  3已结束
     * @param zoneOffset 时区 默认选中第一个
     *                   使用常量接口 com.quhong.constant.activity.model.event.mode.ZoneOffsetConstant
     * @param valid      是否有效 -1ALL 0无效 1有效(默认)
     * @param page       页码 每页10条
     * @return 活动列表
     */
    @GetMapping("/list")
    public ApiResult<PageResultVo<List<UnitEventVO>>> list(Integer status, String zoneOffset, Integer valid, Integer page) {
        log.info("unit event list,status={}, zoneOffset={}", status, zoneOffset);
        Integer eventGroup = EventGroupConstant.UNIT_EVENT;
        PageResultVo<List<UnitEventVO>> vo = unitEventOptService.queryList(status, zoneOffset, page, valid, eventGroup);
        log.info("unit event list result:{}", JSON.toJSONString(vo));
        return ApiResult.getOk(vo);
    }

    /**
     * 创建组件化活动
     *
     * @param dto 活动创建DTO
     * @return 是否创建成功
     */
    @PostMapping("/create")
    public ApiResult<Boolean> createEvent(@RequestBody CreateEventDTO dto) {
        log.info("unit event create, dto:{}", JSON.toJSONString(dto));
        boolean result = unitEventOptService.createEvent(dto);
        log.info("unit event create result:{}", result);
        return ApiResult.getOk(result);
    }

    /**
     * 修改组件化活动
     *
     * @param dto 请求体
     * @return 是否修改成功
     */
    @PostMapping("/update")
    public ApiResult<Boolean> updateEvent(@RequestBody UpdateEventDTO dto) {
        log.info("unit event update, dto:{}", JSON.toJSONString(dto));
        boolean result = unitEventOptService.updateEvent(dto);
        log.info("unit event update result:{}", result);
        return ApiResult.getOk(result);
    }

    /**
     * 删除组件化活动
     *
     * @param id       活动id
     * @param username 操作人
     * @return 是否删除成功
     */
    @GetMapping("/delete")
    public ApiResult<Boolean> deleteEvent(Integer id, String username, Integer eventCode) {
        log.info("unit event delete, id:{}, username:{}, eventCode={}", id, username, eventCode);
        boolean result = unitEventOptService.logicDelActivity(id, username, eventCode);
        log.info("unit event delete result:{}", result);
        return ApiResult.getOk(result);
    }

    /**
     * 拷贝组件化活动去创建
     *
     * @param eventCode 活动code
     * @return 拷贝的数据
     */
    @GetMapping("/copy_to_create")
    public ApiResult<CopyUnitEventVO> copyEvent(Integer eventCode) {
        log.info("unit event copy, eventCode:{}", eventCode);
        CopyUnitEventVO vo = unitEventOptService.copyEvent(eventCode);
        log.info("unit event copy result:{}", vo);
        return ApiResult.getOk(vo);
    }

    /**
     * 查询榜单组件数据
     *
     * @param eventCode eventCode
     * @param unitId    组件id
     */
    @GetMapping("/rank/data")
    public ApiResult<List<RankRowVO>> queryRankData(Integer eventCode, Integer unitId) {
        log.info("unit event rank data, eventCode:{}, unitId={}", eventCode, unitId);
        List<RankRowVO> vo = unitEventOptService.queryRankData(eventCode, unitId);
        log.debug("unit event rank data result:{}", vo);
        return ApiResult.getOk(vo);
    }

    /**
     * 榜单数据导出
     *
     * @param eventCode eventCode
     * @param unitId    组件id
     * @param response  响应
     */
    @GetMapping("/rank/data/download")
    public void queryRankDataExcel(Integer eventCode, Integer unitId, HttpServletResponse response) {
        lock.lock();
        try {
            log.info("unit event rank data download, eventCode:{}, unitId={}", eventCode, unitId);
            List<RankRowVO> vo = unitEventOptService.queryRankData(eventCode, unitId);
            log.debug("unit event rank data download result:{}", JSON.toJSONString(vo));
            ExcelUtils.reportExcel(response, "活动模板排行榜数据" + eventCode + "_" + unitId, "排行榜数据", RankRowVO.class, vo);
        } finally {
            lock.unlock();
        }
    }


}
