package com.quhong.admin.controller.official;

import com.quhong.admin.data.LimitPrivilegeData;
import com.quhong.admin.data.vo.official.LanguageVO;
import com.quhong.admin.data.vo.official.TrainingVideoVO;
import com.quhong.admin.enums.HttpEnum;
import com.quhong.admin.exceptions.BaseException;
import com.quhong.admin.handler.AuthHandler;
import com.quhong.admin.service.TrainingVideoServer;
import com.quhong.data.vo.PageResultVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 培训视频controller
 */
@RestController
@RequestMapping("${server.baseurl}official/video")
public class TrainingVideoController extends AuthHandler {

     private static final Logger LOGGER = LoggerFactory.getLogger(TrainingVideoController.class);

     public static final long VIDEO_MAX_SIZE = 50 * 1024 * 1024L;

     @Autowired
     private TrainingVideoServer trainingVideoServer;

     public TrainingVideoController(){
         super(LimitPrivilegeData.allowReadChannel());
     }

    /**
     * 上传单个培训视频文件
     * @return
     */
     @PostMapping("/upload")
     public String uploadVideo(HttpServletRequest request,String name,MultipartFile video, String language){
         try {
             String username = getUsername(request);
             if(StringUtils.isBlank(username)){
                 LOGGER.error("username can not is empty");
                 return createError(HttpEnum.SERVER_ERROR);
             }
             //校验权限
             String checkAuthResult = checkAuthResult(username);
             if(checkAuthResult != null){
                 LOGGER.error("username = {} not have database auth",username);
                 return checkAuthResult;
             }
             LOGGER.info("upload video begin,video size = {} name = {} language = {}",video == null?0:video.getSize(),name,language);
             //参数校验
             if(video == null || video.getSize() == 0){
                 LOGGER.error("video file can not is empty");
                 return createError(HttpEnum.PARAM_ERROR,"video file can not is empty");
             }
             if(name == null || language == null){
                 LOGGER.error("video name language can not is empty");
                 return createError(HttpEnum.PARAM_ERROR,"video name language can not is empty");
             }
             //限制视频大小不大于50M
             if(video.getSize() > VIDEO_MAX_SIZE){
                 LOGGER.error("video size can not greater than 50M");
                 return createError(HttpEnum.PARAM_ERROR,"video size can not greater than 50M");
             }

             //参数校验通过，调用接口
             trainingVideoServer.uploadVideo(video,name,language,username);
             return createResult(HttpEnum.SUCCESS,null);
         }catch (BaseException e){
             throw e;
         }catch (Exception e){
             LOGGER.error(e.getMessage(),e);
         }
         return createError(HttpEnum.SERVER_ERROR);
     }

    /**
     * 获取培训视频列表
     * @param request
     * @param page
     * @return
     */
     @GetMapping("/list")
     public String videoList(HttpServletRequest request,Integer page){
         try {
             String username = getUsername(request);
             if(StringUtils.isBlank(username)){
                 LOGGER.error("username can not is empty");
                 return createError(HttpEnum.SERVER_ERROR);
             }
             //校验权限
             String checkAuthResult = checkAuthResult(username);
             if(checkAuthResult != null){
                 LOGGER.error("username = {} not have database auth",username);
                 return checkAuthResult;
             }
             LOGGER.info("begin get video list username = {} page = {}",username,page);
             if(page == null){
                 page = -1;
             }
             //调用业务方法
             PageResultVo<List<TrainingVideoVO>> resultVo = trainingVideoServer.listVideo(page);
             return createResult(HttpEnum.SUCCESS,resultVo);
         }catch (BaseException e){
             throw e;
         }catch (Exception e){
             LOGGER.error(e.getMessage(),e);
         }
         return createError(HttpEnum.SERVER_ERROR);
     }

    /**
     * 配置-培训视频过期时间
     * @param request
     * @return
     */
     @PostMapping("/config/expired")
     public String videoConfigExpiredTime(HttpServletRequest request){
         try {
             String username = getUsername(request);
             if(StringUtils.isBlank(username)){
                 LOGGER.error("username can not is empty");
                 return createError(HttpEnum.SERVER_ERROR);
             }
             //校验权限
             String checkAuthResult = checkAuthResult(username);
             if(checkAuthResult != null){
                 LOGGER.error("username = {} not have database auth",username);
                 return checkAuthResult;
             }
             String timeStr = request.getParameter("time");
             LOGGER.info("begin config video expired time; username = {} time = {}",username,timeStr);
             //参数校验
             if(timeStr == null){
                 LOGGER.error("config video expired time param error. username = {} time = {}",username,timeStr);
                 return createError(HttpEnum.PARAM_ERROR);
             }
             //调用业务方法
             int time = Integer.parseInt(timeStr);
             if(time <= 0){
                 LOGGER.error("config video expired time param error. username = {} time = {}",username,time);
                 return createResult(HttpEnum.PARAM_ERROR,null);
             }
             trainingVideoServer.configVideoExpiredTime(time);
             return createResult(HttpEnum.SUCCESS,null);
         }catch (BaseException e){
             throw e;
         }catch (Exception e){
             LOGGER.error(e.getMessage(),e);
         }
         return createError(HttpEnum.SERVER_ERROR);
     }

    /**
     * 获取翻译语言列表
     * @param request
     * @return
     */
     @GetMapping("/slang")
     public String listLanguage(HttpServletRequest request){
         try {
             String username = getUsername(request);
             if(StringUtils.isBlank(username)){
                 LOGGER.error("username can not is empty");
                 return createError(HttpEnum.SERVER_ERROR);
             }
             //校验权限
             String checkAuthResult = checkAuthResult(username);
             if(checkAuthResult != null){
                 LOGGER.error("username = {} not have database auth",username);
                 return checkAuthResult;
             }
             LOGGER.info("begin get language list username = {}",username);
             //调用业务方法
             List<LanguageVO> result = trainingVideoServer.listLanguage();
             return createResult(HttpEnum.SUCCESS,result);
         }catch (BaseException e){
             throw e;
         }catch (Exception e){
             LOGGER.error(e.getMessage(),e);
         }
         return createError(HttpEnum.SERVER_ERROR);
     }

    /**
     * 删除视频
     * @param request
     * @return
     */
     @DeleteMapping("/remove")
     public String removeOne(HttpServletRequest request, @RequestParam("vid")Integer vid){
         try {
             String username = getUsername(request);
             if(StringUtils.isBlank(username)){
                 LOGGER.error("username can not is empty");
                 return createError(HttpEnum.SERVER_ERROR);
             }
             //校验权限
             String checkAuthResult = checkAuthResult(username);
             if(checkAuthResult != null){
                 LOGGER.error("username = {} not have database auth",username);
                 return checkAuthResult;
             }
             LOGGER.info("begin remove video username = {} vid = {}",username,vid);
             //参数校验
             if(vid == null){
                 return createError(HttpEnum.PARAM_ERROR);
             }
             //调用业务方法
             trainingVideoServer.removeVideo(vid);
             return createResult(HttpEnum.SUCCESS,null);
         }catch (BaseException e){
             throw e;
         }catch (Exception e){
             LOGGER.error(e.getMessage(),e);
         }
         return createError(HttpEnum.SERVER_ERROR);
     }

}
