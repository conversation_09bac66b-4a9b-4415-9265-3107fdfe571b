package com.quhong.admin.controller.official;

import com.quhong.admin.data.dto.official.SubjectAnswerDto;
import com.quhong.admin.data.dto.official.SubjectFinishDto;
import com.quhong.admin.data.dto.official.TrainingReceiveDto;
import com.quhong.admin.data.vo.official.RandomVideoVO;
import com.quhong.admin.data.vo.official.SubjectSelectReqVO;
import com.quhong.admin.data.vo.official.TrainingVideoVO;
import com.quhong.admin.enums.HttpEnum;
import com.quhong.admin.exceptions.BaseException;
import com.quhong.admin.handler.BaseHandler;
import com.quhong.admin.service.SubjectSelectServer;
import com.quhong.admin.service.TrainingVideoServer;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.data.vo.PageResultVo;
import com.quhong.redis.PlayerRedis;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 官方频道业务支持controller:h5
 */
@RestController
@RequestMapping("${server.baseurl}training")
public class OfficialServiceController extends BaseHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(OfficialServiceController.class);

    @Autowired
    private TrainingVideoServer trainingVideoServer;

    @Autowired
    private SubjectSelectServer subjectSelectServer;

    @Resource
    protected PlayerRedis playerRedis;

    /**
     * 获取培训视频
     * @return
     */
    @PostMapping("/video")
    public String getVideo(@RequestBody TrainingReceiveDto trainingReceiveDto){
        try {
            String uid = trainingReceiveDto.getUid();
            String slang = trainingReceiveDto.getSlang();
            LOGGER.info("start get h5 random video uid = {} lang = {}",uid,slang);
            //参数校验
            if(StringUtils.isBlank(uid)){
                return createError(HttpEnum.PARAM_ERROR);
            }
            if(StringUtils.isBlank(slang)){
                slang = "en";
                trainingReceiveDto.setSlang(slang);
            }
            //验证token
            String token = playerRedis.getToken(uid);
            if(token == null){
                LOGGER.error("get h5 random video;uid token is null uid = {}",uid);
                return createError(HttpEnum.LOGIN_INVALID);
            }
            //调用业务
            RandomVideoVO randomVideoVo = trainingVideoServer.randomVideo(trainingReceiveDto);
            return createResult(HttpEnum.SUCCESS,randomVideoVo);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            LOGGER.error(e.getMessage(),e);
        }
        return createError(HttpEnum.SERVER_ERROR);
    }

    /**
     * 获取题目
     */
    @PostMapping("/select")
    public String getSelectSubject(@RequestBody TrainingReceiveDto trainingReceiveDto){
        try {
            String uid = trainingReceiveDto.getUid();
            String slang = trainingReceiveDto.getSlang();
            LOGGER.info("start get h5 select subject uid = {} slang = {}",uid,slang);
            //参数校验
            if(StringUtils.isBlank(uid)){
                return createError(HttpEnum.PARAM_ERROR);
            }
            if(StringUtils.isBlank(slang)){
                slang = "en";
                trainingReceiveDto.setSlang(slang);
            }
            //验证token
            String token = playerRedis.getToken(uid);
            if(token == null){
                LOGGER.error("get h5 select subject;uid token is null uid = {}",uid);
                return createError(HttpEnum.LOGIN_INVALID);
            }
            //调用业务
            SubjectSelectReqVO subjectSelectReqVo = subjectSelectServer.listSelectSubject(trainingReceiveDto);
            return createResult(HttpEnum.SUCCESS,subjectSelectReqVo);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            LOGGER.error(e.getMessage(),e);
        }
        return createError(HttpEnum.SERVER_ERROR);
    }

    /**
     * 答题校验
     * @param subjectAnswerDto
     * @return
     */
    @PostMapping("/select/answer")
    public String answerSubject(@RequestBody SubjectAnswerDto subjectAnswerDto){
        try {
            String uid = subjectAnswerDto.getUid();
            Integer subId = subjectAnswerDto.getSubId();
            String answer = subjectAnswerDto.getAnswer();
            LOGGER.info("start h5 answer subject uid = {} subId = {} answer = {}",uid,subId,answer);
            //参数校验
            if(StringUtils.isBlank(uid) || subId == null || StringUtils.isBlank(answer)){
                return createError(HttpEnum.PARAM_ERROR);
            }
            //验证token
            String token = playerRedis.getToken(uid);
            if(token == null){
                LOGGER.error("get h5 answer subject;uid token is null uid = {}",uid);
                return createError(HttpEnum.LOGIN_INVALID);
            }
            //调用业务
            int result = subjectSelectServer.validAnswer(subjectAnswerDto);
            return createResult(HttpEnum.SUCCESS,result);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            LOGGER.error(e.getMessage(),e);
        }
        return createError(HttpEnum.SERVER_ERROR);
    }

    /**
     * 结束答题
     * @param subjectFinishDto
     * @return
     */
    @PostMapping("/select/finish")
    public String finishAnswer(@RequestBody SubjectFinishDto subjectFinishDto){
        try {
            String uid = subjectFinishDto.getUid();
            int status = subjectFinishDto.getStatus();
            LOGGER.info("h5 start finish answer uid = {} status = {}",uid,status);
            //参数校验
            if(StringUtils.isBlank(uid)){
                return createError(HttpEnum.PARAM_ERROR);
            }
            //验证token
            String token = playerRedis.getToken(uid);
            if(token == null){
                LOGGER.error("get h5 finish answer;uid token is null uid = {}",uid);
                return createError(HttpEnum.LOGIN_INVALID);
            }
            //调用业务
            subjectSelectServer.finishAnswer(uid,status);
            return createResult(HttpEnum.SUCCESS,null);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            LOGGER.error(e.getMessage(),e);
        }
        return createError(HttpEnum.SERVER_ERROR);
    }

    /**
     * h5获取培训视频列表
     * @param trainingReceiveDto
     * @return
     */
    @PostMapping("/video/list")
    public String videoList(@RequestBody TrainingReceiveDto trainingReceiveDto){
        try {
            String uid = trainingReceiveDto.getUid();
            String lang = trainingReceiveDto.getSlang();
            Integer page = trainingReceiveDto.getPage();
            LOGGER.info("start h5 get video list data uid = {} lang = {} page = {}",uid,lang,page);
            //参数校验
            if(StringUtils.isBlank(uid)){
                return createError(HttpEnum.PARAM_ERROR);
            }
            if(StringUtils.isBlank(lang)){
                lang = "en";
                trainingReceiveDto.setSlang(lang);
            }
            if(page == null){
                LOGGER.error("get h5 video list data;uid token is null uid = {}",uid);
                trainingReceiveDto.setPage(-1);
            }
            //验证token
            String token = playerRedis.getToken(uid);
            if(token == null){
                return createError(HttpEnum.LOGIN_INVALID);
            }
            //调用业务
            PageResultVo<List<TrainingVideoVO>> resultVo = trainingVideoServer.listVideo(trainingReceiveDto);
            return createResult(HttpEnum.SUCCESS,resultVo);
        }catch (BaseException e){
            throw e;
        }catch (Exception e){
            LOGGER.error(e.getMessage(),e);
        }
        return createError(HttpEnum.SERVER_ERROR);
    }

}
