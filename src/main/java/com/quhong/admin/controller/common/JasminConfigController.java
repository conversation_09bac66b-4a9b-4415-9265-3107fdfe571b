package com.quhong.admin.controller.common;

import com.quhong.admin.controller.BaseHandel;
import com.quhong.admin.enums.HttpResultEnum;
import com.quhong.data.vo.HostChatPriceVo;
import com.quhong.data.vo.TranslateLangVo;
import com.quhong.service.ConfigMgrService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("${server.baseurl}/common")
public class JasminConfigController extends BaseHandel {
    private static final Logger LOGGER = LoggerFactory.getLogger(JasminConfigController.class);

    private ConfigMgrService configMgrService;

    @Autowired
    public JasminConfigController(ConfigMgrService translateLangService) {
        this.configMgrService = translateLangService;
    }

    /**
     * 获取语言列表
     *(谷歌数据)与用户语言不全匹配的上
     * @return
     */
    @GetMapping("/lang/list")
    public String listLang() {
        LOGGER.info("start get translate language list");
        List<TranslateLangVo> translateLangVoList = new ArrayList<>();

        //添加一个all
        TranslateLangVo allVo = new TranslateLangVo();
        allVo.setLangCode("all");
        allVo.setLanguage("all");
        translateLangVoList.add(allVo);

        translateLangVoList.addAll(configMgrService.getTranslateLangVoList());
        LOGGER.info("language list:" + translateLangVoList);
        return createResult(HttpResultEnum.SUCCESS, translateLangVoList);
    }

    /**
     * 获取主播通话价格列表 （金币/min）
     *
     * @return
     */
    @GetMapping("chat/price/list")
    public String listPrice() {
        LOGGER.info("start get host chat price list");
        List<HostChatPriceVo> hostChatPriceVoList = configMgrService.getHostChatPriceVoList();
        LOGGER.info("host chat price list: " + hostChatPriceVoList);
        return createResult(HttpResultEnum.SUCCESS, hostChatPriceVoList);
    }
}
