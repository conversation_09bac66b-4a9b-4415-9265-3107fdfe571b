package com.quhong.admin.controller.config;

import com.alibaba.fastjson.JSONObject;
import com.quhong.admin.controller.BaseHandel;
import com.quhong.admin.data.dto.config.CoinSellerConfigDTO;
import com.quhong.admin.data.dto.config.CoinSellerQueryDTO;
import com.quhong.admin.enums.HttpResultEnum;
import com.quhong.admin.service.config.CoinSellerStatService;
import com.quhong.constant.CoinSellerPayTypeConstant;
import com.quhong.core.datas.vo.FiledDescVO;
import com.quhong.core.utils.ConstantUtils;
import com.quhong.dao.ActorDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.db.CoinSellerConfigData;
import com.quhong.data.vo.PageResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @ClassName CoinSellerStatController
 * <AUTHOR>
 * @date 2024/3/18 14:23
 */
@RestController
@RequestMapping("${server.baseurl}config/coin_seller")
@Slf4j
public class CoinSellerStatController extends BaseHandel {
    @Resource
    private CoinSellerStatService coinSellerStatService;
    @Resource
    private ConstantUtils constantUtils;
    @Resource
    private ActorDao actorDao;

    @RequestMapping("/query_list")
    public String queryList(CoinSellerQueryDTO dto) {
        if (dto == null || dto.getPage() == null) {
            return createResult(HttpResultEnum.PARAM_ERROR);
        }
        PageResultVo<List<CoinSellerConfigData>> resultVo = coinSellerStatService.queryList(dto);
        return createResult(HttpResultEnum.SUCCESS, resultVo);
    }

    @RequestMapping("/query_config")
    public String queryConfig() {
        List<FiledDescVO> voList = constantUtils.getFiledDescVOList(CoinSellerPayTypeConstant.class);
        return createResult(HttpResultEnum.SUCCESS, voList);
    }

    @RequestMapping("/add")
    public String add(HttpServletRequest request, @RequestBody CoinSellerConfigDTO dto) {
        String username = getUserName(request);
        log.info("add coin seller config dto={} username={}", JSONObject.toJSONString(dto), username);
        HttpResultEnum paramError = HttpResultEnum.PARAM_ERROR;
        if (dto == null || dto.getRid() == null) {
            return createResult(paramError);
        }
        ActorData actorData = actorDao.getActorRid(dto.getRid());
        if (actorData == null || actorData.getValid() == 0) {
            paramError.setMsg("actor is not exist");
            return createResult(paramError);
        }
        coinSellerStatService.add(dto, actorData, username);
        return createResult(HttpResultEnum.SUCCESS);
    }

    @RequestMapping("/change")
    public String change(HttpServletRequest request, @RequestBody CoinSellerConfigDTO dto) {
        String username = getUserName(request);
        if (dto == null || StringUtils.isEmpty(dto.getUid())) {
            return createResult(HttpResultEnum.PARAM_ERROR);
        }
        log.info("change coin seller config dto={} username={}", JSONObject.toJSONString(dto), username);
        coinSellerStatService.change(dto, username);
        return createResult(HttpResultEnum.SUCCESS);
    }
}

