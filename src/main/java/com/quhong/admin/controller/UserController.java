package com.quhong.admin.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.admin.enums.HttpResultEnum;
import com.quhong.admin.redis.RedisCacheHandle;
import com.quhong.admin.service.sys.AdminAuthService;
import com.quhong.admin.service.sys.AdminChannelService;
import com.quhong.common.utils.RequestUtils;
import com.quhong.dao.data.AdminAuthData;
import com.quhong.dao.data.AdminChannelData;
import com.quhong.dao.data.AdminRoleData;
import com.quhong.dao.data.UserData;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.LockedAccountException;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("${server.baseurl}")
public class UserController extends BaseHandel {
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);


    private final AdminAuthService adminAuthService;

    private final AdminChannelService adminChannelService;

    private final RedisCacheHandle redisCacheHandle;

    @Autowired
    public UserController(RedisCacheHandle redisCacheHandle, AdminAuthService adminAuthService, AdminChannelService adminChannelService) {
        super();
        this.adminAuthService = adminAuthService;
        this.adminChannelService = adminChannelService;
        this.redisCacheHandle=redisCacheHandle;
    }

    @RequestMapping("/unauth")
    public String noAuth() {
        return createResult(HttpResultEnum.LOGIN_INVALID, "Login failed or no permission, please log in again");
    }

    @RequestMapping("/login")
    public String login(HttpServletRequest request, @RequestBody String body) {
        String loginIp = RequestUtils.getIpAddress(request);
        JSONObject bodyObj = RequestUtils.getBody(body);
        if (bodyObj == null) {
            return createResult(HttpResultEnum.PARAM_ERROR);
        }
        String userName = bodyObj.getString("username").trim();
        String key = bodyObj.getString("key");
        String curTime = bodyObj.getString("time");

        key = curTime + "," + key;

        //获取当前用户
        Subject subject = SecurityUtils.getSubject();
        //封装用户的登录数据
        UsernamePasswordToken userToken = new UsernamePasswordToken(userName, key);
        try {
            logger.info("login username={} loginIp={}", userName, loginIp);
            subject.login(userToken);
            UserData userData = (UserData) subject.getPrincipal();
            //用户当前拥有的全部角色id
            Set<Integer> ridSet = findRolesIdSet(userData);
            //获取权限树
            List<AdminAuthData> auths = adminAuthService.queryByRoleIdSet(ridSet);
            auths = adminAuthService.getAuthTree(auths);

            //获取当前用户所能访问的渠道
            List<AdminChannelData> channels = adminChannelService.queryByRoleIdSet(ridSet);

            //token 存缓存
            String tokenKey= userName + "_token";
            String token=subject.getSession().getId().toString().replace("-","");
            redisCacheHandle.saveCacheData(tokenKey,token);

            JSONObject retObject = new JSONObject();
            retObject.put("token", token);
            retObject.put("roles", userData.getRoles());
            retObject.put("auths", auths);
            retObject.put("channels",channels);
            logger.info("login success. username={}", userData.getUserName());
            return createResult(HttpResultEnum.SUCCESS, retObject);
        } catch (UnknownAccountException e) {
            return createResult(HttpResultEnum.INVALID_USER_OR_PASSWORD, "Account does not exist");
        } catch (IncorrectCredentialsException e) {
            return createResult(HttpResultEnum.INVALID_USER_OR_PASSWORD, "WRONG PASSWORD");
        } catch (LockedAccountException e) {
            return createResult(HttpResultEnum.INVALID_USER_OR_PASSWORD, "Account is frozen");
        }
        //return doLogin(request, userName, key, curTime, loginIp);
    }

    private static Set<Integer> findRolesIdSet(UserData userData) {
        Set<Integer> ridSet = new LinkedHashSet<>();
        if (!CollectionUtils.isEmpty(userData.getRoles())) {
            ridSet = userData.getRoles()
                    .stream()
                    .filter(Objects::nonNull)
                    .map(AdminRoleData::getRoleId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
        }
        return ridSet;
    }

    //private String doLogin(HttpServletRequest request, String username, String key, String curTime, String loginIp) {
    //    logger.info("login username={} loginIp={}", username, loginIp);
    //    UserData userData = userDao.getUserData(username);
    //    if (userData == null) {
    //        logger.info("user data is null. username={}", username);
    //        return createResult(HttpResultEnum.INVALID_USER_OR_PASSWORD);
    //    }
    //    String genrateKey = DigestUtils.md5DigestAsHex((curTime + userData.getPassword()).getBytes());
    //    if (!genrateKey.equals(key)) {
    //        logger.info("password not equal. username={} inPassword={} curTime={} password={}", username, key, curTime, userData.getPassword());
    //        return createResult(HttpResultEnum.INVALID_USER_OR_PASSWORD);
    //    }
    //    if (userData.getAuthorityLevel() < 1) {
    //        logger.info("auth level is zero. username={}", username);
    //        return createResult(HttpResultEnum.SYSTEM_OPERATION);
    //    }
    //    String token = UUID.randomUUID().toString().replace("-", "");
    //    redisTemplate.opsForValue().set(token, userData.getUserName(), 2, TimeUnit.DAYS);
    //    redisTemplate.opsForValue().set(userData.getUserName(), token, 2, TimeUnit.DAYS);
    //
    //    JSONObject retObject = new JSONObject();
    //    retObject.put("token", token);
    //    retObject.put("level", userData.getAuthorityLevel());
    //    logger.info("login success. username={}", username);
    //    return createResult(HttpResultEnum.SUCCESS, retObject);
    //}

    @RequestMapping("/logout")
    public String logout(HttpServletRequest request){
        String username = getUserName(request);
        logger.info("login out username={}", username);
        String tokenKey= username + "_token";
        redisCacheHandle.removeCacheData(tokenKey,String.class);
        return createResult(HttpResultEnum.SUCCESS);
    }
}
