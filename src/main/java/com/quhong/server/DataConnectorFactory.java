package com.quhong.server;

import com.quhong.socket.annotation.MessageGroup;
import com.quhong.socket.annotation.MsgExecutorGroup;
import com.quhong.socket.clusters.servers.ClusterServerConnector;
import com.quhong.socket.net.connect.IConnector;
import com.quhong.socket.net.server.adapter.IConnectorFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2018/10/15
 * @copyright szxx
 */
@Component
public class DataConnectorFactory implements IConnectorFactory {
    @Autowired
    private MessageGroup messageGroup;
    @Autowired
    private MsgExecutorGroup executorGroup;

    public DataConnectorFactory(){

    }

    @Override
    public IConnector createConnector() {
        return new ClusterServerConnector(messageGroup, executorGroup);
    }
}
