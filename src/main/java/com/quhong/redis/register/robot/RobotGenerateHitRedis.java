package com.quhong.redis.register.robot;

import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.constant.robot.RobotStatusConstant;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.RobotInfoDao;
import com.quhong.data.appConfig.robot.GenerateRobotConfig;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.exceptions.WebException;
import com.quhong.service.ConfigApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Optional;

/**
 * 命中机器人生成策略
 *
 * <AUTHOR>
 * @since 2023/6/25 11:30
 */
@Slf4j
@Component
public class RobotGenerateHitRedis {
    private static final String RANDOM_HIT_VALUE_KEY = "str:robot_generate:random_hit_value";
    private static final String HIT_POLLING_VALUE_KEY = "str:robot_generate:hit_polling_value";
    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedis;
    @Resource
    private ConfigApi configApi;
    @Resource
    private RobotInfoDao robotInfoDao;
    @Resource
    private RobotGenerateSignRedis robotGenerateSignRedis;

    public GenerateRobotConfig checkHit(String uid) {
        GenerateRobotConfig configData = getGenerateRobotConfig(uid);
        if (configData.getPowerSwitch() == null || configData.getPowerSwitch() == 0) {
            log.info("config switch is off");
            configData.setPowerSwitch(0);
            return configData;
        }
        //获取轮询值
        int hitPolling = getHitPollingFromRedis(configData);
        mainRedis.opsForValue().decrement(HIT_POLLING_VALUE_KEY);
        hitPolling--;
        //获取随机命中值
        int randomHit = getRandomHitFromRedis();
        if (hitPolling != randomHit) {
            log.info("not hit generate robot");
            configData.setPowerSwitch(0);
            return configData;
        } else {
            //命中则重置轮询值和随机命中值
            randomHitIntFromZeroToNine(hitPolling);
            resetHitPollingVal(configData, hitPolling);
        }

        if (configData.getIsIdle() == 1) {//初始化闲置机器人池处理
            int activeCount = robotInfoDao.getCountByStateSet(RobotStatusConstant.activeStateSet);
            int genSign = activeCount / configData.getGenerateGroupMax() + 1;
            configData.setGenSign(genSign);
            if (activeCount >= configData.getIdleRobotMax()) {
                log.info("isIdle=1,active robot pool not need generate");
                configData.setPowerSwitch(0);
            }
        } else {//正常分组轮替机器人逻辑
            int currSign = robotGenerateSignRedis.getCurrSign();
            int generateCount = robotInfoDao.getCountByGenSign(currSign);
            if (generateCount >= configData.getGenerateGroupMax()) {
                log.info("generate robot pool not need generate");
                configData.setPowerSwitch(0);
            }
            configData.setGenSign(currSign);
        }
        return configData;
    }

    private int getRandomHitFromRedis() {
        return Optional.ofNullable(mainRedis.opsForValue().get(RANDOM_HIT_VALUE_KEY))
                .map(Integer::parseInt)
                .orElseGet(() -> Integer.valueOf(randomHitIntFromZeroToNine(0)));
    }

    private String randomHitIntFromZeroToNine(int hitPolling) {
        if (hitPolling <= 0) {
            String randomHitStr = MathUtils.randomSplitInt(0, 10).toString();
            mainRedis.opsForValue().set(RANDOM_HIT_VALUE_KEY, randomHitStr, Duration.ofDays(2));
            return randomHitStr;
        }
        return "-1";
    }

    private int getHitPollingFromRedis(GenerateRobotConfig configData) {
        return Optional.ofNullable(mainRedis.opsForValue().get(HIT_POLLING_VALUE_KEY))
                .map(Integer::parseInt)
                .filter(val -> val > 0)
                .orElseGet(() -> resetHitPollingVal(configData, 0));
    }

    private int resetHitPollingVal(GenerateRobotConfig configData, int hitPolling) {
        if (hitPolling <= 0) {
            int hitPollingVal = configData.getHitPollingMax();
            mainRedis.opsForValue().set(HIT_POLLING_VALUE_KEY, String.valueOf(hitPollingVal), Duration.ofDays(2));
            return hitPollingVal;
        }
        return hitPolling;
    }

    private GenerateRobotConfig getGenerateRobotConfig(String uid) {
        GenerateRobotConfig configData = configApi.getJavaBeanVal(
                new ConfigDTO(uid, AppConfigKeyConstant.GENERATE_ROBOT_CONFIG, AppConfigKeyConstant.STATUS_SERVER),
                GenerateRobotConfig.class);
        if (configData == null) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.OTHER_NOT_EXISTS).setMsg("generate robot config is not exist"));
        }
        return configData;
    }
}
