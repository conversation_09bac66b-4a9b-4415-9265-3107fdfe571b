package com.quhong.redis;

import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.data.config.DailyRewardTaskConfig;
import com.quhong.data.config.TaskConfig;
import com.quhong.event.diwali.v2410.data.config.DiwaliTaskConfig;
import com.quhong.event.welfare.v2410.WelfareCommingV2410Service;
import com.quhong.redis.base.currency.task.BaseDailyOneLimitRedis;
import com.quhong.redis.base.currency.task.BaseDailyTaskLimitRedis;
import com.quhong.redis.base.currency.task.BaseEveryLimitCurrencyRedis;
import com.quhong.redis.base.reward.BaseDailyNodeRewardLimitRedis;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.util.Collections;

/**
 * <AUTHOR>
 * @since 2024/7/23 10:06
 */
@Configuration
public class EventRedisConfig {

    /**
     * welfare2410 语聊房每日上麦时长任务每日redis
     */
    @Lazy
    @Bean("welfare2410UpMicDailyLimitRedis")
    public BaseDailyNodeRewardLimitRedis<DailyRewardTaskConfig> getWelfare2410UpMicDailyLimitRedis() {
        String key = "event_task:welfare_up_mic_limit:";
        DailyRewardTaskConfig taskConfig = WelfareCommingV2410Service.DAILY_UP_MIC_TASK;
        String offsetId = "+08:00";
        return new BaseDailyNodeRewardLimitRedis<>(key, Collections.singletonList(taskConfig), offsetId);
    }

    /**
     * welfare2410 语聊房每日送礼任务每日领取一次redis
     */
    @Lazy
    @Bean("welfare2410PartyRoomSendGiftDailyLimitRedis")
    public BaseDailyOneLimitRedis getWelfare2410PartyRoomSendGiftDailyLimitRedis() {
        return new BaseDailyOneLimitRedis("event_task:welfare_party_room_send_gift_limit:", "+08:00");
    }


    /**
     * 排灯节 送礼价值达标任务redis
     */
    @Lazy
    @Bean("diwali2410SendGiftTaskLimitRedis")
    public BaseEveryLimitCurrencyRedis<TaskConfig> getDiwali2410SendGiftTaskLimitRedis() {
        String key = "event_task:send_gift_limit:";
        TaskConfig taskConfig = DiwaliTaskConfig.SEND_GIFT_PRICE_TASK;
        return new BaseEveryLimitCurrencyRedis<>(key, taskConfig);
    }

    @Lazy
    @Bean("carnivalLuckyDrawTaskLimitRedis")
    public BaseEveryLimitCurrencyRedis<TaskConfig> getCarnivalLuckyDrawTaskLimitRedis() {
        String key = "hash:event_task:carnival_lucky_draw_limit:";
        TaskConfig taskConfig = com.quhong.data.config.independenceDay.TaskConfig.CARNIVAL_LUCKY_DRAW_COST_GOLD_TASK;
        return new BaseEveryLimitCurrencyRedis<>(key, taskConfig);
    }

    @Lazy
    @Bean("playGameTaskLimitRedis")
    public BaseEveryLimitCurrencyRedis<TaskConfig> getPlayGameTaskLimitRedis() {
        String key = "hash:event_task:play_game_limit:";
        TaskConfig taskConfig = com.quhong.data.config.independenceDay.TaskConfig.PLAY_GAME_COST_GOLD_TASK;
        return new BaseEveryLimitCurrencyRedis<>(key, taskConfig);
    }

    @Lazy
    @Bean("luckyGiftDailyCostTaskLimitRedis")
    public BaseDailyTaskLimitRedis getLuckyGiftDailyCostTaskLimitRedis() {
        String key = "hash:event_task:lucky_gift_daily_cost_limit:";
        TaskConfig taskConfig = new TaskConfig(TaskTypeConstant.LUCKY_GIFT_COST_GOLD, 20, 1, Integer.MAX_VALUE);
        return new BaseDailyTaskLimitRedis(key, taskConfig, "+05:30");
    }

    @Lazy
    @Bean("cupidUserBalanceTaskLimitRedis")
    public BaseEveryLimitCurrencyRedis<TaskConfig> getCupidUserBalanceTaskLimitRedis() {
        String key = "hash:event_task:cupid_user_balance_limit:";
        TaskConfig taskConfig = com.quhong.data.config.cupid.TaskConfig.SEND_GIFT_PRICE_TASK;
        return new BaseEveryLimitCurrencyRedis<>(key, taskConfig);
    }

    /**
     * 枣椰树 每日进入房间领取一次任务redis
     */
    @Lazy
    @Bean("datePalmEnterRoomTaskLimitRedis")
    public BaseDailyOneLimitRedis getDatePalmEnterRoomTaskLimitRedis() {
        return new BaseDailyOneLimitRedis("hash:event_task:date_palm:enter_room_limit:", "+08:00");
    }


    /**
     * 生成并配置每日送礼成本的Redis每日任务限制器
     * 此方法定义了一个带有特定配置的每日任务限制器，用于跟踪和限制每日送礼的总成本
     * 它创建了一个唯一的Redis键、任务配置，并设置了一个时区，以便更精确地处理时间相关的任务执行
     *
     * @return BaseDailyTaskLimitRedis 返回一个配置好的每日任务限制器实例，用于处理每日送礼成本的限制逻辑
     */
    @Lazy
    @Bean("datePalmGiftCostDailyTaskRedis")
    public BaseDailyTaskLimitRedis getDatePalmGiftCostDailyTaskRedis() {
        // 定义用于存储每日送礼成本限制任务的Redis Hash键
        String key = "hash:event_task:date_palm:gift_daily_cost_limit:";

        // 创建任务配置实例，定义任务类型为送礼，每日限制为25，每小时限制为1，无单次执行上限
        TaskConfig taskConfig = new TaskConfig(TaskTypeConstant.SEND_GIFT, 25, 1, Integer.MAX_VALUE);

        // 返回配置好的每日任务限制器实例，设置键、任务配置和时区（+08:00表示东八区，中国标准时间）
        return new BaseDailyTaskLimitRedis(key, taskConfig, "+08:00");
    }

    /**
     * shootV2410 送礼价值每达到指定数额获得金足球任务
     */
    @Lazy
    @Bean("shootV2410GoldBallTaskLimitRedis")
    public BaseEveryLimitCurrencyRedis<TaskConfig> getShootV2410TaskLimitRedis() {
        String key = "event_task:send_gift_task";
        TaskConfig taskConfig = com.quhong.event.shoot.v2410.data.config.TaskConfig.SEND_GIFT_PRICE_TASK;
        return new BaseEveryLimitCurrencyRedis<>(key, taskConfig);
    }

}
