package com.quhong.redis.base.reward;

import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/28 13:48
 */
@Lazy
@Slf4j
@Component
public class BaseNodeRewardRedis {
    public static final String HASH_KEY = "hash:event:note_reward:";

    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedis;

    public List<RewardInfoData> increaseAndGetRewards(String keySuffix, String uid, double increase, List<RewardTaskConfig> taskConfig) {
        BigDecimal preCount = getCount(uid, keySuffix);
        increaseCoin(uid, increase, keySuffix);
        BigDecimal finalCount = preCount.add(BigDecimal.valueOf(increase));
        List<RewardInfoData> rewards = taskConfig.stream()
                .filter(config -> preCount.compareTo(BigDecimal.valueOf(config.getCheckParams())) < 0)
                .filter(config -> BigDecimal.valueOf(config.getCheckParams()).compareTo(finalCount) <= 0)
                .flatMap(config -> config.getRewards().stream())
                .collect(Collectors.toList());
        return rewards.isEmpty() ? new ArrayList<>(0) : rewards;
    }

    public List<RewardTaskConfig> increaseAndGetCanGetTaskConfig(String keySuffix, String uid, double increase, List<RewardTaskConfig> taskConfig) {
        BigDecimal preCount = getCount(uid, keySuffix);
        increaseCoin(uid, increase, keySuffix);
        BigDecimal finalCount = preCount.add(BigDecimal.valueOf(increase));
        return taskConfig.stream()
                .filter(config -> preCount.compareTo(BigDecimal.valueOf(config.getCheckParams())) < 0)
                .filter(config -> BigDecimal.valueOf(config.getCheckParams()).compareTo(finalCount) <= 0)
                .collect(Collectors.toList());
    }


    public BigDecimal getCount(String uid, String keySuffix) {
        HashOperations<String, String, String> hashOps = mainRedis.opsForHash();
        String key = getKey(keySuffix);
        String countStr = hashOps.get(key, uid);
        if (StringUtils.isEmpty(countStr)) {
            setInitialCount(hashOps, key, uid);
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(countStr);
        } catch (NumberFormatException e) {
            log.error("Failed to parse count string: {}", countStr, e);
            return BigDecimal.ZERO;
        }
    }

    private void increaseCoin(String uid, double increase, String keySuffix) {
        HashOperations<String, String, String> hashOps = mainRedis.opsForHash();
        String key = getKey(keySuffix);
        hashOps.increment(key, uid, increase);
        mainRedis.expire(key, Duration.ofDays(30));
    }

    private void setInitialCount(HashOperations<String, String, String> hashOps, String key, String uid) {
        hashOps.put(key, uid, "0");
        mainRedis.expire(key, Duration.ofDays(30));
    }

    private String getKey(String keySuffix) {
        return HASH_KEY + keySuffix;
    }
}
