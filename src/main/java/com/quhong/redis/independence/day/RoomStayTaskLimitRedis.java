package com.quhong.redis.independence.day;

import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.config.independenceDay.TaskConfig;
import com.quhong.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

/**
 * 房间停留时长任务限制redis
 *
 * <AUTHOR>
 * @since 2023/7/31 17:05
 */
@Slf4j
@Component
@Deprecated
public class RoomStayTaskLimitRedis {
    /**
     * pre + yyyy-MM-dd<br/>
     * hashKey: uid
     */
    private static final String HASH_KEY = "hash:independence_day:daily_room_stay_limit:";
    private static final TaskConfig taskConfig = TaskConfig.ROOM_STAY_TIME_TASK;
    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedis;

    public long getRoomStayTimes(String uid) {
        HashOperations<String, String, String> hashOps = mainRedis.opsForHash();
        long currTime = DateHelper.getCurrTime();
        String key = getKey(currTime);
        String timesStr = hashOps.get(key, uid);
        if (StringUtils.isEmpty(timesStr)) {
            hashOps.put(key, uid, "0");
            mainRedis.expire(key, Duration.ofDays(14));
            return 0;
        }
        return Long.parseLong(timesStr);
    }

    private void increaseTimes(String uid) {
        increaseTimes(uid, 1);
    }

    private void increaseTimes(String uid, long increase) {
        HashOperations<String, String, String> hashOps = mainRedis.opsForHash();
        long currTime = DateHelper.getCurrTime();
        String key = getKey(currTime);
        hashOps.increment(key, uid, increase);
        mainRedis.expire(key, Duration.ofDays(14));
    }

    public long getTicket(String uid, long stayTime) {
        long times = getRoomStayTimes(uid);
        if (times >= taskConfig.getLimit()) {
            log.info("times great than or equal limit, so not to give ticket");
            return 0;
        }
        if (stayTime < taskConfig.getCheckParams()) {
            log.info("stay time less than limit, so not to give ticket");
            return 0;
        }
        increaseTimes(uid);
        return taskConfig.getTicket();
    }


    private String getKey(long currTime) {
        ZoneId zoneId = ZoneOffset.of("+05:30");
        String currDate = Instant.ofEpochSecond(currTime)
                .atZone(zoneId)
                .format(DateTimeFormatter.ISO_LOCAL_DATE);
        return HASH_KEY + currDate;
    }
}
