package com.quhong.notification;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.quhong.dao.datas.db.ActorWebPushData;
import com.quhong.data.bo.WebPushMessage;
import com.quhong.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import nl.martijndwars.webpush.Notification;
import nl.martijndwars.webpush.PushService;
import org.apache.http.HttpResponse;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.jose4j.lang.JoseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.Security;
import java.util.concurrent.ExecutionException;

@Component
@Slf4j
public class AppPushNotification {
    private final PushService pushService = new PushService();
    @Value("${server.web-push.privateKey}")
    private String privateKey;
    @Value("${server.web-push.publicKey}")
    private String publicKey;
    @Autowired
    private ObjectMapper objectMapper;

    @PostConstruct
    public void psotInit(){
        try {
            // Add BouncyCastle as an algorithm provider
            if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
                Security.addProvider(new BouncyCastleProvider());
            }
            pushService.setPrivateKey(privateKey);
            pushService.setPublicKey(publicKey);
        }catch (Exception e){
            log.error("set private and public key error. {}", e.getMessage(), e);
            System.exit(0);
        }
    }

    /**
     * 发送消息
     *
     * @param message   消息
     * @param pushData  相关推动数据
     * @throws IOException
     * @throws JoseException
     * @throws GeneralSecurityException
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public void push(WebPushMessage message, ActorWebPushData pushData) throws JoseException, GeneralSecurityException, IOException, ExecutionException, InterruptedException {
        Notification notification = new Notification(
                pushData.getEndPoint(),
                StringUtils.isEmpty(pushData.getUserKey()) ? publicKey : pushData.getUserKey(),
                pushData.getUserAuth(),
                objectMapper.writeValueAsBytes(message));
        HttpResponse response = pushService.send(notification);
        log.info("web push result. {} {} uid={}", JSON.toJSONString(message), JSON.toJSONString(response), pushData.getUid());
    }
}
