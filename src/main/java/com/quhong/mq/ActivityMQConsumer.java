package com.quhong.mq;

import com.quhong.controller.longTerm.pk.LongTermPkController;
import com.quhong.dao.datas.*;
import com.quhong.data.dto.activity.lucky.gift.CheckLuckyDTO;
import com.quhong.data.mq.event.game.lucky.GameLuckyDataProcessMsgData;
import com.quhong.data.pk.PkInfoData;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.event.gala.service.GalaMagicValueService;
import com.quhong.event.gala.service.GalaRoomApplyService;
import com.quhong.event.game.master.v2501.GameMasterMqServiceV2501;
import com.quhong.event.holi.v2503.HoliServiceV2503;
import com.quhong.event.horse.race.v2411.HorseRaceService;
import com.quhong.event.ios.iphone.EventIosIphoneService;
import com.quhong.event.magic.lamp.v2506.EventMagicLampService;
import com.quhong.event.pk.v2502.AnchorPkService;
import com.quhong.event.rank.fool.EventFoolService;
import com.quhong.event.rank.fusion.gift.v2504.EventFusionGiftService;
import com.quhong.event.rank.pk.v2503.EventRankPkServiceV2503;
import com.quhong.event.recharge.node.task.v2505.EventRechargeNodeTaskService;
import com.quhong.event.speed.v2501.SpeedServiceV2501;
import com.quhong.event.speed.v2505.SpeedServiceV2505;
import com.quhong.mq.data.InviteDivideIntoData;
import com.quhong.mq.data.activity.EventUnitCurrencyMsgData;
import com.quhong.mq.data.activity.InviteUserSuccessMsgData;
import com.quhong.mq.data.activity.PlayGameMsgData;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.mq.data.room.DownMicMsgData;
import com.quhong.mq.data.room.EnterRoomMsgData;
import com.quhong.mq.data.room.RoomChatMsgData;
import com.quhong.mq.data.user.operation.FollowMsgData;
import com.quhong.mq.enums.MQConstant;
import com.quhong.service.activity.eid.Eid2024Service;
import com.quhong.service.activity.game.lucky.GameLuckyMqService;
import com.quhong.service.activity.model.mq.action.*;
import com.quhong.service.activity.mvp.user.MvpUserService;
import com.quhong.service.activity.recharge.draw.RechargeDrawService;
import com.quhong.service.activity.week.star.GiftLogService;
import com.quhong.service.activity.week.star.v2.WeekStarTwoMqService;
import com.quhong.service.common.EventCostMonitorService;
import com.quhong.service.invite.InviteOperateService;
import com.quhong.service.longTerm.invite.rank.LongTermInviteRankService;
import com.quhong.service.lucky.gift.LuckyGiftService;
import com.quhong.utils.mq.MqReceiveUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;


/**
 * ActivityMQConsumer 活动模块mq消息消费者
 *
 * <AUTHOR>
 * @date 2021/12/25 15:56
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ActivityMQConsumer {
    private final MqReceiveUtils mqReceiveUtils;
    private final EventMagicLampService eventMagicLampService;
    private final WeekStarTwoMqService weekStarTwoMqService;
    private final GameLuckyMqService gameLuckyMqService;
    private final GiftLogService giftLogService;
    private final LuckyGiftService luckyGiftService;
    private final EventCostMonitorService eventCostMonitorService;
    private final MvpUserService mvpUserService;
    private final InviteOperateService inviteOperateService;
    private final Eid2024Service eid2024Service;
    private final GameMasterMqServiceV2501 gameMasterMqServiceV2501;
    private final GalaRoomApplyService galaRoomApplyService;
    private final GalaMagicValueService galaMagicValueService;
    private final SpeedServiceV2501 speedServiceV2501;
    private final AnchorPkService anchorPkService;
    private final HoliServiceV2503 holiServiceV2503;
    private final EventRankPkServiceV2503 eventRankPkServiceV2503;
    private final EventFoolService eventFoolService;
    private final LongTermPkController longTermPkController;
    private final EventIosIphoneService eventIosIphoneService;
    private final EventFusionGiftService eventFusionGiftService;
    private final SpeedServiceV2505 speedServiceV2505;
    private final EventRechargeNodeTaskService eventRechargeNodeTaskService;
    private final LongTermInviteRankService longTermInviteRankService;
    private final RechargeDrawService rechargeDrawService;
    private final HorseRaceService horseRaceService;

    // 金币流水相关活动模板业务处理
    private final CoinMerchantRechargeMqActionService coinMerchantRechargeMqActionService;
    private final OnlineRechargeMqActionService onlineRechargeMqActionService;
    private final DiamondExchangeForGoldCoinsMqActionService diamondExchangeForGoldCoinsMqActionService;
    private final GameInviteDiamondMqActionService gameInviteDiamondMqActionService;
    private final PlayGameConsumptionMqActionService playGameConsumptionMqActionService;
    private final PlayGameWinMqActionService playGameWinMqActionService;

    //礼物发送记录相关活动模板业务处理
    private final LiveRoomGainDiamondMqActionService liveRoomGainDiamondMqActionService;
    private final ChatRoomGainDiamondMqActionService chatRoomGainDiamondMqActionService;
    private final ReceiveGiftDiamondPriceMqActionService receiveGiftDiamondPriceMqActionService;
    private final ReceivedGiftCountMqActionService receivedGiftCountMqActionService;
    private final ReceivedGiftPriceMqActionService receivedGiftPriceMqActionService;
    private final SendGiftCountMqActionService sendGiftCountMqActionService;
    private final SendGiftPriceMqActionService sendGiftPriceMqActionService;

    private final PkTotalGainMqActionService pkTotalGainMqActionService;
    private final ChatRoomEffectiveDurationMqActionService chatRoomEffectiveDurationMqActionService;
    private final LiveRoomEffectiveDurationMqActionService liveRoomEffectiveDurationMqActionService;
    private final NewFollowUserCountMqActionService newFollowUserCountMqActionService;
    private final PkTotalCountMqActionService pkTotalCountMqActionService;
    private final PkWinCountMqActionService pkWinCountMqActionService;
    //活动组件货币
    private final EventCurrencyConsumeMqActionService eventCurrencyConsumeMqActionService;
    private final EventCurrencyGetMqActionService eventCurrencyGetMqActionService;

    @RabbitListener(queues = MQConstant.EVENT_UNIT_CURRENCY_QUEUE)
    public void unitCurrencyListener(String json) {
        String queueName = MQConstant.EVENT_UNIT_CURRENCY_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
        mqReceiveUtils.actionMethod(queueName, json, EventUnitCurrencyMsgData.class, eventCurrencyConsumeMqActionService::unitMqAction);
        mqReceiveUtils.actionMethod(queueName, json, EventUnitCurrencyMsgData.class, eventCurrencyGetMqActionService::unitMqAction);
    }

    /**
     * 房间场次记录处理队列
     */
    @RabbitListener(queues = MQConstant.EVENT_ROOM_CHAPTER_LOG_QUEUE)
    public void chapterLogListener(String json) {
        String queueName = MQConstant.EVENT_ROOM_CHAPTER_LOG_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
        mqReceiveUtils.actionMethod(queueName, json, RoomChapterData.class, eventIosIphoneService::leaveRoomMqAction);
        //活动组件化
        mqReceiveUtils.actionMethod(queueName, json, RoomChapterData.class, liveRoomEffectiveDurationMqActionService::unitMqAction);
        //活动模版
        mqReceiveUtils.actionMethod(queueName, json, RoomChapterData.class, liveRoomEffectiveDurationMqActionService::mqAction);
    }


    /**
     * pk开始消息处理
     */
    @RabbitListener(queues = MQConstant.EVENT_PK_START_QUEUE)
    public void pkStartListener(String json) {
        String queueName = MQConstant.EVENT_PK_START_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
        mqReceiveUtils.actionMethod(queueName, json, PkInfoData.class, longTermPkController::startPkMqAction);
    }

    /**
     * pk结束消息处理
     */
    @RabbitListener(queues = MQConstant.EVENT_PK_END_QUEUE)
    public void pkEndListener(String json) {
        String queueName = MQConstant.EVENT_PK_END_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
        mqReceiveUtils.actionMethod(queueName, json, PkInfoData.class, longTermPkController::endPkMqAction);
        //活动组件化
        mqReceiveUtils.actionMethod(queueName, json, PkInfoData.class, pkTotalCountMqActionService::unitMqAction);
        mqReceiveUtils.actionMethod(queueName,json,PkInfoData.class, pkWinCountMqActionService::unitMqAction);

        //活动模版
        mqReceiveUtils.actionMethod(queueName, json, PkInfoData.class, pkTotalCountMqActionService::mqAction);
        mqReceiveUtils.actionMethod(queueName, json, PkInfoData.class, pkWinCountMqActionService::mqAction);
    }

    /**
     * 邀请用户成功消息处理
     *
     */
    @RabbitListener(queues = MQConstant.EVNET_INVITE_USER_SUCCESS_QUEUE)
    public void inviteUserListener(String json) {
        String queueName = MQConstant.EVNET_INVITE_USER_SUCCESS_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
        mqReceiveUtils.actionMethod(queueName, json, InviteUserSuccessMsgData.class, longTermInviteRankService::inviteUserSuccessAction);
    }

    /**
     * 星动嘉年华获得背包礼物mq处理
     *
     */
    @RabbitListener(queues = MQConstant.EVENT_STARRY_GET_GIFT_QUEUE)
    public void starryGetGiftListener(String json) {
        String queueName = MQConstant.EVENT_STARRY_GET_GIFT_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
        mqReceiveUtils.actionMethod(queueName, json, ActorBackpackData.class, eid2024Service::starryGetGiftAction);
//        mqReceiveUtils.actionMethod(queueName, json, ActorBackpackData.class, welfareCommingService::starryGetGiftAction);
//        mqReceiveUtils.actionMethod(queueName, json, ActorBackpackData.class, welfareCommingV2410Service::starryGetGiftAction);
    }

    /**
     * 幸运游戏数据处理mq(长期)
     *
     */
    @RabbitListener(queues = MQConstant.EVENT_GAME_LUCKY_DATA_PROCESSING_QUEUE)
    public void gameLuckyDataProcessListener(String json) {
        String queueName = MQConstant.EVENT_GAME_LUCKY_DATA_PROCESSING_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
        //长期
        mqReceiveUtils.actionMethod(queueName, json, GameLuckyDataProcessMsgData.class, gameLuckyMqService::dataProcessAction);
    }

    /**
     * 金币流水处理
     *
     */
    @RabbitListener(queues = MQConstant.EVENT_GAME_LUCKY_MONEY_DETAIL_QUEUE)
    public void moneyDetailListener(String json) {
        String queueName = MQConstant.EVENT_GAME_LUCKY_MONEY_DETAIL_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
        //长期
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, gameLuckyMqService::moneyDetailAction);
//        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, superPlayerService::action);
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, rechargeDrawService::moneyDetailAction);
//        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, rechargeCardDrawService::moneyDetailAction);
//        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, eid2024Service::moneyDetailAction);
//        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, independenceDayMqService::moneyDetailActionLock);
//        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, xmasMqServiceV2412::moneyDetailAction);
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, eventRechargeNodeTaskService::moneyDetailAction);
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, longTermInviteRankService::moneyDetailAction);
    }


    @RabbitListener(queues = MQConstant.EVENT_FOLLOW_QUEUE)
    public void eventFollowListener(String json) {
        String queueName = MQConstant.EVENT_FOLLOW_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);

        //活动组件化
        mqReceiveUtils.actionMethod(queueName, json, FollowMsgData.class, newFollowUserCountMqActionService::unitMqAction);
        //活动模版
        mqReceiveUtils.actionMethod(queueName, json, FollowMsgData.class, newFollowUserCountMqActionService::mqAction);
    }


    @RabbitListener(queues = MQConstant.EVENT_MONEY_DETAIL_QUEUE)
    public void eventMoneyDetailListener(String json) {
        String queueName = MQConstant.EVENT_MONEY_DETAIL_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);

        //活动组件化
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, coinMerchantRechargeMqActionService::unitMqAction);
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, onlineRechargeMqActionService::unitMqAction);
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, diamondExchangeForGoldCoinsMqActionService::unitMqAction);
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, gameInviteDiamondMqActionService::unitMqAction);
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, playGameConsumptionMqActionService::unitMqAction);
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, playGameWinMqActionService::unitMqAction);

        // 活动模版
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, coinMerchantRechargeMqActionService::mqAction);
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, onlineRechargeMqActionService::mqAction);
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, diamondExchangeForGoldCoinsMqActionService::mqAction);
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, gameInviteDiamondMqActionService::mqAction);
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, playGameConsumptionMqActionService::mqAction);
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, playGameWinMqActionService::mqAction);
    }

    /**
     * 玩游戏队列处理
     *
     * @param json mq
     */
    @RabbitListener(queues = MQConstant.GAME_PLAY_QUEUE)
    public void gamePlayListener(String json) {
        String queueName = MQConstant.GAME_PLAY_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
//        mqReceiveUtils.actionMethod(queueName, json, PlayGameMsgData.class, letsPartyService::playGameAction);
//        mqReceiveUtils.actionMethod(queueName, json, PlayGameMsgData.class, welfareCommingService::playGameAction);
//        mqReceiveUtils.actionMethod(queueName, json, PlayGameMsgData.class, welfareCommingV2410Service::playGameAction);
        mqReceiveUtils.actionMethod(queueName, json, PlayGameMsgData.class, horseRaceService::playGameAction);
//        mqReceiveUtils.actionMethod(queueName, json, PlayGameMsgData.class, speedServiceV2501::playGameAction);
//        mqReceiveUtils.actionMethod(queueName, json, PlayGameMsgData.class, loverServiceV2501::playGameAction);
        mqReceiveUtils.actionMethod(queueName, json, PlayGameMsgData.class, holiServiceV2503::playGameAction);
        mqReceiveUtils.actionMethod(queueName, json, PlayGameMsgData.class, speedServiceV2505::playGameAction);
    }

    /**
     * 用户语聊下麦mq处理
     *
     * @param json mq
     */
    @RabbitListener(queues = MQConstant.EVENT_DOWN_WHEAT_QUEUE)
    public void downMicListener(String json) {
        String queueName = MQConstant.EVENT_DOWN_WHEAT_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
//        mqReceiveUtils.actionMethod(queueName, json, DownMicMsgData.class, letsPartyService::downMicAction);
//        mqReceiveUtils.actionMethod(queueName, json, DownMicMsgData.class, welfareCommingV2410Service::downMicAction);
//        mqReceiveUtils.actionMethod(queueName, json, DownMicMsgData.class, eventRankPkService::downMicAction);
        //长期
        mqReceiveUtils.actionMethod(queueName, json, DownMicMsgData.class, galaMagicValueService::downMicAction);
        mqReceiveUtils.actionMethod(queueName, json, DownMicMsgData.class, eventRankPkServiceV2503::downMicAction);
    }


    /**
     * 活动成本预算队列
     *
     * @param json rewardInfoData
     * @see com.quhong.dao.datas.RewardInfoData 奖励信息
     */
    @RabbitListener(queues = MQConstant.EVENT_COST_COUNT_QUEUE)
    public void eventCostCountListener(String json) {
        String queueName = MQConstant.EVENT_COST_COUNT_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
        mqReceiveUtils.actionMethod(queueName, json, RewardInfoData.class, eventCostMonitorService::costCountAction);
    }

    /**
     * 支付成功mq队列处理
     *
     * @param uid uid
     */
    @RabbitListener(queues = MQConstant.ACTIVITY_PAYMENT_QUEUE)
    public void paymentListener(String uid) {
        String queueName = MQConstant.ACTIVITY_PAYMENT_QUEUE;
        mqReceiveUtils.logMqReceive(uid, queueName);
//        mqReceiveUtils.actionMethod(queueName, uid, diwaliMqService::paymentAction);
        //长期
        mqReceiveUtils.actionMethod(queueName, uid, mvpUserService::paymentAction);
    }

    /**
     * 进房mq队列处理
     *
     * @param json json
     */
    @RabbitListener(queues = MQConstant.ACTIVITY_ENTER_ROOM_MSG_QUEUE)
    public void enterRoomListener(String json) {
        String queueName = MQConstant.ACTIVITY_ENTER_ROOM_MSG_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
//        mqReceiveUtils.actionMethod(queueName, json, EnterRoomMsgData.class, welfareCommingService::enterRoomMqAction);
//        mqReceiveUtils.actionMethod(queueName, json, EnterRoomMsgData.class, welfareCommingV2410Service::enterRoomMqAction);
//        mqReceiveUtils.actionMethod(queueName, json, EnterRoomMsgData.class, eventRankPkService::enterRoomMqAction);
//        mqReceiveUtils.actionMethod(queueName, json, EnterRoomMsgData.class, (mqData) -> eventPlantDatePalmService.enterRoomMqAction(mqData, EventCode.EVENT_PLANT_DATE_PALM));
//        mqReceiveUtils.actionMethod(queueName, json, EnterRoomMsgData.class, (mqData) -> eventPlantDatePalmService.enterRoomMqAction(mqData, EventCode.EVENT_PLANT_DATE_PALM_2));
//        mqReceiveUtils.actionMethod(queueName, json, EnterRoomMsgData.class, (mqData) -> eventPlantDatePalmService.enterRoomMqAction(mqData, EventCode.EVENT_PLANT_DATE_PALM_2410));
        //长期
        mqReceiveUtils.actionMethod(queueName, json, EnterRoomMsgData.class, galaRoomApplyService::enterRoomAction);
        mqReceiveUtils.actionMethod(queueName, json, EnterRoomMsgData.class, eventRankPkServiceV2503::enterRoomMqAction);
    }

    /**
     * 房间内发消息 mq处理
     *
     */
    @RabbitListener(queues = MQConstant.ACTIVITY_ROOM_CHAT_MSG_QUEUE)
    public void RoomChatMsgListener(String json) {
        String queueName = MQConstant.ACTIVITY_ROOM_CHAT_MSG_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
//        mqReceiveUtils.actionMethod(queueName, json, RoomChatMsgData.class, xmasMqServiceV2412::roomMsgAction);
//        mqReceiveUtils.actionMethod(queueName, json, RoomChatMsgData.class, independenceDayMqService::receiveRoomChatMsgLock);
        mqReceiveUtils.actionMethod(queueName, json, RoomChatMsgData.class, holiServiceV2503::roomMsgAction);
    }

    /**
     * 用户退出房间mq处理
     *
     * @param json mq
     */
    @RabbitListener(queues = MQConstant.ACTIVITY_LEAVE_ROOM_QUEUE)
    public void leaveRoomListener(String json) {
        String queueName = MQConstant.ACTIVITY_LEAVE_ROOM_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
        //长期
        mqReceiveUtils.actionMethod(queueName, json, EnterRoomLogData.class, galaMagicValueService::leaveRoomAction);
//        mqReceiveUtils.actionMethod(queueName, json, EnterRoomLogData.class, eventIosIphoneService::leaveRoomMqAction);
        mqReceiveUtils.actionMethod(queueName, json, EnterRoomLogData.class, chatRoomEffectiveDurationMqActionService::unitMqAction);
        mqReceiveUtils.actionMethod(queueName, json, EnterRoomLogData.class, chatRoomEffectiveDurationMqActionService::mqAction);
    }

    @RabbitListener(queues = MQConstant.INVITE_DIVIDE_INTO_QUEUE)
    public void inviteDivideInto(String json) {
        String queueName = MQConstant.INVITE_DIVIDE_INTO_QUEUE;
        mqReceiveUtils.actionMethod(queueName, json, InviteDivideIntoData.class, inviteOperateService::doDivideInto);
    }

//
//    /**
//     * 用户玩yxsk系列游戏mq接收
//     *
//     * @param json mq
//     */
//    @RabbitListener(queues = MQConstant.ACTIVITY_PLAY_YXSK_GAME_QUEUE)
//    public void playYxskGameListener(String json) {
//        String queueName = MQConstant.ACTIVITY_PLAY_YXSK_GAME_QUEUE;
//        log.info("mq receive message. queue={}, json={}", queueName, json);
////        mqReceiveUtils.actionMethod(queueName, json, UpdateCoinDTO.class, diwaliMqService::playYxskGameAction);
//    }


    /**
     * 首次付费mq接收
     *
     * @param uid uid
     */
    @RabbitListener(queues = MQConstant.ACTIVITY_FIRST_PAYMENT_QUEUE)
    public void firstPaymentListener(String uid) {
        String queueName = MQConstant.ACTIVITY_FIRST_PAYMENT_QUEUE;
        mqReceiveUtils.logMqReceive(uid, queueName);
//        mqReceiveUtils.actionMethod(queueName, uid, String.class, independenceDayMqService::receiveFirstPaymentMqLock);
    }

    /**
     * 送礼成功mq接收
     *
     * @param json mq
     */
    @RabbitListener(queues = MQConstant.ACTIVITY_SEND_GIFT_SUCCESS_QUEUE)
    public void sendGiftSuccessListener(String json) {

        String queueName = MQConstant.ACTIVITY_SEND_GIFT_SUCCESS_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
        // 周星活动使用
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, (mqData) -> giftLogService.checkAndSaveScoreLog(ActivityTypeEnum.WEEK_STAR_ACTIVITY.getCode(), mqData));
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, weekStarTwoMqService::sendGiftAction);
        //长期
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, galaMagicValueService::giftSendAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, longTermInviteRankService::sendGiftAction);

        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, speedServiceV2501::sendGiftAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, speedServiceV2505::sendGiftAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, holiServiceV2503::giftSendAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, eventFoolService::sendGiftAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, eventMagicLampService::sendGiftAction);

//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, xmasMqServiceV2412::sendGiftMqAction);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, (mqData)-> giftLogService.checkAndSaveScoreLog(ActivityTypeEnum.SEND_LORD_GIFT_AWARD_ACTIVITY.getCode(), mqData));
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, loverServiceV2501::sendGiftAction);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, trainingService::giftSendAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, anchorPkService::sendGiftAction);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, welfareCommingService::sendGiftSuccessAction);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, welfareCommingV2410Service::sendGiftSuccessAction);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, eventRankPkService::giftSendAction);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, eventShootService::shootGiftHandle);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, letsPartyService::sendGiftAction);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, loverGiftPriceRankActivityService::sendGiftAction);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, eventShootServiceV2410::shootGiftHandle);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, eid2024Service::sendGiftAction);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, independenceDayMqService::receiveSendGiftMqLock);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, independenceDayMqService::receiveSendPostcardMqLock);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, eventAnniversaryService::sendGiftAction);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, missContestService::handleReceiveGift);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, missContestService::sendGiftAction);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, cupidService::sendGiftSuccessAction);
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, (mqData) -> eventPlantDatePalmService.sendGiftSuccessAction(mqData, EventCode.EVENT_PLANT_DATE_PALM));
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, (mqData) -> eventPlantDatePalmService.sendGiftSuccessAction(mqData, EventCode.EVENT_PLANT_DATE_PALM_2));
//        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, (mqData) -> eventPlantDatePalmService.sendGiftSuccessAction(mqData, EventCode.EVENT_PLANT_DATE_PALM_2410));
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, horseRaceService::sendGiftAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, eventRankPkServiceV2503::giftSendAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, eventIosIphoneService::giftSendAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, eventFusionGiftService::sendGiftAction);

        //活动组件相关业务逻辑
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, liveRoomGainDiamondMqActionService::unitMqAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, chatRoomGainDiamondMqActionService::unitMqAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, receiveGiftDiamondPriceMqActionService::unitMqAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, receivedGiftCountMqActionService::unitMqAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, receivedGiftPriceMqActionService::unitMqAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, sendGiftCountMqActionService::unitMqAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, sendGiftPriceMqActionService::unitMqAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, pkTotalGainMqActionService::unitMqAction);


        //活动模板相关业务逻辑
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, liveRoomGainDiamondMqActionService::mqAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, chatRoomGainDiamondMqActionService::mqAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, receiveGiftDiamondPriceMqActionService::mqAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, receivedGiftCountMqActionService::mqAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, receivedGiftPriceMqActionService::mqAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, sendGiftCountMqActionService::mqAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, sendGiftPriceMqActionService::mqAction);
        mqReceiveUtils.actionMethod(queueName, json, SendGiftSuccessMsgData.class, pkTotalGainMqActionService::mqAction);

    }

    @RabbitListener(queues = MQConstant.EVENT_LUCKY_GIFT_PROCESS_QUEUE)
    public void luckyGiftProcessListener(String json) {
        String queueName = MQConstant.EVENT_LUCKY_GIFT_PROCESS_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
        mqReceiveUtils.actionMethod(queueName, json, CheckLuckyDTO.class, luckyGiftService::actionProcess);
    }

    @RabbitListener(queues = MQConstant.PLAY_GAME_WIN_QUEUE)
    public void playGameWinProcessListener(String json) {
        String queueName = MQConstant.PLAY_GAME_WIN_QUEUE;
        mqReceiveUtils.logMqReceive(json, queueName);
        mqReceiveUtils.actionMethod(queueName, json, MoneyDetailData.class, gameMasterMqServiceV2501::actionProcess);
    }


}
