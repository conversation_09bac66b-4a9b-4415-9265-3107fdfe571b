package com.quhong;

import com.quhong.annotation.EnableMethodMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableMethodMonitor
@EnableRabbit
@EnableCaching
@EnableScheduling
@ServletComponentScan
@PropertySource(value = {
        "file:./config/common_config.properties",
        "file:./config/jdbc.properties",
        "file:./config/redis.properties",
        "file:./config/mongodb.properties",
        "file:./config/rabbitmq.properties"}, ignoreResourceNotFound = true)
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, RedisAutoConfiguration.class, DataSourceTransactionManagerAutoConfiguration.class})
public class ActivityApplication {
    private static final Logger logger = LoggerFactory.getLogger(ActivityApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(ActivityApplication.class, args);
        logger.info("======================= activity start ==========================");
    }
}
