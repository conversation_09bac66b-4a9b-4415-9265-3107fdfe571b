package com.quhong.scheduled.activity.model;

import com.quhong.constant.activity.EventGroupConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MasterUtils;
import com.quhong.dao.AppConfigActivityDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @since 2024/1/26 16:51
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EventModelScheduled {
    private final AppConfigActivityDao appConfigActivityDao;
    private final MasterUtils masterUtils;

    /**
     * 活动模版活动每日10点过期已结束活动
     */
    @Async("asyncPool")
    @Scheduled(cron = "0 0 2 * * ?")
    public void ModelEventInvalid(){
        if (!masterUtils.isMaster()) {
            return;
        }
        long currTime = DateHelper.getCurrTime();
        long time = currTime - Duration.ofDays(30).getSeconds();//只处理一个月前的
        appConfigActivityDao.updateOverGroupEventToInvalid(time,currTime, EventGroupConstant.MODEL_EVENT, "over auto invalid");
        appConfigActivityDao.updateOverGroupEventToInvalid(time, currTime, EventGroupConstant.UNIT_EVENT, "over auto invalid");
    }
}
