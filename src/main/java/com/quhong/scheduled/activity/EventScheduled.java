package com.quhong.scheduled.activity;

import com.quhong.config.AsyncConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MasterUtils;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.enums.EventCode;
import com.quhong.event.diwali.v2412.XmasServiceV2412;
import com.quhong.event.game.master.v2501.GameMasterServiceV2501;
import com.quhong.event.horse.race.v2411.HorseRaceService;
import com.quhong.event.ios.iphone.EventIosIphoneService;
import com.quhong.event.lover.v2501.LoverServiceV2501;
import com.quhong.event.magic.lamp.v2506.EventMagicLampService;
import com.quhong.event.rank.fool.EventFoolService;
import com.quhong.event.rank.fusion.gift.v2504.EventFusionGiftService;
import com.quhong.event.shoot.v2501.EventShootService;
import com.quhong.event.speed.v2501.SpeedServiceV2501;
import com.quhong.event.speed.v2505.SpeedServiceV2505;
import com.quhong.service.activity.eid.Eid2024Service;
import com.quhong.service.longTerm.invite.rank.LongTermInviteRankService;
import com.quhong.service.longTerm.pk.LongTermPkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
//import com.quhong.service.activity.eid.Eid2024Service;

/**
 * <AUTHOR>
 * @since 2024/11/25 14:53
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EventScheduled {
    private final MasterUtils masterUtils;
    private final Eid2024Service eid2024Service;
    //    private final EventAnniversaryService eventAnniversaryService;
    private final HorseRaceService horseRaceService;
    private final XmasServiceV2412 xmasServiceV2412;
    private final GameMasterServiceV2501 gameMasterServiceV2501;
    private final AppConfigActivityDao appConfigActivityDao;
    private final EventShootService eventShootService;
    private final SpeedServiceV2501 speedServiceV2501;
    private final LoverServiceV2501 loverServiceV2501;
    private final EventFoolService eventFoolService;
    private final LongTermPkService longTermPkService;
    private final EventIosIphoneService eventIosIphoneService;
    private final EventFusionGiftService eventFusionGiftService;
    private final SpeedServiceV2505 speedServiceV2505;
    private final LongTermInviteRankService longTermInviteRankService;
    private final EventMagicLampService eventMagicLampService;

    @Async("asyncPool")
    @Scheduled(cron = "5 0 0 25 6 ?", zone = "GMT+8:00")
    public void magicLampRankReward(){
        eventMagicLampService.rankRewards(EventMagicLampService.EVENT_CODE, EventMagicLampService.SEND_RANK_TYPE, "Sender Rank", EventMagicLampService.SENDER_RANK_AWARDS);
        eventMagicLampService.rankRewards(EventMagicLampService.EVENT_CODE, EventMagicLampService.RECEIVE_RANK_TYPE, "Receiver Rank", EventMagicLampService.RECEIVER_RANK_AWARDS);
    }

    @Async("asyncPool")
    @Scheduled(cron = "5 0 0 * * 1", zone = "GMT+8:00")
    public void longTermInviteRankReward() {
        if (!masterUtils.isMaster()) {
            return;
        }
        if (DateHelper.getCurrTime() < 1748224800) {//2025-05-26 10点之后才可正常使用定时器
            return;
        }
        longTermInviteRankService.inviteCountRankRewards(-1);
        longTermInviteRankService.newUserRechargeRankRewards(-1);
        longTermInviteRankService.newHostIncomeRankRewards(-1);
    }

//    @Async("asyncPool")
//    @Scheduled(cron = "5 0 0 20 5 ?", zone = "GMT+8:00")
    public void speed2505RankReward() {
        if (!masterUtils.isMaster()) {
            return;
        }
        speedServiceV2505.rankRewards(EventCode.EVENT_SPEED_2505, 1);
        speedServiceV2505.rankRewards(EventCode.EVENT_SPEED_2505, 2);
    }

//    @Async(AsyncConfig.ASYNC_TASK)
//    @Scheduled(cron = "5 0 0 * * *", zone = "GMT+8:00")
    public void fusionGiftDailyRankReward() {
        if (!masterUtils.isMaster()) {
            return;
        }

        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EventCode.EVENT_FUSION_GIFT_2504);
        LocalDateTime now = LocalDateTime.now(ZoneOffset.of(configData.getZoneOffset())).minusHours(2);
        long dateSec = now.toEpochSecond(ZoneOffset.of(configData.getZoneOffset()));
        if (dateSec >= configData.getStartTime() && dateSec <= configData.getEndTime()) {
            String localDateStr = now.toLocalDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
            eventFusionGiftService.rankReward(1, localDateStr);
        }
    }

//    @Async(AsyncConfig.ASYNC_TASK)
//    @Scheduled(cron = "10 0 0 15 5 ?", zone = "GMT+8:00")
    public void fusionGiftTotalRankReward() {
        if (!masterUtils.isMaster()) {
            return;
        }
        eventFusionGiftService.rankReward(1, "");
        eventFusionGiftService.rankReward(2, "");
    }


    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "5 0 0 * * 1", zone = "GMT+8:00")
    public void longTermPkRewardAndSengNotice() {
        if (!masterUtils.isMaster()) {
            return;
        }
        longTermPkService.rankRewards(LongTermPkService.EVENT_CODE, 1, "Host Ranking",
                LongTermPkService.INCOME_RANK_REWARDS, -1);
    }

    @Async("asyncPool")
    @Scheduled(cron = "5 0 0 1 7 ?", zone = "GMT+8:00")
    public void iosIphoneRankNotice() {
        if (!masterUtils.isMaster()) {
            return;
        }
        eventIosIphoneService.rankNotice();
    }

//    @Async("asyncPool")
//    @Scheduled(cron = "5 0 0 2 4 ?", zone = "GMT+8:00")
    public void foolRewardAndSendNotice() {
        if (!masterUtils.isMaster()) {
            return;
        }
        eventFoolService.rankRewards(EventCode.EVENT_FOOL_2503, 1, "Prank Master Ranking", EventFoolService.SEND_RANK_REWARDS);
        eventFoolService.rankRewards(EventCode.EVENT_FOOL_2503, 2, "Receiver ranking", EventFoolService.RECEIVE_RANK_REWARDS);
    }


    //    @Async("asyncPool")
//    @Scheduled(cron = "5 0 0 15 2 ?", zone = "GMT+8:00")
    public void giveRewardAndSendNotice() {
        if (!masterUtils.isMaster()) {
            return;
        }
        loverServiceV2501.loverRankReward(EventCode.EVENT_LOVER_2501);
        loverServiceV2501.receiverRankReward(EventCode.EVENT_LOVER_2501);
    }

//    @Async("asyncPool")
//    @Scheduled(cron = "5 0 0 24 3 ?", zone = "GMT+8:00")
    public void speedRankReward() {
        if (!masterUtils.isMaster()) {
            return;
        }
        speedServiceV2501.rankRewards(EventCode.EVENT_SPEED_2503, 1);
        speedServiceV2501.rankRewards(EventCode.EVENT_SPEED_2503, 2);
    }

    /**
     * 射门活动定时器，总榜通知
     */
//    @Async("asyncPool")
//    @Scheduled(cron = "5 0 0 23 1 ?", zone = "GMT+8:00")
    public void giveAndSendNotice() {
        if (!masterUtils.isMaster()) {
            return;
        }
        log.info("shoot活动定时器，总榜通知启动");
        AppConfigActivityData configData = appConfigActivityDao.getOneByCodeAndValid(EventCode.EVENT_SHOOT_2501, 1);
        eventShootService.activityRankNotice(configData, 1, null);
        eventShootService.activityRankNotice(configData, 2, null);
        // 团队榜奖励逻辑
        eventShootService.teamRankRewardAndNotice(configData);
    }

    @Async("asyncPool")
    @Scheduled(cron = "10 0 0 11 6 ?", zone = "GMT+8:00")
    public void rankReward() {
        if (!masterUtils.isMaster()) {
            return;
        }
        horseRaceService.sendRankRewardAndNotice(1);
        horseRaceService.sendRankRewardAndNotice(2);
    }

    //    @Async("asyncPool")
//    @Scheduled(cron = "10 0 0 2 1 ?", zone = "GMT+8:00")//10 0 0 2 1 ?
    public void xmasTotalRankReward() {
        if (!masterUtils.isMaster()) {
            return;
        }
        xmasServiceV2412.sendRankRewardAndNotice(1);
        xmasServiceV2412.sendRankRewardAndNotice(2);
    }

//    @Async("asyncPool")
//    @Scheduled(cron = "5 0 0 9 4 ?", zone = "GMT+8:00")
    public void gameMasterRankReward() {
        if (!masterUtils.isMaster()) {
            return;
        }
        gameMasterServiceV2501.sendRankRewardAndNotice();
    }

    //    @Async("asyncPool")
//    @Scheduled(cron = "10 0 0 * * ?", zone = "GMT+8:00")//10 0 0 * * ?
    public void xmasDailyReward() {
        if (!masterUtils.isMaster()) {
            return;
        }
        xmasServiceV2412.sendDailyRankReward();
    }

//    @Async("asyncPool")
//    @Scheduled(cron = "5 0 0 * * ?", zone = "GMT+5:30")
//    public void dailyReward() {
//        if (!masterUtils.isMaster()) {
//            return;
//        }
//        eventAnniversaryService.dailyReward(false);
//    }
//
//    @Async("asyncPool")
//    @Scheduled(cron = "10 0 0 28 8 ?", zone = "GMT+5:30")
//    public void rankReward() {
//        if (!masterUtils.isMaster()) {
//            return;
//        }
//        eventAnniversaryService.rankReward();
//    }

//
//    @PostConstruct
//    public void init(){
//        distributionTotalRanking();
//    }


//    @Async("asyncPool")
//    @Scheduled(cron = "5 0 0 6 5 ?", zone = "GMT+8:00")
    public void distributionTotalRanking() {
        if (!masterUtils.isMaster()) {
            return;
        }
        eid2024Service.distributionTotalRanking();
    }

}
