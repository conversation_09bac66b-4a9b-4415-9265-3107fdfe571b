package com.quhong.scheduled.event.unit;

import com.quhong.config.AsyncConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MasterUtils;
import com.quhong.service.event.unit.UnitEventRankService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-06-11 11:21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RankSettlementScheduled {
    private final MasterUtils masterUtils;
    private final UnitEventRankService unitEventRankService;

    /**
     * 活动榜单每天0点结算
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "5 0 0 * * ?", zone = "GMT+8:00")
    public void eventRankUnitSettlementBeiJing() {
        log.info("EventRankUnitSettlement BeiJing");
        if (!masterUtils.isMaster()) {
            return;
        }
        unitEventRankService.rankSettlement(DateHelper.BEIJING);
        log.info("Rank settlement completed BeiJing");
    }

    /**
     * 活动榜单每天0点结算
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "5 0 0 * * ?", zone = "GMT")
    public void eventRankUnitSettlementUTC() {
        log.info("EventRankUnitSettlement UTC");
        if (!masterUtils.isMaster()) {
            return;
        }
        unitEventRankService.rankSettlement(DateHelper.UTC);
        log.info("Rank settlement completed UTC");
    }
}
