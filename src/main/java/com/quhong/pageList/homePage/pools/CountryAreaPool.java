package com.quhong.pageList.homePage.pools;

import com.quhong.data.HostPoolData;
import com.quhong.data.objects.CountryInfoVO;
import com.quhong.pageList.base.pools.SimplePool;
import com.quhong.pageList.hostList.pools.AbstractHostPool;
import com.quhong.utils.CountryIndexUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class CountryAreaPool extends AbstractHostPool<HostPoolData> {
    private static final Logger logger = LoggerFactory.getLogger(CountryAreaPool.class);

    private Map<String, SimplePool<HostPoolData>> countryMap = new ConcurrentHashMap<>();
    private Map<String, CountryInfoVO> infoVOMap = new ConcurrentHashMap<>();
    private Map<String, SimplePool<HostPoolData>> areaMap = new ConcurrentHashMap<>();

    private Map<String, String> countryToAreaMap = new HashMap<>();
    private Map<String, String> countryCodeToCountryMap = new HashMap<>();
    //    private Map<String, List<String>> areaToCountryMap = new HashMap<>();
    private Map<String, SimplePool<HostPoolData>> newAreaMap = new ConcurrentHashMap<>();

    public CountryAreaPool() {
        initCountries();
        initAreas();
    }

    @Override
    public void clear() {
        this.infoVOMap.clear();
        for (SimplePool<HostPoolData> simplePool : this.countryMap.values()) {
            simplePool.clear();
        }
        for (SimplePool<HostPoolData> simplePool : this.areaMap.values()) {
            simplePool.clear();
        }
        for (SimplePool<HostPoolData> simplePool : this.newAreaMap.values()) {
            simplePool.clear();
        }
    }

    private void initCountries() {
        addCountryCodeToCountry("VN", "Vietnam");
    }

    private void addCountryCodeToCountry(String countryCode, String country) {
        countryCodeToCountryMap.put(countryCode, country);
    }

    private void initAreas() {
        // 东南亚
        addArea("SASIA", Arrays.asList("BN", "KH", "ID", "LA", "MY",
                "MM", "PH", "SG", "TH", "TL", "VN"));
        // 阿拉伯
        addArea("ARAB", Arrays.asList("DZ", "BH", "KM", "DJ", "EG",
                "IQ", "MA", "OM", "BL", "QA", "SA",
                "SO", "SD", "SY", "TN", "AE", "YE",
                "JO", "KW", "LB", "LY", "MR"));
        // 印度
        addArea("INDIA", Arrays.asList("IN"));
        // 欧美
        addArea("EUAM", Arrays.asList("UA", "CO", "VE", "US", "CA",
                "BR", "RU", "RO", "PE", "RS", "RU",
                "FI", "CL", "AR", "TR", "ZA", "BO",
                "CU"));
        //中东
        addArea("MIDDLEEAST", Arrays.asList("DZ", "BH", "CY", "EG",
                "IR", "IQ", "IL", "JO", "KW", "LB", "LY", "OM", "PS",
                "QA", "SA", "SY", "TN", "TR", "AE", "YE", "MA"));
        //美洲
        addArea("AMERICA", Arrays.asList("AG", "AR", "BS", "BB", "BO",
                "BR", "CA", "CL", "CO", "CR", "CU", "DM", "DO", "EC", "SV",
                "GF", "GD", "GT", "GY", "HT", "HN", "JM", "MX", "NI", "PA",
                "PY", "PE", "KN", "LC", "VC", "SR", "TT", "UM", "US", "UY",
                "VE"));
        //西太平洋
        addArea("WESTERNPACIFIC", Arrays.asList("AU", "CN", "HK", "JP",
                "KR", "SG", "TW", "AT", "BY", "BE", "BQ", "CZ", "EE", "FI",
                "FR", "DE", "HU", "IS", "IE", "LV", "LI", "LT", "LU", "MD",
                "MC", "NL", "NO", "PL", "RU", "SK", "SE", "CH", "UA", "GB"));
        //独联体
        addArea("CIS", Arrays.asList("AM", "AZ", "BY", "KZ", "KG", "MD",
                "RU", "TJ", "UA", "UZ"));
    }

    private void addArea(String area, List<String> countryCodeList) {
        area = area.toUpperCase();
        for (String countryCode : countryCodeList) {
            this.countryToAreaMap.put(countryCode.toUpperCase(), area);
        }
//        this.areaToCountryMap.put(area, countryCodeList);
    }

    @Override
    public void add(HostPoolData hostPoolData) {
        // 添加到国家
        doAddToCountry(hostPoolData);
        // 添加到区域
        doAddToArea(hostPoolData);
        // 添加国家信息
        doAddCountryInfo(hostPoolData);
        // 添加到新的区域map
        doAddToNewArea(hostPoolData);

    }

    @Override
    public void finish() {
        for (SimplePool<HostPoolData> simplePool : this.countryMap.values()) {
            simplePool.finish();
        }
        for (SimplePool<HostPoolData> simplePool : this.areaMap.values()) {
            simplePool.finish();
        }
        for (SimplePool<HostPoolData> simplePool : this.newAreaMap.values()) {
            simplePool.finish();
        }
    }

    private void doAddToNewArea(HostPoolData hostPoolData) {
        if (StringUtils.isEmpty(hostPoolData.getCountryCode())) {
            logger.error("host country code is empty. uid={}", hostPoolData.getUid());
            return;
        }
        String hostCountryIndex = CountryIndexUtils.getHostCountryIndex(hostPoolData.getCountryCode());
        if (StringUtils.isEmpty(hostCountryIndex)) {
            return;
        }
        SimplePool<HostPoolData> simplePool = newAreaMap.get(hostCountryIndex);
        if (simplePool == null) {
            simplePool = new SimplePool<>();
            newAreaMap.put(hostCountryIndex, simplePool);
        }
        simplePool.add(hostPoolData);
    }

    private void doAddToCountry(HostPoolData hostPoolData) {
        if (StringUtils.isEmpty(hostPoolData.getCountryCode())) {
            logger.error("host country code is empty. uid={}", hostPoolData.getUid());
            return;
        }
        String countryCode = hostPoolData.getCountryCode().toUpperCase();
        SimplePool<HostPoolData> pool = countryMap.get(countryCode);
        if (pool == null) {
            pool = new SimplePool<>();
            countryMap.put(countryCode, pool);
        }
        pool.add(hostPoolData);
    }

    private void doAddToArea(HostPoolData hostPoolData) {
        String[] areas = hostPoolData.getArea();
        for (String countryCode : areas) {
            countryCode = countryCode.toUpperCase();
            String area = countryToAreaMap.get(countryCode);
            if (area == null || countryCode.equals("ALL")) {
                // 这里需要优化
                area = "GLOBAL";
            }
            SimplePool<HostPoolData> pool = areaMap.get(area);
            if (pool == null) {
                pool = new SimplePool<>();
                areaMap.put(area, pool);
            }
//            logger.info("add to area. area={} countryCode={} uid={}", area, countryCode, hostPoolData.getUid());
            pool.add(hostPoolData);
        }
    }

    private void doAddCountryInfo(HostPoolData hostPoolData) {
        String countryCode = hostPoolData.getCountryCode().toUpperCase();
        CountryInfoVO infoVO = infoVOMap.get(countryCode);
        if (infoVO == null) {
            String country = countryCodeToCountryMap.get(countryCode);
            if (StringUtils.isEmpty(country)) {
                country = hostPoolData.getActorData().getCountry();
            }
            if (StringUtils.isEmpty(country)) {
                return;
            }
            infoVO = new CountryInfoVO(country, countryCode);
            infoVOMap.put(countryCode, infoVO);
        }
    }

    public String searchArea(String countryCode) {
        if (StringUtils.isEmpty(countryCode)) {
            return null;
        }
        countryCode = countryCode.toUpperCase();
        return countryToAreaMap.get(countryCode);
    }

    public CountryInfoVO getCountry(String code) {
        return infoVOMap.get(code);
    }

    public Map<String, SimplePool<HostPoolData>> getCountryMap() {
        return countryMap;
    }

    public List<HostPoolData> getHostList(String area) {
        if (StringUtils.isEmpty(area)) {
            return new ArrayList<>();
        }
        area = area.toUpperCase();
        SimplePool<HostPoolData> pool = areaMap.get(area);
        if (pool == null) {
            pool = countryMap.get(area);
        }
        if (pool == null) {
            return new ArrayList<>();
        }
        return new ArrayList<>(pool.getList());
    }

    public List<HostPoolData> getCountryList(String countryCode){
        if(StringUtils.isEmpty(countryCode)){
            return new ArrayList<>();
        }
        countryCode = countryCode.toUpperCase();
        SimplePool<HostPoolData> simplePool = countryMap.get(countryCode);
        if (simplePool == null) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(simplePool.getList())) {
            return new ArrayList<>();
        }
        return new ArrayList<>(simplePool.getList());
    }

    public List<HostPoolData> getAreaList(String countryCode){
        if (StringUtils.isEmpty(countryCode)) {
            return new ArrayList<>();
        }
        String userCountryIndex = CountryIndexUtils.getUserCountryIndex(countryCode);
        if (StringUtils.isEmpty(userCountryIndex)) {
            return new ArrayList<>();
        }
        SimplePool<HostPoolData> simplePool = newAreaMap.get(userCountryIndex);
        if (simplePool == null) {
            logger.info("get new positive area host simplePool is null. countryCode={}",countryCode);
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(simplePool.getList())) {
            logger.info("get new positive area host simplePool list is null. countryCode={}",countryCode);
            return new ArrayList<>();
        }
        return new ArrayList<>(simplePool.getList());
    }
}
