package com.quhong.cache;

import com.quhong.common.utils.CdnUtils;
import com.quhong.core.cache.CacheMap;
import com.quhong.core.room.redis.RoomActorRedis;
import com.quhong.dao.datas.ActorData;
import com.quhong.data.vo.model.rank.row.ActorInfo;
import com.quhong.moderatoin.ModerationService;
import com.quhong.players.ActorMgr;
import com.quhong.utils.RoomUtils;
import com.quhong.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * <AUTHOR>
 * @since 2024/1/29 16:57
 */
@Slf4j
@Component
public class ActorInfoCache {
    private final CacheMap<String, ActorInfo> actorInfoCache;

    private final CacheMap<String, String> roomIdMap;
    @Resource
    private ActorMgr actorMgr;
    @Resource
    private ModerationService moderationService;
    @Resource
    private CdnUtils cdnUtils;

    @Resource
    private RoomActorRedis roomActorRedis;


    public ActorInfoCache() {
        this.roomIdMap = new CacheMap<>(Duration.ofSeconds(15).toMillis());
        this.actorInfoCache = new CacheMap<>(Duration.ofMinutes(1).toMillis());
    }

    public ActorInfo queryActorInfo(String uid, String channel) {
        ActorInfo data = actorInfoCache.getData(uid);
        if (data == null) {
            data = initActorInfo(uid);
        }
        return fillActorInfo(uid, channel, data);
    }


    public ActorInfo queryActorInfo(ActorData currActor) {
        ActorInfo data = actorInfoCache.getData(currActor.getUid());
        if (data == null) {
            data = initActorInfo(currActor);
        }

        return fillActorInfo(currActor.getUid(), currActor.getChannel(), data);
    }

    private ActorInfo fillActorInfo(String uid, String channel, ActorInfo data) {
        ActorInfo actorInfo = data.copy();
        if (!StringUtils.isEmpty(actorInfo.getUid())) {
            actorInfo.setRoomId(doGetRoomId(uid));
            if (StringUtils.hasLength(actorInfo.getRoomId())) {
                data.setRoomType(RoomUtils.getRoomType(actorInfo.getRoomId()));
            }
        }
        actorInfo.setHead(cdnUtils.replaceUrlDomain(channel, actorInfo.getHead(),0));
        return actorInfo;
    }

    private String doGetRoomId(String aid) {
        if (roomIdMap.hasData(aid)) {
            return roomIdMap.getData(aid);
        }
        String roomId = roomActorRedis.getActorRoomStatus(aid);
        roomIdMap.cacheData(aid, roomId);
        return roomId;
    }

    private ActorInfo initActorInfo(String uid) {
        ActorData currActor = actorMgr.getActorData(uid);
        if (currActor == null) {
            actorInfoCache.cacheData(uid, new ActorInfo());
            return new ActorInfo();
        }
        return initActorInfo(currActor);
    }

    private ActorInfo initActorInfo(ActorData currActor) {
        ActorInfo actorInfo = new ActorInfo();
        actorInfo.setUid(currActor.getUid());
        actorInfo.setRid(currActor.getRid());
        actorInfo.setName(currActor.getName());
        actorInfo.setHead(moderationService.dealRankHeadModeration(currActor));
        return actorInfo;
    }
}
