package com.quhong.controller.host;

import com.quhong.common.controllers.KissuJsonController;
import com.quhong.common.data.ApiResult;
import com.quhong.data.dto.host.HostIncomeDetailDTO;
import com.quhong.dao.dto.GameCoinDetailDTO;
import com.quhong.dao.dto.HostSalaryDTO;
import com.quhong.dao.dto.HostSalaryHistoryDTO;
import com.quhong.dao.dto.HostStatisticsDTO;
import com.quhong.dao.vo.GameCoinDetailVO;
import com.quhong.dao.vo.HostSalaryHistoryVO;
import com.quhong.dao.vo.HostSalaryVO;
import com.quhong.dao.vo.HostStatisticsVO;
import com.quhong.data.vo.host.IncomeTimeDetailVO;
import com.quhong.service.host.HostIncomeDetailService;
import com.quhong.service.host.HostStatisticsDetailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName HostStatisticsController
 * <AUTHOR>
 * @date 2022/3/29 14:47
 */
@RestController
@RequestMapping("${server.baseurl}h5/host")
public class HostStatisticsController extends KissuJsonController {
    private static final Logger logger = LoggerFactory.getLogger(HostStatisticsController.class);

    @Autowired
    private HostStatisticsDetailService hostStatisticsDetailService;
    @Autowired
    private HostIncomeDetailService hostIncomeDetailService;

    @RequestMapping("/statistics")
    public String statistics(HostStatisticsDTO dto) {
        return doGetStatistics(dto);
    }

    private String doGetStatistics(HostStatisticsDTO dto) {
//        HostStatisticsDTO dto = createDTO(request, body, HostStatisticsDTO.class, true);
        logger.info("pre get host statistics controller. dto={}", dto);
        ApiResult<HostStatisticsVO> result = hostStatisticsDetailService.getStatisticDetail(dto);
        return createResult(null, result.getHttpCode(), result.getData());
    }

    @RequestMapping("/salary")
    private String doGetSalary(HostSalaryDTO dto) {
        logger.info("pre get host salary controller. dto={}", dto);
        HostSalaryVO hostSalaryVO = hostStatisticsDetailService.getHostSalaryVO(dto);
        return createSuccess(dto, hostSalaryVO);
    }

    @RequestMapping("/salary_history")
    private String doGetSalaryHistory(HostSalaryHistoryDTO dto) {
        logger.info("pre get host salary history controller. dto={}", dto);
        HostSalaryHistoryVO hostSalaryHistoryVO = hostStatisticsDetailService.getHostSalaryHistoryVO(dto);
        return createSuccess(dto, hostSalaryHistoryVO);
    }

    @PostMapping("income/detail")
    private String incomeDetail(HostIncomeDetailDTO dto) {
        logger.info("pre get host income time controller. dto={}", dto);
        IncomeTimeDetailVO incomeTimeDetail = hostIncomeDetailService.getIncomeTimeDetail(dto);
        logger.info("get host income time controller.dto={} vo={}", dto, incomeTimeDetail);
        return createSuccess(dto, incomeTimeDetail);
    }

    @PostMapping("game_coin/detail")
    private String gameCoinDetail(HttpServletRequest request, @RequestBody GameCoinDetailDTO dto) {
        dto.copyFrom(request);
        logger.info("pre get game coin detail controller. dto={}", dto);
        GameCoinDetailVO gameCoinDetailVO = hostStatisticsDetailService.getGameCoinDetail(dto);
        return createSuccess(dto, gameCoinDetailVO);
    }
}
