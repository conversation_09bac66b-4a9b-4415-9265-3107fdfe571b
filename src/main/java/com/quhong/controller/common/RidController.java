package com.quhong.controller.common;

import com.quhong.common.data.HttpRes;
import com.quhong.dao.datas.ActorData;
import com.quhong.players.ActorMgr;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 通用
 * <AUTHOR>
 * @since 2024/7/11 16:10
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("${server.baseurl}/user")
public class RidController {

    private final ActorMgr actorMgr;

    @GetMapping("/get_rid")
    public HttpRes<Long> getRid(String uid) {
        ActorData currActor = actorMgr.getCurrActorData(uid);
        return HttpRes.createSuccessRes(currActor.getRid());
    }
}
