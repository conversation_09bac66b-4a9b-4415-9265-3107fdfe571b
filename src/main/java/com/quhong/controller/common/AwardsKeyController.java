package com.quhong.controller.common;

import com.quhong.common.data.HttpRes;
import com.quhong.common.enums.HttpCode;
import com.quhong.dao.AwardsKeyConfigDao;
import com.quhong.dao.datas.db.AwardsKeyConfigData;
import com.quhong.data.dto.common.BatchQueryAwardsDTO;
import com.quhong.exceptions.WebException;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公共接口/礼包信息查询
 * <AUTHOR>
 * @since 2025-06-06 14:43
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/activity/awards_key")
public class AwardsKeyController {

    private final AwardsKeyConfigDao awardsKeyConfigDao;

    /**
     * 礼包查询
     * @param awardsKey 礼包资源key
     */
    @GetMapping("/query")
    public HttpRes<AwardsKeyConfigData> query(String awardsKey) {
        if (StringUtils.isEmpty(awardsKey)) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, true, "AwardsKey is empty"));
        }
        AwardsKeyConfigData awards = awardsKeyConfigDao.queryByAwardsKey(awardsKey);
        if (awards.getValid() != 1) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, true, "Awards is not exist"));
        }
        return HttpRes.success(awards);
    }

    /**
     * 批量查询礼包
     * @param dto 礼包资源key
     */
    @PostMapping("/batch_query")
    public HttpRes<List<AwardsKeyConfigData>> batchQuery(@RequestBody BatchQueryAwardsDTO dto) {
        if (ObjectUtils.isEmpty(dto.getAwardsKeys())) {
            return HttpRes.success(Collections.emptyList());
        }

        List<AwardsKeyConfigData> list = dto.getAwardsKeys().parallelStream()
                .map(awardsKeyConfigDao::queryByAwardsKey)
                .filter(awards -> awards.getValid() == 1)
                .collect(Collectors.toList());
        return HttpRes.success(list);
    }

}
