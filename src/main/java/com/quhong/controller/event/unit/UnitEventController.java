package com.quhong.controller.event.unit;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpRes;
import com.quhong.data.dto.event.unit.BaseDTO;
import com.quhong.data.dto.event.unit.RankDTO;
import com.quhong.data.vo.event.unit.UnitPageInitVO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.service.event.unit.UnitEventAwardPoolService;
import com.quhong.service.event.unit.UnitEventPageInitService;
import com.quhong.service.event.unit.UnitEventRankService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 组件化活动
 *
 * <AUTHOR>
 * @since 2025-06-04 15:14
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/activity/unit")
public class UnitEventController {
    private final UnitEventPageInitService unitEventPageInitService;
    private final UnitEventRankService unitEventRankService;
    private final UnitEventAwardPoolService unitEventAwardPoolService;

    /**
     * 通用页面初始化
     *
     * @param dto 请求体
     * @return 页面初始化数据
     */
    @GetMapping("/page_init")
    public HttpRes<UnitPageInitVO> pageInit(BaseDTO dto) {
        log.info("page_init, dto={}", JSON.toJSONString(dto));
        dto.checkParams();
        UnitPageInitVO vo = unitEventPageInitService.pageInit(dto);
        log.info("page_init, vo={}", JSON.toJSONString(vo));
        return HttpRes.success(vo);
    }


    /**
     * 通用榜单查询
     * @param dto 请求体
     * @return 榜单数据
     */
    @GetMapping("/rank")
    public HttpRes<ModelRankVO<RankRowVO>> rank(RankDTO dto) {
        log.info("rank, dto={}", JSON.toJSONString(dto));
        dto.checkParams();
        ModelRankVO<RankRowVO> vo = unitEventRankService.rank(dto);
        log.info("rank, vo={}", JSON.toJSONString(vo));
        return HttpRes.success(vo);
    }

    /**
     * 通用奖池抽奖
     */

    /**
     * 通用奖池抽奖记录
     */

    /**
     * 通用奖池抽奖最近30条记录
     */


}
