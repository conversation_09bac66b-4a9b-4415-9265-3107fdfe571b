package com.quhong.controller;

import com.quhong.common.data.HttpEnvData;
import com.quhong.controllers.KissuController;
import com.quhong.data.dto.recharge.CancelReasonReportDTO;
import com.quhong.data.dto.recharge.RechargeReportConfigDTO;
import com.quhong.data.vo.recharge.PayReportConfigVO;
import com.quhong.service.PayReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 支付报表控制器
 *
 * 主要功能：
 * 1. 提供充值取消原因配置查询接口
 * 2. 接收用户充值取消原因数据上报
 * 3. 支持多种支付渠道的数据收集
 * 4. 集成第三方数据分析平台
 *
 * 业务场景：
 * - 用户在充值过程中取消支付时，记录取消原因
 * - 为产品优化提供数据支撑
 * - 帮助分析不同支付渠道的转化率
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("${server.baseurl}report")
public class PayReportController extends KissuController {
    private static final Logger logger = LoggerFactory.getLogger(PayReportController.class);

    /** 支付报表服务，处理配置查询和数据上报的核心业务逻辑 */
    @Autowired
    private PayReportService payReportService;

    /**
     * 获取充值取消原因配置接口
     *
     * 功能说明：
     * - 根据支付渠道获取对应的取消原因配置列表
     * - 为客户端提供可选的取消原因选项
     * - 支持不同支付渠道的个性化配置
     *
     * 请求参数：
     * - payChannel: 支付渠道类型（如：0-谷歌支付，1-苹果支付等）
     *
     * 返回数据：
     * - reason: 取消原因列表，包含原因ID和描述
     * - payChannel: 对应的支付渠道
     *
     * @param request HTTP请求对象，包含用户环境信息
     * @param body 请求体，包含支付渠道等参数（支持Protobuf和JSON格式）
     * @return JSON格式的响应结果，包含取消原因配置列表
     */
    @RequestMapping("config")
    public String getConfig(HttpServletRequest request, @RequestBody(required = false) byte[] body) {
        RechargeReportConfigDTO dto = createDTO(request, body, RechargeReportConfigDTO.class);
        PayReportConfigVO vo = payReportService.getConfig(dto);
        return createSuccess(dto, vo);
    }

    /**
     * 充值取消原因数据上报接口
     *
     * 功能说明：
     * - 接收用户充值取消的详细原因数据
     * - 记录到本地数据库用于后续分析
     * - 同步数据到第三方分析平台
     * - 支持监控告警机制
     *
     * 请求参数：
     * - phase: 取消阶段（1-弹窗，2-第三方购买中，3-向服务器发送请求中，4-服务器请求结束）
     * - reasonId: 取消原因ID
     * - reasonDesc: 原因描述
     * - orderId: 订单号
     * - payChannel: 支付渠道
     *
     * 业务流程：
     * 1. 验证用户是否存在
     * 2. 保存数据到本地数据库
     * 3. 异步同步到第三方数据库
     * 4. 如果同步失败，发送监控告警
     *
     * @param request HTTP请求对象，包含用户环境信息
     * @param body 请求体，包含取消原因详细信息（支持Protobuf和JSON格式）
     * @return JSON格式的响应结果，表示上报是否成功
     */
    @RequestMapping("upload")
    public String upload(HttpServletRequest request, @RequestBody(required = false) byte[] body) {
        CancelReasonReportDTO dto = createDTO(request, body, CancelReasonReportDTO.class);
        payReportService.upload(dto);
        return createSuccess(dto, null);
    }
}
