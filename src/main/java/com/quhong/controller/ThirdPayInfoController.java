package com.quhong.controller;

import com.quhong.common.controllers.BaseController;
import com.quhong.common.data.BaseHttpData;
import com.quhong.common.data.HttpEnvData;
import com.quhong.controllers.KissuController;
import com.quhong.dao.dto.ThirdPayInfoDTO;
import com.quhong.dao.dto.ThirdPayVerificationDTO;
import com.quhong.dao.dto.statement.UserStatementDTO;
import com.quhong.dao.vo.ThirdPayInfoVO;
import com.quhong.dao.vo.statement.UserStatementConfigVO;
import com.quhong.dao.vo.statement.UserStatementVO;
import com.quhong.service.StatementService;
import com.quhong.service.ThirdPayInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("${server.baseurl}third_pay")
@Slf4j
public class ThirdPayInfoController extends BaseController<BaseHttpData> {
    @Resource
    private ThirdPayInfoService thirdPayInfoService;

    @RequestMapping("info")
    public String queryThirdPayInfo(HttpServletRequest request, @RequestBody ThirdPayInfoDTO dto) {
        log.info("third pay info. dto={}", dto);
        ThirdPayInfoVO vo = thirdPayInfoService.queryInfo(dto);
        return createSuccess(dto, vo);
    }

    @RequestMapping("send_verification_code")
    public String sendVerificationCode(HttpServletRequest request, @RequestBody ThirdPayVerificationDTO dto) {
        log.info("send verification code. dto={}", dto);
        thirdPayInfoService.sendVerificationCode(dto);
        return createSuccess(dto, null);
    }
}
