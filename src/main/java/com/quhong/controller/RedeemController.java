package com.quhong.controller;

import com.quhong.common.controllers.KissuJsonController;
import com.quhong.common.data.ApiResult;
import com.quhong.common.data.HttpEnvData;
import com.quhong.dao.dto.RedeemGoldDTO;
import com.quhong.dao.vo.RedeemGoldListVO;
import com.quhong.dao.vo.GameCoinRedeemListVO;
import com.quhong.dao.dto.GameCoinRedeemDTO;
import com.quhong.service.RedeemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName RedeemController
 * <AUTHOR>
 * @date 2022/12/30 16:21
 */
@Slf4j
@RestController
@RequestMapping("${server.baseurl}h5/redeem")
public class RedeemController extends KissuJsonController {

    @Autowired
    private RedeemService redeemService;

    @RequestMapping("/redeem_list")
    public String redeemList(HttpServletRequest request) {
        HttpEnvData envData = createEnvData(request);
        log.info("pre redeem list. envData={}", envData);
        ApiResult<RedeemGoldListVO> result = redeemService.redeemList(envData);
        return createResult(envData, result.getHttpCode(), result.getData());
    }

    @RequestMapping("/redeem_gold")
    public String redeemGold(RedeemGoldDTO dto) {
        log.info("pre redeem. dto={}", dto);
        ApiResult<String> result = redeemService.redeemGold(dto);
        return createResult(dto, result.getHttpCode(), null);
    }

    @RequestMapping("/game_coin_redeem_list")
    public String gamePointsRedeemList(HttpServletRequest request) {
        HttpEnvData envData = createEnvData(request);
        log.info("pre game points redeem list. envData={}", envData);
        GameCoinRedeemListVO result = redeemService.gameCoinRedeemList(envData);
        return createSuccess(result);
    }

    @PostMapping("/game_coin_redeem")
    public String gamePointsRedeem(HttpServletRequest request, @RequestBody GameCoinRedeemDTO dto) {
        log.info("pre game points redeem. dto={}", dto);
        dto.copyFrom(request);
        redeemService.gameCoinRedeem(dto);
        return createSuccess();
    }
}
