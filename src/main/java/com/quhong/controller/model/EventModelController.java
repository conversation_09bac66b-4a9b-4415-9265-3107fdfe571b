package com.quhong.controller.model;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpRes;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.vo.activity.model.PageInitVO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.service.activity.model.EventModelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 新活动模版接口
 *
 * <AUTHOR>
 * @since 2024/1/29 14:46
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/activity/model/v2")
public class EventModelController {
    private final EventModelService eventModelService;

    /**
     * 新活动模版页面初始化
     *
     * @param dto 请求体
     * @return 页面初始化数据
     */
    @GetMapping("/init")
    public HttpRes<PageInitVO> pageInit(CommonDTO dto) {
        log.info("pre pageInit:dto={}", JSON.toJSONString(dto));
        PageInitVO vo = eventModelService.initPage(dto);
        log.info("end pageInit:vo={}", JSON.toJSONString(vo));
        return HttpRes.createSuccessRes(vo);
    }

    /**
     * 新活动模版榜单数据
     *
     * @param dto 请求体
     * @return 榜单数据
     */
    @GetMapping("/ranking")
    public HttpRes<ModelRankVO<RankRowVO>> ranking(CommonDTO dto) {
        log.info("pre ranking:dto={}", JSON.toJSONString(dto));
        ModelRankVO<RankRowVO> vo = eventModelService.ranking(dto);
        log.info("end ranking,vo={}", JSON.toJSONString(vo));
        return HttpRes.createSuccessRes(vo);
    }
}
