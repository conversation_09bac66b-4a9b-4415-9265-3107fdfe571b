package com.quhong.msg;

import com.quhong.constant.MatchGuaranteeTypeConstant;
import com.quhong.core.datas.ChatCmdData;
import com.quhong.dao.datas.ActorData;
import com.quhong.socket.protobuf.MessageProtobuf;
import com.quhong.core.annotation.Message;
import com.quhong.socket.msg.ServerMsg;
import com.quhong.core.enums.AppMsgType;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Message(cmd = AppMsgType.CHAT_REQUEST)
public class ChatRequest extends ServerMsg {
    private int coin;
    private int price;
    private String cid = "";
    private String aid = "";
    private String name = "";
    private int channel;
    private String head = "";
    private int rid;
    private int callType;
    private int timestamp;
    private int fromType; //0 用户主动拨打1 主播打给用户
    private int recordId; //记录id，不发送给客户端
    private boolean fakeCall;
    private int svipFreeTime;
    private int callingCardTime;
    private int autoDeduction;
    private String videoUrl = "";
    private int needProbationReport;
    private String streamAppId; // 流的appId
    private String streamToken; // 流token
    private String realCoin;
    private int premium; // 优质

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        MessageProtobuf.ChatRequest msg = MessageProtobuf.ChatRequest.parseFrom(bytes);
        this.coin = msg.getCoin();
        this.price = msg.getPrice();
        this.cid = msg.getCid();
        this.aid = msg.getAid();
        this.name = msg.getName();
        this.channel = msg.getChannel();
        this.head = msg.getHead();
        this.rid = msg.getRid();
        this.callType = msg.getCallType();
        this.timestamp = msg.getTimestamp();
        this.fromType = msg.getFromType();
        this.fakeCall = msg.getFakeCall();
        this.svipFreeTime = msg.getSvipFreeTime();
        this.callingCardTime = msg.getCallingCardTime();
        this.autoDeduction = msg.getAutoDeduction();
        this.videoUrl = msg.getVideoUrl();
        this.needProbationReport = msg.getNeedProbationReport();
        this.streamAppId = msg.getStreamAppId();
        this.streamToken = msg.getStreamToken();
        this.realCoin = msg.getRealCoin();
        this.premium = msg.getPremium();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        MessageProtobuf.ChatRequest.Builder builder = MessageProtobuf.ChatRequest.newBuilder();
        builder.setCoin(coin);
        builder.setPrice(price);
        builder.setCid(cid == null ? "" : cid);
        builder.setAid(aid == null ? "" : aid);
        builder.setName(name == null ? "" : name);
        builder.setChannel(channel);
        builder.setHead(head == null ? "" : head);
        builder.setRid(rid);
        builder.setCallType(callType);
        builder.setTimestamp(timestamp);
        builder.setFromType(fromType);
        builder.setFakeCall(fakeCall);
        builder.setSvipFreeTime(svipFreeTime);
        builder.setCallingCardTime(callingCardTime);
        builder.setAutoDeduction(autoDeduction);
        builder.setVideoUrl(videoUrl == null ? "" : videoUrl);
        builder.setNeedProbationReport(needProbationReport);
        builder.setStreamAppId(streamAppId == null ? "" : streamAppId);
        builder.setStreamToken(streamToken == null ? "" : streamToken);
        builder.setRealCoin(realCoin == null ? "0" : realCoin);
        builder.setPremium(premium);
        return builder.build().toByteArray();
    }

    public void copyFrom(ChatCmdData data, ActorData fromActor, ActorData toActor, BigDecimal receiveActorBalance){
        this.coin = receiveActorBalance.intValue();
        this.realCoin = receiveActorBalance.toString();
        this.price = data.getPrice();
        this.cid = data.getCid();
        this.name = fromActor.getName();
        this.channel = data.getCallChannel();
        this.head = fromActor.getHeadIcon();
        this.rid = fromActor.getRid().intValue();
        this.aid = fromActor.getUid();
        this.callType = data.getCallType();
        this.timestamp = (int)(data.getOpenTime() / 1000);
        this.fromType = data.getFromType();
        this.recordId = data.getRecordId();
        this.fakeCall = false;
        this.svipFreeTime = 0;
        this.autoDeduction = 0;
        this.needProbationReport = MatchGuaranteeTypeConstant.FilterMatchList.contains(data.getGuaranteeType()) ? 1 : 0;
        this.streamAppId = data.getStreamAppId();
        this.streamToken = data.getUser(toActor.getUid()).getStreamToken();
    }
}
