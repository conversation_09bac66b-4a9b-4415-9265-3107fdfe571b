package com.quhong.gate.msg;

import com.quhong.core.enums.AppMsgType;
import com.quhong.socket.msg.AppMsg;
import com.quhong.socket.msg.AppMsgHeader;
import com.quhong.socket.protobuf.MessageProtobuf;

/**
 * 该消息gate层不加入解析，防止出现别的问题
 */
public class ServerRecvAck extends AppMsg {
    private int msgId;
    private int msgType;

    protected void initHeader(){
        this.header = new AppMsgHeader();
        this.header.setMsgType(AppMsgType.SERVER_RECV_ACK);
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        MessageProtobuf.ServerRecvAck msg = MessageProtobuf.ServerRecvAck.parseFrom(bytes);
        this.msgId = msg.getMsgId();
        this.msgType = msg.getMsgType();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        MessageProtobuf.ServerRecvAck.Builder builder = MessageProtobuf.ServerRecvAck.newBuilder();
        builder.setMsgId(msgId);
        builder.setMsgType(msgType);
        return builder.build().toByteArray();
    }

    public int getClientMsgId() {
        return msgId;
    }

    public void setClientMsgId(int msgId) {
        this.msgId = msgId;
    }

    public int getMsgType() {
        return msgType;
    }

    public void setMsgType(int msgType) {
        this.msgType = msgType;
    }
}
