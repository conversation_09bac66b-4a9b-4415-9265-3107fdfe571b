package com.quhong.dao;

import com.quhong.core.utils.DateHelper;
import com.quhong.dao.data.RoomRedEnvelopeReceiveRecordData;
import com.quhong.dao.data.RoomRedEnvelopeSendRecordData;
import com.quhong.dao.mapper.log.RoomRedEnvelopeReceiveRecordMapper;
import com.quhong.dao.mapper.log.RoomRedEnvelopeSendRecordMapper;
import com.quhong.data.utils.SubTableSupport;
import org.springframework.stereotype.Component;

import javax.persistence.Table;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
public class RoomRedEnvelopeReceiveRecordDao extends MonthShardingDao<RoomRedEnvelopeReceiveRecordMapper> {

    public RoomRedEnvelopeReceiveRecordDao() {
        super(RoomRedEnvelopeReceiveRecordData.class.getAnnotation(Table.class).name());
    }

    public void insert(RoomRedEnvelopeReceiveRecordData data) {
        checkAndCreateTable(data.getDynamicTableName());
        tableMapper.insertSelective(data);
    }

    private String genTableName(String orderId) {
        String tableName = tablePre;
        tableName = tableName + "_" + SubTableSupport.getTableSuffixByObjectId(orderId);
        checkAndCreateTable(tableName);
        return tableName;
    }

    /**
     * 获取最近30天的记录表
     * @param uid
     * @param start
     * @param size
     * @return
     */
    public List<RoomRedEnvelopeReceiveRecordData> getListByRoomIdAndUid(String uid, Integer start, Integer size) {
        long endTime = DateHelper.getCurrentTime();
        long startTime = endTime - TimeUnit.DAYS.toSeconds(30);
        List<String> suffixes = getSuffixes(startTime, endTime);
        if(suffixes.isEmpty()){
            return new ArrayList<>();
        }
        return tableMapper.selectListByRoomIdAndUid(suffixes, uid, startTime, endTime, start, size);
    }
}
