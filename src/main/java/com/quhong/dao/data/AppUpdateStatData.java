package com.quhong.dao.data;

import com.quhong.dao.AppUpdateStatLogDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = AppUpdateStatLogDao.BASE_TABLE_NAME)
public class AppUpdateStatData {
    @Id
    private ObjectId _id;
    @Indexed
    private String uid;
    private String channel;
    private int pay_type; // 支付渠道 -1 无 0 全部
    private int login_count; // 登录次数

    private String send_msg_uid; //发送消息的主播id

    private int sendCount; // 通知次数

    private int ctime;
    private int mtime;

    public AppUpdateStatData(){

    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public int getPay_type() {
        return pay_type;
    }

    public void setPay_type(int pay_type) {
        this.pay_type = pay_type;
    }

    public String getSend_msg_uid() {
        return send_msg_uid;
    }

    public void setSend_msg_uid(String send_msg_uid) {
        this.send_msg_uid = send_msg_uid;
    }


    public int getLogin_count() {
        return login_count;
    }

    public void setLogin_count(int login_count) {
        this.login_count = login_count;
    }

    public int getSendCount() {
        return sendCount;
    }

    public void setSendCount(int sendCount) {
        this.sendCount = sendCount;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getMtime() {
        return mtime;
    }

    public void setMtime(int mtime) {
        this.mtime = mtime;
    }
}
