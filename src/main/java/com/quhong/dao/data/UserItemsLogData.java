package com.quhong.dao.data;

import com.quhong.dao.datas.AbstractShardingData;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Id;
import javax.persistence.Table;

@Table(name = "user_items_log")
@Getter
@Setter
public class UserItemsLogData extends AbstractShardingData {
    /**
     * 自增id
     */
    @Id
    private Integer id;

    /**
     * 用户的物品id
     */
    private String itemId;

    /**
     * 用户uid
     */
    private String uid;

    /**
     * 物品的配置id
     */
    private Integer configId;

    /**
     * 物品的类型
     */
    private Integer type;

    /**
     * 物品的类别
     */
    private Integer category;

    /**
     * 动作 1增加 2减少
     */
    private Integer action;

    /**
     * 本次变化的数量
     */
    private Integer changed;

    /**
     * 本次变化前的数量
     */
    private Integer beforeNums;

    /**
     * 本次变化后的数量
     */
    private Integer afterNums;

    /**
     * 场景id
     */
    private Integer sceneId;

    /**
     * 关联id
     */
    private String relatedId;

    /**
     * 对方的uid
     */
    private String toUid;

    /**
     * 描述
     */
    private String itemsDesc;

    /**
     * uid的channel
     */
    private String channel;

    /**
     * uid的country_code
     */
    private String countryCode;

    /**
     * uid的语言
     */
    private String langId;

    /**
     * 创建记录的时间
     */
    private Long ctime;

    /**
     * 更新记录的时间
     */
    private Long mtime;

}

