package com.quhong.dao.dto;


import com.quhong.common.data.ProtoDTO;
import com.quhong.common.utils.RequestUtils;
import com.quhong.core.utils.SpringUtils;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.vo.profile.UserOtherVO;
import com.quhong.data.bo.ActorConfigCollatedBO;
import com.quhong.data.bo.UserOtherParamsBO;
import com.quhong.server.protobuf.UserInfoMessageProtobuf;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.servlet.http.HttpServletRequest;

@Getter
@Setter
@ToString(callSuper = true)
public class UserOtherDTO extends ProtoDTO {

    //  目标用户uid
    private String aid;
    private int position;


    /************************以上为请求参数**********************/


    private UserOtherVO userOtherVO;

    private ActorData aidActorData;

    private ActorConfigCollatedBO aidActorConfigData;

    private ActorData uidActorData;

    private UserOtherParamsBO userOtherParamsBO;


    public void fillOtherParam(HttpServletRequest request) {
        this.aid = RequestUtils.getParam(request, "otherId");
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        UserInfoMessageProtobuf.UserOtherReq msg = UserInfoMessageProtobuf.UserOtherReq.parseFrom(bytes);
        SpringUtils.copyPropertiesIgnoreNull(msg, this);
    }

    @Override
    protected byte[] doToBody() throws Exception {
        return new byte[0];
    }
}
