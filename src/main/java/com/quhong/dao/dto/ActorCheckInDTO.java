package com.quhong.dao.dto;

import com.quhong.common.data.ProtoDTO;
import com.quhong.server.protobuf.UserInfoMessageProtobuf;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Description: 签到操作请求参数集
 *
 * <AUTHOR>
 * @date 2021/10/27 13:56
 */
@Setter
@Getter
@ToString(callSuper = true)
public class ActorCheckInDTO extends ProtoDTO {
    /**
     * 连续签到天数
     */
    private Integer days;
    /**
     * 时区
     */
    private String timeZone;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        UserInfoMessageProtobuf.ActorCheckInReq msg = UserInfoMessageProtobuf.ActorCheckInReq.parseFrom(bytes);
        this.days = msg.getDays();
        this.timeZone = msg.getTimeZone();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        UserInfoMessageProtobuf.ActorCheckInReq.Builder builder = UserInfoMessageProtobuf.ActorCheckInReq.newBuilder();
        builder.setDays(this.days);
        builder.setTimeZone(this.timeZone);
        return builder.build().toByteArray();
    }
}
