package com.quhong.dao.mapper.log;

import com.quhong.dao.data.RoomRedEnvelopeSendRecordData;
import com.quhong.dao.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Set;

public interface RoomRedEnvelopeSendRecordMapper extends ShardingMapper, Mapper<RoomRedEnvelopeSendRecordData> {
    @Select("select * from ${tableName} where order_id = #{orderId}")
    RoomRedEnvelopeSendRecordData selectDataByOrderId(@Param("orderId") String orderId,
                                                      @Param("tableName") String tableName);

    @Select("<script>" +
            "select * from ${tableName} where order_id in " +
            "<foreach collection='orderIds' open='(' item='orderId' separator=',' close=')'>" +
            "#{orderId}" +
            "</foreach>" +
            "</script>")
    List<RoomRedEnvelopeSendRecordData> selectListByOrderIds(@Param("orderIds") Set<String> orderIds,
                                                             @Param("tableName") String tableName);

    @Select({
            "<script>" +
                    "<foreach collection='suffixes' item='suffix' separator='UNION ALL'>" +
                    "select * from room_red_envelope_send_record_${suffix}" +
                    " where uid = #{uid} and ctime &gt;= #{startTime} and ctime &lt; #{endTime}" +
                    "</foreach>" +
                    " order by ctime desc " +
                    "limit #{start},#{size}" +
                    "</script>"})
    List<RoomRedEnvelopeSendRecordData> selectListByRoomIdAndUid(@Param("suffixes") List<String> suffixes,
                                                                 @Param("uid") String uid,
                                                                 @Param("startTime") Long startTime,
                                                                 @Param("endTime") Long endTime,
                                                                 @Param("start") Integer start,
                                                                 @Param("size") Integer size);

}
