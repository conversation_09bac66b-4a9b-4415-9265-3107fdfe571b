package com.quhong.dao.datas;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @ClassName LogShieldData
 * @Description TODO
 * <AUTHOR>
 * @Date 10:17 AM 2022/9/26
 * @Version 1.0
 **/
@Data
@Table(name = "log_shield")
public class LogShieldData {

    /**
     * id
     */
    @Id
    private Integer id;

    /**
     * 用户uid
     */
    private String uid;

    /**
     * 用户rid
     */
    private Integer rid;

    /**
     * 用户版本号
     */
    private Integer ver;

    /**
     * 用户渠道
     */
    private String channel;

    /**
     * 条件的key
     */
    private String conditionsKey;

    /**
     * 条件的值
     */
    private String conditionsValue;

    /**
     * 时间戳
     */
    private Integer ctime;
}
