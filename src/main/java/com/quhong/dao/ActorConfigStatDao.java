package com.quhong.dao;

import com.quhong.admin.constant.RedisConsts;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.dao.datas.ActorConfigData;
import com.quhong.dao.mapper.db.ActorConfigMapper;
import com.quhong.dao.slave.mapper.db.ActorConfigStatSlaveMapper;
import com.quhong.utils.StringUtils;
import com.quhong.utils.TKUtils;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Component
public class ActorConfigStatDao extends TestUserBaseDao{

    private static final Logger LOGGER = LoggerFactory.getLogger(ActorConfigStatDao.class);

    @Autowired
    private ActorConfigMapper actorConfigMapper;

    @Autowired
    private ActorConfigStatSlaveMapper actorConfigStatSlaveMapper;

    @Resource(name = BaseRedisBeanConstant.ACTOR_BEAN)
    private StringRedisTemplate actorInfoRedisTemplate;

    /**
     * 获取数据
     * @param uid
     * @param configKey
     * @return
     */
    public ActorConfigData getData(String uid,String configKey){
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(configKey)) {
            return null;
        }
        Example example = TKUtils.creatExample(ActorConfigData.class);
        example.createCriteria().andEqualTo("uid", uid)
                     .andEqualTo("configKey",configKey);
        List<ActorConfigData> actorConfigDataList = actorConfigStatSlaveMapper.selectByExampleAndRowBounds(example, new RowBounds(0, 1));
        if(CollectionUtils.isEmpty(actorConfigDataList)){
            return null;
        }
        return actorConfigDataList.get(0);
    }

    /**
     * 插入一条数据
     * @param actorConfigData
     * @return
     */
    public int saveOne(ActorConfigData actorConfigData){
        int res = actorConfigMapper.insertSelective(actorConfigData);
        if(res < 1){
            LOGGER.error("actor_config insert one error;uid = {} configKey = {} valueStr = {}",actorConfigData.getUid(),actorConfigData.getConfigKey(),actorConfigData.getValueStr());
            return res;
        }
        //删除缓存
        String key = RedisConsts.ACTOR_INFO_KEY_PRE + actorConfigData.getUid();
        String cache = actorInfoRedisTemplate.opsForValue().get(key);
        if(cache != null){
            actorInfoRedisTemplate.delete(key);
        }
        return res;
    }

    /**
     * 更新数据
     * @param actorConfigData
     * @param uid
     * @param configKey
     * @return
     */
    public int updateOne(ActorConfigData actorConfigData,String uid,String configKey){
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(configKey)) {
            return 0;
        }
        Example example = TKUtils.creatExample(ActorConfigData.class);
        example.createCriteria().andEqualTo("uid", uid)
                .andEqualTo("configKey",configKey);
        int res = actorConfigMapper.updateByExampleSelective(actorConfigData, example);
        if(res < 1){
            LOGGER.error("actor_config update one error;uid = {} configKey = {}",uid,configKey);
            return res;
        }
        //删除缓存
        String key = RedisConsts.ACTOR_INFO_KEY_PRE + uid;
        String cache = actorInfoRedisTemplate.opsForValue().get(key);
        if(cache != null){
            actorInfoRedisTemplate.delete(key);
        }
        return res;
    }

    /**
     * 通过uid和config_key获取数据数组
     * @param uidSet
     * @param configKey
     * @return
     */
    public List<ActorConfigData> listByUidAndKey(Set<String> uidSet,String configKey){
        if (StringUtils.isEmpty(configKey)) {
            return new ArrayList<>();
        }
        Example example = TKUtils.creatExample(ActorConfigData.class);
        Example.Criteria criteria = example.createCriteria().andEqualTo("configKey", configKey);
        if(!CollectionUtils.isEmpty(uidSet)){
            //排除测试账号
            uidSet = getNormalUserSet(uidSet);
            criteria.andIn("uid",uidSet);
        }else {
            return new ArrayList<>();
        }
//        else{
//            //排除测试账号
//            tkMybatisExcludeTest(criteria,"uid");
//        }
        List<ActorConfigData> list = actorConfigStatSlaveMapper.selectByExample(example);
        if(list == null){
            return new ArrayList<>();
        }
        return list;
    }

}
