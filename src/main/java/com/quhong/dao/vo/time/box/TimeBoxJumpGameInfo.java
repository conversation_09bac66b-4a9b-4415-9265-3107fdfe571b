package com.quhong.dao.vo.time.box;

import com.quhong.core.utils.SpringUtils;
import com.quhong.server.protobuf.UserInfoMessageProtobuf;
import com.quhong.socket.msg.IProto;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/12/21 21:04
 */
@Data
public class TimeBoxJumpGameInfo implements IProto<UserInfoMessageProtobuf.TimeBoxJumpGameInfo> {
    /**
     * 游戏登录类型
     * 1: 原生（老虎机, 砸蛋）
     * 2: 三方游戏登录
     * 3: joy游戏大厅登录
     */
    private Integer gameLoginType;
    /**
     * 游戏id
     */
    private Integer gameId;
    /**
     * 游戏图标
     */
    private String icon;
    /**
     * 游戏名
     */
    private String name;

    private int gamePointGame;

    /**
     * @param proto
     */
    @Override
    public void doFromBody(UserInfoMessageProtobuf.TimeBoxJumpGameInfo proto) {
        SpringUtils.copyPropertiesIgnoreNull(proto, this);
    }

    /**
     * @return
     */
    @Override
    public UserInfoMessageProtobuf.TimeBoxJumpGameInfo.Builder doToBody() {
        UserInfoMessageProtobuf.TimeBoxJumpGameInfo.Builder builder = UserInfoMessageProtobuf.TimeBoxJumpGameInfo.newBuilder();
        SpringUtils.copyPropertiesIgnoreNull(this, builder);
        return builder;
    }
}
