package com.quhong.dao.vo.time.box;

import com.quhong.core.utils.SpringUtils;
import com.quhong.server.protobuf.UserInfoMessageProtobuf;
import com.quhong.socket.msg.IProto;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/12/21 21:08
 */
@Data
@Accessors(chain = true)
public class ReceiveUserInfo implements IProto<UserInfoMessageProtobuf.ReceiveUserInfo> {
    private String uid;// uid
    private String name;//用户名
    private String head;//头像
    private Integer coinCount;//金币数量
    private String awardIcon;//奖品图标
    private Integer awardType;//奖品类型 1金币 11游戏货币

    /**
     * @param proto
     */
    @Override
    public void doFromBody(UserInfoMessageProtobuf.ReceiveUserInfo proto) {
        SpringUtils.copyPropertiesIgnoreNull(proto, this);
    }

    /**
     * @return
     */
    @Override
    public UserInfoMessageProtobuf.ReceiveUserInfo.Builder doToBody() {
        UserInfoMessageProtobuf.ReceiveUserInfo.Builder builder = UserInfoMessageProtobuf.ReceiveUserInfo.newBuilder();
        SpringUtils.copyPropertiesIgnoreNull(this, builder);
        builder.setAwardType(this.awardType != null ? this.awardType : 1);
        return builder;
    }
}
