package com.quhong.dao.vo.profile;

import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.common.data.ProtoVO;
import com.quhong.core.utils.SpringUtils;
import com.quhong.data.RoomProgramList;
import com.quhong.data.bo.BannerDictBO;
import com.quhong.data.bo.InterestInfoBO;
import com.quhong.data.bo.ParamsDictBO;
import com.quhong.data.bo.UserOtherParamsBO;
import com.quhong.data.room.ResourceInfoObject;
import com.quhong.server.protobuf.UserInfoMessageProtobuf;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName UserMeVo
 * <AUTHOR>
 * @Date 3:52 PM 2023/2/20
 * @Version 1.0
 **/
@NoArgsConstructor
@Data
public class UserOtherVO extends ProtoVO {

    @JSONField(name = "aid")
    private String aid;

    @JSONField(name = "gold")
    private int gold;

    @JSONField(name = "name")
    private String name;

    @JSONField(name = "head")
    private String head;

    @JSONField(name = "rid")
    private int rid;

    @JSONField(name = "gender")
    private int gender;

    @JSONField(name = "country")
    private String country;

    @JSONField(name = "city")
    private String city;

    @JSONField(name = "desc")
    private String desc;

    @JSONField(name = "age")
    private int age;

    @JSONField(name = "birthday")
    private String birthday;

    @JSONField(name = "banner")
    private List<BannerDictBO> banner;

    @JSONField(name = "in_block")
    private int inBlock;

    @JSONField(name = "in_review_status")
    private int inReviewStatus;

    @JSONField(name = "is_browse")
    private int isBrowse;

    @JSONField(name = "accept_talk")
    private int acceptTalk;

    @JSONField(name = "accept_voice")
    private int acceptVoice;

    @JSONField(name = "translate_id")
    private String translateId;
    @Deprecated

    @JSONField(name = "vip_level")
    private int vipLevel;
    @Deprecated

    @JSONField(name = "is_vip")
    private boolean isVip;

    @JSONField(name = "isPremium")
    private boolean isPremium;
    /**
     * 主播月评等级
     */
    @JSONField(name = "host_level")
    private int hostLevel;
    /**
     * 主播等级
     */
    private int hostGrade;
    /**
     * 主播质量等级
     */
    private int hostQualityLevel;

    @JSONField(name = "followed")
    private int followed;

    @JSONField(name = "follower")
    private int follower;

    @JSONField(name = "newFans")
    private int newFans;

    @JSONField(name = "status")
    private int status;

    //    @Deprecated 原video废弃改为countryCode
    @JSONField(name = "countryCode")
    private String countryCode;

    @JSONField(name = "high_light")
    private int highLight;

    @JSONField(name = "isFollowed")
    private int isFollowed;

    @JSONField(name = "isFollowMe")
    private int isFollowMe;

    @JSONField(name = "heat")
    private int heat;

    @Deprecated
    @JSONField(name = "gifts")
    private List<?> gifts;

    @JSONField(name = "is_top")
    private boolean isTop;
    @Deprecated
    @JSONField(name = "is_new")
    private boolean isNew;

    @JSONField(name = "hostNew")
    private int hostNew;

    @Deprecated
    @JSONField(name = "ugwh_id")
    private String ugwhId;

    @JSONField(name = "show_price")
    private int showPrice;

    @JSONField(name = "call_cost")
    private int callCost;

    @Deprecated
    @JSONField(name = "free_call_status")
    private int freeCallStatus;

    @Deprecated
    @JSONField(name = "free_call_url")
    private String freeCallUrl;

    /**
     * @see UserOtherParamsBO
     */
    @JSONField(name = "params_list")
    private List<ParamsDictBO> paramsList;

    private int lordLevel;

    private String lordIcon;

    private int userLevel;

    private int verifyStatus;//认证状态 1通过
    private int officialLabel;//官方标签 0没有 1有
    private int agencyLabel;//公会长标签 0没有 1有
    private int coinSellerLabel;//币商标签 0没有 1有
    private int sex;//性别 1男2女
    private List<InterestInfoBO> userInterestList; //选择的兴趣列表
    private RoomProgramList roomProgramData;//
    private int isOfficialRoom;
    private String partyRoomId;//语聊房房间id
    private String aidChannel;//对方渠道
    private int hadSayHi;//是否打过招呼

    /**
     * 麦位框
     */
    private ResourceInfoObject micFrame;
    private ResourceInfoObject designation;//称号
    private ResourceInfoObject achievementDesignation;//成就称号
    private int labelType;//主播标签 1 recommend标签  2 hot new 标签  其他没有标签
    private int identityTag;//身份标签 同列表的s c t标签  1:s  2:c  3:t
    private String realGold;
    private int goldNickname;//金色昵称
    private String newLordIcon;//新贵族图标
    private int callButtonSwitch;   // 该用户是否可以拨打电话给主播的拨打按钮开关

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {

    }

    @Override
    protected byte[] doToBody() throws Exception {
        UserInfoMessageProtobuf.UserOtherResp.Builder builder = UserInfoMessageProtobuf.UserOtherResp.newBuilder();
        SpringUtils.copyPropertiesIgnoreNull(this, builder);
        if (this.banner != null) {
            for (BannerDictBO bannerDictBO : this.banner) {
                builder.addBanner(bannerDictBO.doToBody());
            }
        }
        if (this.paramsList != null) {
            for (ParamsDictBO paramsDictBO : this.paramsList) {
                builder.addParamsList(paramsDictBO.doToBody());
            }
        }
        if (this.userInterestList != null) {
            for (InterestInfoBO interestInfoBO : this.userInterestList) {
                builder.addUserInterestList(interestInfoBO.doToBody());
            }
        }
        if (this.micFrame != null) {
            builder.setMicFrame(this.micFrame.doToBody());
        }
        if (this.designation != null) {
            builder.setDesignation(this.designation.doToBody());
        }
        builder.setIsPremium(this.isPremium);
        if (this.roomProgramData != null) {
            builder.setRoomProgramData(this.roomProgramData.doToBody());
        }
        if (this.achievementDesignation != null) {
            builder.setAchievementDesignation(this.achievementDesignation.doToBody());
        }
        builder.setIsOfficialRoom(this.isOfficialRoom);
        builder.setPartyRoomId(this.partyRoomId != null ? this.partyRoomId : "");
        return builder.build().toByteArray();
    }

}
