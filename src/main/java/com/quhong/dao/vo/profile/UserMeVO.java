package com.quhong.dao.vo.profile;

import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.common.data.ProtoVO;
import com.quhong.core.utils.SpringUtils;
import com.quhong.data.bo.ParamsDictBO;
import com.quhong.data.bo.PremiumInfoBO;
import com.quhong.data.room.ResourceInfoObject;
import com.quhong.server.protobuf.UserInfoMessageProtobuf;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName UserMeVo
 * <AUTHOR>
 * @Date 3:52 PM 2023/2/20
 * @Version 1.0
 **/
@NoArgsConstructor
@Data
public class UserMeVO extends ProtoVO {
    /**
     * aid
     */
    private String aid;
    /**
     * gold
     */
    private Integer gold;
    /**
     * 金币
     */
    private String realGold;
    /**
     * currency1
     */
    private Long currency1;
    /**
     * currency2
     */
    private Long currency2;

    private String realCurrency1;

    private String realCurrency2;
    /**
     * name
     */
    private String name;
    /**
     * head
     */
    private String head;
    /**
     * rid
     */
    private Integer rid;
    /**
     * gender
     */
    private Integer gender;
    /**
     * banner
     */
    private List<String> banner;
    /**
     * country
     */
    private String country;
    /**
     * city
     */
    private String city;
    /**
     * desc
     */
    private String desc;
    /**
     * age
     */
    private Integer age;
    /**
     * acceptTalk
     */
    @JSONField(name = "accept_talk")
    private Integer acceptTalk;
    /**
     * birthday
     */
    private String birthday;
    /**
     * acceptVoice
     */
    @JSONField(name = "accept_voice")
    private Integer acceptVoice;
    /**
     * randomCall
     */
    @JSONField(name = "random_call")
    private Integer randomCall;
    /**
     * translateId
     */
    @JSONField(name = "translate_id")
    private String translateId;
    /**
     * translateName
     */
    @JSONField(name = "translate_name")
    private String translateName;
    /**
     * phone
     */
    private String phone;
    /**
     * isPayUser
     */
    @JSONField(name = "is_pay_user")
    private Integer isPayUser;
    /**
     * msgPaySwitch
     */
    @JSONField(name = "msg_pay_switch")
    private Integer msgPaySwitch;
    /**
     * limitedTime
     */
    @JSONField(name = "limited_time")
    private Integer limitedTime;
    /**
     * useBeauty
     */
    @JSONField(name = "use_beauty")
    private Integer useBeauty;
    /**
     * isOver24
     */
    @Deprecated
    @JSONField(name = "is_over_24")
    private Boolean isOver24;
    /**
     * rechargedTotal
     */
    @Deprecated
    @JSONField(name = "recharged_total")
    private Integer rechargedTotal;
    /**
     * isGclid
     */
    @Deprecated
    @JSONField(name = "is_gclid")
    private Boolean isGclid;
    /**
     * vipLevel
     */
    @Deprecated
    @JSONField(name = "vip_level")
    private Integer vipLevel;
    /**
     * isVip
     */
    @Deprecated
    @JSONField(name = "is_vip")
    private Boolean isVip;
    /**
     * vipLevelInfo
     */
    @Deprecated
    @JSONField(name = "vip_level_info")
    private PremiumInfoBO vipLevelInfo;

    /**
     * premiunInfo
     */
    @JSONField(name = "premiun_info")
    private PremiumInfoBO premiunInfo;
    /**
     * hasSubscribed
     */
    @JSONField(name = "hasSubscribed")
    private Boolean hasSubscribed;
    /**
     * isPremium
     */
    @JSONField(name = "isPremium")
    private Boolean isPremium;
    /**
     * isBoss
     */
    private Integer isBoss;

    /**
     * 关注的人
     */
    private Integer followed;

    /**
     * 粉丝数
     */
    private Integer follower;

    /**
     * 互关人数
     */
    private Integer friend;
    /**
     * newFans
     */
    private Integer newFans;
    /**
     * isTop
     */
    @Deprecated
    @JSONField(name = "is_top")
    private Boolean isTop;
    /**
     * isNew
     */
    @Deprecated
    @JSONField(name = "is_new")
    private Boolean isNew;
    /**
     * hostNew
     */
    private Integer hostNew;
    /**
     * chatPrice
     */
    @JSONField(name = "chat_price")
    private Integer chatPrice;
    /**
     * status
     */
    private Integer status;
    /**
     * registerTime
     */
    @JSONField(name = "register_time")
    private Integer registerTime;
    /**
     * todayCharge
     */
    @Deprecated
    @JSONField(name = "today_charge")
    private Integer todayCharge;
    /**
     * freeCallTimes
     */
    @Deprecated
    @JSONField(name = "free_call_times")
    private Integer freeCallTimes;
    /**
     * isAutoDeduction
     */
    @Deprecated
    @JSONField(name = "is_auto_deduction")
    private Boolean isAutoDeduction;
    /**
     * phoneBindReward
     */
    @Deprecated
    @JSONField(name = "phone_bind_reward")
    private Integer phoneBindReward;
    /**
     * closeFloatWindow
     */
    @JSONField(name = "close_float_window")
    private Integer closeFloatWindow;
    /**
     * headAuthStatus
     */
    @JSONField(name = "head_auth_status")
    private Integer headAuthStatus;
    /**
     * failHead
     */
    @JSONField(name = "fail_head")
    private String failHead = "";
    /**
     * failHeadDesc 头像审核未通过提示
     */
    @JSONField(name = "fail_head_desc")
    private String failHeadDesc = "";
    /**
     * grayParameter
     */
    @Deprecated
    @JSONField(name = "gray_parameter")
    private Integer grayParameter;
    /**
     * anchorHeadStatus
     */
    @JSONField(name = "anchor_head_status")
    private Integer anchorHeadStatus;
    /**
     * anchorHeight
     */
    @JSONField(name = "anchor_height")
    private Integer anchorHeight;
    /**
     * anchorJob
     */
    @JSONField(name = "anchor_job")
    private String anchorJob;
    /**
     * anchorStyle
     */
    @JSONField(name = "anchor_style")
    private String anchorStyle;
    /**
     * starLevel
     */
    @Deprecated
    @JSONField(name = "star_level")
    private Integer starLevel;
    /**
     * leftFreeMsg
     */
    @Deprecated
    @JSONField(name = "left_free_msg")
    private Integer leftFreeMsg;
    /**
     * vlogStatus
     */
    @JSONField(name = "vlog_status")
    private Integer vlogStatus;
    /**
     * vlogNotify
     */
    @JSONField(name = "vlog_notify")
    private String vlogNotify;
    /**
     * validType
     */
    @JSONField(name = "valid_type")
    private Integer validType;
    /**
     * hasMatchPay
     */
    @JSONField(name = "has_match_pay")
    private Integer hasMatchPay;
    /**
     * hasMatchReward
     */
    @Deprecated
    @JSONField(name = "has_match_reward")
    private Integer hasMatchReward;
    /**
     * hasMatchGuide
     */
    @Deprecated
    @JSONField(name = "has_match_guide")
    private Integer hasMatchGuide;
    /**
     * subscribeHold
     */
    @Deprecated
    @JSONField(name = "subscribe_hold")
    private Integer subscribeHold;
    /**
     * bossPoints
     */
    @JSONField(name = "boss_points")
    private Integer bossPoints;
    /**
     * bossLevel
     */
    @JSONField(name = "boss_level")
    private Integer bossLevel;
    /**
     * 用户等级
     */
    private Integer userLevel;
    /**
     * 贵族等级
     */
    private Integer lordLevel;
    /**
     * 贵族等级勋章
     */
    private String lordIcon;
    /**
     * 用户等级积分
     */
    private Long userPoints;
    /**
     * 主播等级
     */
    private Integer hostGrade;
    /**
     * 主播等级积分
     */
    private Long hostGradePoints;
    /**
     * hadBossWindows
     */
    @JSONField(name = "had_boss_windows")
    private Integer hadBossWindows;

    /**
     * 麦位框
     */
    private ResourceInfoObject micFrame;
    /**
     * paramsList
     */
    @JSONField(name = "params_list")
    private List<ParamsDictBO> paramsList;

    private int verifyStatus;//认证状态 1通过
    private int officialLabel;//官方标签 0没有 1有
    private int agencyLabel;//公会长标签 0没有 1有
    private int coinSellerLabel;//币商标签 0没有 1有
    private int sex;//性别 1男 2女
    private String medalIcon;//勋章链接
    private long channelId;
    private int gameInviteWhiteListConfig;
    private int haveFirstChargeReward;//是否拥有首充礼包资格 0没有 1有
    private int isAgent;//是否为代理 0不是1是
    private int banFunctionWhite;//功能封禁白名单 0不是在白名单中  1在白名单中
    private int feedbackUnread;//反馈未读数
    private int identityTag;//身份标签 同列表的s c t标签  1:s  2:c  3:t
    private String countryCode;//用户国家码
    private int goldNickname;//金色昵称
    private String newLordIcon;//新贵族图标

    /**
     * 直播中匹配的开关
     * 1-开 0-关
     */
    private int liveMatchSwitch;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {

    }

    @Override
    protected byte[] doToBody() throws Exception {
        UserInfoMessageProtobuf.UserMeResp.Builder builder = UserInfoMessageProtobuf.UserMeResp.newBuilder();
        SpringUtils.copyPropertiesIgnoreNull(this, builder);
        banner.forEach(builder::addBanner);
        if (premiunInfo != null) {
            builder.setPremiunInfo(premiunInfo.doToBody());
        }
        if (this.paramsList != null) {
            for (ParamsDictBO paramsDictBO : this.paramsList) {
                builder.addParamsList(paramsDictBO.doToBody());
            }
        }
        return builder.build().toByteArray();
    }
}
