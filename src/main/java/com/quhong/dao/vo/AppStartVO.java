package com.quhong.dao.vo;

import com.quhong.common.data.ProtoVO;
import com.quhong.core.utils.SpringUtils;
import com.quhong.server.protobuf.UserInfoMessageProtobuf;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AppStartVO extends ProtoVO {

    /**
     * 工具马甲包    0=工具包   1=1v1包
     */
    private int linkB;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {

    }

    @Override
    protected byte[] doToBody() throws Exception {
        UserInfoMessageProtobuf.AppStartResp.Builder builder = UserInfoMessageProtobuf.AppStartResp.newBuilder();
        SpringUtils.copyPropertiesIgnoreNull(this, builder);
        return builder.build().toByteArray();
    }
}
