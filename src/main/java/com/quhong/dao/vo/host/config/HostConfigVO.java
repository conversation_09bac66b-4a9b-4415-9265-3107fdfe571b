package com.quhong.dao.vo.host.config;

import com.quhong.common.data.ProtoVO;
import com.quhong.core.utils.SpringUtils;
import com.quhong.server.protobuf.UserInfoMessageProtobuf;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/10/8 17:32
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HostConfigVO extends ProtoVO {

    private String configKey = "";
    private int valType = 1;
    private String configVal = "";
    /**
     * @param bytes
     * @throws Exception
     */
    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
    }

    /**
     * @return
     * @throws Exception
     */
    @Override
    protected byte[] doToBody() throws Exception {
        UserInfoMessageProtobuf.GetHostConfigResp.Builder builder = UserInfoMessageProtobuf.GetHostConfigResp.newBuilder();
        SpringUtils.copyPropertiesIgnoreNull(this, builder);
        return builder.build().toByteArray();
    }
}
