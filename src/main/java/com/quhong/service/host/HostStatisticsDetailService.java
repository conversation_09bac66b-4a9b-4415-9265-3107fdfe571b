package com.quhong.service.host;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.common.data.ApiResult;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.ActType;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.datas.DayTimeData;
import com.quhong.core.enums.CallFromType;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.*;
import com.quhong.dao.data.HostSalaryBO;
import com.quhong.dao.data.HostStatisticsDetailData;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.GameCoinDetailData;
import com.quhong.dao.datas.TitleForMoneyDetailData;
import com.quhong.dao.datas.db.HostSalaryConfigData;
import com.quhong.dao.datas.doris.DorisMoneyDetailData;
import com.quhong.dao.datas.log.HostPopularEffectExposureLogData;
import com.quhong.dao.doris.DorisMoneyDetailDao;
import com.quhong.dao.dto.GameCoinDetailDTO;
import com.quhong.dao.dto.HostSalaryDTO;
import com.quhong.dao.dto.HostSalaryHistoryDTO;
import com.quhong.dao.dto.HostStatisticsDTO;
import com.quhong.dao.slave.mapper.db.ActorSlaveMapper;
import com.quhong.dao.slave.mapper.db.HostConfigSlaveMapper;
import com.quhong.dao.vo.GameCoinDetailVO;
import com.quhong.dao.vo.HostSalaryHistoryVO;
import com.quhong.dao.vo.HostSalaryVO;
import com.quhong.dao.vo.HostStatisticsVO;
import com.quhong.data.ClientCountData;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.data.utils.SubTableSupport;
import com.quhong.data.vo.ConnectRankVO;
import com.quhong.data.vo.LiveTimeVO;
import com.quhong.exceptions.WebException;
import com.quhong.players.ActorMgr;
import com.quhong.redis.HostOnlineTimeRedis;
import com.quhong.service.ConfigApi;
import com.quhong.service.HostSalaryConfigService;
import com.quhong.service.money.MoneyDetailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName HostStatisticsDetailService
 * <AUTHOR>
 * @date 2022/3/30 9:35
 */
@Service
public class HostStatisticsDetailService {
    private static final Logger logger = LoggerFactory.getLogger(HostStatisticsDetailService.class);

    private static final int EXPIRE_TIME = 30;

    private static final int SALARY_HISTORY_START_TIME = 1672531200;

    @Autowired
    private ActorMgr actorMgr;
    @Autowired
    private MoneyDetailDao moneyDetailDao;
    @Autowired
    private HostOnlineTimeLogDao hostOnlineTimeLogDao;
    @Autowired
    private HostOnlineTimeRedis hostOnlineTimeRedis;
    @Autowired
    private RoomChapterDao roomChapterDao;
    @Autowired
    private ClientLogCountDao clientLogCountDao;
    @Autowired
    private ChatDetailRecordDao chatDetailRecordDao;
    @Autowired
    private ActorSlaveMapper actorSlaveMapper;
    @Autowired
    private HostPopularEffectExposureLogDao hostPopularEffectExposureLogDao;
    @Autowired
    private HostConfigSlaveMapper hostConfigSlaveMapper;
    @Autowired
    private HostSalaryConfigService hostSalaryConfigService;
    @Autowired
    private HostDataBaseService hostDataBaseService;
    @Autowired
    private MoneyDetailService moneyDetailService;
    @Autowired
    private TitleForMoneyDetailDao titleForMoneyDetailDao;
    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedisTemplate;
    @Resource
    private DorisMoneyDetailDao dorisMoneyDetailDao;
    @Resource
    private ConfigApi configApi;

    public ApiResult<HostStatisticsVO> getStatisticDetail(HostStatisticsDTO dto) {
        String requestId = dto.getRequestId();
        HostStatisticsVO vo = new HostStatisticsVO();
        int currentTime = DateHelper.getCurrentTime();
        if (StringUtils.isEmpty(dto.getStartTime()) || StringUtils.isEmpty(dto.getEndTime()) || StringUtils.isEmpty(dto.getUid())) {
            logger.info("get statistic detail fail. param error. dto={} requestId={}", dto, requestId);
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorMgr.getActorData(dto.getUid());
        if (actorData == null) {
            logger.info("get statistic detail fail. host actor data is null. uid={} requestId={}", dto.getUid(), requestId);
            return ApiResult.getError(HttpCode.USER_NOT_EXISTS);
        }
        List<DayTimeData> continuesDays = DateHelper.UTC.getContinuesDays(dto.getStartTime(), dto.getEndTime());
        List<HostStatisticsDetailData> hostStatisticsList = new ArrayList<>();
        for (DayTimeData continuesDay : continuesDays) {
            logger.debug("start get statistic detail by single day. day={} uid={} requestId={}", continuesDay.getDate(), dto.getUid(), requestId);
            HostStatisticsDetailData data;
            //先从redis中取
            HostStatisticsDetailData cacheValue = getCacheValue(dto.getUid(), continuesDay);
            if (cacheValue != null) {
                logger.info("get statistic detail from redis. uid={} requestId={}", dto.getUid(), requestId);
                data = cacheValue;
            } else {
                data = getStatisticDetailFromDB(continuesDay, dto.getUid(), requestId);
                //不是当天的数据存入redis缓存中
                if (currentTime > continuesDay.getEndTime()) {
                    saveHostStatisticsToRedis(data, dto.getUid());
                }
            }
            data.setDate(getDate(data.getDate()));
            hostStatisticsList.add(data);
        }
        hostStatisticsList.sort(((o1, o2) -> -(o1.getCtime() - o2.getCtime())));
        vo.setHostStatisticsList(hostStatisticsList);
        vo.setTotalData(getTotalData(hostStatisticsList));
//        logger.info("statistic result json={}", JSONObject.toJSONString(vo));
        logger.info("statistic total consume time totalTime={} requestId={}", DateHelper.getCurrentTime() - currentTime, requestId);
        return ApiResult.getOk(vo);
    }

    private HostStatisticsDetailData getTotalData(List<HostStatisticsDetailData> hostStatisticsList) {
        logger.info("get statistic detail get total data.");
        HostStatisticsDetailData data = new HostStatisticsDetailData();
        for (HostStatisticsDetailData detailData : hostStatisticsList) {
            data.setDate("total");
            data.setTotalIncome(data.getTotalIncome() + detailData.getTotalIncome());
            data.setCallIncome(data.getCallIncome() + detailData.getCallIncome());
            data.setMatchCallIncome(data.getMatchCallIncome() + detailData.getMatchCallIncome());
            data.setGiftIncome(data.getGiftIncome() + detailData.getGiftIncome());
//            data.setTaskIncome(data.getTaskIncome() + detailData.getTaskIncome());
//            data.setInviteIncome(data.getInviteIncome() + detailData.getInviteIncome());
            data.setOnlineTime(data.getOnlineTime() + detailData.getOnlineTime());
//            data.setLiveTime(data.getLiveTime() + detailData.getLiveTime());
//            data.setPopularCount(data.getPopularCount() + detailData.getPopularCount());
            //data.setCallTimes(data.getCallTimes() + detailData.getCallTimes());
            data.setActivePopularCount(data.getActivePopularCount() + detailData.getActivePopularCount());
            data.setActiveCallCount(data.getActiveCallCount() + detailData.getActiveCallCount());
            data.setActiveCallConnectCount(data.getActiveCallConnectCount() + detailData.getActiveCallConnectCount());
            data.setActiveMatchCallConnectCount(data.getActiveMatchCallConnectCount() + detailData.getActiveMatchCallConnectCount());
        }
        //data.setActiveCallRate(getRate(data.getActiveCallCount(), data.getActivePopularCount()));
        data.setPopularRate(getRate(data.getActiveCallCount(), data.getActivePopularCount()));
        data.setActiveCallConnectRate(getRate(data.getActiveCallConnectCount(), data.getActiveCallCount()));
        return data;
    }

//    private double getSumTime(int v1, int v2) {
//        return new BigDecimal(String.valueOf(v1)).add(new BigDecimal(String.valueOf(v2))).doubleValue();
//    }

    private HostStatisticsDetailData getStatisticDetailFromDB(DayTimeData continuesDay, String uid, String requestId) {
        logger.debug("start get host statistic detail from db. uid={} day={} requestId={}", uid, continuesDay.getDate(), requestId);
        HostStatisticsDetailData data = new HostStatisticsDetailData();
        data.setDate(continuesDay.getDate());
        //获取钻石收益
        long time1 = System.currentTimeMillis();
        Double totalIncome = moneyDetailDao.getOneIncomeByActId(uid, continuesDay, 0);
        data.setTotalIncome(totalIncome.intValue());
        data.setRealTotalIncome(String.valueOf(totalIncome));
        //获取通话收益
        long time2 = System.currentTimeMillis();
        double totalCallIncome = moneyDetailDao.getOneIncomeByActId(uid, continuesDay, ActType.VIDEO_CHAT);
        List<Integer> segCodeMatchList = Arrays.asList(CallFromType.FROM_USER_MATCH_HOST, CallFromType.FROM_FILTER_HOST_MATCH);
        Double matchCallIncome = moneyDetailDao.getOneIncomeByActIdAndSegCode(uid, continuesDay, ActType.VIDEO_CHAT, segCodeMatchList);
        data.setMatchCallIncome(matchCallIncome.intValue());
        data.setRealMatchCallIncome(String.valueOf(matchCallIncome));
        Double callIncome = totalCallIncome - matchCallIncome;
        data.setCallIncome(callIncome.intValue());
        data.setRealCallIncome(String.valueOf(callIncome));

        //获取礼物收益
        long time3 = System.currentTimeMillis();
        Double giftIncome = moneyDetailDao.getOneIncomeByActId(uid, continuesDay, ActType.GAIN_GIFT);
        data.setGiftIncome(giftIncome.intValue());
        data.setRealGiftIncome(String.valueOf(giftIncome));
        //获取任务收益
        //data.setTaskIncome(moneyDetailDao.getOneIncomeByActId(uid, continuesDay, 18));
        //获取邀请收益
        //data.setInviteIncome(moneyDetailDao.getOneIncomeByActId(uid, continuesDay, 17));
        //获取主播在线时长
        long time4 = System.currentTimeMillis();
        data.setOnlineTime(getOnlineTime(uid, continuesDay));
        //获取直播时长
        data.setLiveTime(getLiveTime(uid, continuesDay));

        //获取曝光次数
        //int popularCount = getPopularCount(uid, continuesDay);
        //获取有效曝光次数
        long time5 = System.currentTimeMillis();
        int activePopularCount = getActivePopularCount(uid, continuesDay);
        data.setActivePopularCount(activePopularCount);
        //data.setPopularCount(popularCount);

        //获取通话次数
        //data.setCallTimes(getTotalCallTimes(uid, continuesDay));
        //获取有效拨打数据
        long time6 = System.currentTimeMillis();

        // 拨打电话相关数据
        updateCallConnectCount(data, continuesDay, uid);
        // 匹配电话相关数据
        updateMatchCallConnectCount(data, continuesDay, uid);

        //曝光拨打率 计算公式=拨打次数/曝光次数 //注：这里的曝光次数为有效曝光次数
        data.setPopularRate(getRate(data.getActiveCallCount(), activePopularCount));
        //获取有效拨打率
        //data.setActiveCallRate(getActiveCallRate(activeCallData, activePopularCount));
        //获取有效接通率
        data.setCtime(continuesDay.getTime());
        logger.info("get host statistic detail consume time. t1={} t2={} t3={} t4={} t5={} t6={} requestId={}", time1, time2, time3, time4, time5, time6, requestId);
        return data;
    }

    /**
     * 拨打电话相关数据
     */
    private void updateCallConnectCount(HostStatisticsDetailData data, DayTimeData continuesDay, String uid) {
        List<Integer> answerVideoCallsFromTypeList = Collections.singletonList(CallFromType.FROM_USER);
        ConnectRankVO activeCallData = chatDetailRecordDao.getActiveCallDataByFromTypeList(continuesDay, uid, answerVideoCallsFromTypeList);
        // 拨打电话有效次数    (Receiving Call)
        data.setActiveCallCount(activeCallData != null ? activeCallData.getCallCount() : 0);
        // 拨打电话接通次数     (Answer Calls)
        data.setActiveCallConnectCount(activeCallData != null ? activeCallData.getConnectCount() : 0);
        // 拨打电话有效接通率  (Answer Rate)
        data.setActiveCallConnectRate(getActiveCallConnectRate(activeCallData));
    }

    /**
     * 匹配电话相关数据
     */
    private void updateMatchCallConnectCount(HostStatisticsDetailData data, DayTimeData continuesDay, String uid) {
        List<Integer> answerRandomCallsFromTypeList = Arrays.asList(CallFromType.FROM_USER_MATCH_HOST, CallFromType.FROM_FILTER_HOST_MATCH);
        ConnectRankVO activeMatchCallData = chatDetailRecordDao.getActiveCallDataByFromTypeList(continuesDay, uid, answerRandomCallsFromTypeList);
//        data.setActiveMatchCallCount(activeMatchCallData != null ? activeMatchCallData.getCallCount() : 0);
        // 匹配电话接通次数
        data.setActiveMatchCallConnectCount(activeMatchCallData != null ? activeMatchCallData.getConnectCount() : 0);
    }

    private String getDate(String data) {
        int currentTime = DateHelper.getCurrentTime();
        DayTimeData continuesDay = DateHelper.UTC.getContinuesDays(data);
        if (currentTime < continuesDay.getEndTime()) {
            return "today";
        } else if (currentTime - 86400 < continuesDay.getEndTime()) {
            return "yesterday";
        } else {
            return continuesDay.getDate();
        }
    }

    private String getActiveCallConnectRate(ConnectRankVO activeCallData) {
        logger.debug("get statistic detail get active call connect rate. activeCallData={}", activeCallData);
        if (activeCallData == null) {
            return "0.0%";
        }
        return getRate(activeCallData.getConnectCount(), activeCallData.getCallCount());
    }

    private String getActiveCallRate(ConnectRankVO activeCallData, int activePopularCount) {
        logger.debug("get statistic detail get active call rate. activeCallData={} activePopularCount={}", activeCallData, activePopularCount);
        int callCount = 0;
        if (activeCallData != null) {
            callCount = activeCallData.getCallCount();
        }
        return getRate(callCount, activePopularCount);
    }

    private int getActivePopularCount(String uid, DayTimeData continuesDay) {
        int count = 0;
        logger.debug("get statistic detail get active popular count. uid={} day={}", uid, continuesDay.getDate());
        if (continuesDay.getTime() <= 1682294400) {//2023-04-24之前的有效曝光数据 用旧的查询方式 之后用新的查询方式
            logger.debug("get statistic detail get active popular count old type. uid={} day={}", uid, continuesDay.getDate());
            Set<String> activeUser = getActiveUser(continuesDay);
            if (CollectionUtils.isEmpty(activeUser)) {
                return 0;
            }
            ClientCountData hostActivePopularCount = clientLogCountDao.getHostActivePopularCount(uid, continuesDay, activeUser);
            count = hostActivePopularCount != null ? hostActivePopularCount.getCount() : 0;
        } else {
            logger.debug("get statistic detail get active popular count new type. uid={} day={}", uid, continuesDay.getDate());
            String tableSuffix = SubTableSupport.getTableSuffix(continuesDay);
            HostPopularEffectExposureLogData data = hostPopularEffectExposureLogDao.getData(continuesDay.getDate(), uid, tableSuffix);
            count = data != null ? data.getStatCount() : 0;
        }
        logger.debug("get statistic detail get active popular count uid={} day={} count={}", uid, continuesDay.getDate(), count);
        return count;
    }

    public Set<String> getActiveUser(DayTimeData continuesDay) {
        try {
            int currentTime = DateHelper.getCurrentTime();
            String key = "str:active_user:" + continuesDay.getDate();
            String strVal = mainRedisTemplate.opsForValue().get(key);
            if (StringUtils.isEmpty(strVal)) {
                //获取虚拟主播uid列表
                List<String> fakeHostUidList = actorSlaveMapper.selectFakeHostUid();
                //获取主播最低档金币 排除虚拟主播
                int gold = hostConfigSlaveMapper.selectGold(fakeHostUidList);
                int halfGold = gold / 2;
                //获取有效用户
                Set<String> activeUser = moneyDetailDao.getActiveUser(continuesDay, halfGold);
                if (currentTime > continuesDay.getEndTime()) {
                    mainRedisTemplate.opsForValue().set(key, JSONObject.toJSONString(activeUser), 30, TimeUnit.DAYS);
                } else {
                    //当天的缓存5分钟
                    mainRedisTemplate.opsForValue().set(key, JSONObject.toJSONString(activeUser), 5, TimeUnit.MINUTES);
                }
                return activeUser;
            }
            return new HashSet<>(JSONObject.parseArray(strVal, String.class));
        } catch (Exception e) {
            logger.error("get active user set error. e={}", e.getMessage(), e);
        }
        return new HashSet<>();
    }

    private int getTotalCallTimes(String uid, DayTimeData continuesDay) {
        logger.debug("get statistic detail get total call times. uid={} day={}", uid, continuesDay.getDate());
        return chatDetailRecordDao.getTotalCallTimes(uid, continuesDay);
    }

    private int getPopularCount(String uid, DayTimeData continuesDay) {
        logger.debug("get statistic detail get popular count. uid={} day={}", uid, continuesDay.getDate());
        ClientCountData hostPopularCount = clientLogCountDao.getHostPopularCount(uid, continuesDay);
        return hostPopularCount != null ? hostPopularCount.getCount() : 0;
    }

    private int getLiveTime(String uid, DayTimeData continuesDay) {
        logger.debug("get statistic detail get live time. uid={} day={}", uid, continuesDay.getDate());
        LiveTimeVO hostLiveTimeData = roomChapterDao.getHostLiveTimeData(uid, continuesDay);
        return getMin(hostLiveTimeData != null ? hostLiveTimeData.getLiveTime() : 0);
    }

//    private int getOnlineTime(String uid, DayTimeData continuesDay) {
//        logger.info("get statistic detail get online time. uid={} day={}", uid, continuesDay.getDate());
//        int currentTime = DateHelper.getCurrentTime();
//        if (currentTime <= continuesDay.getEndTime()) {
//            //当天的数据 从redis中拿 单位毫秒
//            int hostOnlineTime = hostOnlineTimeRedis.getHostOnlineTime(uid, continuesDay.getDate());
//            int hostOnlineSecond = hostOnlineTime / 1000;
//            return getMin(hostOnlineSecond);
//        }
//        return hostOnlineTimeLogDao.getHostOnlineTime(uid, continuesDay);
//    }

    private int getOnlineTime(String uid, DayTimeData continuesDay) {
        logger.debug("get statistic detail get online time. uid={} day={}", uid, continuesDay.getDate());
        int currentTime = DateHelper.getCurrentTime();
        if (currentTime <= continuesDay.getEndTime()) {
            //当天的数据 从redis中拿 单位毫秒
            int hostOnlineTime = hostOnlineTimeRedis.getHostOnlineTime(uid, continuesDay.getDate());
            int hostOnlineSecond = hostOnlineTime / 1000;
            return getMin(hostOnlineSecond);
        }
        Integer hostOnlineTime = hostOnlineTimeLogDao.getHostOnlineTime(uid, continuesDay.getDate());
        if (hostOnlineTime == null) {
            //如果数据库为null 从redis中拿  拿不到则为在线时长为0
            int hostOnlineTime1 = hostOnlineTimeRedis.getHostOnlineTime(uid, continuesDay.getDate());
            int hostOnlineSecond = hostOnlineTime1 / 1000;
            return getMin(hostOnlineSecond);
        }
        return hostOnlineTime;
    }

    private int getMin(int second) {
        if (second == 0) {
            return 0;
        }
        return new BigDecimal(String.valueOf(second)).divide(new BigDecimal("60"), 0, BigDecimal.ROUND_DOWN).intValue();
    }

    private String getRate(int dividend, int divisor) {
        if (divisor == 0 || dividend == 0) {
            return "0.0%";
        }
        double result = new BigDecimal(String.valueOf(dividend)).divide(new BigDecimal(String.valueOf(divisor)), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")).doubleValue();
        return result + "%";
    }

    public String getHostStatisticsKey(String uid, String date) {
        return "str:host_statistic_record:" + date + ":" + uid;
    }

    public HostStatisticsDetailData getCacheValue(String uid, DayTimeData dayTimeData) {
        String hostStatisticsKey = getHostStatisticsKey(uid, dayTimeData.getDate());
        logger.debug("start get statistic detail from redis. uid={} key={}", uid, hostStatisticsKey);
        String cacheValue = mainRedisTemplate.opsForValue().get(hostStatisticsKey);
        if (StringUtils.isEmpty(cacheValue)) {
            logger.info("get statistic detail from redis is null. uid={}", uid);
            return null;
        }
        return JSONObject.parseObject(cacheValue, HostStatisticsDetailData.class);
    }

    private void saveHostStatisticsToRedis(HostStatisticsDetailData data, String uid) {
        try {
            String hostStatisticsKey = getHostStatisticsKey(uid, data.getDate());
            mainRedisTemplate.opsForValue().set(hostStatisticsKey, JSON.toJSONString(data), EXPIRE_TIME, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save host statistic to redis error. e={} {}", e, e.getMessage());
        }
    }

    public HostSalaryVO getHostSalaryVO(HostSalaryDTO dto) {
        String uid = dto.getUid();
        ActorData actorData = actorMgr.getActorData(uid);
        if (actorData == null) {
            throw new WebException(dto, HttpCode.USER_NOT_EXISTS);
        }
        DateHelper dateHelper;
        if (ChannelEnum.CDE.getName().equals(actorData.getChannel())) {
            dateHelper = DateHelper.INDIAN;
        } else {
            dateHelper = DateHelper.UTC;
        }
        DayTimeData currentMonthTime = dateHelper.getMonthStartAndEndTime(0);
        //获取时间段所有收入
        int income = (int) moneyDetailService.getCurMonthIncome(uid, currentMonthTime);

        HostSalaryVO hostSalaryVO = new HostSalaryVO();
        List<HostSalaryConfigData> allHostSalaryConfig = hostSalaryConfigService.getAllHostSalaryConfig();
        long fixedSalary = 0;
        List<HostSalaryBO> hostSalaryList = new ArrayList<>();
        for (HostSalaryConfigData hostSalaryConfigData : allHostSalaryConfig) {
            HostSalaryBO hostSalaryBO = new HostSalaryBO();
            hostSalaryBO.setIncome(hostSalaryConfigData.getIncome());
            hostSalaryBO.setFixedSalary(hostSalaryConfigData.getFixedSalary());
            hostSalaryList.add(0, hostSalaryBO);
            if (fixedSalary == 0 && income >= hostSalaryConfigData.getIncome()) {
                fixedSalary = hostSalaryConfigData.getFixedSalary();
            }
        }
        String curMonth = dateHelper.formatDateInMonth().replace("-", "/");
        hostSalaryVO.setCurMonth(curMonth);
        hostSalaryVO.setCurIncome(income);
        hostSalaryVO.setCurFixedSalary("$" + fixedSalary);
        hostSalaryVO.setHostSalaryList(hostSalaryList);
        return hostSalaryVO;
    }

    public HostSalaryHistoryVO getHostSalaryHistoryVO(HostSalaryHistoryDTO dto) {
        String uid = dto.getUid();
        ActorData actorData = actorMgr.getActorData(uid);
        if (actorData == null) {
            throw new WebException(dto, HttpCode.USER_NOT_EXISTS);
        }
        DateHelper dateHelper;
        if (ChannelEnum.CDE.getName().equals(actorData.getChannel())) {
            dateHelper = DateHelper.INDIAN;
        } else {
            dateHelper = DateHelper.UTC;
        }
        int page = dto.getPage();
        if (page <= 0) {
            page = 1;
        }
        int queryPage = page - 1;
        HostSalaryHistoryVO hostSalaryHistoryVO = new HostSalaryHistoryVO();
        List<HostSalaryVO> salaryHistory = new ArrayList<>();
        List<HostSalaryConfigData> allHostSalaryConfig = hostSalaryConfigService.getAllHostSalaryConfig();
        int pageSize = 10;
        int startPageIndex = queryPage * pageSize;
        int endPageIndex = (queryPage + 1) * pageSize;
        Long registerTime = actorData.getRegisterTime();
        for (int i = -startPageIndex; i > -endPageIndex; i--) {
            DayTimeData currentMonthTime = dateHelper.getMonthStartAndEndTime(i);
            int startTime = currentMonthTime.getTime();
            int endTime = currentMonthTime.getEndTime();
//            logger.info("i={} uid={} startTime={} endTime={} endPageIndex={} registerTime={} currentMonthTimeData={}", i, uid, startTime, endTime, endPageIndex, registerTime, currentMonthTime.getDate());
            if (startTime < SALARY_HISTORY_START_TIME) {
                break;
            }
            if (registerTime >= endTime) {
                break;
            }
            int income;
            if (i == 0) {
                income = (int) moneyDetailService.getCurMonthIncome(uid, currentMonthTime);
            } else {
                income = moneyDetailService.getTotalIncomeByLastCycle(uid, startTime, endTime);
            }
            HostSalaryVO hostSalaryVO = new HostSalaryVO();
            long fixedSalary = 0;
            for (HostSalaryConfigData hostSalaryConfigData : allHostSalaryConfig) {
                if (fixedSalary == 0 && income >= hostSalaryConfigData.getIncome()) {
                    fixedSalary = hostSalaryConfigData.getFixedSalary();
                }
            }
            String curMonth = dateHelper.formatDateInMonth(new Date(startTime * 1000L)).replace("-", "/");
            hostSalaryVO.setCurMonth(curMonth);
            hostSalaryVO.setCurIncome(income);
            hostSalaryVO.setCurFixedSalary("$" + fixedSalary);
            salaryHistory.add(hostSalaryVO);
        }
        hostSalaryHistoryVO.setSalaryHistory(salaryHistory);
        if (salaryHistory.size() == pageSize) {
            hostSalaryHistoryVO.setNextPage(true);
        }
        return hostSalaryHistoryVO;
    }

    public GameCoinDetailVO getGameCoinDetail(GameCoinDetailDTO dto) {
        String uid = dto.getUid();
        ActorData actorData = actorMgr.getActorData(uid);
        if (actorData == null) {
            throw new WebException(dto, HttpCode.USER_NOT_EXISTS);
        }
        // 获取最近三个月的时间范围
        long currentTime = DateHelper.getCurrTime();
        long threeMonthsAgo = currentTime - (90L * 24 * 60 * 60);
        // 获取游戏币流水记录
        List<DorisMoneyDetailData> moneyDetailList = dorisMoneyDetailDao.getGameCoinDetail(uid, threeMonthsAgo, currentTime, dto.getSearchType(), dto.getPage());
        GameCoinDetailVO vo = new GameCoinDetailVO();
        List<GameCoinDetailData> detailList = new ArrayList<>();
        // 获取标题映射
        List<TitleForMoneyDetailData> titleList = titleForMoneyDetailDao.getAllData();
        Map<Integer, String> titleMap = new HashMap<>();
        for (TitleForMoneyDetailData titleData : titleList) {
            titleMap.put(titleData.getActionType(), titleData.getActionTitle());
        }
        // 转换数据
        Integer gameCoinMultiplier = configApi.getIntegerVal(new ConfigDTO(uid, AppConfigKeyConstant.GAME_COIN_MULTIPLIER, -1));
        for (DorisMoneyDetailData detail : moneyDetailList) {
            GameCoinDetailData gameCoinDetail = new GameCoinDetailData();
            gameCoinDetail.setTitle(titleMap.getOrDefault(detail.getActType(), ""));
            gameCoinDetail.setDesc(detail.getActDesc());
            gameCoinDetail.setTime(detail.getCtime());
            gameCoinDetail.setChange(detail.getRealChange().multiply(new BigDecimal(gameCoinMultiplier.toString())).toString());
            gameCoinDetail.setAfterChange(detail.getAfterRealChange().multiply(new BigDecimal(gameCoinMultiplier.toString())).toString());
            gameCoinDetail.setAction(detail.getAction());
            detailList.add(gameCoinDetail);
        }
        vo.setDetailList(detailList);
        // 判断是否有下一页
        vo.setHaveNextPage(!CollectionUtils.isEmpty(detailList));
        return vo;
    }

}
