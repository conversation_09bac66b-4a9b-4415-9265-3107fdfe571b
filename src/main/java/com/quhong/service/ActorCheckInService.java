package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.ApiResult;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.ActType;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.constant.medal.MedalCategoryConstant;
import com.quhong.constant.members.MemberShipRightsNameConstant;
import com.quhong.controller.ActorSignInController;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.datas.DayTimeData;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.*;
import com.quhong.dao.datas.*;
import com.quhong.dao.dto.ActorCheckInDTO;
import com.quhong.dao.dto.CheckInListDTO;
import com.quhong.dao.vo.signin.ActorCheckInInfoVO;
import com.quhong.dao.vo.signin.CheckInRewardsVo;
import com.quhong.dao.vo.signin.DaysInfoVO;
import com.quhong.dao.vo.smashegg.RewardInfoVo;
import com.quhong.data.appConfig.SubscribeSignRewardConfig;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.RewardItemType;
import com.quhong.exceptions.WebException;
import com.quhong.mq.data.MedalIssueData;
import com.quhong.mq.service.MedalIssueSender;
import com.quhong.players.ActorMgr;
import com.quhong.redis.RewardedRecordRedis;
import com.quhong.service.login.ActorBindLoginService;
import com.quhong.service.medal.BaseMedalService;
import com.quhong.service.members.MemberShipLevelService;
import com.quhong.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 用户签到 服务层
 *
 * <AUTHOR>
 * @date 2021/10/27 11:42
 */
@Slf4j
@Service
public class ActorCheckInService {
    private static final Logger logger = LoggerFactory.getLogger(ActorCheckInService.class);
    /**
     * 奖品类型（vip）后缀
     */
    private static final String REWARD_TYPE_VIP_SUFFIX = "-day";
    /**
     * 活动名
     */
    private static final String ACTIVITY_NAME = "check in";

    private static final String REWARD_TEXT = "#num";

    @Resource
    private ActorMgr actorMgr;
    @Resource
    private ActorCheckInInfoDao actorCheckInInfoDao;
    @Resource
    private ActorCheckInLogDao actorCheckInLogDao;
    @Resource
    private CheckInRewardConfigDao checkInRewardConfigDao;
    @Resource
    private RewardInfoDao rewardInfoDao;
    @Resource
    private GiveOutRewardService giveOutRewardService;
    @Resource
    private RewardedRecordRedis rewardedRecordRedis;
    @Resource
    private CdnUtils cdnUtils;
    @Resource
    private ConfigApi configApi;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private MemberDao memberDao;
    @Autowired
    private MedalIssueSender medalIssueSender;
    @Autowired
    private BaseMedalService baseMedalService;
    @Resource
    private ActorDangerAccountDao actorDangerAccountDao;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private ActorBindLoginService actorBindLoginService;
    @Resource
    private ActorExternalDao actorExternalDao;
    @Resource
    private MemberShipLevelService memberShipLevelService;


    /**
     * Description: 拉取用户签到列表
     *
     * @param dto 请求参数dto
     * @return com.quhong.common.data.ApiResult<com.quhong.data.vo.signin.ActorCheckInInfoVO>
     * <AUTHOR>
     * @date 2021/10/27 14:45
     */
    public ActorCheckInInfoVO getActorCheckInInfoVO(CheckInListDTO dto) {
        log.info("actorCheckInInfo data request. timeZone={}, uid={}, requestId={}", dto.getTimeZone(), dto.getUid(), dto.getRequestId());
        ActorCheckInInfoVO vo = new ActorCheckInInfoVO();
        String uid = dto.getUid();
        ActorData currActor = actorMgr.getCurrActorData(uid);
        if (ChannelEnum.CDE.getName().equals(currActor.getUid())) {
            dto.setTimeZone(DateHelper.BEIJING_TIME_ZONE_STR);
        }
//        if (!memberShipLevelService.hasRightByUidAndName(currActor, MemberShipRightsNameConstant.SIGN_IN.getRightCode())) {
//            throw new WebException(dto, new HttpCode(HttpCode.MEMBER_SHIPS_NO_RIGHTS).setMsg("no sign in right"));
//        }
        String normalTimeZone = genNormalTimeZone(dto, currActor);
        //获取签到数据
        ActorCheckInInfoData checkInInfoData = getActorCheckInInfoData(dto, uid, normalTimeZone);
        if (ChannelEnum.CDE.getName().equals(currActor.getUid())) {
            checkInInfoData.setTimeZone(dto.getTimeZone());
        }
        // timeZone 只采用第一次拉取列表时的时区
        dto.setTimeZone(checkInInfoData.getTimeZone());
        checkInInfoData.setNormalTimeZone(normalTimeZone);

        boolean isTester = actorExternalDao.checkCurrActorIsTester(currActor.getUid());
        // 是否可进行签到
        int isSign = checkIsSign(checkInInfoData, dto.getTimeZone(), dto, isTester, true);
        // 根据 【渠道 地区】 获取X天奖励数据
        List<DaysInfoVO> prizeList = getDaysInfoVoByChannelAndArea(currActor);
        // 断签处理
        noCheckInDaysProcess(checkInInfoData, dto, prizeList.size(), isTester);
        //图标处理
        genIcon(currActor, prizeList);

        vo.setCheckInDays(checkInInfoData.getDays());
        vo.setIsSign(isSign);
        vo.setIsPay(currActor.getIsPayUser());
        vo.setPrizeList(prizeList);
        vo.setIsDaysSign(checkInInfoData.getIsDaysSign());
        vo.setIsQualifiedSign(checkInInfoData.getIsQualifiedSign());
        log.debug("result getActorCheckInInfoVO method. vo={},uid={},requestId={},isDaysSign={},isQualifiedSign={}", vo, dto.getUid(), dto.getRequestId(), vo.getIsDaysSign(), vo.getIsQualifiedSign());
        return vo;
    }

    private String genNormalTimeZone(CheckInListDTO dto, ActorData currActor) {
        String timeZone = genTimeZone(dto.getTimeZone(), currActor.getPlatform());
        String normalTimeZone = timeZone.substring(0, 6);
        dto.setTimeZone(timeZone);
        return normalTimeZone;
    }

    private ActorCheckInInfoData getActorCheckInInfoData(CheckInListDTO dto, String uid, String normalTimeZone) {
        ActorCheckInInfoData checkInInfoData = actorCheckInInfoDao.queryOneByUid(uid);
        if (checkInInfoData != null) {
            return checkInInfoData;
        }
        boolean flag = actorCheckInInfoDao.insertOneByUid(uid, dto.getTimeZone(), normalTimeZone);
        if (!flag) {
            log.error("insertOneByUid method is error. uid={} ,requestId={}", uid, dto.getRequestId());
            throw new WebException(dto, new HttpCode(HttpCode.SERVER_ERROR).setMsg("insert actor check in is error,uid=" + dto.getUid()));
        }
        checkInInfoData = actorCheckInInfoDao.queryOneByUid(uid);
        return checkInInfoData;
    }

    private void genIcon(ActorData userData, List<DaysInfoVO> prizeList) {
        prizeList.forEach(data -> genPrizes(data.getPrizes(), userData, "https://statics.kissu.mobi/icon/sign_match_card_s.png"));
    }

    /**
     * Description: timeZOne格式统一
     *
     * @param timeZone 时区
     * @param platform 1安卓 2苹果
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/11/26 10:29
     */
    private String genTimeZone(String timeZone, Integer platform) {
        timeZone = timeZone.trim();
        timeZone = timeZone.replace("\u200F", "");
        //timeZone.replace("\\", "");
        if (platform == 2 && timeZone.charAt(timeZone.length() - 3) != ':') {
            String suffix = timeZone.substring(timeZone.length() - 2);
            String reSuffix = ":" + suffix;
            timeZone = timeZone.substring(0, timeZone.length() - 2);
            timeZone = timeZone + reSuffix;
        }
        if (timeZone.length() < 6) {
            timeZone = "GMT+00:00";
        }
        return timeZone;
    }


    /**
     * Description: 根据 【渠道 地区】 获取奖励数据
     *
     * @param userData 用户数据
     * @return java.util.List<com.quhong.data.vo.signin.DaysInfoVO>
     * <AUTHOR>
     * @date 2021/10/27 20:10
     */
    private List<DaysInfoVO> getDaysInfoVoByChannelAndArea(ActorData userData) {
        //获取奖励配置
        String fixChannel = getFixChannel(userData);
        List<CheckInRewardConfigData> checkInRewardConfigDataList = checkInRewardConfigDao.queryListByChannelAndArea(fixChannel, "ALL");
        Set<Integer> idSet = checkInRewardConfigDataList.stream().map(CheckInRewardConfigData::getId).collect(Collectors.toCollection(HashSet::new));
        int activityType = checkInRewardConfigDataList.get(0).getActivityType();
        //获取奖励信息
        List<RewardInfoData> rewardInfoDataList = rewardInfoDao.queryListByActivityTypeAndActivityIdList(activityType, idSet);
        /**
         * 获取奖励倍率
         */
        int rewardTimes = getRewardTimes(userData.getUid());
        rewardInfoDataList.forEach(data -> data.setNums(data.getNums() * rewardTimes));
        //checkIsPay(userData, checkInInfoData, checkInRewardConfigDataList, rewardInfoDataList);

        return setPrizeList(checkInRewardConfigDataList, rewardInfoDataList, userData);
    }

    private String getFixChannel(ActorData userData) {
        if (memberShipLevelService.validNewMemberShipLevel(userData.getUid()))
            return userData.getChannel() + "-member-new";
        return userData.getChannel();
    }

    private List<DaysInfoVO> setPrizeList(List<CheckInRewardConfigData> checkInRewardConfigDataList, List<RewardInfoData> rewardInfoDataList, ActorData actorData) {
        List<DaysInfoVO> prizeList = new ArrayList<>();
        checkInRewardConfigDataList.forEach(data -> {
            fillPrizeList(rewardInfoDataList, actorData, data, prizeList);
        });
        log.debug("setPrizeList method. data={}", prizeList);
        return prizeList;
    }

    private void fillPrizeList(List<RewardInfoData> rewardInfoDataList, ActorData actorData, CheckInRewardConfigData data, List<DaysInfoVO> prizeList) {
        DaysInfoVO vo = new DaysInfoVO();
        vo.setDays(data.getDays());
        List<RewardInfoVo> prizes = new ArrayList<>();
        rewardInfoDataList.stream()
                .filter(infoData -> infoData.getActivityId().equals(data.getId()))
                .forEach(infoData -> fillPrizes(actorData, infoData, prizes));
        vo.setPrizes(prizes);
        prizeList.add(vo);
    }

    private void fillPrizes(ActorData actorData, RewardInfoData infoData, List<RewardInfoVo> prizes) {
        RewardInfoVo infoVo = new RewardInfoVo();
        infoVo.setIcon(cdnUtils.replaceUrlDomain(actorData.getChannel(), infoData.getIcon().trim(), 0));
        infoVo.setType(infoData.getType());
        infoVo.setNum(infoData.getNums());
        infoVo.setText(REWARD_TEXT);
        if (infoData.getType() == RewardItemType.VIP_DAYS) {
            //vip赠送文案
            infoVo.setNums(infoData.getNums() + REWARD_TYPE_VIP_SUFFIX);
            infoVo.setText(REWARD_TEXT + REWARD_TYPE_VIP_SUFFIX);
        } else {
            infoVo.setNums(String.valueOf(infoData.getNums()));
        }
        prizes.add(infoVo);
    }

    /**
     * Description: 断签处理
     *
     * @param data     用户签到信息
     * @param dto      签到列表请求参数集
     * @param maxDays  最大签到天数
     * @param isTester
     * <AUTHOR>
     * @date 2021/10/27 19:51
     */
    private void noCheckInDaysProcess(ActorCheckInInfoData data, CheckInListDTO dto, int maxDays, boolean isTester) {
        log.info("noCheckInDaysProcess method. data={},uid={},requestId={}", data, dto.getUid(), dto.getRequestId());
        //  1. 签到天数为0，无操作；
        if (data.getDays() != 0) {
            // 分时区处理
            long currTime = DateHelper.getCurrentTime();
            long lastCheckInTime = data.getCheckInTime() * 1000L;
            String dataStr = DateHelper.getHelper(dto.getTimeZone()).getDayDateByTime(lastCheckInTime);
            DayTimeData lastCheckInDate = DateHelper.getHelper(dto.getTimeZone()).getContinuesDays(dataStr);
            //  2. **当前时间戳-上次签到日期的0：00时 >= 2天**，将签到天数重置为0；
            if (currTime - lastCheckInDate.getTime() >= Duration.ofDays(2).getSeconds()) {
                data.setTimeZone(dto.getTimeZone());
                data.setNormalTimeZone(data.getNormalTimeZone());
                updateDays(data);
                data.setDays(0);
            }
            //  3. 签到天数(X天周期)，**当前时间戳-上次签到日期的0：00时> 1天**，将签到天数重置为0；
            if (data.getDays() >= maxDays) {
                if (currTime - lastCheckInDate.getTime() > Duration.ofDays(1).getSeconds() || checkCanUnlimitedCheckIn(dto.getUid())
//                        || isTester
                ) {
                    data.setDays(0);
                }
            }
        }
    }

    /**
     * 没有连续签到-断签了
     */
    private boolean brokenCheckIn(ActorCheckInInfoData data, ActorCheckInDTO dto) {
        long currTime = DateHelper.getCurrentTime();
        long lastCheckInTime = data.getCheckInTime() * 1000L;
        String dataStr = DateHelper.getHelper(dto.getTimeZone()).getDayDateByTime(lastCheckInTime);
        DayTimeData lastCheckInDate = DateHelper.getHelper(dto.getTimeZone()).getContinuesDays(dataStr);
        //  2. **当前时间戳-上次签到日期的0：00时 >= 2天**，将签到天数重置为0；
        return currTime - lastCheckInDate.getTime() >= Duration.ofDays(2).getSeconds();
    }

    /**
     * Description: 断签(连续签到天数)置零操作
     *
     * @param data 签到信息
     * <AUTHOR>
     * @date 2021/11/2 14:02
     */
    private void updateDays(ActorCheckInInfoData data) {
        ActorCheckInInfoData infoData = new ActorCheckInInfoData();
        infoData.setId(data.getId());
        infoData.setDays(0);
        infoData.setTimeZone(data.getTimeZone());
        infoData.setNormalTimeZone(data.getNormalTimeZone());
        actorCheckInInfoDao.updateOne(infoData);
    }

    /**
     * Description: 用户签到
     *
     * @param dto 参数集
     * @return com.quhong.common.data.ApiResult<com.quhong.data.vo.signin.CheckInRewardsVo>
     * <AUTHOR>
     * @date 2021/10/27 14:45
     */
    public ApiResult<CheckInRewardsVo> checkIn(ActorCheckInDTO dto) {
        log.info("actor check-in request. uid={},days={},requestId={}", dto.getUid(), dto.getDays(), dto.getRequestId());
        // 获取用户签到数据
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        if (!memberShipLevelService.hasRightByUidAndName(currActor, MemberShipRightsNameConstant.SIGN_IN.getRightCode())) {
            throw new WebException(dto, new HttpCode(HttpCode.MEMBER_SHIPS_NO_RIGHTS).setMsg("no sign in right"));
        }
        if (ChannelEnum.CDE.getName().equals(currActor.getUid())) {
            dto.setTimeZone(DateHelper.BEIJING_TIME_ZONE_STR);
        }
        //timeZone 格式处理
        dto.setTimeZone(genTimeZone(dto.getTimeZone(), currActor.getPlatform()));
        String normalTimeZone = dto.getTimeZone().substring(0, 6);

        ActorCheckInInfoData checkInInfoData = actorCheckInInfoDao.queryOneByUid(dto.getUid());
        if (checkInInfoData == null) {
            log.error("The user's check-in data does not exist, or the check-in list is not pulled up.uid={}," + "requestId={}", dto.getUid(), dto.getRequestId());
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }
        checkInInfoData.setNormalTimeZone(normalTimeZone);
        if (ChannelEnum.CDE.getName().equals(currActor.getUid())) {
            checkInInfoData.setTimeZone(dto.getTimeZone());
        }
        // 根据 【渠道 地区】 获取X天奖励数据
        List<DaysInfoVO> prizeList = getDaysInfoVoByChannelAndArea(currActor);
        if (checkInInfoData.getDays() >= prizeList.size()) {
            checkInInfoData.setDays(0);
        }
        //判断是否出现跳格签到错误操作
        if (checkInInfoData.getDays() + 1 != dto.getDays()) {
            log.error("When checking in, the returned consecutive check-in date and the last check-in date are " + "not" + " consecutive.uid={},lastDays={},currDays={},requestId={}", dto.getUid(), checkInInfoData.getDays(), dto.getDays(), dto.getRequestId());
            return ApiResult.getError(HttpCode.ILLEGAL_OPERATION);
        }

//        int bossPoints = actorConfigDao.getConfigIntValue(currActor.getUid(), ActorConfigDao.BOSS_POINTS);
        boolean isTester = actorExternalDao.checkCurrActorIsTester(currActor.getUid());
//        if (actorDangerAccountDao.isDangerousActor(currActor) && bossPoints < 500 && !isTester) {
//            log.error("you are app danger user,so you can't get reward, rid={}", currActor.getRid());
//            return ApiResult.getError(new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("There is suspicious activity on your account. You can't get rewards now."));
//        }
        //timeZone 只采用第一次拉取列表时的时区
        dto.setTimeZone(checkInInfoData.getTimeZone());
        // 判断是否可进行签到
        int isSign = checkIsSign(checkInInfoData, dto.getTimeZone(), dto, isTester, false);
        if (isSign == 0) {
            log.info("Sign-in is not allowed, but the sign-in interface is called, uid={}, requestId={}", dto.getUid(), dto.getRequestId());
            return ApiResult.getError(new HttpCode(HttpCode.ILLEGAL_OPERATION.getCode(), false, "Already checked in."));
        }
        dealConsecutiveDays(dto, checkInInfoData);
        int checkInTime = DateHelper.getCurrentTime();
        //  修改签到表（actor_check_in_info）数据
        ActorCheckInInfoData actorCheckInInfoData = updateAndGetActorCheckInInfoData(dto, checkInInfoData, checkInTime);

        String fixChannel = getFixChannel(currActor);
        CheckInRewardConfigData checkInRewardConfigData = checkInRewardConfigDao.queryOneByChannelAndAreaAndDays(fixChannel, "ALL", dto.getDays());
        // 匹配当前签到天数奖品
        List<RewardInfoData> rewardInfoDataList = genRewardInfoDataList(currActor, checkInRewardConfigData);
        //  签到日志表数据添加
        ActorCheckInLogData logData = saveActorCheckInLogData(dto, currActor, actorCheckInInfoData, checkInTime);

        // 奖励发放给用户
        rewardDistribution(dto, rewardInfoDataList, logData);
        //返回奖励详情
        List<RewardInfoVo> prizes = getPrizes(rewardInfoDataList, currActor);
        genPrizes(prizes, currActor, "https://statics.kissu.mobi/icon/sign_match_card_l.png");
        CheckInRewardsVo vo = new CheckInRewardsVo();
        vo.setPrizes(prizes);
        return ApiResult.getOk(vo);
    }

    /**
     * 处理连续签到的  勋章相关
     */
    private void dealConsecutiveDays(ActorCheckInDTO dto, ActorCheckInInfoData checkInInfoData) {
//        log.info("dto={} checkInInfoData={}", JSON.toJSONString(dto), JSON.toJSONString(checkInInfoData));
        if (baseMedalService.validNotHaveMedal(dto.getUid())) {
            return;
        }
        Integer consecutiveDays = checkInInfoData.getConsecutiveDays();
        Integer maxConsecutiveDays = checkInInfoData.getMaxConsecutiveDays();
        MedalIssueData medalIssueData;
        long currTime = DateHelper.getCurrTime();
        int newConsecutiveDays = 1;
        if (brokenCheckIn(checkInInfoData, dto)) {
            // 断签了
            checkInInfoData.setConsecutiveDays(1);
            if (maxConsecutiveDays == 0) {
                checkInInfoData.setMaxConsecutiveDays(1);
                checkInInfoData.setMaxConsecutiveDaysTime(currTime);
            }
        } else {
            // 没有断签
            newConsecutiveDays = consecutiveDays + 1;
            checkInInfoData.setConsecutiveDays(newConsecutiveDays);
            if (newConsecutiveDays > maxConsecutiveDays) {
                checkInInfoData.setMaxConsecutiveDays(newConsecutiveDays);
                checkInInfoData.setMaxConsecutiveDaysTime(currTime);
            }
        }
        medalIssueData = new MedalIssueData(dto.getUid(), (long) newConsecutiveDays, (long) maxConsecutiveDays, MedalCategoryConstant.MEDAL_CATEGORY_CHECK_IN);
        medalIssueSender.sendMedalIssueMq(medalIssueData);
//        log.info("dto={} checkInInfoData={} medalIssueData={} newConsecutiveDays={}, maxConsecutiveDays={}", JSON.toJSONString(dto), JSON.toJSONString(checkInInfoData), medalIssueData, newConsecutiveDays, maxConsecutiveDays);
    }

    private void genPrizes(List<RewardInfoVo> prizes, ActorData actorData, String icon) {
        //老版本使用老匹配卡图片
        prizes.stream().filter(reward -> reward.getType() == RewardItemType.MATCH_CARD && (actorData.getPlatform() == 2 && actorData.getVer() < 286 || actorData.getPlatform() == 1 && actorData.getVer() < 374)).forEach(reward -> reward.setIcon(cdnUtils.replaceUrlDomain(actorData.getChannel(), icon, 0)));
    }

    private List<RewardInfoData> genRewardInfoDataList(ActorData actorData, CheckInRewardConfigData checkInRewardConfigData) {
        List<RewardInfoData> rewardInfoDataList = rewardInfoDao.queryListByActivityTypeAndActivityIdList(checkInRewardConfigData.getActivityType(), Collections.singleton(checkInRewardConfigData.getId()));
        int rewardTimes = getRewardTimes(actorData.getUid());
        rewardInfoDataList.forEach(data -> data.setNums(data.getNums() * rewardTimes));
        return rewardInfoDataList;
    }

    /**
     * Description: 异步发放奖励
     *
     * @param dto                请求参数集
     * @param rewardInfoDataList 奖励信息列表
     * @param logData            相关活动日志表实体
     * <AUTHOR>
     * @date 2021/10/30 18:30
     */
    private void rewardDistribution(ActorCheckInDTO dto, List<RewardInfoData> rewardInfoDataList, ActorCheckInLogData logData) {
        log.info("start to rewardDistribution async : uid={},requestId={},rewardList={},logData={}", dto.getUid(), dto.getRequestId(), rewardInfoDataList, logData);
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                // VIP 额外奖励处理
                vipBonusProcessingNew(dto, rewardInfoDataList);
                giveOutRewardService.giveOutReward(dto.getUid(), rewardInfoDataList, logData.getId(), ACTIVITY_NAME, ActType.SIGN_GOLD);
            }
        });
    }

    private void vipBonusProcessing(ActorCheckInDTO dto, List<RewardInfoData> rewardInfoDataList) {
        try {
            ConfigDTO configDTO = new ConfigDTO();
            configDTO.setUid(dto.getUid());
            configDTO.setStatus(AppConfigKeyConstant.STATUS_ALL);
            configDTO.setKey(AppConfigKeyConstant.SUBSCRIBE_SIGN_SWITCH);
            Integer canSendReward = configApi.getIntegerVal(configDTO);
            if (canSendReward != null && canSendReward == 1) {
                boolean isMember = memberDao.isMember(dto.getUid());
                log.debug("is member,{}", isMember);
                if (isMember) {
                    configDTO.setKey(AppConfigKeyConstant.SUBSCRIBE_SIGN_REWARD_COIN);
                    Integer coinNum = configApi.getIntegerVal(configDTO);
                    log.debug("coinNum={}", coinNum);
                    if (coinNum != null && coinNum > 0) {
                        RewardInfoData rewardInfoData = new RewardInfoData();
                        rewardInfoData.setType(RewardItemType.GOLD);
                        rewardInfoData.setActivityType(ActivityTypeEnum.CHECK_IN.getCode());
                        rewardInfoData.setNums(coinNum);
                        rewardInfoDataList.add(rewardInfoData);
                    }
                }
            }
        } catch (Exception e) {
            log.info("error to vipBonusProcessing,msg={}", e.getMessage(), e);
            monitorSender.info("sign_in_notice", "error to vipBonusProcessing", "requestId=" + MDC.get("request_id") + "msg=" + e.getMessage() + "," + e);
        }
    }

    private void vipBonusProcessingNew(ActorCheckInDTO dto, List<RewardInfoData> rewardInfoDataList) {
        try {
            ConfigDTO configDTO = new ConfigDTO();
            String uid = dto.getUid();
            configDTO.setUid(uid);
            configDTO.setStatus(AppConfigKeyConstant.STATUS_SERVER);
            boolean isMember = memberDao.isMember(uid);
            log.debug("is member,{}", isMember);
            if (isMember) {
//                    configDTO.setKey(AppConfigKeyConstant.SUBSCRIBE_SIGN_REWARD_COIN);
                configDTO.setKey(AppConfigKeyConstant.SUBSCRIBE_SIGN_REWARD_CONFIG);
                SubscribeSignRewardConfig subscribeSignRewardConfig = configApi.getJavaBeanVal(configDTO, SubscribeSignRewardConfig.class);
                if (subscribeSignRewardConfig != null) {
//                    log.info("uid={} subscribeSignRewardConfig={} rewardInfoDataList={}", uid, JSON.toJSONString(subscribeSignRewardConfig), JSON.toJSONString(rewardInfoDataList));
                    String validityType = subscribeSignRewardConfig.getValidityType();
//                    log.info("uid={} validityType={}", uid, validityType);
                    if (StringUtils.isEmpty(validityType)) {
                        log.error("validityType={} subscribeSignRewardConfig={}", validityType, JSON.toJSONString(subscribeSignRewardConfig));
                        return;
                    }
//                    log.info("uid={} validityType={} SubscribeSignRewardConfig.validityTypeConstant.rewardMultiple = {}  SubscribeSignRewardConfig.validityTypeConstant.rewardNums={}", uid, validityType, SubscribeSignRewardConfig.validityTypeConstant.rewardMultiple, SubscribeSignRewardConfig.validityTypeConstant.rewardNums);
                    if (validityType.equals(SubscribeSignRewardConfig.validityTypeConstant.rewardMultiple)) {
                        for (RewardInfoData rewardInfoData : rewardInfoDataList) {
                            String rewardMultiple = subscribeSignRewardConfig.getRewardMultiple();
                            Integer type = rewardInfoData.getType();
                            if (type == RewardItemType.GOLD) {
                                Integer nums = rewardInfoData.getNums();
                                Integer addNums = Integer.parseInt(rewardMultiple) * nums;
                                rewardInfoData.setNums(addNums);
                            }
                        }
                    } else if (validityType.equals(SubscribeSignRewardConfig.validityTypeConstant.rewardNums)) {
                        String rewardNums = subscribeSignRewardConfig.getRewardNums();
                        RewardInfoData addRewardInfoData = new RewardInfoData();
                        addRewardInfoData.setType(RewardItemType.GOLD);
                        addRewardInfoData.setActivityType(ActivityTypeEnum.CHECK_IN.getCode());
                        addRewardInfoData.setNums(Integer.parseInt(rewardNums));
//                        log.info("uid={} rewardNums={} subscribeSignRewardConfig={} addRewardInfoData={}", uid, rewardNums, JSON.toJSONString(subscribeSignRewardConfig), JSON.toJSONString(addRewardInfoData));
                        rewardInfoDataList.add(addRewardInfoData);
                    }
                }
//                log.info("uid={} subscribeSignRewardConfig={} rewardInfoDataList={}", uid, JSON.toJSONString(subscribeSignRewardConfig), JSON.toJSONString(rewardInfoDataList));
            }
        } catch (Exception e) {
            log.info("error to vipBonusProcessingNew,msg={}", e.getMessage(), e);
            monitorSender.info("sign_in_notice", "error to vipBonusProcessingNew", "requestId=" + MDC.get("request_id") + "msg=" + e.getMessage() + "," + e);
        }
    }

    /**
     * Description: 修改签到表
     *
     * @param dto             签到操作请求参数集
     * @param checkInInfoData 用户签到表(actor_check_in_info)实体类
     * @param checkInTime     签到时间戳
     * @return com.quhong.dao.datas.ActorCheckInInfoData
     * <AUTHOR>
     * @date 2021/10/29 10:38
     */
    private ActorCheckInInfoData updateAndGetActorCheckInInfoData(ActorCheckInDTO dto, ActorCheckInInfoData checkInInfoData, int checkInTime) {
        ActorCheckInInfoData actorCheckInInfoData = new ActorCheckInInfoData();
        actorCheckInInfoData.setId(checkInInfoData.getId());
        actorCheckInInfoData.setTimeZone(dto.getTimeZone());
        //yyyy-MM-dd (当地时区日期)
        String dateByTime = DateHelper.getHelper(dto.getTimeZone()).getDayDateByTime(checkInTime * 1000L);
        actorCheckInInfoData.setDate(dateByTime);
        actorCheckInInfoData.setCheckInTime(checkInTime);
        actorCheckInInfoData.setDays(dto.getDays());
        actorCheckInInfoData.setMaxDays(Math.max(checkInInfoData.getMaxDays(), dto.getDays()));
        actorCheckInInfoData.setTotalDays(checkInInfoData.getTotalDays() + 1);
        actorCheckInInfoData.setTimeZone(dto.getTimeZone());
        actorCheckInInfoData.setNormalTimeZone(checkInInfoData.getNormalTimeZone());
        actorCheckInInfoData.setConsecutiveDays(checkInInfoData.getConsecutiveDays());
        actorCheckInInfoData.setMaxConsecutiveDays(checkInInfoData.getMaxConsecutiveDays());
        actorCheckInInfoData.setMaxConsecutiveDaysTime(checkInInfoData.getMaxConsecutiveDaysTime());
        actorCheckInInfoDao.updateOne(actorCheckInInfoData);
        actorCheckInInfoData = actorCheckInInfoDao.queryOneByUid(dto.getUid());
        return actorCheckInInfoData;
    }

    private ActorCheckInLogData saveActorCheckInLogData(ActorCheckInDTO dto, ActorData actorData, ActorCheckInInfoData actorCheckInInfoData, int checkInTime) {
        ActorCheckInLogData actorCheckInLogData = new ActorCheckInLogData();
        actorCheckInLogData.setUid(dto.getUid());
        actorCheckInLogData.setChannel(dto.getChannel());
        actorCheckInLogData.setLanguage(dto.getLang());
        actorCheckInLogData.setArea(actorData.getCountryCode());
        actorCheckInLogData.setDays(actorCheckInInfoData.getDays());
        actorCheckInLogData.setMaxDays(actorCheckInInfoData.getMaxDays());
        actorCheckInLogData.setTotalDays(actorCheckInInfoData.getTotalDays());
        actorCheckInLogData.setImei(dto.getImei());
        actorCheckInLogData.setImsi(dto.getImsi());
        actorCheckInLogData.setIp(dto.getRemoteIP());
        actorCheckInLogData.setTimeZone(dto.getTimeZone());
        //yyyy-MM-dd (当地时区日期)
        String dateByTime = DateHelper.getHelper(dto.getTimeZone()).getDayDateByTime(checkInTime * 1000L);
        actorCheckInLogData.setDate(dateByTime);
        actorCheckInLogData.setCtime(checkInTime);
        actorCheckInLogData.setMtime(checkInTime);
        actorCheckInLogDao.insert(actorCheckInLogData);
        return actorCheckInLogData;
    }

    private List<RewardInfoVo> getPrizes(List<RewardInfoData> rewardInfoDataList, ActorData actorData) {
        List<RewardInfoVo> prizes = new ArrayList<>();
        for (RewardInfoData data : rewardInfoDataList) {
            RewardInfoVo vo = new RewardInfoVo();
            vo.setIcon(cdnUtils.replaceUrlDomain(actorData.getChannel(), data.getBigIcon().trim(), 0));
            vo.setNum(data.getNums());
            vo.setType(data.getType());
            vo.setText(data.getName() + " X" + REWARD_TEXT);
            if (data.getType() == RewardItemType.VIP_DAYS) {
                //vip赠送文案
                vo.setNums(data.getName() + " +" + data.getNums() + REWARD_TYPE_VIP_SUFFIX);
                vo.setText(data.getName() + " +" + REWARD_TEXT + REWARD_TYPE_VIP_SUFFIX);
            } else {
                vo.setNums(data.getName() + " X" + data.getNums());
            }
            prizes.add(vo);
        }
        return prizes;
    }

    private boolean checkCanUnlimitedCheckIn(String uid) {
        if (ServerConfiguration.isProduct()) {
            return false;
        }
        Integer code = configApi.getIntegerVal(new ConfigDTO(uid, "can_unlimited_check_in", -1));
        return code != null && code == 1;
    }

    /**
     * Description: 判断是否可进行签到
     *
     * @param checkInInfoData
     * @param dto
     * @param isTester
     * <AUTHOR>
     * @date 2021/10/27 16:36
     */
    private int checkIsSign(ActorCheckInInfoData checkInInfoData, String timeZone, HttpEnvData dto, boolean isTester, boolean isList) {
        // web版本，签到移除绑定社交账号限制（@wangzixue）
//        if (dto.getChannel().contains(ChannelEnum.WEB_CAMCHAT.getName()) && (!actorBindLoginService.getActorBindLoginDataByUid(dto.getUid()))) {
//            // web版本，未绑定账号，不能签到
//            log.info("no bind. do not sign. channel={} uid={}", dto.getChannel(), dto.getUid());
//            return 0;
//        }
        int isSign;
        logger.info("look isSign. isDaysSign={},isQualifiedSign={}", checkInInfoData.getIsDaysSign(), checkInInfoData.getIsQualifiedSign());
        if (checkCanUnlimitedCheckIn(dto.getUid())) {
            checkInInfoData.setIsDaysSign(0);
            checkInInfoData.setIsQualifiedSign(1);
            return 1;
        }
        checkInInfoData.setIsDaysSign(0);
        // 分时区处理
        long currTime = DateHelper.getCurrentTime();
        long lastCheckInTime = checkInInfoData.getCheckInTime() * 1000L;
        String dataStr = DateHelper.getHelper(timeZone).getDayDateByTime(lastCheckInTime);
        DayTimeData lastCheckInDate = DateHelper.getHelper(timeZone).getContinuesDays(dataStr);
        if (currTime - lastCheckInDate.getTime() <= Duration.ofDays(1).getSeconds()) {//是否已经签到查询前置
            checkInInfoData.setIsDaysSign(1);
        }
        checkInInfoData.setIsQualifiedSign(1);
        //防止刷（ip,imei,imsi判断）
        isSign = checkIp(timeZone, dto.getRemoteIP(), dto.getImsi(), dto.getImei(), isTester);
        if (isSign == 0) {
            if (isList) {
                checkInInfoData.setIsQualifiedSign(0);
                return isSign;
            }
            throw new WebException(dto, HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "You have already claimed the reward on your current device and can no longer claim it again."));
        } else {
            if (checkInInfoData.getIsDaysSign().equals(1)) {//是否已经签到查询前置
                isSign = 0;
            }
        }
        // 2. 签到天数为0，无操作（可签到isSign=1）
        // 3. 当前时间戳-上次签到日期的0：00时< 1天** ，不可签到（isSign=0）
        if (checkInInfoData.getDays() == 0) {
            return isSign;
        }
        return isSign;
    }


    /**
     * Description: 校验ip imsi imei
     *
     * @param timeZone
     * @param remoteIP
     * @param imsi
     * @param imei
     * @param isTester
     * @return int
     * <AUTHOR>
     * @date 2021/10/27 19:09
     */
    private int checkIp(String timeZone, String remoteIP, String imsi, String imei, boolean isTester) {
        if (!ServerConfiguration.isProduct() || isTester) {
            //测试服不做ip限制
            return 1;
        }
        int isSign = 0;
        String dataStr = DateHelper.getHelper(timeZone).timestampToDateStr(System.currentTimeMillis());
        DayTimeData currDate = DateHelper.getHelper(timeZone).getContinuesDays(dataStr);
        Integer id = actorCheckInLogDao.queryCountByDayAndIpAndImeiAndImsi(currDate, remoteIP, imei, imsi);
        if (id == null || id <= 0) {
            isSign = 1;
        }
        return isSign;
    }

    /**
     * 获取奖励倍率
     *
     * @param uid
     * @return
     */
    private Integer getRewardTimes(String uid) {
        ConfigDTO configDTO = new ConfigDTO();
        configDTO.setUid(uid);
        configDTO.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        configDTO.setKey(AppConfigKeyConstant.SIGNIN_REWARD_BONUS);
        Integer value = configApi.getIntegerVal(configDTO);
        return (value == null || value < 1) ? 1 : value;
    }
}
