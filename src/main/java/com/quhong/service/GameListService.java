package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.common.data.ApiResult;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.constant.SafeStrategyTypeConstant;
import com.quhong.constant.WhiteTypeConstant;
import com.quhong.constant.general.operating.GeneralOperatingSceneConstant;
import com.quhong.core.constant.game.GameTypeConstant;
import com.quhong.core.datas.DayTimeData;
import com.quhong.core.enums.GenderTypeEnum;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.*;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.ActorSourceData;
import com.quhong.dao.datas.GeneralOperatingConfigData;
import com.quhong.dao.datas.db.GameBdRelationData;
import com.quhong.dao.datas.db.GameConfigInfoData;
import com.quhong.data.appConfig.activity.gameList.MappingUrlConfig;
import com.quhong.data.bo.GameDetailBo;
import com.quhong.data.bo.generate.operating.GenerateOperatingConfigBO;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.data.vo.GameLobbyVO;
import com.quhong.game.data.vo.HaveGameLobbyListVO;
import com.quhong.players.ActorMgr;
import com.quhong.redis.GameInviteRedis;
import com.quhong.service.money.CurrencyService;
import com.quhong.service.user.auth.level.LevelApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName GameListService
 * <AUTHOR>
 * @date 2023/4/26 11:31
 */
@Component
@Lazy
@Slf4j
public class GameListService {

    @Resource
    private ActorMgr actorMgr;
    @Resource
    private CurrencyService currencyService;
    @Resource
    private GeneralOperatingConfigDao generalOperatingConfigDao;
    @Resource
    private MemberDao memberDao;
    @Resource
    private CdnUtils cdnUtils;
    @Resource
    private BossLevelService bossLevelService;
    @Resource
    private GameConfigInfoDao gameConfigInfoDao;
    @Resource
    private ConfigApi configApi;
    @Resource
    private GrayService grayService;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private ActorSourceDao actorSourceDao;
    @Resource
    private LevelApi levelApi;
    @Resource
    private SafeStrategyService safeStrategyService;
    @Resource
    private GameInviteRedis gameInviteRedis;
    @Resource
    private GameBdRelationDao gameBdRelationDao;
    @Resource
    private WhiteListConfigService whiteListConfigService;

    public ApiResult<GameLobbyVO> queryGameList(String uid) {
        GameLobbyVO vo = new GameLobbyVO();
        ActorData actorData = actorMgr.getActorData(uid);
        if (actorData == null) {
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }
        dealUserInfo(vo, actorData);
        dealGameList(vo, actorData);
        GameBdRelationData bdRelationData = gameBdRelationDao.getDataByUid(actorData.getUid());
        if (bdRelationData != null) {//游戏用户
            String today = DateHelper.genDateHelper(actorData.getChannel()).getToday();
            dealPopSign(vo, actorData, today);
            dealPhonePop(vo, actorData, today, bdRelationData);
        }
        return ApiResult.getOk(vo);
    }

    private void dealPhonePop(GameLobbyVO vo, ActorData actorData, String date, GameBdRelationData bdRelationData) {
        if (!StringUtils.isEmpty(actorData.getPhone())) {//已经绑定
            return;
        }
        DateHelper dateHelper = DateHelper.genDateHelper(actorData.getChannel());
        DayTimeData continuesDays = dateHelper.getContinuesDays(dateHelper.getDateByDeltaDay(-6));
        if (bdRelationData.getCtime() < continuesDays.getTime()) {//成为游戏用户后7天后用户不弹出
            return;
        }
        int total = gameInviteRedis.getGameLobbyPopPhoneBindRecord(actorData.getUid(), "total");
        if (total >= 5) {//注册前7天已经弹了5次
            return;
        }
        boolean b = gameInviteRedis.haveGameLobbyPopPhoneBindRecord(actorData.getUid(), date);
        if (b) {//今天已经弹出
            return;
        }
        vo.setBindPhone(1);
        gameInviteRedis.incGameLobbyPopPhoneBindRecord(actorData.getUid(), date);
        gameInviteRedis.incGameLobbyPopPhoneBindRecord(actorData.getUid(), "total");
    }

    private void dealPopSign(GameLobbyVO vo, ActorData actorData, String date) {
        //判断今天是否已经弹出
        boolean popRecord = gameInviteRedis.haveGameLobbyPopSignRecord(actorData.getUid(), date);
        vo.setPopSign(popRecord ? 0 : 1);
    }

    private void dealGameList(GameLobbyVO vo, ActorData actorData) {
        List<GeneralOperatingConfigData> generalOperatingConfigList = generalOperatingConfigDao.queryListByChannel(actorData, actorData.getChannel(), Integer.parseInt(GeneralOperatingSceneConstant.ME_GAME_LIST), 1);
        if (CollectionUtils.isEmpty(generalOperatingConfigList)) {
            return;
        }
        List<GeneralOperatingConfigData> list = JSONObject.parseArray(JSONObject.toJSONString(generalOperatingConfigList), GeneralOperatingConfigData.class);
        dealGeneralOperatingConfigList(list, actorData);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        MappingUrlConfig urlConfig = getMappingUrlConfig(actorData);

        List<GameDetailBo> gameDetailBoList = new ArrayList<>();
        for (GeneralOperatingConfigData data : list) {
            GameConfigInfoData gameConfigInfoData;
            if (!data.getUrl().contains(",")) {
                gameConfigInfoData = gameConfigInfoDao.getGameConfigInfoByName(data.getDesc());
            } else {
                String[] split = data.getUrl().split(",");
                gameConfigInfoData = gameConfigInfoDao.getGameConfigInfoByTypeAndId(Integer.parseInt(split[0]), split[1]);
            }
            if (gameConfigInfoData == null) {
                continue;
            }
            if (safeStrategyService.decideUserTypeFromGeneral(actorData, data, SafeStrategyTypeConstant.GAME_SHOW_STRATEGY)) {
                continue;
            }
            GameDetailBo bo = new GameDetailBo();
            bo.setGameName(gameConfigInfoData.getGameName());
            bo.setHotLabel(data.getHot());
            bo.setGameLink(gameConfigInfoData.getGameLink());
            bo.setGameIcon(cdnUtils.replaceUrlDomain(actorData.getChannel(), data.getIcon(), 0));
            if (gameConfigInfoData.getGameType() == 3) {
                bo.setLoginUrl(urlConfig.getJoyLoginUrl());
            } else {
                bo.setLoginUrl(urlConfig.getCommonLoginUrl());
            }
            if (gameConfigInfoData.getGameType() == GameTypeConstant.MY_GAME) {
                bo.setLoginUrl("");
            }
            bo.setGameType(gameConfigInfoData.getGameType());
            bo.setGameId(gameConfigInfoData.getGameId());
            gameDetailBoList.add(bo);
        }
        vo.setGameList(gameDetailBoList);
    }

    private MappingUrlConfig getMappingUrlConfig(ActorData actorData) {
        MappingUrlConfig urlConfig = configApi.getJavaBeanVal(new ConfigDTO(actorData.getUid(), AppConfigKeyConstant.WEB_GAME_LOGIN_MAPPING, -1), MappingUrlConfig.class);
        if (urlConfig == null) {
            urlConfig = new MappingUrlConfig();
        }
        return urlConfig;
    }

    private void dealGeneralOperatingConfigList(List<GeneralOperatingConfigData> generalOperatingConfigList, ActorData actorData) {
        for (int i = 0; i < generalOperatingConfigList.size(); i++) {
            GeneralOperatingConfigData data = generalOperatingConfigList.get(i);
            try {
                if (checkConfig(actorData, data)) {
                    generalOperatingConfigList.remove(i);
                    i--;
                }
            } catch (Exception e) {
                log.error("error to check config. uid={} e={}", actorData.getUid(), e.getMessage(), e);
            }

        }
    }

    private boolean checkConfig(ActorData actorData, GeneralOperatingConfigData data) throws Exception {
        if (!StringUtils.isEmpty(data.getConfigKey())) {
            GenerateOperatingConfigBO configBO = getGenerateOperatingConfigBO(actorData, data);
            if (configBO != null) {
                if (configBO.getPowerSwitch() != null && configBO.getPowerSwitch() == 0) {
                    return true;
                }
                //灰度判定
                boolean gray = grayService.isGray(actorData, configBO);
                if (!gray) {
                    return true;
                }
                //AB面展示逻辑
                return !checkShowUser(actorData, configBO);
            }
            return false;
        }
        return false;
    }

    private boolean checkShowUser(ActorData actorData, GenerateOperatingConfigBO configBO) throws Exception {
        boolean isAFace = checkIsAFaceUser(actorData, configBO);
        boolean isShow = (configBO.getShowMedium() > 0) == isAFace;
        if (isShow) {
            //最后特定逻辑处理
            if (configBO.getMinBossPoint() != 0 || configBO.getMinRechargeTimes() != 0) {
                int bossPoints = actorConfigDao.getConfigIntValue(actorData.getUid(), ActorConfigDao.BOSS_POINTS);
                int rechargeTimes = actorConfigDao.getConfigIntValue(actorData.getUid(), ActorConfigDao.HAD_TIMES_RECHARGE);
                log.debug("uid={},bossPoints={},rechargeTimes={},configBo={}",
                        actorData.getUid(), bossPoints, rechargeTimes, JSON.toJSONString(configBO));
                if (configBO.getIsAnd() == 1) {
                    isShow = bossPoints >= configBO.getMinBossPoint() && rechargeTimes >= configBO.getMinRechargeTimes();
                } else {
                    isShow = bossPoints >= configBO.getMinBossPoint() || rechargeTimes >= configBO.getMinRechargeTimes();
                }
            }
        }
        return isShow;
    }

    private boolean checkIsAFaceUser(ActorData actorData, GenerateOperatingConfigBO configBO) throws Exception {
        //钱包页 第三方支付banner 面向用户判定
        if (ObjectUtils.isEmpty(configBO.getMediumList())) {
            return false;
        }
        ActorSourceData sourceData = actorSourceDao.getOneByUid(actorData.getUid());
        return sourceData != null && configBO.getMediumList().contains(sourceData.getMedium());
    }

    private GenerateOperatingConfigBO getGenerateOperatingConfigBO(ActorData actorData, GeneralOperatingConfigData data) {
        ConfigDTO configDTO = new ConfigDTO();
        configDTO.setKey(data.getConfigKey());
        configDTO.setUid(actorData.getUid());
        configDTO.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        return configApi.getJavaBeanVal(configDTO, GenerateOperatingConfigBO.class);
    }


    private void dealUserInfo(GameLobbyVO vo, ActorData actorData) {
        vo.setName(actorData.getName());
        vo.setHead(cdnUtils.replaceUrlDomain(actorData.getChannel(), actorData.getHeadIcon(), 0));
        vo.setCountryCode(actorData.getCountryCode());
        vo.setVip(memberDao.isMember(actorData.getUid()));
        BigDecimal gold = currencyService.getRealCurrency1Balance(actorData.getUid());
        vo.setGold(gold.longValue());
        vo.setRealGold(gold.toString());
        BigDecimal gameCoin = currencyService.getRealCurrency4Balance(actorData.getUid());
        vo.setGameCoin(gameCoin.longValue());
        vo.setRealGameCoin(gameCoin.toString());
        vo.setGender(actorData.getGender());
        vo.setUserLevel(levelApi.getLevelByUid(actorData.getUid(), GenderTypeEnum.USER.getType()));
        vo.setHostGrade(levelApi.getLevelByUid(actorData.getUid(), GenderTypeEnum.HOST.getType()));
        vo.setLevel(vo.getUserLevel());
        if (GenderTypeEnum.HOST.getType().equals(actorData.getGender())) {
            vo.setLevel(vo.getHostGrade());
        }
    }

    public HaveGameLobbyListVO haveGameLobbyList(HttpEnvData envData) {
        HaveGameLobbyListVO vo = new HaveGameLobbyListVO();
        ActorData actorData = actorMgr.getCurrActorData(envData.getUid());
        GameLobbyVO gameLobbyVO = new GameLobbyVO();
        if (whiteListConfigService.validSkipWord(actorData.getUid(), WhiteTypeConstant.GAME_SHOW_BLOCK_LIST)) {
            vo.setHaveResult(0);
            return vo;
        }
        dealGameList(gameLobbyVO, actorData);
        vo.setHaveResult(CollectionUtils.isEmpty(gameLobbyVO.getGameList()) ? 0 : 1);
        return vo;
    }
}
