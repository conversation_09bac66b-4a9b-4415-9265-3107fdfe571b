package com.quhong.service;

import com.quhong.core.enums.ChannelEnum;
import com.quhong.dao.HostConfigDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.dto.AppStartDTO;
import com.quhong.dao.vo.AppStartVO;
import com.quhong.data.vo.ShieldResVO;
import com.quhong.players.ActorMgr;
import com.quhong.redis.AppLinkRedis;
import com.quhong.service.actor.HostReviewInfoBaseService;
import com.quhong.service.assessment.AssessmentService;
import com.quhong.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.quhong.core.enums.ActorType.HOST;

@Component
@Slf4j
public class AppStartService {
    @Resource
    private AppLinkRedis appLinkRedis;
    @Resource
    private HostConfigDao hostConfigDao;
    @Resource
    private AssessmentService assessmentService;
    @Resource
    private ActorMgr actorMgr;
    @Resource
    private HostReviewInfoBaseService hostReviewInfoBaseService;
    @Resource
    private ShieldService shieldService;

    public AppStartVO appStart(AppStartDTO dto) {
        AppStartVO appStartVO = new AppStartVO();
        if (StringUtils.isEmpty(dto.getUid())) {
            //
        } else {
            ActorData actorData = actorMgr.getActorData(dto.getUid(), "appStart");
            if (actorData == null) {
                return appStartVO;
            }
            appStartVO.setLinkB(getLinkB(actorData.getUid(), dto.getVpn(), dto.getRequestId()));
            appLinkRedis.setLinkId(dto.getUid(), dto.getLinkId());
            // app启动时，进入匹配开关重置为0（关闭状态）
            hostConfigDao.updateHostConfigIntValue(dto.getUid(), HostConfigDao.IN_MATCH_SWITCH, 0);
            dealWindowsDesc(dto);
            addHostToReviewHeadInfo(actorData);
            return appStartVO;
        }
        return appStartVO;
    }

    /**
     * 获取linkB
     */
    private int getLinkB(String uid, String vpn, String requestId) {
        ShieldResVO shieldResVO = shieldService.getShieldRes(uid, vpn);
        if (shieldResVO != null) {
            return shieldResVO.getLinkB();
        }
        return 0;
    }

    private void dealWindowsDesc(AppStartDTO dto) {
        assessmentService.issueEnterDieOutAssessmentWindows(dto.getUid());
    }

    private void addHostToReviewHeadInfo(ActorData actorData) {
        if (ChannelEnum.GOOGLEPLAY_KISSBOY.getName().equals(actorData.getChannel()) && actorData.getGender().equals(HOST)) {
            hostReviewInfoBaseService.addNewReviewHead(actorData.getUid(), actorData.getRegisterTime());
        }
    }
}
