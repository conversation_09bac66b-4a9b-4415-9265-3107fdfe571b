package com.quhong.service.longTerm.pk;

import com.alibaba.fastjson.JSON;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.ActType;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.constant.pk.PkFromTypeConstant;
import com.quhong.constant.pk.PkStepConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.money.MoneyChangeActionConstant;
import com.quhong.core.datas.DayTimeData;
import com.quhong.core.date.DateSupport;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.enums.GenderTypeEnum;
import com.quhong.core.exceptions.GoldException;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.data.dto.longTerm.pk.DurationDTO;
import com.quhong.data.dto.longTerm.pk.GuessingDTO;
import com.quhong.data.dto.longTerm.pk.QueryPkDTO;
import com.quhong.data.dto.money.CurrencyAddDTO;
import com.quhong.data.dto.money.CurrencyDeductDTO;
import com.quhong.data.dto.rank.WeekRankDTO;
import com.quhong.data.enums.ActivityHttpCode;
import com.quhong.data.pk.PkInfoData;
import com.quhong.data.pk.PkRoomContributionData;
import com.quhong.data.pk.PkUserData;
import com.quhong.data.thData.BettingGuessGameplayEvent;
import com.quhong.data.vo.BaseHistoryVO;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.event.longTerm.pk.*;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.CurrencyEnum;
import com.quhong.enums.RewardItemType;
import com.quhong.exceptions.WebException;
import com.quhong.players.ActorMgr;
import com.quhong.redis.BaseHashSaveRedis;
import com.quhong.redis.BaseZSetRedis;
import com.quhong.redis.base.reward.BaseNodeRewardRedis;
import com.quhong.report.EventReport;
import com.quhong.service.ConfigApi;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.PkBaseService;
import com.quhong.service.activity.AppConfigActivityService;
import com.quhong.service.common.EventBaseRankService;
import com.quhong.service.common.EventBaseRecordService;
import com.quhong.service.common.GrayTestSupport;
import com.quhong.service.common.RewardService;
import com.quhong.service.money.CurrencyService;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 考虑线上灰度测试
 *
 * <AUTHOR>
 * @since 2025/3/25 10:29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LongTermPkService {
    public static final boolean PROD = ServerConfiguration.isProduct();
    public static final int EVENT_CODE = ActivityTypeEnum.TIKKO_LONG_TERM_PK.getCode();
    /**
     * 灰度测试
     * ""不进行灰度测试
     * "test" 进行灰度测试
     */
    public static final String USE_TEST = GrayTestSupport.PROD;
    private static final String NOTICE_IMG = "https://statics.kissu.mobi/event/rich/party/notice.png";
    private static final String EVENT_URL = PROD ? "https://videochat.kissu.site/pk_bonus_battle/" : "https://testvideochat.kissu.site/pk_bonus_battle/";

    private static final DateHelper DATE_HELPER = DateHelper.genDateHelper(ChannelEnum.CDE.getName());

    private static final DateSupport DATE_SUPPORT = DateSupport.BEIJING;
    /**
     * 一注的价格
     */
    public static final int UNIT_BETS = 25;
    /**
     * 可竞猜的时间
     */
    public static final long CAN_GUESS_DURATION_SEC = Duration.ofMinutes(2).getSeconds();

    public static final long MIN_JOIN_GUESS_LIST_PK_DURATION = Duration.ofMinutes(5).getSeconds();
    /**
     * 每日pk win次数任务最低pk总营收限制
     */
    public static final int DAILY_WIN_TIMES_TASK_MIN_INCOME_LIMIT = 2000;
    private static final List<RewardTaskConfig> DAILY_WIN_TIMES_NODE_TASKS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.DESIGNATION, PROD ? 245 : 115));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1).setTaskName("Lv1"));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.GOLD, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards2).setTaskName("Lv2"));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 25, RewardItemType.GOLD, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(10).setRewards(rewards3).setTaskName("Lv3"));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 40, RewardItemType.GOLD, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(15).setRewards(rewards4).setTaskName("Lv4"));

        List<RewardInfoData> rewards5 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 75, RewardItemType.GOLD, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(30).setRewards(rewards5).setTaskName("Lv5"));
    }};


    public static final List<RewardTaskConfig> INCOME_RANK_REWARDS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 71 : 23, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 116 : 122, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.ENTER_EFFECT, PROD ? 71 : 23, 0));
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.SEAT_FRAME, PROD ? 116 : 122, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, PROD ? 71 : 23, 0));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.SEAT_FRAME, PROD ? 116 : 122, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 71 : 23, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 116 : 122, 0));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));

    }};

    public static final List<RewardTaskConfig> GUESSING_RANK_REWARDS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 376 : 98, 0));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90088 : 900017, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.SEAT_FRAME, PROD ? 376 : 98, 0));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90088 : 900017, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.SEAT_FRAME, PROD ? 376 : 98, 0));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90088 : 900017, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 376 : 98, 0));
            add(new RewardInfoData(EVENT_CODE, -1, RewardItemType.MEDAL, PROD ? 90088 : 900017, 0));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));

    }};


    private final ActorMgr actorMgr;
    private final AppConfigActivityDao appConfigActivityDao;
    private final BaseHashSaveRedis baseHashSaveRedis;
    private final EventBaseRankService eventBaseRankService;
    private final EventBaseRecordService eventBaseRecordService;
    private final AppConfigActivityService appConfigActivityService;
    private final RewardService rewardService;
    private final GiveOutRewardService giveOutRewardService;
    private final PkBaseService pkBaseService;
    private final CurrencyService currencyService;
    private final BaseZSetRedis baseZSetRedis;
    private final BaseNodeRewardRedis baseNodeRewardRedis;
    private final GrayTestSupport grayTestSupport;
    private final EventReport eventReport;
    private final ConfigApi configApi;


    /**
     * 每日pk信息存储
     * hashKey: pkId  value：pk信息
     *
     * @param eventCode 活动码
     * @param date      日期 yyyy-MM-dd
     */
    private String hashDailyPkInfoKey(int eventCode, String date) {
        return String.format("hash:event:daily_pk_info:%s:%d:%s", USE_TEST, eventCode, date);
    }

    /**
     * 我的pk 维护数据
     * member: pkId  value：pk开始时间
     */
    private String zsetMyDailyPkInfoKey(int eventCode, String date, String uid) {
        return eventBaseRankService.zsetRankKey(eventCode, String.format("daily_pk_record:%s:%s:%s", USE_TEST, date, uid));
    }

    /**
     * 子榜数据来源(源数据，不可加USE_TEST)
     * member: uid  value：pk贡献
     *
     * @param pid    pkId
     * @param roomId 直播间id
     */
    private String zsetPkUserContributionKey(String pid, String roomId) {
        return "zset:contribution:record:pid:" + pid + ":roomId:" + roomId;
    }


    /**
     * 周pk赢得钻石排行榜
     * member: 竞猜者uid  value：pk赢得
     *
     * @param eventCode 活动码
     * @param weekIndex 周索引 weekIndex
     */
    private String zsetWeeklyPkWinDiamondRankKey(int eventCode, String weekIndex) {
        String keySuffix = USE_TEST + ":pkWinGoldRank:" + weekIndex;
        return eventBaseRankService.zsetRankKey(eventCode, keySuffix);
    }

    /**
     * 周竞猜获得的金币排行榜
     * member: 竞猜者uid  value：竞猜赢得
     *
     * @param eventCode 活动码
     * @param weekIndex 周索引 weekIndex
     */
    private String zsetWeeklyGuessingWinGoldRankKey(int eventCode, String weekIndex) {
        String keySuffix = USE_TEST + ":guessingWinGoldRank:" + weekIndex;
        return eventBaseRankService.zsetRankKey(eventCode, keySuffix);
    }

    /**
     * 周竞猜用户信息key
     * hashKey: 竞猜者uid  value：周信息
     *
     * @param eventCode 活动码
     * @param weekIndex 周索引 weekIndex
     */
    private String hashWeeklyGuessingUserInfoKey(int eventCode, String weekIndex) {
        return String.format("hash:event:weekly_guessing_user_info::%s:%d:%s", USE_TEST, eventCode, weekIndex);
    }

    /**
     * 进行中且选择的pk时长大于10分钟的pk信息key
     * member: pkId  value：pk开始时间
     *
     * @param eventCode 活动码
     * @param weekIndex 周索引 weekIndex
     */
    private String zsetInProgressLargePkKey(int eventCode, String weekIndex) {
        return eventBaseRankService.zsetRankKey(eventCode, String.format("weekly_in_progress_large_pk_info:%s:%s", USE_TEST, weekIndex));
    }

    /**
     * 竞猜信息榜key
     * member: 竞猜者uid  value：下注数
     *
     * @param eventCode 活动码
     * @param pid       pkId
     * @param roomId    主播房号
     */
    private String zsetGuessingInfoKey(int eventCode, String pid, String roomId) {
        return String.format("zset:event:guessing_info:%s:%d:%s:%s", USE_TEST, eventCode, pid, roomId);
    }

    /**
     * 当前未结束且我已竞猜的pk列表
     *
     * @param eventCode 活动码
     * @param weekIndex 周索引 weekIndex
     * @param uid       uid
     */
    private String zsetGuessedInProgressPkKey(int eventCode, String weekIndex, String uid) {
        return String.format("zset:event:guessed_in_progress_pk:%s:%d:%s:%s", USE_TEST, eventCode, weekIndex, uid);
    }

    /**
     * 每周竞猜记录key
     *
     * @param eventCode 活动码
     * @param weekIndex 周索引 yyyy-weekIndex
     * @param uid       用户uid
     */
    private String listGuessingRecordKey(int eventCode, String weekIndex, String uid) {
        return eventBaseRecordService.listRecordKey(USE_TEST, String.valueOf(eventCode), weekIndex, uid);
    }

    /**
     * 页面初始化数据
     */
    public PageInitVO pageInit(CommonDTO dto) {
        //给出活动时间
        DayTimeData weekStartAndEndTime = DATE_HELPER.getWeekStartAndEndTime(0);
        //给出上周营收榜top1用户信息
        int lastWeekOfYear = DATE_SUPPORT.getWeekOfYearByWeekOffset(-1);
        String zsetWeeklyPkWinGoldRankKey = zsetWeeklyPkWinDiamondRankKey(dto.getEventType(), lastWeekOfYear + "");
        List<CountVO> top1s = baseZSetRedis.getRange(zsetWeeklyPkWinGoldRankKey, 1, 1);
        List<RankRowVO> topRowVos = eventBaseRankService.fillRankRowList(top1s);
        RankRowVO lastWeeklyIncomeTop1 = new RankRowVO();
        if (!ObjectUtils.isEmpty(topRowVos)) {
            lastWeeklyIncomeTop1 = topRowVos.get(0);
        }
        return new PageInitVO()
                .setStartTime((long) weekStartAndEndTime.getTime())
                .setEndTime((long) weekStartAndEndTime.getEndTime())
                .setLastWeeklyIncomeTop1(lastWeeklyIncomeTop1);
    }

    /**
     * 我的pk列表初始化数据
     */
    public MyPkPageInitVO myPkPageInit(DurationDTO dto) {
        //校验是否为主播
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        if (!GenderTypeEnum.HOST.getType().equals(currActor.getGender())) {
            return new MyPkPageInitVO();
        }
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        if (!appConfigActivityService.isWithInChannelSet(configData, currActor)) {
            throw new WebException(ActivityHttpCode.CHANNEL_NOT_JOIN_EVENT);
        }
        //拉取个人当天/昨天pk数据
        String date = DATE_SUPPORT.offsetDateStr(dto.getOffset());
        DayTimeData dayTimeData = DATE_HELPER.getContinuesDays(date);
        BigDecimal winTimes = baseNodeRewardRedis.getCount(dto.getUid(), EVENT_CODE + date);
        return new MyPkPageInitVO()
                .setCanShow(true)
                .setStartTime((long) dayTimeData.getTime())
                .setEndTime((long) dayTimeData.getEndTime())
                .setWinPkTimes(winTimes.intValue());
    }

    /**
     * 我的每日pk记录(zset分页)
     */
    public BaseHistoryVO<PkInfoVO> dailyPkRecords(QueryPkDTO dto) {
        //校验信息
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        if (!GenderTypeEnum.HOST.getType().equals(currActor.getGender())) {
            return new BaseHistoryVO<>();
        }
        String date = DATE_SUPPORT.offsetDateStr(dto.getOffset());
        String zsetMyDailyPkInfoKey = zsetMyDailyPkInfoKey(dto.getEventType(), date, dto.getUid());
        List<CountVO> pidTimeList = baseZSetRedis.getRange(zsetMyDailyPkInfoKey, dto.getPage(), BaseHistoryVO.RECORD_PAGE_SIZE);
        if (ObjectUtils.isEmpty(pidTimeList)) {
            return new BaseHistoryVO<>();
        }
        List<String> pidList = pidTimeList.stream().map(CountVO::getUid).collect(Collectors.toList());
        String hashDailyPkInfoKey = hashDailyPkInfoKey(dto.getEventType(), date);
        List<PkInfoVO> pkInfoVOs = baseHashSaveRedis.getDataListFromRedis(hashDailyPkInfoKey, pidList, PkInfoVO.class);
        pkInfoVOs.forEach(this::dealProcessPkData);
        pkInfoVOs.sort(Comparator.comparing(PkInfoVO::getPkStartTime).reversed());
        //拉取pk信息
        return new BaseHistoryVO<>(pkInfoVOs, BaseHistoryVO.checkNextPage(dto.getPage(), pkInfoVOs.size()));
    }

    private void dealProcessPkData(PkInfoVO vo) {
        if (vo.getPkStatus() != 1) {
            return;
        }
        //进行中的实时进行数据填充
        List<PkRoomContributionData> pkScores = pkBaseService.queryRoomContributionData(vo.getPid());
        if (!ObjectUtils.isEmpty(pkScores)) {
            fillProcessScore(pkScores, vo.getLeftActor());
            fillProcessScore(pkScores, vo.getRightActor());
            Integer diamondRate = findDiamondRate(vo.getLeftActor().getUid());
            vo.computePkPool(diamondRate);
        }
        //填充榜一大哥
        fillUserContributionTop1(vo.getPid(), vo.getLeftActor());
        fillUserContributionTop1(vo.getPid(), vo.getRightActor());
    }

    private Integer findDiamondRate(String uid) {
        Integer diamondRate = configApi.getIntegerVal(new ConfigDTO(uid, AppConfigKeyConstant.DIAMOND_COIN_MULTIPLIER, -1));
        return diamondRate == null ? 1 : diamondRate;
    }

    private void fillUserContributionTop1(String pid, PkActorInfoVO pkActorInfoVO) {
        fillTop1(zsetPkUserContributionKey(pid, pkActorInfoVO.getRoomId()), pkActorInfoVO);
    }

    private static void fillProcessScore(List<PkRoomContributionData> pkScores, PkActorInfoVO vo) {
        pkScores.stream()
                .filter(scoreData -> scoreData.getRoomId().equals(vo.getRoomId()))
                .findFirst()
                .ifPresent(scoreData -> vo.setScore(scoreData.getRealContributionValue()));
    }

    public GuessPageInfoVO guessPageInit(DurationDTO dto) {
        //校验信息
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        if (!appConfigActivityService.isWithInChannelSet(configData, currActor)) {
            return new GuessPageInfoVO(dto.getUid());
        }
        //获取竞猜用户周信息
        int weekOfYear = DATE_SUPPORT.getWeekOfYearByWeekOffset(dto.getOffset());
        String hashWeeklyGuessingUserInfoKey = hashWeeklyGuessingUserInfoKey(dto.getEventType(), String.valueOf(weekOfYear));
        return findWeeklyGuessPageInfo(dto.getUid(), hashWeeklyGuessingUserInfoKey);
    }

    /**
     * 可进行竞猜的pk列表
     * pk选择时长>10min
     * 且pk处于前5分钟（使用进行中zset对时间进行筛选）zsetInProgressLargePkKey
     */
    public List<PkInfoVO> ableGuessPkList(DurationDTO dto) {
        int weekOfYear = DATE_SUPPORT.getWeekOfYearByWeekOffset(dto.getOffset());
        String zsetInProgressLargePkKey = zsetInProgressLargePkKey(dto.getEventType(), String.valueOf(weekOfYear));
        long currTime = DateHelper.getCurrTime();
        List<CountVO> pidTimeList = baseZSetRedis.getRangeByScore(zsetInProgressLargePkKey, currTime - CAN_GUESS_DURATION_SEC, Long.MAX_VALUE);
        if (ObjectUtils.isEmpty(pidTimeList)) {
            return new ArrayList<>();
        }
        List<PkInfoVO> pkInfoVOs = findPkInfos(dto, pidTimeList);
        if (ObjectUtils.isEmpty(pkInfoVOs)) {
            return new ArrayList<>();
        }
        // 填充pkInfo
        pkInfoVOs.forEach(pkInfo -> fillInProcessPkInfo(dto, pkInfo, true));
        pkInfoVOs.sort(Comparator.comparing(PkInfoVO::getPkStartTime).reversed());
        return pkInfoVOs;
    }

    private List<PkInfoVO> findPkInfos(DurationDTO dto, List<CountVO> pidTimeList) {
        List<String> pidList = pidTimeList.stream().map(CountVO::getUid).collect(Collectors.toList());
        String today = DATE_SUPPORT.offsetDateStr(0);
        String yesterday = DATE_SUPPORT.offsetDateStr(-1);
        String todayInfoKey = hashDailyPkInfoKey(dto.getEventType(), today);
        String yesterdayInfoKey = hashDailyPkInfoKey(dto.getEventType(), yesterday);
        List<PkInfoVO> pkInfoVOs = baseHashSaveRedis.getDataListFromRedis(todayInfoKey, pidList, PkInfoVO.class);
        if (ObjectUtils.isEmpty(pkInfoVOs) || pkInfoVOs.size() < pidList.size()) {
            pkInfoVOs.addAll(baseHashSaveRedis.getDataListFromRedis(yesterdayInfoKey, pidList, PkInfoVO.class));
        }
        return pkInfoVOs.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private void fillInProcessPkInfo(DurationDTO dto, PkInfoVO pkInfo, boolean canFillCurrBets) {
        //进行中的实时进行数据填充 score, 竞猜榜top1, 赔率
        log.info("fillInProcessPkInfo pkInfo:{}", JSON.toJSONString(pkInfo));
        List<PkRoomContributionData> pkScores = pkBaseService.queryRoomContributionData(pkInfo.getPid());
        if (!ObjectUtils.isEmpty(pkScores)) {
            fillProcessScore(pkScores, pkInfo.getLeftActor());
            fillProcessScore(pkScores, pkInfo.getRightActor());
            Integer diamondRate = findDiamondRate(pkInfo.getLeftActor().getUid());
            pkInfo.computePkPool(diamondRate);
        }
        String fromGuessingInfoKey = zsetGuessingInfoKey(dto.getEventType(), pkInfo.getPid(), pkInfo.getLeftActor().getRoomId());
        String toGuessingInfoKey = zsetGuessingInfoKey(dto.getEventType(), pkInfo.getPid(), pkInfo.getRightActor().getRoomId());
        //填充竞猜榜一大哥
        fillTop1(fromGuessingInfoKey, pkInfo.getLeftActor());
        fillTop1(toGuessingInfoKey, pkInfo.getRightActor());

        fillBets(dto, pkInfo, pkInfo.getLeftActor(), canFillCurrBets);
        fillBets(dto, pkInfo, pkInfo.getRightActor(), canFillCurrBets);
        pkInfo.computeOdds();
    }

    private void fillBets(DurationDTO dto, PkInfoVO pkInfo, PkActorInfoVO pkActorInfo, boolean canFillCurrBets) {
        String zsetGuessingInfoKey = zsetGuessingInfoKey(dto.getEventType(), pkInfo.getPid(), pkActorInfo.getRoomId());
        List<CountVO> guessList = baseZSetRedis.getRange(zsetGuessingInfoKey, 1, 0);
        long guessSum = guessList.stream().mapToLong(CountVO::getCount).sum();
        pkActorInfo.setTotalBets(guessSum);

        fillCurrBets(dto, pkActorInfo, canFillCurrBets, zsetGuessingInfoKey);
    }

    private void fillCurrBets(DurationDTO dto, PkActorInfoVO pkActorInfo, boolean canFillCurrBets, String zsetGuessingInfoKey) {
        if (!canFillCurrBets) {
            return;
        }
        CountVO currBets = baseZSetRedis.getOne(zsetGuessingInfoKey, dto.getUid());
        if (currBets.getCount() <= 0) {
            return;
        }
        pkActorInfo.setCurrBets(currBets.getCount());
    }

    private void fillTop1(String key, PkActorInfoVO pkInfo) {
        RankRowVO top1Data = eventBaseRankService.findTop1(key);
        pkInfo.setSonRankTop1(top1Data);
    }


    /**
     * 竞猜
     */
    public boolean guessing(GuessingDTO dto) {
        dto.checkParams();
        //校验信息
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        if (!appConfigActivityService.isWithInChannelSet(configData, currActor)) {
            throw new WebException(HttpCode.CHANNEL_NOT_JOIN_EVENT);
        }
        boolean fromCanJoin = grayTestSupport.shouldAllowAccess(USE_TEST, currActor.getUid());
        if (!fromCanJoin) {
            throw new WebException(HttpCode.NOT_JOIN_EVENT);
        }
        Integer currWeekOfYear = DATE_HELPER.getWeekOfYear(0);
        String today = DATE_SUPPORT.offsetDateStr(0);
        String yesterday = DATE_SUPPORT.offsetDateStr(-1);
        //校验pk是否在进行中
        String zsetInProgressLargePkKey = zsetInProgressLargePkKey(dto.getEventType(), String.valueOf(currWeekOfYear));
        Double score = baseZSetRedis.getScore(zsetInProgressLargePkKey, dto.getPid());
        if (score == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "this pk ended or not exist"));
        }
        //获取pk信息
        String todayHashDailyPkInfoKey = hashDailyPkInfoKey(dto.getEventType(), today);
        String yesterdayHashDailyPkInfoKey = hashDailyPkInfoKey(dto.getEventType(), yesterday);
        PkInfoVO pkInfo = baseHashSaveRedis.getDataByRedis(todayHashDailyPkInfoKey, dto.getPid(), PkInfoVO.class);
        if (pkInfo == null) {
            pkInfo = baseHashSaveRedis.getDataByRedis(yesterdayHashDailyPkInfoKey, dto.getPid(), PkInfoVO.class);
        }
        if (pkInfo == null || pkInfo.getPkStatus() != 1) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "this pk ended or not exist"));
        }
        if (CAN_GUESS_DURATION_SEC + pkInfo.getPkStartTime() < DateHelper.getCurrTime()) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "the guessing time for this pk has ended"));
        }

        //不可pk双方都下注
        if (dto.getRoomId().equals(pkInfo.getLeftActor().getRoomId())) {
            String guessingInfoKey = zsetGuessingInfoKey(dto.getEventType(), dto.getPid(), pkInfo.getRightActor().getRoomId());
            CountVO guessInfo = baseZSetRedis.getOne(guessingInfoKey, dto.getUid());
            if (guessInfo.getCount() > 0) {
                throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "You cannot bet on both sides of the PK"));
            }
        } else if (dto.getRoomId().equals(pkInfo.getRightActor().getRoomId())) {
            String guessingInfoKey = zsetGuessingInfoKey(dto.getEventType(), dto.getPid(), pkInfo.getLeftActor().getRoomId());
            CountVO guessInfo = baseZSetRedis.getOne(guessingInfoKey, dto.getUid());
            if (guessInfo.getCount() > 0) {
                throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "You cannot bet on both sides of the PK"));
            }
        } else {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "The room ID is not one of the two rooms in the current PK"));
        }
        //获取竞猜用户信息
        int needGold = dto.getBetsAmount() * UNIT_BETS;
        BigDecimal balance = currencyService.getRealCurrency1Balance(dto.getUid());
        if (balance == null || balance.compareTo(new BigDecimal(needGold)) < 0) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.BALANCE_NOT_ENOUGH, false, "Insufficient balance"));
        }
        //扣除金币
        CurrencyDeductDTO deductDTO = generateBetDeductBalanceDTO(dto, needGold);
        currencyService.deduct(deductDTO);

        //记录竞猜信息
        String guessingInfoKey = zsetGuessingInfoKey(dto.getEventType(), dto.getPid(), dto.getRoomId());
        baseZSetRedis.increaseToZSet(guessingInfoKey, dto.getUid(), needGold);
        String zsetGuessedInProgressPkKey = zsetGuessedInProgressPkKey(dto.getEventType(), currWeekOfYear + "", dto.getUid());
        baseZSetRedis.addToZSet(zsetGuessedInProgressPkKey, dto.getPid(), pkInfo.getPkStartTime());
        return true;
    }

    private static CurrencyDeductDTO generateBetDeductBalanceDTO(GuessingDTO dto, int needGold) {
        CurrencyDeductDTO deductDTO = new CurrencyDeductDTO();
        deductDTO.setUid(dto.getUid());
        deductDTO.setChangeNum(needGold);
        deductDTO.setActType(ActType.ACTIVITY_REWARD);
        deductDTO.setActDesc("PK activity betting");
        deductDTO.setSegmentCode(EVENT_CODE);
        deductDTO.setAction(MoneyChangeActionConstant.ACTION_DEDUCT);
        deductDTO.setCurrencyCode(CurrencyEnum.CURRENCY1.getCurrencyCode());
        deductDTO.setOperator("PK activity betting");
        deductDTO.setRelatedId(dto.getPid() + "_" + dto.getRoomId());
        return deductDTO;
    }

    /**
     * 我已竞猜的还在进行中的列表
     * 维护一个redis set
     * 再与zsetInProgressLargePkKey  取交集
     * 再从今日/昨日的hashDailyPkInfoKey中获取pk信息
     */
    public List<PkInfoVO> guessedInProgressPkList(DurationDTO dto) {
        int weekOfYear = DATE_SUPPORT.getWeekOfYearByWeekOffset(dto.getOffset());
        String zsetGuessedInProgressPkKey = zsetGuessedInProgressPkKey(dto.getEventType(), String.valueOf(weekOfYear), dto.getUid());
        List<CountVO> pidTimeList = baseZSetRedis.getRange(zsetGuessedInProgressPkKey, 1, 0);
        if (ObjectUtils.isEmpty(pidTimeList)) {
            return new ArrayList<>();
        }
        log.info("pidTimes={}", JSON.toJSONString(pidTimeList));
        List<PkInfoVO> pkInfos = findPkInfos(dto, pidTimeList);
        if (ObjectUtils.isEmpty(pkInfos)) {
            return new ArrayList<>();
        }
        pkInfos.forEach(pkInfo -> fillInProcessPkInfo(dto, pkInfo, true));
        pkInfos.sort(Comparator.comparing(PkInfoVO::getPkStartTime).reversed());
        return pkInfos;
    }

    /**
     * 周已结算竞猜记录（考虑分页查询）
     */
    public BaseHistoryVO<PkInfoVO> weeklyGuessingRecords(QueryPkDTO dto) {
        dto.checkParams();
        int weekOfYear = DATE_SUPPORT.getWeekOfYearByWeekOffset(dto.getOffset());
        String listGuessingRecordKey = listGuessingRecordKey(dto.getEventType(), String.valueOf(weekOfYear), dto.getUid());
        //周竞猜记录拉取
        return eventBaseRecordService.findRecord(dto, listGuessingRecordKey, PkInfoVO.class);
    }

    public ModelRankVO<RankRowVO> queryRank(WeekRankDTO dto) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
//        if (DateHelper.getCurrentTime() < configData.getStartTime()) {
//            throw new WebException(HttpCode.EVENT_NOT_START);
//        }
        int weekOfYear = DATE_SUPPORT.getWeekOfYearByWeekOffset(dto.getWeekOffset());
        String key = zsetWeeklyPkWinDiamondRankKey(dto.getEventType(), String.valueOf(weekOfYear));
        // 榜单逻辑 1代表pk收益榜，2代表pk竞猜收益榜
        if (dto.getRankType() == 2) {
            key = zsetWeeklyGuessingWinGoldRankKey(dto.getEventType(), String.valueOf(weekOfYear));
        }
        return eventBaseRankService.rank(currActor, key);
    }

    public static final String TEXT_FORMAT = "\uD83D\uDE01 Congratulations to the following users for making it to the Top#top in \"#eventName\" Event #rankName\n" +
            "#content" +
            "\uD83D\uDE01 The rewards have been issued! \uD83C\uDF89\uD83C\uDF89\uD83C\uDF89\n" +
            "Official Notification: PK Weekly Glory Ranking Event Rewards Distributed.";

    public void rankRewards(Integer eventCode, Integer rankType, String rankName, List<RewardTaskConfig> rewardConfigs, int weekOffset) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventCode);
        int weekOfYear = DATE_SUPPORT.getWeekOfYearByWeekOffset(weekOffset);
        String rankKey = zsetWeeklyPkWinDiamondRankKey(EVENT_CODE, weekOfYear + "");
        if (rankType == 2) {
            rankKey = zsetWeeklyGuessingWinGoldRankKey(EVENT_CODE, weekOfYear + "");
        }
        String globalNoticeFormatter = TEXT_FORMAT.replace("#eventName", configData.getName())
                .replace("#top", rewardConfigs.size() + "")
                .replace("#rankName", rankName);
        eventBaseRankService.rankRewards(configData, rankKey, rewardConfigs, rankName, globalNoticeFormatter,
                rankType == 1 ? "4" : "5", NOTICE_IMG, EVENT_URL);
    }


    public void startPkAction(PkInfoData mqData) {
        //校验信息
        checkParams(mqData);
        if (mqData.getPkFromType() != PkFromTypeConstant.TYPE_PK_MATCH) {
            return;
        }
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        ActorData fromActor = actorMgr.getCurrActorData(mqData.getPkFromUser().getUid());
        ActorData toActor = actorMgr.getCurrActorData(mqData.getPkToUser().getUid());
        boolean fromActorCanJoin = appConfigActivityService.isWithInChannelSet(configData, fromActor);
        boolean toActorCanJoin = appConfigActivityService.isWithInChannelSet(configData, toActor);
        if (!fromActorCanJoin || !toActorCanJoin) {
            log.error("{} or {} can not join long term pk event", fromActor.getRid(), toActor.getRid());
            return;
        }
        if (!GenderTypeEnum.HOST.getType().equals(fromActor.getGender()) || !GenderTypeEnum.HOST.getType().equals(toActor.getGender())) {
            log.error("{} or {} is not host", fromActor.getRid(), toActor.getRid());
            return;
        }
        if (excludeByGrayTest(fromActor, toActor)) return;
        //确定时间节点
        long startTime = mqData.getPkConfrontStartTime();
        //yyyy-MM-dd
        String dayDate = DATE_SUPPORT.secToDateStr(startTime);
        int weekOfYear = DATE_SUPPORT.getWeekOfYearBySec(startTime);
        //记录pk信息数据 hashDailyPkInfoKey
        String hashDailyPkInfoKey = hashDailyPkInfoKey(EVENT_CODE, dayDate);
        PkInfoVO vo = initPkInfoVO(fromActor, toActor, mqData);
        baseHashSaveRedis.saveToRedis(hashDailyPkInfoKey, mqData.getPid(), vo);
        //处理双方pk记录 zsetMyDailyPkInfoKey
        String fromActorZsetMyDailyPkInfoKey = zsetMyDailyPkInfoKey(EVENT_CODE, dayDate, fromActor.getUid());
        baseZSetRedis.addToZSet(fromActorZsetMyDailyPkInfoKey, mqData.getPid(), vo.getPkStartTime());
        String toActorZsetMyDailyPkInfoKey = zsetMyDailyPkInfoKey(EVENT_CODE, dayDate, toActor.getUid());
        baseZSetRedis.addToZSet(toActorZsetMyDailyPkInfoKey, mqData.getPid(), vo.getPkStartTime());

        //处理进行中且选择的pk时长大于10分钟的pk信息 zsetInProgressLargePkKey
        if (vo.getPkDuration() >= MIN_JOIN_GUESS_LIST_PK_DURATION) {
            String zsetInProgressLargePkKey = zsetInProgressLargePkKey(EVENT_CODE, weekOfYear + "");
            baseZSetRedis.addToZSet(zsetInProgressLargePkKey, mqData.getPid(), vo.getPkStartTime());
        }

    }

    private boolean excludeByGrayTest(ActorData fromActor, ActorData toActor) {
        boolean fromCanJoin = grayTestSupport.shouldAllowAccess(USE_TEST, fromActor.getUid());
        boolean toCanJoin = grayTestSupport.shouldAllowAccess(USE_TEST, toActor.getUid());
        if (!fromCanJoin || !toCanJoin) {
            log.info("{} or {} can not join long term pk event,env={}", fromActor.getRid(), toActor.getRid(), USE_TEST);
            return true;
        }
        return false;
    }

    private PkInfoVO initPkInfoVO(ActorData fromActor, ActorData toActor, PkInfoData mqData) {
        PkInfoVO vo = new PkInfoVO(fromActor, toActor, mqData.getPid());
        vo.setPkStartTime(mqData.getPkConfrontStartTime());
        vo.setPkEndTime(mqData.getPkConfrontStartTime() + mqData.getPkConfrontDuration());
        vo.setPkDuration(mqData.getPkConfrontDuration());
        vo.getLeftActor().setRoomId(mqData.getPkFromUser().getRoomId());
        vo.getRightActor().setRoomId(mqData.getPkToUser().getRoomId());
        return vo;
    }


    public void endPkAction(PkInfoData mqData) {
        checkParams(mqData);
        if (mqData.getPkConfrontEndTime() <= 0) {
            return;
        }
        if (mqData.getPkFromType() != PkFromTypeConstant.TYPE_PK_MATCH) {
            return;
        }
        if (mqData.getPkStep() < PkStepConstant.STEP_CONFRONT_ING) {
            return;
        }
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        ActorData fromActor = actorMgr.getCurrActorData(mqData.getPkFromUser().getUid());
        ActorData toActor = actorMgr.getCurrActorData(mqData.getPkToUser().getUid());
        boolean fromActorCanJoin = appConfigActivityService.isWithInChannelSet(configData, fromActor);
        boolean toActorCanJoin = appConfigActivityService.isWithInChannelSet(configData, toActor);
        if (!fromActorCanJoin || !toActorCanJoin) {
            log.error("{} or {} can not join long term pk event", fromActor.getRid(), toActor.getRid());
            return;
        }
        if (excludeByGrayTest(fromActor, toActor)) return;
        //确定时间节点
        long startTime = mqData.getPkConfrontStartTime();
        //yyyy-MM-dd
        String dayDate = DATE_SUPPORT.secToDateStr(startTime);
        int weekOfYear = DATE_SUPPORT.getWeekOfYearBySec(startTime);
        String hashDailyPkInfoKey = hashDailyPkInfoKey(EVENT_CODE, dayDate);
        String zsetWeeklyPkWinGoldRankKey = zsetWeeklyPkWinDiamondRankKey(EVENT_CODE, weekOfYear + "");
        String zsetWeeklyGuessingWinGoldRankKey = zsetWeeklyGuessingWinGoldRankKey(EVENT_CODE, weekOfYear + "");
        String hashWeeklyGuessingUserInfoKey = hashWeeklyGuessingUserInfoKey(EVENT_CODE, weekOfYear + "");
        String zsetInProgressLargePkKey = zsetInProgressLargePkKey(EVENT_CODE, weekOfYear + "");
        //审核pk状态 (确定输赢或平局或异常)
        int pkStatus = findPkStatus(mqData);

        //处理pk信息数据 hashDailyPkInfoKey
        PkInfoVO vo = baseHashSaveRedis.getDataByRedis(hashDailyPkInfoKey, mqData.getPid(), PkInfoVO.class);
        if (vo == null || vo.getPkStatus() != 1) {
            log.warn("PK info not found for pid:{}", mqData.getPid());
            return;
        }
        vo.setPkStatus(pkStatus);

        String fromZsetPkUserContributionKey = zsetPkUserContributionKey(mqData.getPid(), mqData.getPkFromUser().getRoomId());
        String fromActorZsetGuessingInfoKey = zsetGuessingInfoKey(EVENT_CODE, mqData.getPid(), mqData.getPkFromUser().getRoomId());
        List<CountVO> fromGuessList = baseZSetRedis.getRange(fromActorZsetGuessingInfoKey, 1, 0);
        fillPkActorInfo(vo.getLeftActor(), mqData.getPkFromUser(), fromGuessList, fromZsetPkUserContributionKey);

        String toZsetPkUserContributionKey = zsetPkUserContributionKey(mqData.getPid(), mqData.getPkToUser().getRoomId());
        String toActorZsetGuessingInfoKey = zsetGuessingInfoKey(EVENT_CODE, mqData.getPid(), mqData.getPkToUser().getRoomId());
        List<CountVO> toGuessList = baseZSetRedis.getRange(toActorZsetGuessingInfoKey, 1, 0);
        fillPkActorInfo(vo.getRightActor(), mqData.getPkToUser(), toGuessList, toZsetPkUserContributionKey);

        Integer diamondRate = findDiamondRate(vo.getLeftActor().getUid());
        vo.computePkPool(diamondRate);
        vo.computeOdds();
        //从进行中大pk列表移除
        baseZSetRedis.removeToZSet(zsetInProgressLargePkKey, mqData.getPid());
        switch (pkStatus) {
            //正常结束
            case 0:
                dealNormalEndPkLogic(mqData, vo, dayDate, zsetWeeklyPkWinGoldRankKey, toGuessList, fromGuessList, zsetWeeklyGuessingWinGoldRankKey, hashWeeklyGuessingUserInfoKey);
                break;
            //异常结束
            case -1:
                //平局
            case 2:
                dealAbnormalEndPkLogic(vo, fromGuessList, toGuessList);
                break;
//            case 2:
//                //判定pk时间是否>=10分钟，如果不是则结束，如果是，则查看是否有用户对本场pk下注
//                dealDrawEndPkLogic(vo, fromGuessList, hashWeeklyGuessingUserInfoKey, toGuessList);
//                break;
            default:
                break;
        }

        baseHashSaveRedis.saveToRedis(hashDailyPkInfoKey, vo.getPid(), vo);
        //赢家输家都需要处理的逻辑(竞猜记录相关逻辑)
        dealGuessUserRecord(fromActorZsetGuessingInfoKey, vo, toActorZsetGuessingInfoKey, fromGuessList, weekOfYear, toGuessList);
    }

    private void dealGuessUserRecord(String fromActorZsetGuessingInfoKey, PkInfoVO vo, String toActorZsetGuessingInfoKey, List<CountVO> fromGuessList, int weekOfYear, List<CountVO> toGuessList) {
        if (vo.getPkDuration() < MIN_JOIN_GUESS_LIST_PK_DURATION) {
            return;
        }
        //竞猜top1填充
        fillTop1(fromActorZsetGuessingInfoKey, vo.getLeftActor());
        fillTop1(toActorZsetGuessingInfoKey, vo.getRightActor());
        fromGuessList.forEach(guess -> dealPkEndAfterLogic(guess, weekOfYear, vo, vo.getLeftActor()));
        toGuessList.forEach(guess -> dealPkEndAfterLogic(guess, weekOfYear, vo, vo.getRightActor()));
    }

    private void dealPkEndAfterLogic(CountVO guess, int weekOfYear, PkInfoVO vo, PkActorInfoVO pkActorInfo) {
        // uid来自 zsetGuessingInfoKey  移除
        String zsetGuessedInProgressPkKey = zsetGuessedInProgressPkKey(EVENT_CODE, weekOfYear + "", guess.getUid());
        baseZSetRedis.removeToZSet(zsetGuessedInProgressPkKey, vo.getPid());
        // 下注记录
        pkActorInfo.setCurrBets(guess.getCount());
        String listGuessingRecordKey = listGuessingRecordKey(EVENT_CODE, weekOfYear + "", guess.getUid());
        eventBaseRecordService.leftPush(listGuessingRecordKey, vo);
    }

    private void dealDrawEndPkLogic(PkInfoVO vo, List<CountVO> fromGuessList, String hashWeeklyGuessingUserInfoKey, List<CountVO> toGuessList) {
        if (vo.getPkDuration() < MIN_JOIN_GUESS_LIST_PK_DURATION) {
            return;
        }
        //下注数据通吃
        if (!ObjectUtils.isEmpty(fromGuessList)) {
            fromGuessList.forEach(loseGuess -> dealGuessLoserLogic(loseGuess, hashWeeklyGuessingUserInfoKey, vo, vo.getLeftActor()));
        }
        if (!ObjectUtils.isEmpty(toGuessList)) {
            toGuessList.forEach(loseGuess -> dealGuessLoserLogic(loseGuess, hashWeeklyGuessingUserInfoKey, vo, vo.getRightActor()));
        }
    }

    private void dealAbnormalEndPkLogic(PkInfoVO vo, List<CountVO> fromGuessList, List<CountVO> toGuessList) {
        //判定pk时间是否>=10分钟，如果不是则结束，如果是，则查看是否有用户对本场pk下注
        if (vo.getPkDuration() < MIN_JOIN_GUESS_LIST_PK_DURATION) {
            return;
        }
        //下注数据返还（?是否统计进竞猜收益榜）
        if (!ObjectUtils.isEmpty(fromGuessList)) {
            fromGuessList.forEach(guess -> dealAbnormalGuessLogic(guess, vo, vo.getLeftActor()));
        }
        if (!ObjectUtils.isEmpty(toGuessList)) {
            toGuessList.forEach(guess -> dealAbnormalGuessLogic(guess, vo, vo.getRightActor()));
        }
    }

    private void dealAbnormalGuessLogic(CountVO guess, PkInfoVO vo, PkActorInfoVO pkActor) {
        if (guess.getCount() <= 0) {
            return;
        }
        //下注金额返还
        returnCoins(guess.getUid(), guess.getCount());
        //竞猜结果上报
        reportGuessResult(vo, pkActor, guess, 3, 0);
    }

    private void reportGuessResult(PkInfoVO vo, PkActorInfoVO pkActor, CountVO guess, int guessingResult, int guessingWinCoins) {
        BettingGuessGameplayEvent logData = new BettingGuessGameplayEvent();
        logData.setCtime(DateHelper.getCurrTime());
        logData.setChannel(ChannelEnum.CDE.getName());
        logData.setEventCode(EVENT_CODE);
        logData.setActivityName("PK Bonus Battle");
        logData.setScene(String.valueOf(EVENT_CODE));
        logData.setSceneDetail(vo.getPid());
        logData.setPkStartAt(vo.getPkStartTime());
        logData.setBetObj(pkActor.getUid());
        logData.setBetCurrency(CurrencyEnum.CURRENCY1.getCurrencyCode());
        logData.setBetQuantity((int) guess.getCount());
        logData.setGuessingResult(guessingResult);
        logData.setGuessingWinCoins(guessingWinCoins);
        logData.setUid(guess.getUid());
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    protected void returnCoins(String uid, long returnCoins) {
        try {
            CurrencyAddDTO currencyAddDTO = generateReturnCoinsDTO(uid, returnCoins);
            currencyService.add(currencyAddDTO);
        } catch (GoldException e) {
            log.error("add to screen gold error. returnCoins={} uid={} {}", returnCoins, uid, e.getMessage(), e);
            throw new WebException(HttpCode.createHttpCode(HttpCode.SERVER_ERROR, true, e.getMessage()));
        }
    }

    private static CurrencyAddDTO generateReturnCoinsDTO(String uid, long returnCoins) {
        String desc = "pk abnormal or draw, bet coins refunded";
        CurrencyAddDTO currencyAddDTO = new CurrencyAddDTO();
        currencyAddDTO.setUid(uid);
        currencyAddDTO.setCurrencyCode(CurrencyEnum.CURRENCY1.getCurrencyCode());
        currencyAddDTO.setChangeNum(returnCoins);
        currencyAddDTO.setActDesc(desc);
        currencyAddDTO.setActType(ActType.ACTIVITY_REWARD);
        currencyAddDTO.setSegmentCode(EVENT_CODE);
        currencyAddDTO.setOperator("returnCoins");
        return currencyAddDTO;
    }

    private void dealNormalEndPkLogic(PkInfoData mqData, PkInfoVO vo, String dayDate, String zsetWeeklyPkWinGoldRankKey, List<CountVO> toGuessList, List<CountVO> fromGuessList, String zsetWeeklyGuessingWinGoldRankKey, String hashWeeklyGuessingUserInfoKey) {
        vo.checkActorPkStatus(mqData.getWinUid());
        //双方PK总收益3% 作为奖励给到赢家
        int pkRewardGoldNum = Integer.parseInt(vo.getPkPool());
        if (pkRewardGoldNum <= 0) {
            log.warn("Invalid pkRewardGoldNum={}, skip award", pkRewardGoldNum);
            return;
        }
        awardPkWinPool(mqData, pkRewardGoldNum);

        //每日累计胜利场次且双方pk总收益大于5000diamond 的次数达到指定节点可获得对应奖励
        awardDailyWinTimesNodeRewards(mqData, vo, dayDate);
        //每周PK主播获得的奖励的数值大小进行排榜
        eventBaseRankService.rankValueIncrease(zsetWeeklyPkWinGoldRankKey, mqData.getWinUid(), pkRewardGoldNum);
        //判定pk时间是否>=10分钟，如果不是则结束，如果是，则查看是否有用户对本场pk下注
        if (vo.getPkDuration() < MIN_JOIN_GUESS_LIST_PK_DURATION) {
            return;
        }
        //抽水奖池计算，如果抽水奖池为空，则结束，不为空，则按照抽水奖池计算赔率给赢家发放金币奖励
        // PK竞猜周收益榜单：在竞猜中获得的奖励大小排行榜
        List<CountVO> winGuessList;
        List<CountVO> loseGuessList;
        PkActorInfoVO winActor;
        PkActorInfoVO loseActor;
        String odds;
        if (mqData.getWinUid().equals(vo.getRightActor().getUid())) {
            winGuessList = toGuessList;
            winActor = vo.getRightActor();
            loseActor = vo.getLeftActor();
            odds = vo.getRightActor().getOdds();
            loseGuessList = fromGuessList;
        } else {
            winGuessList = fromGuessList;
            winActor = vo.getLeftActor();
            loseActor = vo.getRightActor();
            odds = vo.getLeftActor().getOdds();
            loseGuessList = toGuessList;
        }

        if (!ObjectUtils.isEmpty(winGuessList)) {
            // 赢家逻辑处理
            winGuessList.forEach(winGuess -> this.awardGuessWinReward(winGuess, odds, zsetWeeklyGuessingWinGoldRankKey, hashWeeklyGuessingUserInfoKey, vo, winActor));
        }
        if (!ObjectUtils.isEmpty(loseGuessList)) {
            // 输家逻辑处理
            loseGuessList.forEach(loseGuess -> this.dealGuessLoserLogic(loseGuess, hashWeeklyGuessingUserInfoKey, vo, loseActor));
        }
    }

    private void dealGuessLoserLogic(CountVO loseGuess, String hashWeeklyGuessingUserInfoKey, PkInfoVO vo, PkActorInfoVO loseActor) {
        GuessPageInfoVO guessInfo = findWeeklyGuessPageInfo(loseGuess.getUid(), hashWeeklyGuessingUserInfoKey);
        guessInfo.incrementLoseTimes();
        baseHashSaveRedis.saveToRedis(hashWeeklyGuessingUserInfoKey, loseGuess.getUid(), guessInfo);

        // 竞猜结果上报数数
        reportGuessResult(vo, loseActor, loseGuess, 2, 0);
    }

    private void fillPkActorInfo(PkActorInfoVO actorInfoVO, PkUserData pkUserData, List<CountVO> guessList, String zsetPkUserContributionKey) {
        actorInfoVO.setScore(StringUtils.hasLength(pkUserData.getRealContributionValue()) ? pkUserData.getRealContributionValue() : "0");
        long fromGuessSum = guessList.stream().mapToLong(CountVO::getCount).sum();
        actorInfoVO.setTotalBets(fromGuessSum);
        //填充榜一大哥
        RankRowVO top1Data = eventBaseRankService.findTop1(zsetPkUserContributionKey);
        actorInfoVO.setSonRankTop1(top1Data);
    }


    private void awardGuessWinReward(CountVO winGuess, String odds, String zsetWeeklyGuessingWinGoldRankKey, String hashWeeklyGuessingUserInfoKey, PkInfoVO vo, PkActorInfoVO winActor) {
        if (winGuess.getCount() <= 0) {
            return;
        }
        int winGold = new BigDecimal(odds)
                .multiply(new BigDecimal(winGuess.getCount()))
                .setScale(0, RoundingMode.DOWN)
                .intValue();
        RewardInfoData GoldReward = new RewardInfoData(EVENT_CODE, winGold, RewardItemType.GOLD, 0, 1);
        giveOutRewardService.giveEventReward(winGuess.getUid(), Collections.singletonList(GoldReward), EVENT_CODE, "2");
        eventBaseRankService.rankValueIncrease(zsetWeeklyGuessingWinGoldRankKey, winGuess.getUid(), winGold);

        GuessPageInfoVO guessInfo = findWeeklyGuessPageInfo(winGuess.getUid(), hashWeeklyGuessingUserInfoKey);
        guessInfo.incrementWinTimes();
        guessInfo.incrementIncome((long) winGold);
        baseHashSaveRedis.saveToRedis(hashWeeklyGuessingUserInfoKey, winGuess.getUid(), guessInfo);

        // 竞猜结果上报数数
        reportGuessResult(vo, winActor, winGuess, 1, winGold);

    }

    private GuessPageInfoVO findWeeklyGuessPageInfo(String uid, String hashWeeklyGuessingUserInfoKey) {
        GuessPageInfoVO guessInfo = baseHashSaveRedis.getDataByRedis(hashWeeklyGuessingUserInfoKey, uid, GuessPageInfoVO.class);
        if (guessInfo == null) {
            guessInfo = new GuessPageInfoVO(uid);
        }
        return guessInfo;
    }

    private void awardDailyWinTimesNodeRewards(PkInfoData mqData, PkInfoVO vo, String dayDate) {
        if (vo.findTotalScore().compareTo(BigDecimal.valueOf(DAILY_WIN_TIMES_TASK_MIN_INCOME_LIMIT)) < 0) {
            return;
        }
        dealWinTimesNodeRewards(mqData.getWinUid(), dayDate, 1);

    }

    public void dealWinTimesNodeRewards(String uid, String dayDate, int incr) {
        List<RewardTaskConfig> configs = baseNodeRewardRedis.increaseAndGetCanGetTaskConfig(EVENT_CODE + dayDate, uid, incr, DAILY_WIN_TIMES_NODE_TASKS);
        if (ObjectUtils.isEmpty(configs)) {
            return;
        }
        configs.forEach(config ->
                giveOutRewardService.giveEventReward(uid, config.getRewards(), EVENT_CODE,
                        "3", config.getTaskName()));
    }

    private void awardPkWinPool(PkInfoData mqData, int pkRewardGoldNum) {
        RewardInfoData pkReward = new RewardInfoData(EVENT_CODE, pkRewardGoldNum, RewardItemType.GOLD, 0, 1);
        giveOutRewardService.giveEventReward(mqData.getWinUid(), Collections.singletonList(pkReward), EVENT_CODE, "1");
    }

    /**
     * pk状态 1进行中 0正常结束 -1异常结束 2平局
     */
    private int findPkStatus(PkInfoData mqData) {
        boolean normal = mqData.getPkConfrontEndTime() - mqData.getPkConfrontStartTime() >= mqData.getPkConfrontDuration();
        if (!normal) {
            return -1;
        }
        if (StringUtils.hasLength(mqData.getWinUid())) {
            return 0;
        }
        //同时比较贡献值是否相等来判断平局
        if (!StringUtils.hasLength(mqData.getPkFromUser().getRealContributionValue())) {
            mqData.getPkFromUser().setRealContributionValue("0");
        }
        if (!StringUtils.hasLength(mqData.getPkToUser().getRealContributionValue())) {
            mqData.getPkToUser().setRealContributionValue("0");
        }
        int compare = new BigDecimal(mqData.getPkFromUser().getRealContributionValue()).compareTo(new BigDecimal(mqData.getPkToUser().getRealContributionValue()));
        if (compare == 0) {
            return 2;
        }

        String winUid;
        if (compare > 0) {
            winUid = mqData.getPkFromUser().getUid();
        } else {
            winUid = mqData.getPkToUser().getUid();
        }
        mqData.setWinUid(winUid);
        return 0;
    }

    private void checkParams(PkInfoData mqData) {
        if (StringUtils.isEmpty(mqData.getPid())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "pid is empty"));
        }
        if (mqData.getPkFromUser() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "pkFromUser is empty"));
        }
        if (mqData.getPkToUser() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "pkToUser is empty"));
        }
        if (mqData.getPkConfrontStartTime() <= 0) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "pkConfrontStartTime is empty"));
        }
        if (mqData.getPkConfrontDuration() <= 0) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "pkConfrontDuration is empty"));
        }
    }

    public BaseHistoryVO<RankRowVO> guessedRank(QueryPkDTO dto) {
        String key = zsetGuessingInfoKey(dto.getEventType(), dto.getPid(), dto.getRoomId());
        List<CountVO> guessList = baseZSetRedis.getRange(key, dto.getPage(), BaseHistoryVO.RECORD_PAGE_SIZE);
        if (ObjectUtils.isEmpty(guessList)) {
            return new BaseHistoryVO<>();
        }
        List<RankRowVO> rankRowVOS = eventBaseRankService.fillRankRowList(guessList);
        return new BaseHistoryVO<>(rankRowVOS, BaseHistoryVO.checkNextPage(dto.getPage(), rankRowVOS.size()));
    }

    /**
     * 异常遗留数据处理
     */
    public void dealAbnormalPk(int weekOffset) {
        int weekOfYear = DATE_SUPPORT.getWeekOfYearByWeekOffset(weekOffset);
        String inProgressLargePkKey = zsetInProgressLargePkKey(EVENT_CODE, String.valueOf(weekOfYear));
        long currTime = DateHelper.getCurrTime();
        List<CountVO> dataList = baseZSetRedis.getRangeByScore(inProgressLargePkKey, 0, currTime - Duration.ofMinutes(10).getSeconds());
        if (ObjectUtils.isEmpty(dataList)) {
            return;
        }
        dataList.forEach(pidTime -> abnormalDataLeftInTheDeal(pidTime, weekOfYear));

    }

    /**
     * 遗留异常数据处理
     */
    private void abnormalDataLeftInTheDeal(CountVO pidTime, int weekOfYear) {
        String pid = pidTime.getUid();
        long startTime = pidTime.getCount();
        String dayDate = DATE_SUPPORT.secToDateStr(startTime);
        String hashDailyPkInfoKey = hashDailyPkInfoKey(EVENT_CODE, dayDate);
        int pkStatus = -1;
        PkInfoVO vo = baseHashSaveRedis.getDataByRedis(hashDailyPkInfoKey, pid, PkInfoVO.class);
        if (vo == null || vo.getPkStatus() != 1) {
            return;
        }
        vo.setPkStatus(pkStatus);
        PkActorInfoVO leftActor = vo.getLeftActor();
        String fromActorZsetGuessingInfoKey = zsetGuessingInfoKey(EVENT_CODE, pid, leftActor.getRoomId());
        List<CountVO> fromGuessList = baseZSetRedis.getRange(fromActorZsetGuessingInfoKey, 1, 0);
        long fromGuessSum = fromGuessList.stream().mapToLong(CountVO::getCount).sum();
        leftActor.setTotalBets(fromGuessSum);
        //填充榜一大哥
        String fromZsetPkUserContributionKey = zsetPkUserContributionKey(pid, leftActor.getRoomId());
        RankRowVO top1Data = eventBaseRankService.findTop1(fromZsetPkUserContributionKey);
        leftActor.setSonRankTop1(top1Data);
        leftActor.setScore("0");

        PkActorInfoVO rightActor = vo.getRightActor();
        String toActorZsetGuessingInfoKey = zsetGuessingInfoKey(EVENT_CODE, pid, rightActor.getRoomId());
        List<CountVO> toGuessList = baseZSetRedis.getRange(toActorZsetGuessingInfoKey, 1, 0);
        long toGuessSum = toGuessList.stream().mapToLong(CountVO::getCount).sum();
        rightActor.setTotalBets(toGuessSum);
        //填充榜一大哥
        String toZsetPkUserContributionKey = zsetPkUserContributionKey(pid, rightActor.getRoomId());
        RankRowVO toTop1Data = eventBaseRankService.findTop1(toZsetPkUserContributionKey);
        rightActor.setSonRankTop1(toTop1Data);
        rightActor.setScore("0");

        Integer diamondRate = findDiamondRate(leftActor.getUid());
        vo.computePkPool(diamondRate);
        vo.computeOdds();
        dealAbnormalEndPkLogic(vo, fromGuessList, toGuessList);
        baseHashSaveRedis.saveToRedis(hashDailyPkInfoKey, pid, vo);
        dealGuessUserRecord(fromActorZsetGuessingInfoKey, vo, toActorZsetGuessingInfoKey, fromGuessList, weekOfYear, toGuessList);
    }
}
