package com.quhong.service.longTerm.invite.rank;

import com.quhong.common.enums.HttpCode;
import com.quhong.constant.ActType;
import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.money.MoneyChangeActionConstant;
import com.quhong.core.datas.DayTimeData;
import com.quhong.core.date.DateSupport;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.enums.GenderTypeEnum;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.MoneyDetailData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.db.InviteRelationData;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.dto.longTerm.invite.rank.QueryRankDTO;
import com.quhong.data.vo.event.longTerm.invite.rank.PageInitVO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.dbService.InviteDBService;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.CurrencyEnum;
import com.quhong.enums.RewardItemType;
import com.quhong.exceptions.WebException;
import com.quhong.mq.data.activity.InviteUserSuccessMsgData;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.redis.BaseZSetRedis;
import com.quhong.redis.base.reward.BaseNodeRewardRedis;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.activity.AppConfigActivityService;
import com.quhong.service.common.EventBaseRankService;
import com.quhong.service.common.GrayTestSupport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.IntStream;

/**
 * 长期邀请激励活动服务
 * <AUTHOR>
 * @since 2025-05-13 10:30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LongTermInviteRankService {

    /**
     * 是否生产环境
     */
    public static final boolean PROD = ServerConfiguration.isProduct();

    /**
     * 活动代码
     */
    public static final int EVENT_CODE = ActivityTypeEnum.TIKKO_LONG_TERM_INVITE_RANK.getCode();

    /**
     * 每周新主播收入任务奖励配置
     */
    public static final List<RewardTaskConfig> WEEKLY_NEW_HOST_INCOME_TASKS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GOLD, 0, 1).setName("Coin"));
        }};
        add(new RewardTaskConfig()
                .setTaskName("1")
                .setTaskType(TaskTypeConstant.RECEIVE_GIFT_DIAMONDS)
                .setCheckParams(10)
                .setRewards(rewards1)
                .setNotice("\uD83C\uDF89 Nice work! You earned 40 Coins for your this week invited new host’s earnings reaching 400 Diamonds this week.")
        );

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.GOLD, 0, 1).setName("Coin"));
        }};
        add(new RewardTaskConfig()
                .setTaskName("2")
                .setTaskType(TaskTypeConstant.RECEIVE_GIFT_DIAMONDS)
                .setCheckParams(100)
                .setRewards(rewards2)
                .setNotice("\uD83C\uDF89 Awesome! You earned 400 Coins for your this week invited new host’s earnings reaching 4,000 Diamonds this week.")
        );

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 25, RewardItemType.GOLD, 0, 1).setName("Coin"));
        }};
        add(new RewardTaskConfig()
                .setTaskName("3")
                .setTaskType(TaskTypeConstant.RECEIVE_GIFT_DIAMONDS)
                .setCheckParams(250)
                .setRewards(rewards3)
                .setNotice("\uD83C\uDF89 Great job! You earned 1000 Coins for your this week  invited new host’s earnings reaching 10,000 Diamonds this week.")
        );

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 125, RewardItemType.GOLD, 0, 1).setName("Coin"));
        }};
        add(new RewardTaskConfig()
                .setTaskName("4")
                .setTaskType(TaskTypeConstant.RECEIVE_GIFT_DIAMONDS)
                .setCheckParams(1250)
                .setRewards(rewards4)
                .setNotice("\uD83C\uDF89 Congratulations! You have earned 5000 Coins for your this week invited new host’s earnings reaching 50,000 Diamonds this week. Keep it up!")
        );
    }};
    /**
     * 通知图片
     */
    private static final String NOTICE_IMG = "https://statics.kissu.mobi/Event/invite/guanfangtongzhi.jpg";
    /**
     * 日期帮助类
     */
    private static final DateHelper DATE_HELPER = DateHelper.genDateHelper(ChannelEnum.CDE.getName());
    /**
     * 日期支持类
     */
    private static final DateSupport DATE_SUPPORT = DateSupport.BEIJING;
    /**
     * 每日邀请人数任务奖励配置
     */
    public static final List<RewardTaskConfig> DAILY_INVITE_COUNT_TASKS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.GOLD, 0, 1).setName("Coin"));
        }};
        add(new RewardTaskConfig()
                .setTaskName("1")
                .setTaskType(TaskTypeConstant.INVITE_USER_COUNT)
                .setCheckParams(2)
                .setRewards(rewards1)
                .setNotice("\uD83D\uDCCC 2 Invites Achieved:\n" +
                        "\uD83C\uDF89 Congratulations! You’ve successfully invited 2 friends today and earned 400 Coins! " +
                        "The reward has been sent to your account. Keep going — more rewards await! \uD83D\uDD25")
        );

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 25, RewardItemType.GOLD, 0, 1).setName("Coin"));
        }};
        add(new RewardTaskConfig()
                .setTaskName("2")
                .setTaskType(TaskTypeConstant.INVITE_USER_COUNT)
                .setCheckParams(5)
                .setRewards(rewards2)
                .setNotice("\uD83D\uDCCC 5 Invites Achieved:\n" +
                        "\uD83C\uDF8A Awesome! You’ve reached 5 invites today and grabbed an extra 1000 Coins! " +
                        "Claim your next big reward and climb the leaderboard now!")
        );

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 100, RewardItemType.GOLD, 0, 1).setName("Coin"));
        }};
        add(new RewardTaskConfig()
                .setTaskName("3")
                .setTaskType(TaskTypeConstant.INVITE_USER_COUNT)
                .setCheckParams(10)
                .setRewards(rewards3)
                .setNotice("\uD83D\uDCCC 10 Invites Achieved:\n" +
                        "\uD83E\uDD73 Impressive! You’ve invited 10 friends and won 4000 Coins! " +
                        "The reward has been sent — keep it up and aim for the top of the leaderboard!")
        );

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 250, RewardItemType.GOLD, 0, 1).setName("Coin"));
        }};
        add(new RewardTaskConfig()
                .setTaskName("4")
                .setTaskType(TaskTypeConstant.INVITE_USER_COUNT)
                .setCheckParams(20)
                .setRewards(rewards4)
                .setNotice("\uD83D\uDCCC 20 Invites Achieved:\n" +
                        "\uD83D\uDC51 Invite Champion! You’ve successfully invited 20 friends today and claimed a massive 10000 Coins! " +
                        "Reward delivered — check your rank on the leaderboard now! \uD83D\uDCA5")
        );
    }};
    /**
     * 每周新用户充值任务奖励配置
     */
    public static final List<RewardTaskConfig> WEEKLY_NEW_USER_RECHARGE_TASKS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.GOLD, 0, 1).setName("Coin"));
        }};
        add(new RewardTaskConfig()
                .setTaskName("1")
                .setTaskType(TaskTypeConstant.REAL_RECHARGE_GOLD)
                .setCheckParams(100)
                .setRewards(rewards1)
                .setNotice("\uD83C\uDF89 Congratulations!\n" +
                        "You have earned 400 Coins for your invited users’ weekly recharge reaching 4,000 Coins.\n" +
                        "The reward has been credited to your account. Keep inviting and earn more!\n")
        );

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 50, RewardItemType.GOLD, 0, 1).setName("Coin"));
        }};
        add(new RewardTaskConfig()
                .setTaskName("2")
                .setTaskType(TaskTypeConstant.REAL_RECHARGE_GOLD)
                .setCheckParams(500)
                .setRewards(rewards2)
                .setNotice("\uD83C\uDF89 Congratulations!\n" +
                        "You have earned 2,000 Coins for your invited users’ weekly recharge reaching 20,000 Coins.\n" +
                        "The reward has been credited to your account. Keep inviting and earn more!")
        );

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 250, RewardItemType.GOLD, 0, 1).setName("Coin"));
        }};
        add(new RewardTaskConfig()
                .setTaskName("3")
                .setTaskType(TaskTypeConstant.REAL_RECHARGE_GOLD)
                .setCheckParams(2500)
                .setRewards(rewards3)
                .setNotice("\uD83C\uDF89 Congratulations!\n" +
                        "You have earned 10,000 Coins for your invited users’ weekly recharge reaching 100,000 Coins.\n" +
                        "The reward has been credited to your account. Keep inviting and earn more!")
        );

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 500, RewardItemType.GOLD, 0, 1).setName("Coin"));
        }};
        add(new RewardTaskConfig()
                .setTaskName("4")
                .setTaskType(TaskTypeConstant.REAL_RECHARGE_GOLD)
                .setCheckParams(5000)
                .setRewards(rewards4)
                .setNotice("\uD83C\uDF89 Congratulations!\n" +
                        "You have earned 20,000 Coins for your new invited users’ weekly recharge reaching 200,000 Coins.\n" +
                        "The reward has been credited to your account. Keep inviting and earn more!")
        );
    }};
    /**
     * 活动URL
     */
    private static final String EVENT_URL = PROD ? "https://videochat.kissu.site/invitational_rewards/" : "https://testvideochat.kissu.site/invitational_rewards/";
    /**
     * 灰度测试
     * "prod"不进行灰度测试
     * "gray_test" 进行灰度测试
     * @see GrayTestSupport
     */
    public static final String USE_TEST = GrayTestSupport.PROD;
    public static final Set<Integer> ACT_TYPE_SET = new HashSet<Integer>() {{
        add(ActType.GOOGLE_RECHARGE);
        add(ActType.MEMBER_REWARD);
        add(ActType.HMS_RECHARGE);
        add(16);
        add(81);
        add((int) ActType.MIDDLE_PAY);
        add(120);
        add((int) ActType.IOS_RECHARGE);
        add(124);
        add(125);
        add(126);
        add(127);
        add((int) ActType.COIN_DEALERS_RECHARGE);//币商充值
    }};
    /**
     * 邀请人数榜单奖励配置
     */
    public static final List<RewardTaskConfig> INVITE_COUNT_RANK_REWARDS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 156 : 66, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 395 : 189, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.DESIGNATION, PROD ? 159 : 83, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.MEDAL, PROD ? 900023 : 90002, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 750, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.ENTER_EFFECT, PROD ? 156 : 66, 0));
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.SEAT_FRAME, PROD ? 395 : 189, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.DESIGNATION, PROD ? 159 : 83, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.MEDAL, PROD ? 900023 : 90002, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 375, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.ENTER_EFFECT, PROD ? 156 : 66, 0));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.SEAT_FRAME, PROD ? 395 : 189, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.DESIGNATION, PROD ? 159 : 83, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.MEDAL, PROD ? 900023 : 90002, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 156 : 66, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 395 : 189, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.DESIGNATION, PROD ? 159 : 83, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.MEDAL, PROD ? 900023 : 90002, 0));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));
    }};
    /**
     * 新用户充值榜单奖励配置
     */
    public static final List<RewardTaskConfig> NEW_USER_RECHARGE_RANK_REWARDS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 373 : 66, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 396 : 189, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.DESIGNATION, PROD ? 394 : 83, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.MEDAL, PROD ? 100004 : 90002, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 750, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.ENTER_EFFECT, PROD ? 373 : 66, 0));
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.SEAT_FRAME, PROD ? 396 : 189, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.DESIGNATION, PROD ? 394 : 83, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.MEDAL, PROD ? 100004 : 90002, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 375, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.ENTER_EFFECT, PROD ? 373 : 66, 0));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.SEAT_FRAME, PROD ? 396 : 189, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.DESIGNATION, PROD ? 394 : 83, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.MEDAL, PROD ? 100004 : 90002, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 75, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 373 : 66, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 396 : 189, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.DESIGNATION, PROD ? 394 : 83, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.MEDAL, PROD ? 100004 : 90002, 0));
        }};
        IntStream.rangeClosed(4, 5).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));
    }};
    /**
     * 新主播收入榜单奖励配置
     */
    public static final List<RewardTaskConfig> NEW_HOST_INCOME_RANK_REWARDS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 277 : 66, 0));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 397 : 189, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.DESIGNATION, PROD ? 393 : 83, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.MEDAL, PROD ? 100005 : 90002, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 750, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.ENTER_EFFECT, PROD ? 277 : 66, 0));
            add(new RewardInfoData(EVENT_CODE, 20, RewardItemType.SEAT_FRAME, PROD ? 397 : 189, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.DESIGNATION, PROD ? 393 : 83, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.MEDAL, PROD ? 100005 : 90002, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 375, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.ENTER_EFFECT, PROD ? 277 : 66, 0));
            add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.SEAT_FRAME, PROD ? 397 : 189, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.DESIGNATION, PROD ? 393 : 83, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.MEDAL, PROD ? 100005 : 90002, 0));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, PROD ? 277 : 66, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, PROD ? 397 : 189, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.DESIGNATION, PROD ? 393 : 83, 0));
            add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.MEDAL, PROD ? 100005 : 90002, 0));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));
    }};
    /**
     * 用于EventBaseRankService的全局通知文本格式
     */
    private static final String INVITE_RANK_FORMAT = "\uD83D\uDCE3 Weekly Invite Leaderboard Results \uD83C\uDF89\n" +
            "The results are in! Congrats to this week’s TOP 10 Invite Masters! \uD83D\uDC4F\n" +
            "#content" +
            "\uD83C\uDFC6 Rewards Include\n" +
            "Massive Coins + AeroLuxe Car + [Invite Master] Title + [Invite Experts] Badge\n" +
            "\uD83C\uDF89 Rewards sent! New week, new chance — keep inviting!";
    private static final String RECHARGE_RANK_FORMAT = "The results are in! \uD83C\uDF8A\n" +
            "Here are this week’s TOP Invite Recharge Masters — huge applause to these legends! \uD83D\uDC4F\uD83D\uDC4F\uD83D\uDC4F\n" +
            "\uD83C\uDFC6 Leaderboard Results:\n" +
            "#content" +
            "\uD83C\uDF81 Rewards Include:\n" +
            "Massive Coins\n" +
            "[AeroLuxe] Car\n" +
            "Wealthy Friend Avatar Frame\n" +
            "Wealthy Friend Title (7Days)\n" +
            "Wealthy Friend Badge (7Days)\n" +
            "All rewards have been issued to your accounts — check your inbox and enjoy your prizes! \uD83D\uDCE5✨\n" +
            "A new week, a new challenge awaits! Who will top the leaderboard next? \uD83D\uDC51 Keep inviting and earning big! \uD83D\uDE80";
    private static final String NEW_HOST_INCOME_RANK_FORMAT = "\uD83D\uDCE3 Weekly Broadcaster Earnings Leaderboard Results \uD83C\uDF89\n" +
            "The results are in! Congrats to this week’s TOP 10 Popularity Stars! \uD83D\uDC4F\n" +
            "#content" +
            "\uD83C\uDFC6 Rewards Include\n" +
            "\uD83D\uDCB0 Massive Coins\n" +
            "\uD83D\uDE98 Black Supercar (Timed)\n" +
            "\uD83C\uDF96\uFE0F [Popularity Star] Title (7Days)\n" +
            "\uD83D\uDCF8 [Popularity Star] Avatar Frame (Timed)\n" +
            "\uD83C\uDFC5 [Popularity Star] Badge (7Days)\n" +
            "\uD83C\uDF89 All rewards have been sent to your account! New week, new challenge — keep broadcasting, " +
            "keep earning, and climb the leaderboard for even greater prizes!";

    private final ActorMgr actorMgr;
    private final AppConfigActivityDao appConfigActivityDao;
    private final EventBaseRankService eventBaseRankService;
    private final AppConfigActivityService appConfigActivityService;
    private final GiveOutRewardService giveOutRewardService;
    private final BaseZSetRedis baseZSetRedis;
    private final BaseNodeRewardRedis baseNodeRewardRedis;
    private final InviteDBService inviteDBService;
    private final GrayTestSupport grayTestSupport;
    private final OfficialNoticeService officialNoticeService;

    // =================== Redis键值方法 ===================

    /**
     * 每日邀请人数key
     * @param date 日期 yyyy-MM-dd
     * @param uid 用户ID
     * @return Redis key
     */
    private String dailyInviteCountKey(String date, String uid) {
        return String.format("%s:%s:daily_invite_count:%s", EVENT_CODE, date, uid);
    }

    /**
     * 每周邀请人数排行榜key
     * @param weekIndex 周索引 yyyy-weekOfYear
     * @return Redis key
     */
    private String weeklyInviteCountRankKey(String weekIndex) {
        return eventBaseRankService.zsetRankKey(EVENT_CODE, USE_TEST + ":invite_count_rank:" + weekIndex);
    }

    /**
     * 每周新用户充值排行榜key
     * @param weekIndex 周索引 yyyy-weekOfYear
     * @return Redis key
     */
    private String weeklyNewUserRechargeRankKey(String weekIndex) {
        return eventBaseRankService.zsetRankKey(EVENT_CODE, USE_TEST + ":new_user_recharge_rank:" + weekIndex);
    }

    /**
     * 每周新主播收入排行榜key
     * @param weekIndex 周索引 yyyy-weekOfYear
     * @return Redis key
     */
    private String weeklyNewHostIncomeRankKey(String weekIndex) {
        return eventBaseRankService.zsetRankKey(EVENT_CODE, USE_TEST + ":new_host_income_rank:" + weekIndex);
    }

    /**
     * 邀请的新用户充值总额Key
     * @param weekIndex 周索引 yyyy-weekOfYear
     * @param uid 邀请人ID
     * @return Redis key
     */
    private String weeklyNewUserRechargeAmountKey(String weekIndex, String uid) {
        return String.format("%s:%s:new_user_recharge:%s:%s", EVENT_CODE, USE_TEST, weekIndex, uid);
    }

    /**
     * 邀请的新主播收入总额Key
     * @param weekIndex 周索引 yyyy-weekOfYear
     * @param uid 邀请人ID
     * @return Redis key
     */
    private String weeklyNewHostIncomeAmountKey(String weekIndex, String uid) {
        return String.format("%s:%s:new_host_income:%s:%s", EVENT_CODE, USE_TEST, weekIndex, uid);
    }

    /**
     * 页面初始化
     * @param dto 请求DTO
     * @return 页面初始化VO
     */
    public PageInitVO pageInit(CommonDTO dto) {
        // 获取当前用户
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());

        // 获取活动配置
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());

        // 检查渠道是否支持
        if (!appConfigActivityService.isWithInChannelSet(configData, currActor)) {
            throw new WebException(HttpCode.CHANNEL_NOT_JOIN_EVENT);
        }

        // 灰度测试检查
//        boolean canJoin = grayTestSupport.shouldAllowAccess(USE_TEST, currActor.getUid());
//        if (!canJoin) {
//            throw new WebException(HttpCode.NOT_JOIN_EVENT);
//        }

        // 创建返回对象
        PageInitVO vo = new PageInitVO()
                .setStartTime(configData.getStartTime())
                .setEndTime(configData.getEndTime());
        if (DateHelper.getCurrTime() < configData.getStartTime()) {
            return vo;
        }

        // 填充节点任务信息
        fillNodeTasksInfo(dto.getUid(), vo);

        // 填充上周榜单信息
//        fillLastWeekRankInfo(vo);

        return vo;
    }

    /**
     * 填充节点任务信息
     * @param uid 用户ID
     * @param vo 返回VO
     */
    private void fillNodeTasksInfo(String uid, PageInitVO vo) {
        // 获取当天日期
        String today = DATE_SUPPORT.offsetDateStr(0);

        // 获取当前周索引
        int weekOfYear = DATE_HELPER.getWeekOfYear(0);
        String weekIndex = weekOfYear + "";

        DayTimeData weekStartAndEndTime = DATE_HELPER.getWeekStartAndEndTime(0);
        vo.setStartTime((long) weekStartAndEndTime.getTime());
        vo.setEndTime((long) weekStartAndEndTime.getEndTime());

        // 获取当天邀请人数
        BigDecimal dailyInviteCount = baseNodeRewardRedis.getCount(uid, dailyInviteCountKey(today, uid));
        vo.setDailyInviteCount(dailyInviteCount.intValue());

        // 获取当周邀请人数
//        long weeklyInviteCount = baseZSetRedis.getOne(weeklyInviteCountRankKey(weekIndex), uid).getCount();
//        vo.setWeeklyInviteCount((int) weeklyInviteCount);

        // 获取当周邀请的新用户充值总额
        BigDecimal weeklyNewUserRechargeAmount = baseNodeRewardRedis.getCount(uid, weeklyNewUserRechargeAmountKey(weekIndex, uid));
        vo.setWeeklyNewUserRechargeAmount(weeklyNewUserRechargeAmount.longValue());

        // 获取当周邀请的新主播收入总额
        BigDecimal weeklyNewHostIncomeAmount = baseNodeRewardRedis.getCount(uid, weeklyNewHostIncomeAmountKey(weekIndex, uid));
        vo.setWeeklyNewHostIncomeAmount(weeklyNewHostIncomeAmount.longValue());
//
//        // 填充每日邀请人数任务
//        fillDailyInviteTasks(dailyInviteCount.intValue(), vo);
//
//        // 填充每周新用户充值任务
//        fillWeeklyNewUserRechargeTasks(weeklyNewUserRechargeAmount.longValue(), vo);
//
//        // 填充每周新主播收入任务
//        fillWeeklyNewHostIncomeTasks(weeklyNewHostIncomeAmount.longValue(), vo);
    }

//    /**
//     * 填充每日邀请人数任务
//     * @param dailyInviteCount 当天邀请人数
//     * @param vo 返回VO
//     */
//    private void fillDailyInviteTasks(int dailyInviteCount, PageInitVO vo) {
//        List<DailyInviteTaskVO> tasks = new ArrayList<>();
//
//        for (RewardTaskConfig config : DAILY_INVITE_COUNT_TASKS) {
//            DailyInviteTaskVO taskVO = new DailyInviteTaskVO()
//                    .setTaskName(config.getTaskName())
//                    .setTaskDesc("每日邀请" + config.getCheckParams() + "人")
//                    .setTargetCount(config.getCheckParams())
//                    .setRewardGold(config.getRewards().get(0).getNums()) // 假设第一个奖励是金币
//                    .setRewardDesc(config.getRewards().get(0).getNums() + "金币");
//
//            // 设置任务状态
//            if (dailyInviteCount >= config.getCheckParams()) {
//                taskVO.setStatus(1); // 已完成未领取，实际项目中需根据领取状态确定
//            } else {
//                taskVO.setStatus(0); // 未完成
//            }
//
//            tasks.add(taskVO);
//        }
//
//        vo.setDailyInviteTasks(tasks);
//    }
//
//    /**
//     * 填充每周新用户充值任务
//     * @param rechargeAmount 充值总额
//     * @param vo 返回VO
//     */
//    private void fillWeeklyNewUserRechargeTasks(long rechargeAmount, PageInitVO vo) {
//        List<WeeklyNewUserRechargeTaskVO> tasks = new ArrayList<>();
//
//        for (RewardTaskConfig config : WEEKLY_NEW_USER_RECHARGE_TASKS) {
//            WeeklyNewUserRechargeTaskVO taskVO = new WeeklyNewUserRechargeTaskVO()
//                    .setTaskName(config.getTaskName())
//                    .setTaskDesc("邀请的新用户充值达到" + config.getCheckParams() + "金币")
//                    .setTargetAmount(Long.valueOf(config.getCheckParams()))
//                    .setRewardGold(config.getRewards().get(0).getNums()) // 假设第一个奖励是金币
//                    .setRewardDesc(config.getRewards().get(0).getNums() + "金币");
//
//            // 设置任务状态
//            if (rechargeAmount >= config.getCheckParams()) {
//                taskVO.setStatus(1); // 已完成未领取，实际项目中需根据领取状态确定
//            } else {
//                taskVO.setStatus(0); // 未完成
//            }
//
//            tasks.add(taskVO);
//        }
//
//        vo.setWeeklyNewUserRechargeTasks(tasks);
//    }
//
//    /**
//     * 填充每周新主播收入任务
//     * @param incomeAmount 收入总额
//     * @param vo 返回VO
//     */
//    private void fillWeeklyNewHostIncomeTasks(long incomeAmount, PageInitVO vo) {
//        List<WeeklyNewHostIncomeTaskVO> tasks = new ArrayList<>();
//
//        for (RewardTaskConfig config : WEEKLY_NEW_HOST_INCOME_TASKS) {
//            WeeklyNewHostIncomeTaskVO taskVO = new WeeklyNewHostIncomeTaskVO()
//                    .setTaskName(config.getTaskName())
//                    .setTaskDesc("邀请的新主播收入达到" + config.getCheckParams() + "钻石")
//                    .setTargetAmount(Long.valueOf(config.getCheckParams()))
//                    .setRewardGold(config.getRewards().get(0).getNums()) // 假设第一个奖励是金币
//                    .setRewardDesc(config.getRewards().get(0).getNums() + "金币");
//
//            // 设置任务状态
//            if (incomeAmount >= config.getCheckParams()) {
//                taskVO.setStatus(1); // 已完成未领取，实际项目中需根据领取状态确定
//            } else {
//                taskVO.setStatus(0); // 未完成
//            }
//
//            tasks.add(taskVO);
//        }
//
//        vo.setWeeklyNewHostIncomeTasks(tasks);
//    }
//
//    /**
//     * 填充上周榜单信息
//     * @param vo 返回VO
//     */
//    private void fillLastWeekRankInfo(PageInitVO vo) {
//        // 获取上周周索引
//        int lastWeekOfYear = DATE_SUPPORT.getWeekOfYearByWeekOffset(-1);
//        String lastWeekIndex = lastWeekOfYear + "";
//
//        // 获取上周邀请人数榜单Top1
//        String inviteCountRankKey = weeklyInviteCountRankKey(lastWeekIndex);
//        List<CountVO> inviteTop1s = baseZSetRedis.getRange(inviteCountRankKey, 1, 1);
//        List<RankRowVO> inviteTopRowVos = eventBaseRankService.fillRankRowList(inviteTop1s);
//        if (!ObjectUtils.isEmpty(inviteTopRowVos)) {
//            vo.setLastWeekInviteCountTop1(inviteTopRowVos.get(0));
//        }
//
//        // 获取上周新用户充值榜单Top1
//        String rechargeRankKey = weeklyNewUserRechargeRankKey(lastWeekIndex);
//        List<CountVO> rechargeTop1s = baseZSetRedis.getRange(rechargeRankKey, 1, 1);
//        List<RankRowVO> rechargeTopRowVos = eventBaseRankService.fillRankRowList(rechargeTop1s);
//        if (!ObjectUtils.isEmpty(rechargeTopRowVos)) {
//            vo.setLastWeekRechargeTop1(rechargeTopRowVos.get(0));
//        }
//
//        // 获取上周新主播收入榜单Top1
//        String incomeRankKey = weeklyNewHostIncomeRankKey(lastWeekIndex);
//        List<CountVO> incomeTop1s = baseZSetRedis.getRange(incomeRankKey, 1, 1);
//        List<RankRowVO> incomeTopRowVos = eventBaseRankService.fillRankRowList(incomeTop1s);
//        if (!ObjectUtils.isEmpty(incomeTopRowVos)) {
//            vo.setLastWeekHostIncomeTop1(incomeTopRowVos.get(0));
//        }
//    }

    /**
     * 查询排行榜
     * @param dto 请求DTO
     * @return 榜单VO
     */
    public ModelRankVO<RankRowVO> queryRank(QueryRankDTO dto) {
        // 获取当前用户
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());

        // 获取活动配置
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());

        // 检查渠道是否支持
        if (!appConfigActivityService.isWithInChannelSet(configData, currActor)) {
            throw new WebException(HttpCode.CHANNEL_NOT_JOIN_EVENT);
        }

        // 获取周索引
        int weekOfYear = DATE_SUPPORT.getWeekOfYearByWeekOffset(dto.getWeekOffset());
        String weekIndex = weekOfYear + "";

        // 根据榜单类型获取对应的Redis key
        String rankKey;
        switch (dto.getRankType()) {
            case 1: // 邀请人数榜
                rankKey = weeklyInviteCountRankKey(weekIndex);
                break;
            case 2: // 新用户充值榜
                rankKey = weeklyNewUserRechargeRankKey(weekIndex);
                break;
            case 3: // 新主播收入榜
                rankKey = weeklyNewHostIncomeRankKey(weekIndex);
                break;
            default:
                throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "Invalid rankType"));
        }

        // 查询榜单
        return eventBaseRankService.rank(currActor, rankKey);
    }

    /**
     * 邀请人数榜单奖励结算
     * @return 是否成功
     */
    public boolean inviteCountRankRewards(int weekOffset) {
        // 获取活动配置
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);

        // 获取当前周索引
        int weekOfYear = DATE_HELPER.getWeekOfYear(weekOffset);
        String weekIndex = weekOfYear + "";

        // 获取榜单Redis key
        String rankKey = weeklyInviteCountRankKey(weekIndex);

        // 结算奖励
        String rankName = "Weekly Invitation Ranking";
        rankRewards(configData, rankKey, INVITE_COUNT_RANK_REWARDS, rankName, "4", INVITE_RANK_FORMAT);
        return true;
    }

    /**
     * 新用户充值榜单奖励结算
     * @return 是否成功
     */
    public boolean newUserRechargeRankRewards(int weekOffset) {
        // 获取活动配置
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);

        // 获取当前周索引
        int weekOfYear = DATE_HELPER.getWeekOfYear(weekOffset);
        String weekIndex = weekOfYear + "";

        // 获取榜单Redis key
        String rankKey = weeklyNewUserRechargeRankKey(weekIndex);

        // 结算奖励
        String rankName = "Weekly New User Recharge Ranking";
        rankRewards(configData, rankKey, NEW_USER_RECHARGE_RANK_REWARDS, rankName, "5", RECHARGE_RANK_FORMAT);

        return true;
    }

    /**
     * 新主播收入榜单奖励结算
     * @return 是否成功
     */
    public boolean newHostIncomeRankRewards(int weekOffset) {
        // 获取活动配置
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);

        // 获取当前周索引
        int weekOfYear = DATE_HELPER.getWeekOfYear(weekOffset);
        String weekIndex = weekOfYear + "";

        // 获取榜单Redis key
        String rankKey = weeklyNewHostIncomeRankKey(weekIndex);

        // 结算奖励
        String rankName = "Weekly New Host Income Ranking";
        rankRewards(configData, rankKey, NEW_HOST_INCOME_RANK_REWARDS, rankName, "6", NEW_HOST_INCOME_RANK_FORMAT);
        return true;
    }

    /**
     * 榜单奖励发放
     *
     * @param configData    活动配置
     * @param rankKey       榜单Redis key
     * @param rewardConfigs 奖励配置
     * @param rankName      榜单名称
     * @param formatter     通知格式
     */
    private void rankRewards(AppConfigActivityData configData, String rankKey, List<RewardTaskConfig> rewardConfigs, String rankName, String changeDesc, String formatter) {
        String globalNoticeFormatter = formatter
                .replace("#eventName", configData.getName())
                .replace("#top", rewardConfigs.size() + "")
                .replace("#rankName", rankName);

        eventBaseRankService.rankRewards(configData, rankKey, rewardConfigs, rankName, globalNoticeFormatter,
                changeDesc, NOTICE_IMG, EVENT_URL);
    }

    // =================== 数据更新方法 ===================

    /**
     * 活动通用处理方法
     * @param uid 用户ID
     * @param specificAction 特定操作的回调函数
     */
    private void processActivityAction(String uid, Consumer<String> specificAction) {
        // 获取当前用户
        ActorData currActor = actorMgr.getCurrActorData(uid);

        // 获取活动配置
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        if (configData.getStartTime() > DateHelper.getCurrTime()) {
            return;
        }
        // 检查渠道是否支持
        if (!appConfigActivityService.isWithInChannelSet(configData, currActor)) {
            return;
        }

        // 灰度测试检查
        boolean canJoin = grayTestSupport.shouldAllowAccess(USE_TEST, uid);
        if (!canJoin) {
            return;
        }

        // 获取当前周索引
        int weekOfYear = DATE_HELPER.getWeekOfYear(0);
        String weekIndex = weekOfYear + "";

        // 执行特定操作
        specificAction.accept(weekIndex);
    }

    /**
     * 增加每日邀请人数
     * 仅演示用，实际项目中应该由MQ触发或其他模块调用
     * @param uid 邀请人ID
     * @param date 日期 yyyy-MM-dd
     * @param count 邀请人数
     */
    public void increaseInviteCount(String uid, String date, int count) {
        processActivityAction(uid, weekIndex -> dealInviteCount(uid, date, count, weekIndex));
    }

    /**
     * 增加新用户充值金额
     * 仅演示用，实际项目中应该由MQ触发或其他模块调用
     * @param inviterUid 邀请人ID
     * @param rechargeAmount 充值金额
     */
    public void increaseNewUserRechargeAmount(String inviterUid, double rechargeAmount) {
        processActivityAction(inviterUid, weekIndex -> dealNewUserRechargeAmount(inviterUid, rechargeAmount, weekIndex));
    }

    /**
     * 增加新主播收入金额
     * 仅演示用，实际项目中应该由MQ触发或其他模块调用
     * @param inviterUid 邀请人ID
     * @param incomeAmount 收入金额
     */
    public void increaseNewHostIncomeAmount(String inviterUid, double incomeAmount) {
        processActivityAction(inviterUid, weekIndex -> dealNewHostIncomeAmount(inviterUid, incomeAmount, weekIndex));
    }

    private void dealInviteCount(String uid, String date, int count, String weekIndex) {
        // 增加每日邀请人数
        String dailyKey = dailyInviteCountKey(date, uid);
        List<RewardTaskConfig> configs = baseNodeRewardRedis.increaseAndGetCanGetTaskConfig(dailyKey, uid, count, DAILY_INVITE_COUNT_TASKS);
        if (!ObjectUtils.isEmpty(configs)) {
            configs.forEach(config -> {
                giveOutRewardService.giveEventReward(uid, config.getRewards(), EVENT_CODE, "1", config.getTaskName());
                // 发送官方通知
                officialNoticeService.sendOfficialNotice(uid, "Congratulations", config.getNotice(), NOTICE_IMG, EVENT_URL, actorMgr.getCurrActorData(uid).getChannel(),
                        DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50), EVENT_CODE);
            });
        }

        // 增加周榜单的分数
        baseZSetRedis.increaseToZSet(weeklyInviteCountRankKey(weekIndex), uid, count);
    }

    private void dealNewUserRechargeAmount(String inviterUid, double rechargeAmount, String weekIndex) {
        // 增加周充值总额
        String rechargeKey = weeklyNewUserRechargeAmountKey(weekIndex, inviterUid);
        List<RewardTaskConfig> configs = baseNodeRewardRedis.increaseAndGetCanGetTaskConfig(rechargeKey, inviterUid, rechargeAmount, WEEKLY_NEW_USER_RECHARGE_TASKS);
        if (!ObjectUtils.isEmpty(configs)) {
            configs.forEach(config -> {
                giveOutRewardService.giveEventReward(inviterUid, config.getRewards(), EVENT_CODE, "2", config.getTaskName());
                // 发送官方通知
                officialNoticeService.sendOfficialNotice(inviterUid, "Congratulations", config.getNotice(), NOTICE_IMG, EVENT_URL, actorMgr.getCurrActorData(inviterUid).getChannel(),
                        DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50), EVENT_CODE);
            });
        }

        // 增加周榜单的分数
        baseZSetRedis.increaseToZSet(weeklyNewUserRechargeRankKey(weekIndex), inviterUid, rechargeAmount);
    }

    private void dealNewHostIncomeAmount(String inviterUid, double incomeAmount, String weekIndex) {
        // 增加周收入总额
        String incomeKey = weeklyNewHostIncomeAmountKey(weekIndex, inviterUid);
        List<RewardTaskConfig> configs = baseNodeRewardRedis.increaseAndGetCanGetTaskConfig(incomeKey, inviterUid, incomeAmount, WEEKLY_NEW_HOST_INCOME_TASKS);
        if (!ObjectUtils.isEmpty(configs)) {
            configs.forEach(config -> {
                giveOutRewardService.giveEventReward(inviterUid, config.getRewards(), EVENT_CODE, "3", config.getTaskName());
                // 发送官方通知
                officialNoticeService.sendOfficialNotice(inviterUid, "Congratulations", config.getNotice(), NOTICE_IMG, EVENT_URL, actorMgr.getCurrActorData(inviterUid).getChannel(),
                        DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50), EVENT_CODE);
            });
        }

        // 增加周榜单的分数
        baseZSetRedis.increaseToZSet(weeklyNewHostIncomeRankKey(weekIndex), inviterUid, incomeAmount);
    }

    public void inviteUserSuccessAction(InviteUserSuccessMsgData mqData) {
        if (StringUtils.isEmpty(mqData.getInviterUid()) || StringUtils.isEmpty(mqData.getInvitedUid())) {
            return;
        }
        increaseInviteCount(mqData.getInviterUid(), DATE_HELPER.getDateByDeltaDay(0), 1);
    }

    public void moneyDetailAction(MoneyDetailData mqData) {
        if (mqData.getSingleAction() != MoneyChangeActionConstant.ACTION_ADD) {
            return;
        }
        if (!CurrencyEnum.CURRENCY1.getCurrencyCode().equals(mqData.getCurrencyCode())) {
            return;
        }
        if (mqData.getSingleChange() == null || mqData.getSingleChange() < 1) {
            return;
        }
        if (!ACT_TYPE_SET.contains(mqData.getActType())) {
            return;
        }
        InviteRelationData inviteData = inviteDBService.queryValidRelationRecordByInvitedUid(mqData.getUserid());
        if (inviteData == null || inviteData.getCtime() < DATE_HELPER.getWeekStartAndEndTime(0).getTime()) {
            return;
        }
        increaseNewUserRechargeAmount(inviteData.getUid(),mqData.fetchRealSingleChange());
    }

    public void sendGiftAction(SendGiftSuccessMsgData mqData) {
        ActorData currActor = actorMgr.getCurrActorData(mqData.getToUid());
        if (!GenderTypeEnum.HOST.getType().equals(currActor.getGender())) {
            return;
        }
        InviteRelationData inviteData = inviteDBService.queryValidRelationRecordByInvitedUid(mqData.getToUid());
        if (inviteData == null || inviteData.getCtime() < DATE_HELPER.getWeekStartAndEndTime(0).getTime()) {
            return;
        }
        increaseNewHostIncomeAmount(inviteData.getUid(), mqData.fetchRealGain());
    }
}
