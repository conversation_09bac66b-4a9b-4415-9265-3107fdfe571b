package com.quhong.service;

import com.quhong.common.data.ApiResult;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.NoticeBodyType;
import com.quhong.constant.OfficialNoticeActConstant;
import com.quhong.constant.OfficialUserGroup;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.enums.ActorType;
import com.quhong.core.enums.ClientSystemType;
import com.quhong.core.enums.RoomOwnerStatus;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.*;
import com.quhong.dao.data.ActorNoticeReadRecordsData;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.ActorExternalData;
import com.quhong.dao.datas.OfficialNotice;
import com.quhong.dao.datas.RoomData;
import com.quhong.dao.datas.db.GameConfigInfoData;
import com.quhong.data.JumpDetailData;
import com.quhong.data.OfficialData;
import com.quhong.data.dto.OfficialListGetDTO;
import com.quhong.data.vo.OfficialListGetVO;
import com.quhong.data.vo.OfficialQueryUnreadVO;
import com.quhong.enums.RoomType;
import com.quhong.exceptions.WebException;
import com.quhong.players.ActorMgr;
import com.quhong.utils.MessageUtils;
import com.quhong.utils.ParamsListUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName MessageOfficialService
 * <AUTHOR>
 * @date 2022/9/26 20:24
 */
@Service
public class MessageOfficialService {
    private static final Logger logger = LoggerFactory.getLogger(MessageOfficialService.class);

    @Autowired
    private OfficialNoticeDao officialNoticeDao;
    @Autowired
    private CdnUtils cdnUtils;
    @Autowired
    private ActorMgr actorMgr;
    @Autowired
    private GameConfigInfoDao gameConfigInfoDao;
    @Autowired
    private RoomDao roomDao;
    @Autowired
    private ActorNoticeReadRecordsDao actorNoticeReadRecordsDao;
    @Autowired
    private ActorExternalDao actorExternalDao;

    private static String OFFICIAL_UID;

    public MessageOfficialService() {
        if (ServerConfiguration.isTest()) {
            OFFICIAL_UID = "5f48e4c585676708d5807c4e";
        } else {
            OFFICIAL_UID = "64a7816f9e65082cbefaa061";
        }
    }


    public ApiResult<OfficialListGetVO> getList(OfficialListGetDTO dto) {
        OfficialListGetVO vo = new OfficialListGetVO();
        if (dto.getPage() == 1) {
            //清除未读
            officialNoticeDao.clearUnread(dto.getUid());
            // 更新全局通知的进度
            actorNoticeReadRecordsDao.updateReadTime(dto.getUid(), DateHelper.getCurrentTime());
        }
        ActorData actorData = actorMgr.getActorData(dto.getUid());
        if (actorData == null) {
            return ApiResult.getError(HttpCode.USER_NOT_EXISTS);
        }
        boolean containRich = containRichText(actorData);
        List<OfficialNotice> listByUid = selectNoticeList(dto, actorData);
        List<OfficialData> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(listByUid)) {
            for (OfficialNotice officialNotice : listByUid) {
                OfficialData data = new OfficialData();
                data.setTitle(officialNotice.getTitle());
                data.setPicture(StringUtils.isEmpty(officialNotice.getPicture()) ? "" : cdnUtils.replaceUrlDomain(actorData.getChannel(), officialNotice.getPicture(), 0));
                if (containRich && officialNotice.getBodyType() == NoticeBodyType.RICH_TEXT) {
                    // 富文本
                    data.setTranslateTitle(StringUtils.isEmpty(officialNotice.getTranslateTitle()) ? officialNotice.getTitle() : officialNotice.getTranslateTitle());
                    data.setBody(officialNotice.getRichBody());
                    data.setRichText(1);
                } else {
                    data.setBody(officialNotice.getBody());
                    data.setTranslateTitle(StringUtils.isEmpty(officialNotice.getTranslateTitle()) ? officialNotice.getTitle() : officialNotice.getTranslateTitle());
                    data.setTranslateBody(StringUtils.isEmpty(officialNotice.getTranslateBody()) ? officialNotice.getBody() : officialNotice.getTranslateBody());
                    data.setRichText(0);
                }
                data.setMtime(officialNotice.getMtime());
                data.setUrl(officialNotice.getUrl());
                data.setCtime(officialNotice.getCtime());
                dealJumpAct(officialNotice, data, actorData);
                resultList.add(data);
            }
        }
        if (MessageUtils.isHostClientChannel(dto.getChannel())) {
            //主播端 返回的为json  用户端走协议
            dto.setProto(1);
        }
        vo.setList(resultList);
        vo.setNextPage(false);
        return ApiResult.getOk(vo);
    }

    private boolean containRichText(ActorData actorData) {
        if (actorData.getPlatform() == ClientSystemType.ANDRIOD) {
            if (actorData.getVer() >= 690) {
                return true;
            }
        }
        return false;
    }

    private List<OfficialNotice> selectNoticeList(OfficialListGetDTO dto, ActorData actorData) {
        //获取所有官方消息
        List<String> channelList = new ArrayList<>();
        channelList.add(actorData.getChannel());
        channelList.add("");
        List<OfficialNotice> listByUid = officialNoticeDao.getListByUid(dto.getUid(), channelList);
        ActorExternalData externalData = actorExternalDao.getData(dto.getUid());
        List<Integer> userGroupList = getUserGroupList(actorData);
        List<OfficialNotice> globalList = officialNoticeDao.getGlobalList(dto.getChannel(), getLanguage(externalData.getClientSysLang()), userGroupList, actorData.getRegisterTime(), actorData.getCountryCode());
        return Stream.of(listByUid, globalList).flatMap(Collection::stream).sorted(((o1, o2) -> o2.getCtime() - o1.getCtime())).collect(Collectors.toList());
    }


    private String getLanguage(String sysLang) {
        switch (sysLang) {
            case "en":
                break;
            case "ar":
                break;
            case "es":
                break;
            default:
                sysLang = "en";
                break;
        }
        return sysLang;
    }

    private void dealJumpAct(OfficialNotice officialNotice, OfficialData data, ActorData actorData) {
        String act = officialNotice.getAct();
        if (StringUtils.isEmpty(act)) {
            return;
        }
        if (!ParamsListUtils.isInteger(act) && !act.contains(",")) {
            return;
        }
        if (!act.contains(",")) {
            data.setJumpAct(act);
        }
        String[] split = act.split(",");
        data.setJumpAct(split[0]);
        switch (Integer.parseInt(split[0])) {
            case OfficialNoticeActConstant.SUPPORT_MESSAGE_PAGE:
                data.setJumpDetailData(getJumpOfficialDetail());
                break;
            case OfficialNoticeActConstant.GAME_PAGE:
                data.setJumpDetailData(getJumpGameDetail(act));
                break;
            case OfficialNoticeActConstant.RID_PAGE:
                data.setJumpDetailData(getJumpRoomDetail(act, RoomType.LIVE, actorData));
                break;
            case OfficialNoticeActConstant.PARTY_RID_PAGE:
                data.setJumpDetailData(getJumpRoomDetail(act, RoomType.CHAT, actorData));
                break;
        }
    }

    private JumpDetailData getJumpOfficialDetail() {
        JumpDetailData jumpDetailData = new JumpDetailData();
        jumpDetailData.setRoomOwnerUid(OFFICIAL_UID);
        jumpDetailData.setJumpRoomType(2);
        return jumpDetailData;
    }

    private JumpDetailData getJumpRoomDetail(String act, int roomType, ActorData queryActor) {
        String[] split = act.split(",");
        String uid = split[1];
        ActorData actorData = actorMgr.getActorData(uid);
        if (actorData == null) {
            return null;
        }
        JumpDetailData jumpDetailData = new JumpDetailData();
        String roomId = RoomUtils.formatRoomId(uid, roomType);
        RoomData roomData = roomDao.getRoomData(roomId);
        if (roomData == null) {
            jumpDetailData.setRoomOwnerUid(uid);
            jumpDetailData.setJumpRoomType(2);
        } else {
            jumpDetailData.setRoomId(roomId);
            jumpDetailData.setOwnerHead(cdnUtils.replacePrivateHeadAndUrlDomain(queryActor.getChannel(), actorData.getHeadIcon(), 0, actorData.getUid()));
            jumpDetailData.setOwnerName(actorData.getName());
            jumpDetailData.setRoomType(roomData.getRoomType());
            jumpDetailData.setRoomOwnerUid(uid);
            if (roomData.getGameRelatedId() != null && roomData.getGameRelatedId() > 0) {
                GameConfigInfoData gameConfigInfoData = gameConfigInfoDao.getGameConfigInfoByRelatedId(roomData.getGameRelatedId());
                if (gameConfigInfoData != null) {
                    jumpDetailData.setGameRelatedId(roomData.getGameRelatedId());
                    jumpDetailData.setGameId(gameConfigInfoData.getGameId());
                    jumpDetailData.setGameType(gameConfigInfoData.getGameType());
                    jumpDetailData.setGameName(gameConfigInfoData.getGameName());
                }
            }
            jumpDetailData.setJumpRoomType(roomData.getOwnerStatus() == RoomOwnerStatus.ENTER ? 1 : 2);
        }
        return jumpDetailData;
    }

    private JumpDetailData getJumpGameDetail(String act) {
        String[] split = act.split(",");
        int gameType = Integer.parseInt(split[1]);
        String gameId = split[2];
        GameConfigInfoData gameConfigInfoData = gameConfigInfoDao.getGameConfigInfoByTypeAndId(gameType, gameId);
        if (gameConfigInfoData == null) {
            return new JumpDetailData();
        }
        JumpDetailData jumpDetailData = new JumpDetailData();
        jumpDetailData.setGameType(gameType);
        jumpDetailData.setGameId(gameId);
        jumpDetailData.setGameName(gameConfigInfoData.getGameName());
        return jumpDetailData;
    }

    public OfficialQueryUnreadVO queryUnread(HttpEnvData dto) {
        if (StringUtils.isEmpty(dto.getUid())) {
            throw new WebException(dto, HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorMgr.getActorData(dto.getUid());
        if (actorData == null) {
            throw new WebException(dto, HttpCode.USER_NOT_EXISTS);
        }
        OfficialQueryUnreadVO vo = new OfficialQueryUnreadVO();
        List<String> channelList = new ArrayList<>();
        channelList.add(actorData.getChannel());
        channelList.add("");
        long globalUnreadTime = 0;
        ActorNoticeReadRecordsData recordsData = actorNoticeReadRecordsDao.getData(dto.getUid());
        if (recordsData != null) {
            globalUnreadTime = recordsData.getReadTime();
        } else {
            globalUnreadTime = actorData.getRegisterTime();
        }
        List<Integer> userGroupList = getUserGroupList(actorData);
        ActorExternalData externalData = actorExternalDao.getData(dto.getUid());
        List<OfficialNotice> globalOfficialList = officialNoticeDao.getGlobalList(dto.getChannel(), getLanguage(externalData.getClientSysLang()), userGroupList, globalUnreadTime, actorData.getCountryCode());
        List<OfficialNotice> unreadOfficialNotice = officialNoticeDao.getUnreadOfficialNotice(actorData.getUid(), channelList);
        vo.setUnread(globalOfficialList.size() + unreadOfficialNotice.size());
        OfficialNotice latestNotice = null;
        if (!CollectionUtils.isEmpty(unreadOfficialNotice)) {
            unreadOfficialNotice.sort(Comparator.comparing(OfficialNotice::getCtime).reversed());
            latestNotice = unreadOfficialNotice.get(0);
        }
        if (globalOfficialList.size() > 0) {
            if (latestNotice != null) {
                if (latestNotice.getCtime() < globalOfficialList.get(0).getCtime()) {
                    latestNotice = globalOfficialList.get(0);
                }
            } else {
                latestNotice = globalOfficialList.get(0);
            }
        }
        if (latestNotice != null) {
            vo.setLastDesc(latestNotice.getBody());
            vo.setLastTitle(latestNotice.getTitle());
            vo.setLastTime(latestNotice.getCtime());
        }
        return vo;
    }

    public OfficialQueryUnreadVO queryLatestOfficialData(ActorData actorData) {
        OfficialQueryUnreadVO vo = new OfficialQueryUnreadVO();
        List<String> channelList = new ArrayList<>();
        channelList.add(actorData.getChannel());
        channelList.add("");
        long globalUnreadTime = 0;
        ActorNoticeReadRecordsData recordsData = actorNoticeReadRecordsDao.getData(actorData.getUid());
        if (recordsData != null) {
            globalUnreadTime = recordsData.getReadTime();
        } else {
            globalUnreadTime = actorData.getRegisterTime();
        }
        List<Integer> userGroupList = getUserGroupList(actorData);
        ActorExternalData externalData = actorExternalDao.getData(actorData.getUid());
        List<OfficialNotice> globalNoticeList = officialNoticeDao.getGlobalList(actorData.getChannel(), getLanguage(externalData.getClientSysLang()), userGroupList, globalUnreadTime, actorData.getCountryCode());
        List<OfficialNotice> unreadOfficialNotice = officialNoticeDao.getUnreadOfficialNotice(actorData.getUid(), channelList);
        vo.setUnread(globalNoticeList.size() + unreadOfficialNotice.size());
        OfficialNotice latestNotice = null;
        if (!CollectionUtils.isEmpty(unreadOfficialNotice)) {
            unreadOfficialNotice.sort(Comparator.comparing(OfficialNotice::getCtime).reversed());
            latestNotice = unreadOfficialNotice.get(0);
        } else {
            latestNotice = officialNoticeDao.getLatestOfficialNotice(actorData.getUid(), channelList);
        }
        OfficialNotice globalNotice = null;
        if (!globalNoticeList.isEmpty()) {
            globalNotice = globalNoticeList.get(0);
        } else {
            globalNotice = officialNoticeDao.getLatestGlobalData(actorData.getChannel(), getLanguage(externalData.getClientSysLang()), userGroupList, actorData.getRegisterTime(), actorData.getCountryCode());
        }
        if (globalNotice != null) {
            if (latestNotice != null) {
                if (latestNotice.getCtime() < globalNotice.getCtime()) {
                    latestNotice = globalNotice;
                }
            } else {
                latestNotice = globalNotice;
            }
        }
        if (latestNotice != null) {
            vo.setLastDesc(latestNotice.getBody());
            vo.setLastTitle(latestNotice.getTitle());
            vo.setLastTime(latestNotice.getCtime());
        }
        return vo;
    }

    private List<Integer> getUserGroupList(ActorData actorData) {
        List<Integer> userGroupList = new ArrayList<>();
        userGroupList.add(OfficialUserGroup.CHANNEL);
        if (actorData.getGender() == ActorType.HOST) {
            userGroupList.add(OfficialUserGroup.CHANNEL_HOST);
        } else {
            userGroupList.add(OfficialUserGroup.CHANNEL_USER);
        }
        return userGroupList;
    }
}
