package com.quhong.service;

import com.quhong.common.controllers.ProtoController;
import com.quhong.common.data.ApiResult;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.utils.CdnUtils;
import com.quhong.common.utils.RequestUtils;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.enums.PythonMsgType;
import com.quhong.dao.ActorExternalDao;
import com.quhong.dao.FollowDao;
import com.quhong.dao.HostBlockUserDao;
import com.quhong.dao.MsgListDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.data.HostPoolData;
import com.quhong.data.http.RspUserListData;
import com.quhong.data.http.SuggestedData;
import com.quhong.data.vo.SuggestListVO;
import com.quhong.data.vo.UserListVO;
import com.quhong.enums.SuggestedEnum;
import com.quhong.pageList.homePage.pools.HomePageMgr;
import com.quhong.players.ActorMgr;
import com.quhong.redis.PlayerRedis;
import com.quhong.utils.VideoUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

import static com.quhong.constant.RedisKeyConstant.ACTOR_ACTIVE_RECORD_HASH;
import static com.quhong.core.enums.ActorType.HOST;
import static com.quhong.enums.PlayerStatus.ONLINE;

/**
 * @ClassName SuggestedListService
 * <AUTHOR> <EMAIL>
 * @Version 1.0
 **/
@Component
public class SuggestedListService extends ProtoController {
    private static final Logger logger = LoggerFactory.getLogger(SuggestedListService.class);

    @Autowired
    private ActorMgr actorMgr;

    @Autowired
    private HostBlockUserDao hostBlockUserDao;

    @Autowired
    private FollowDao followDao;

    @Autowired
    private MsgListDao msgListDao;

    @Autowired
    private PlayerRedis playerRedis;

    @Autowired
    private HomePageMgr homePageMgr;

    @Autowired
    private CdnUtils cdnUtils;

    @Resource
    private ListService listService;

    @Resource(name = BaseRedisBeanConstant.OTHER_BEAN)
    protected StringRedisTemplate otherRedisTemplate;

    @Resource
    private ActorExternalDao actorExternalDao;

    private static final int PAGE_SIZE = 3;

    private static final int CHAT_TYPE = 1;
    private static final int GIFT_TYPE = 21;
    private static final int CALL_TYPE = 22;

    public ApiResult<SuggestListVO> querySuggestList(HttpEnvData envData, ActorData actorData, HttpServletRequest request) {
        String uid = actorData.getUid();
        int page = RequestUtils.getParamInt(request, "page");
        boolean isAdmin = actorMgr.isAdmin(actorData.getUid());

        // 客户端page从1开始
        page = page - 1;
        if (page < 0) {
            page = 0;
        }
        if (isAdmin) {
            logger.info("can not find the page. page={} isAdmin={} uid={} requestId={}", page, isAdmin, uid, envData.getRequestId());
            return getEmptyResult(envData);
        }
        List<SuggestedData> hostList = new ArrayList<>();
        int start = page * PAGE_SIZE;
        setListStatus(hostList);
        int end = (page + 1) * PAGE_SIZE;
        boolean nextPage = true;
        if (end >= hostList.size()) {
            end = hostList.size();
            nextPage = false;
        }
        List<String> hostUidList = new ArrayList<>();
        List<SuggestedData> retList = getSuggestListMap(uid, page, hostUidList);
        int count = 0;
        if (end > hostList.size()) {
            start = 0;
            nextPage = false;
        }
        end = hostList.size();
        for (count = start; count < hostList.size(); count++) {
            if (count >= end) {
                break;
            } else if (count < start) {
                continue;
            }
            SuggestedData hostPoolData = hostList.get(count);
            if (hostBlockUserDao.isBlock(uid, hostPoolData.getUid())) {
                continue;
            }
            if (actorExternalDao.isTester(hostPoolData.getUid())) {
                logger.info("suggested host failed. actor uid={} isTest", hostPoolData.getUid());
                continue;
            }
            SuggestedData resultData = new SuggestedData();
            resultData.copyFrom(hostPoolData);
            if (retList.contains(resultData) || hostUidList.contains(resultData.getUid())) {
                continue;
            }
            if (StringUtils.isEmpty(resultData.getHead()) || resultData.getHead().contains("_default")) {
                continue;
            }

            if (resultData.getStatus() != ONLINE) {
                continue;
            }
            hostUidList.add(resultData.getUid());
            retList.add(resultData);
            if (retList.size() == 3) {
                break;
            }
        }
        if (count != 0) {
            end = count;
        }
        if (retList.size() < 3) {
            nextPage = false;
            patchSuggestList(envData, hostList, uid, hostUidList, retList);
            logger.info("suggested list.get host success. and get list less 3 next page false. uid={} requestId={}", uid, envData.getRequestId());
        }
//        logger.info("suggested list success. onlineHost.size={}, retList.size={} end={} uid={} requestId={}", hostList.size(), retList.size(), end, uid, envData.getRequestId());
        SuggestListVO vo = new SuggestListVO();
        vo.setList(retList);
        vo.setNext_page(nextPage);
        return ApiResult.getOk(vo);
    }

    private void patchSuggestList(HttpEnvData envData, List<SuggestedData> hostList, String uid, List<String> hostUidList, List<SuggestedData> retList) {
        logger.info("suggested list.get host success host list size={} host uid list={} requestId={}", hostList.size(), hostUidList, envData.getRequestId());
        for (int count = 0; count < hostList.size(); count++) {
            SuggestedData hostPoolData = hostList.get(count);
            if (hostBlockUserDao.isBlock(uid, hostPoolData.getUid())) {
                logger.error("isBlock(uid, hostPoolData.getUid()) uid={} requestId={}", hostPoolData.getUid(), envData.getRequestId());
                continue;
            }
            if (actorExternalDao.isTester(hostPoolData.getUid())) {
                logger.info("suggested host failed. actor uid={} isTest. requestId+{}", hostPoolData.getUid(), envData.getRequestId());
                continue;
            }
            SuggestedData resultData = new SuggestedData();
            resultData.copyFrom(hostPoolData);
            if (hostUidList.contains(resultData.getUid())) {
                logger.error("suggested list.get host retList.contains(resultData) || hostUidList.contains(resultData.getUid()) uid={} requestId={}", hostPoolData.getUid(), envData.getRequestId());
                continue;
            }
            if (StringUtils.isEmpty(resultData.getHead()) || resultData.getHead().contains("_default")) {

                logger.error("suggested list.get host StringUtils.isEmpty(resultData.getHead()) || resultData.getHead().contains(kissu_default) uid={} requestId={}", hostPoolData.getUid(), envData.getRequestId());
                continue;
            }
            if (resultData.getStatus() != ONLINE) {
                continue;
            }
//            logger.info("suggested list.get host success uid={} requestId={}", hostPoolData.getUid(), envData.getRequestId());
            hostUidList.add(resultData.getUid());
            retList.add(resultData);
            if (retList.size() == 3) {
                break;
            }
        }
    }

    private void setListStatus(List<SuggestedData> hostList) {
        for (SuggestedData data : hostList) {
            data.setStatus(playerRedis.getStatus(data.getUid()));
        }
    }

    public ApiResult<SuggestListVO> getEmptyResult(HttpEnvData envData) {
        SuggestListVO vo = new SuggestListVO();
        vo.setList(new ArrayList<>());
        vo.setNext_page(false);
        return ApiResult.getOk(vo);
    }

    public ApiResult<UserListVO> getEmptyUserResult(HttpEnvData envData) {
        UserListVO vo = new UserListVO();
        vo.setList(new ArrayList<>());
        vo.setNext_page(false);
        return ApiResult.getOk(vo);
    }

    private List<SuggestedData> getSuggestListMap(String uid, int page, List<String> hostUidList) {
        List<SuggestedData> result = new ArrayList<>();
        HashMap<String, List<String>> listHashMap = new HashMap<>();
        listHashMap.put("follow", getFollowList(uid));
        listHashMap.put("call", new ArrayList<>());
        listHashMap.put("chat", new ArrayList<>());
        listHashMap.put("gift", new ArrayList<>());
        getMsgList(uid, listHashMap);

        int random = ThreadLocalRandom.current().nextInt(4);

        switch (random) {
            case 1:
                addSuggestedData(result, listHashMap, "follow", page, hostUidList);
                break;
            case 2:
                addSuggestedData(result, listHashMap, "call", page, hostUidList);
                break;
            case 3:
                addSuggestedData(result, listHashMap, "chat", page, hostUidList);
                break;
            case 4:
            default:
                addSuggestedData(result, listHashMap, "gift", page, hostUidList);
                break;
        }
        return result;
    }

    private void addSuggestedData(List<SuggestedData> suggestedList, HashMap<String, List<String>> suggestedMap, String ignore, int page, List<String> hostCountList) {
        Iterator<String> suggestedItems = suggestedMap.keySet().iterator();
        while (suggestedItems.hasNext()) {
            String key = suggestedItems.next();
            if (!ObjectUtils.isEmpty(suggestedMap.get(key)) && !ignore.equals(key)) {
                List<String> suggestItem = suggestedMap.get(key);
                if (!CollectionUtils.isEmpty(suggestItem) && suggestItem.size() >= page + 1) {
                    String hostUid = suggestItem.get(page);
                    int status = playerRedis.getStatus(hostUid);
                    if (status != ONLINE) {
                        continue;
                    }
                    ActorData actorData = actorMgr.getActorData(hostUid);
                    if (actorData.getGender() != HOST || hostCountList.contains(hostUid)) {
                        continue;
                    }
                    SuggestedData suggestedData = new SuggestedData();
                    suggestedData.setAge(actorData.getAge());
                    suggestedData.setGender(actorData.getGender());
                    suggestedData.setHead(actorData.getHeadIcon());
                    suggestedData.setName(actorData.getName());
                    suggestedData.setRid(actorData.getRid().intValue());
                    suggestedData.setUid(actorData.getUid());
                    suggestedData.setSuggestDesc(SuggestedEnum.getValueByName(key).getDesc());
                    suggestedData.setStatus(status);
                    suggestedData.setCountry(actorData.getCountry());
                    suggestedData.setCountryCode(actorData.getCountryCode());

                    hostCountList.add(hostUid);
                    suggestedList.add(suggestedData);
                }
            }
        }

    }

    private void getMsgList(String uid, HashMap<String, List<String>> listHashMap) {
        Map<Object, Object> activeMap = getActiveMap(uid);
        if (CollectionUtils.isEmpty(activeMap)) {
            return;
        } else {
            Iterator it = activeMap.keySet().iterator();
            while (it.hasNext()) {
                Object host = it.next();
                int type = Integer.parseInt(activeMap.get(host).toString());

                switch (type) {
                    case PythonMsgType.CHAT_MSG:
                        listHashMap.get("chat").add(host.toString());
                        continue;
                    case PythonMsgType.CHAT_REQUEST:
                        listHashMap.get("call").add(host.toString());
                        continue;
                    case PythonMsgType.GIFT:
                        listHashMap.get("gift").add(host.toString());
                        continue;
                }
            }
        }

    }

    public Map<Object, Object> getActiveMap(String uid) {
        return otherRedisTemplate.opsForHash().entries(ACTOR_ACTIVE_RECORD_HASH + uid);
    }

    private List<String> getFollowList(String uid) {
        return new ArrayList<>(followDao.getFollowSetByUid(uid));
    }

    public ApiResult<UserListVO> queryZeroPayFreeSuggestList(HttpEnvData envData, ActorData actorData, HttpServletRequest request) {
        String uid = actorData.getUid();
        int page = RequestUtils.getParamInt(request, "page");
        boolean isAdmin = actorMgr.isAdmin(actorData.getUid());

        if (isAdmin) {
            logger.info("query zero pay free fake host. can not find the page. is admin.page={} uid={} requestId={}", page, uid, envData.getRequestId());
            return getEmptyUserResult(envData);
        }
        List<HostPoolData> hostList = homePageMgr.getFreeCallHostList4ZeroPayUser();
        logger.info("query zero pay free fake host. get fake success uid={} host size={} requestId={}", uid, hostList.size(), envData.getRequestId());
        List<RspUserListData> retList = new ArrayList<>();
        for (int count = 0; count < hostList.size(); count++) {
            HostPoolData hostPoolData = hostList.get(count);
            if (hostBlockUserDao.isBlock(uid, hostPoolData.getUid())) {
                continue;
            }
            if (actorExternalDao.isTester(hostPoolData.getUid())) {
                logger.info("query zero pay free fake host. get failed. actor uid={} isTest. requestId={}", hostPoolData.getUid(), envData.getRequestId());
                continue;
            }
            RspUserListData resultData = new RspUserListData();
            //resultData.copyFrom(hostPoolData, actorData.getChannel(), envData.getLang());
            String clientSysLang = actorExternalDao.getClientSysLang(envData.getUid());
            resultData.copyFrom(hostPoolData, actorData.getChannel(), clientSysLang);
            listService.dealReplaceUrl(resultData, envData.getChannel());
            resultData.setRid(hostPoolData.getActorData().getRid());
            resultData.setCountryCode(hostPoolData.getActorData().getCountryCode());
            resultData.setVideo_url(cdnUtils.replaceUrlDomain(envData.getChannel(), hostPoolData.getVideoUrl(), 1));
            resultData.setFreeCallUrl(cdnUtils.replaceUrlDomain(envData.getChannel(), hostPoolData.getFreeCallUrl(), 1));
            resultData.setStatus(hostPoolData.getStatus());
            if (StringUtils.isEmpty(resultData.getHead()) || resultData.getHead().contains("_default")) {
                continue;
            }
            if (!StringUtils.isEmpty(resultData.getFreeCallUrl())) {
                resultData.setFreeCallUrl(VideoUtils.checkCompressUrl(resultData.getFreeCallUrl(), resultData.getVideoType(), envData.getChannel()));
                resultData.setFreeCallUrl(cdnUtils.replaceUrlDomain(envData.getChannel(), resultData.getFreeCallUrl(), 1));
            }
            if (!StringUtils.isEmpty(resultData.getVideo_url())) {
                resultData.setVideo_url(VideoUtils.checkCompressUrl(resultData.getVideo_url(), resultData.getVideoType(), envData.getChannel()));
                resultData.setVideo_url(cdnUtils.replaceUrlDomain(envData.getChannel(), resultData.getVideo_url(), 1));
            }
            retList.add(resultData);
        }
        UserListVO vo = new UserListVO();
        vo.setList(retList);
        vo.setNext_page(false);
        return ApiResult.getOk(vo);
    }
}
