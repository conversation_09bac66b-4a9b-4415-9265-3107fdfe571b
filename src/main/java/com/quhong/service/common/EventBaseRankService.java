package com.quhong.service.common;

import com.quhong.constant.LangIdConstant;
import com.quhong.constant.OfficialUserGroup;
import com.quhong.core.constant.WarnName;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.ActorInfo;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.enums.RewardItemType;
import com.quhong.moderatoin.ModerationService;
import com.quhong.players.ActorMgr;
import com.quhong.redis.BaseZSetRedis;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.OfficialNoticeService;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 活动榜单组件化
 *
 * <AUTHOR>
 * @since 2025/1/16 19:06
 */
@Lazy
@Slf4j
@Component
@RequiredArgsConstructor
public class EventBaseRankService {
    public static final String TEXT_FORMAT = "\uD83D\uDE01 Congratulations to the follows get Top#top in \"#eventName\" Event #rankName\n" +
            "#content" +
            "\uD83D\uDE01 The reward has been issued>>>>";

    public static final String KEY_PRE = "zset:event:rank:";

    private final BaseZSetRedis baseZSetRedis;
    private final ActorMgr actorMgr;
    private final ModerationService moderationService;
    private final GiveOutRewardService giveOutRewardService;
    private final OfficialNoticeService officialNoticeService;
    private final MonitorSender monitorSender;
    private final RewardService rewardService;

    public String zsetRankKey(@NonNull String... args) {
        String keySuffix = "";
        if (args.length > 0) {
            keySuffix = StringUtils.arrayToDelimitedString(args, ":");
        }
        return KEY_PRE + keySuffix;
    }

    /**
     * 榜单key
     */
    public String zsetRankKey(Integer eventCode, Integer rankType) {
        return KEY_PRE + eventCode + ":" + rankType;
    }

    public String zsetRankKey(Integer eventCode, String keySuffix) {
        return KEY_PRE + eventCode + ":" + keySuffix;
    }


    public void rankValueIncrease(String rankKey, String uid, double incr) {
        baseZSetRedis.increaseToZSet(rankKey, uid, incr);
    }

    /**
     * 获取榜单列表(只有简单的键值)
     */
    public List<CountVO> getRankList(String key, int page, int pageSize) {
        return baseZSetRedis.getRange(key, page, pageSize);
    }

    public CountVO getOne(String rankKey, String uid) {
        return baseZSetRedis.getOne(rankKey, uid);
    }

    public Long getRankNum(String rankKey, String uid) {
        return baseZSetRedis.getRank(rankKey, uid);
    }

    public RankRowVO findTop1(String key) {
        List<CountVO> top1s = baseZSetRedis.getRange(key, 1, 1);
        if (!ObjectUtils.isEmpty(top1s)) {
            List<RankRowVO> top1Rows = fillRankRowList(top1s);
            return top1Rows.get(0);
        }
        return new RankRowVO();
    }

    public ModelRankVO<RankRowVO> rank(ActorData currActor, String rankKey) {
        return rank(currActor, rankKey, "99+");
    }

    /**
     * 榜单接口
     *
     * @param rankKey        榜单key
     * @param defaultRankNum 榜单排名默认值
     */
    public ModelRankVO<RankRowVO> rank(ActorData currActor, String rankKey, String defaultRankNum) {
        List<RankRowVO> rankList = fillRankList(rankKey);
        RankRowVO self = fillSelfData(currActor, rankKey, defaultRankNum);
        return new ModelRankVO<>(self, rankList);
    }

    private RankRowVO fillSelfData(ActorData currActor, String rankKey, String defaultRankNum) {
        RankRowVO self = new RankRowVO();
        self.setRankNum(defaultRankNum);
        ActorInfo actorInfo = new ActorInfo(currActor);
        String head = moderationService.dealRankHeadModeration(currActor);
        actorInfo.setHead(head);
        self.setActorInfo(actorInfo);
        Long rank = baseZSetRedis.getRank(rankKey, currActor.getUid());
        if (rank != 0) {
            if (rank < 100) {
                self.setRankNum(rank.toString());
            }
            CountVO selfData = baseZSetRedis.getOne(rankKey, currActor.getUid());
            self.setScore(selfData.getCount());
            self.setRealScore(String.valueOf(selfData.fetchRealCount()));
        }
        return self;
    }

    private List<RankRowVO> fillRankList(String rankKey) {
        List<CountVO> dataList = baseZSetRedis.getRange(rankKey, 1, 10);
        return fillRankRowList(dataList);
    }

    public List<RankRowVO> fillRankRowList(List<CountVO> dataList) {
        if (dataList.isEmpty()) {
            return new ArrayList<>(0);
        }
        return IntStream.range(0, dataList.size())
                .mapToObj(index -> this.fillRankRowVO(index + 1, dataList.get(index)))
                .collect(Collectors.toList());
    }

    private RankRowVO fillRankRowVO(Integer rankNum, CountVO data) {
        RankRowVO vo = new RankRowVO();
        vo.setRankNum(rankNum.toString());
        vo.setScore(data.getCount());
        vo.setRealScore(String.valueOf(data.fetchRealCount()));
        ActorData rankActor = actorMgr.getActorData(data.getUid());
        ActorInfo actorInfo = fillActorInfo(rankActor);
        actorInfo.setUid(data.getUid());
        vo.setActorInfo(actorInfo);
        return vo;
    }

    private ActorInfo fillActorInfo(ActorData rankActor) {
        ActorInfo actorInfo = new ActorInfo();
        if (rankActor == null) {
            return actorInfo;
        }
        actorInfo = new ActorInfo(rankActor);
        String head = moderationService.dealRankHeadModeration(rankActor);
        actorInfo.setHead(head);
        return actorInfo;
    }


    private static void fillGlobalNoticeFormatter(RankRewardsDTO dto) {
        if (!dto.getNeedSendGlobalNotice()) {
            dto.setGlobalNoticeFormatter(null);
            return;
        }
        if (StringUtils.isEmpty(dto.getGlobalNoticeFormatter())) {
            dto.setGlobalNoticeFormatter(TEXT_FORMAT);
        }
        String formatter = dto.getGlobalNoticeFormatter().replace("#eventName", dto.getConfigData().getName())
                .replace("#top", dto.getRewardConfigs().size() + "")
                .replace("#rankName", dto.getRankName());
        dto.setGlobalNoticeFormatter(formatter);
    }

    /**
     * （推荐）榜单奖励发放方法
     */
    public List<CountVO> rankRewards(RankRewardsDTO dto) {
        fillGlobalNoticeFormatter(dto);
        return rankRewards(dto.getConfigData(), dto.getRankKey(), dto.getRewardConfigs(), dto.getRankName(),
                dto.getGlobalNoticeFormatter(), dto.getChangeDesc(), dto.getNoticeImg(), dto.getEventUrl());
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class RankRewardsDTO {
        /**
         * 活动配置
         */
        @NonNull
        private AppConfigActivityData configData;
        /**
         * 榜单key
         */
        @NonNull
        private String rankKey;
        /**
         * 榜单奖励配置
         */
        @NonNull
        private List<RewardTaskConfig> rewardConfigs;
        /**
         * 榜单名
         */
        @NonNull
        private String rankName;
        /**
         * 数数物品变动信息描述
         */
        private String changeDesc = "";
        /**
         * 通知图片
         */
        @NonNull
        private String noticeImg;
        /**
         * 活动地址
         */
        @NonNull
        private String eventUrl;
        /**
         * 是否生成和发送全服官方通知
         */
        private Boolean needSendGlobalNotice = false;
        /**
         * 全局通知消息结构
         * 结构：pre\n #content end
         */
        private String globalNoticeFormatter;
    }

    /**
     * 发榜单奖励，并控制是否生成和发送全服官方通知
     *
     * @param configData           活动配置
     * @param rankKey              榜单key
     * @param rewardConfigs        奖励配置
     * @param rankName             榜单名
     * @param changeDesc           数数信息描述
     * @param noticeImg            通知图片
     * @param eventUrl             活动地址
     * @param needSendGlobalNotice 是否生成和发送全服官方通知
     * @return 榜单信息
     */
    public List<CountVO> rankRewards(AppConfigActivityData configData, String rankKey,
                                     List<RewardTaskConfig> rewardConfigs,
                                     String rankName,
                                     String changeDesc,
                                     String noticeImg, String eventUrl, boolean needSendGlobalNotice) {
        String globalNoticeFormatter = "";
        if (needSendGlobalNotice) {
            globalNoticeFormatter = TEXT_FORMAT.replace("#eventName", configData.getName())
                    .replace("#top", rewardConfigs.size() + "")
                    .replace("#rankName", rankName);
        }
        return rankRewards(configData, rankKey, rewardConfigs, rankName, globalNoticeFormatter, changeDesc, noticeImg, eventUrl);
    }

    /**
     * 奖励发放逻辑
     *
     * @param globalNoticeFormatter 全局通知消息结构
     *                              结构：pre\n #content end
     */
    public List<CountVO> rankRewards(AppConfigActivityData configData, String rankKey,
                                     List<RewardTaskConfig> rewardConfigs,
                                     String rankName, String globalNoticeFormatter,
                                     String changeDesc,
                                     String noticeImg, String eventUrl) {
        List<CountVO> dataList = baseZSetRedis.getRange(rankKey, 1, rewardConfigs.size());
        if (ObjectUtils.isEmpty(dataList)) {
            return new ArrayList<>(0);
        }
        StringBuilder content = new StringBuilder();
        content.append("## ").append(rankName).append("（").append(configData.getActivityDesc()).append(")\n")
                .append("排名\t分数\t\t用户id\n");
        StringBuilder globalContent = new StringBuilder();
        IntStream.range(0, dataList.size())
                .forEach(index -> this.giveRewardAndFillNotice(index + 1, dataList.get(index), content, rewardConfigs, globalContent, changeDesc, configData, eventUrl));
        sendGlobalNotice(configData, globalNoticeFormatter, noticeImg, eventUrl, globalContent);
        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, content.toString());
        return dataList;
    }

    /**
     * @param configData            活动配置
     * @param globalNoticeFormatter 结构：pre\n #content end
     * @param noticeImg             消息图片地址
     * @param eventUrl              活动地址
     * @param globalContent         全局内容
     */
    private void sendGlobalNotice(AppConfigActivityData configData, String globalNoticeFormatter, String noticeImg, String eventUrl, StringBuilder globalContent) {
        if (!StringUtils.hasLength(globalNoticeFormatter)) {
            return;
        }
        String globalNotice = globalNoticeFormatter.replace("#content", globalContent.toString());
        Set<String> channelSet = com.quhong.utils.StringUtils.getStrSetFromStr(configData.getChannel(), ",");
        channelSet.forEach(channel -> sendGlobalNotice(configData, noticeImg, eventUrl, channel, globalNotice));
    }

    private void sendGlobalNotice(AppConfigActivityData configData, String noticeImg, String eventUrl, String channel, String globalNotice) {
        int fixTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        officialNoticeService.sendGlobalNotice(configData.getName(), globalNotice, noticeImg, eventUrl, channel,
                fixTime, configData.getActivityCode(),
                LangIdConstant.ENGLISH, null, OfficialUserGroup.CHANNEL);
    }

    private void giveRewardAndFillNotice(int rankNum, CountVO data, StringBuilder content, List<RewardTaskConfig> rewardConfigs, StringBuilder globalContent, String desc, AppConfigActivityData configData, String eventUrl) {
        try {
            content.append(rankNum).append("\t").append(data.getCount()).append("\t").append(data.getUid());
            ActorData rankActor = actorMgr.getActorData(data.getUid());
            if (rankActor == null) {
                return;
            }
            content.append("\t").append(rankActor.getRid());
            rewardConfigs.stream().filter(reward -> reward.getCheckParams() <= rankNum)
                    .max(Comparator.comparingInt(RewardTaskConfig::getCheckParams))
                    .ifPresent(rewardConfig -> giveReward(rankNum, data, rewardConfig, globalContent, desc, rankActor, configData, eventUrl));
        } finally {
            content.append("\n");
        }
    }

    /**
     * 检查奖励限制条件
     */
    private static List<RewardInfoData> checkRewards(CountVO data, RewardTaskConfig rewardConfig) {
        if (rewardConfig.getLimit() == null) {
            return rewardConfig.getRewards();
        }
        if (data.getCount() >= rewardConfig.getLimit()) {
            return rewardConfig.getRewards();
        }
        return rewardConfig.getRewards().stream()
                .filter(reward -> reward.getType() != RewardItemType.GOLD)
                .collect(Collectors.toList());
    }

    private void giveReward(int rankNum, CountVO data, RewardTaskConfig rewardConfig, StringBuilder globalContent, String desc, ActorData rankActor, AppConfigActivityData configData, String eventUrl) {
        if (rankNum <= 3) {
            globalContent.append("Top").append(rankNum).append(" ").append(rankActor.getRid()).append("\n");
        }
        if (!ObjectUtils.isEmpty(rewardConfig.getRewards())) {
            List<RewardInfoData> rewards = checkRewards(data, rewardConfig);
            giveOutRewardService.giveEventReward(data.getUid(), rewards, configData.getActivityCode(), desc);
            return;
        }
        if (StringUtils.isEmpty(rewardConfig.getAwardsKey())) {
            log.error("rewardConfig.getAwardsKey() is null,eventCode={}, rankName={}, rankNum={}", configData.getActivityCode(), desc, rankNum);
            return;
        }
        //通过awardsKey发放奖励
        giveRewardByAwardsKey(data, rewardConfig, desc, configData, eventUrl);
    }

    private void giveRewardByAwardsKey(CountVO data, RewardTaskConfig rewardConfig, String desc, AppConfigActivityData configData, String eventUrl) {
        RewardService.GiveAwardsKeyDTO giveRewardDTO = new RewardService.GiveAwardsKeyDTO()
                .setUid(data.getUid())
                .setAwardsKey(rewardConfig.getAwardsKey())
                .setEventCode(configData.getActivityCode())
                .setChangeDesc(desc)
                .setDescDetail(desc)
                .setOperator("event_rank_auto_reward")
                .setEventUrl(eventUrl);
        rewardService.giveAwardsKeyReward(giveRewardDTO);
    }

}
