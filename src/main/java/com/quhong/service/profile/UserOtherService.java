package com.quhong.service.profile;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.*;
import com.quhong.constant.levelConfig.LevelTypeConstant;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.enums.GenderTypeEnum;
import com.quhong.core.enums.RoomOwnerStatus;
import com.quhong.core.room.redis.RoomActorRedis;
import com.quhong.core.utils.CustomerServiceUtils;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.*;
import com.quhong.dao.datas.*;
import com.quhong.dao.datas.db.LordConfigData;
import com.quhong.dao.datas.db.PrivateActorInfoData;
import com.quhong.dao.dto.UserOtherDTO;
import com.quhong.dao.vo.profile.UserOtherVO;
import com.quhong.data.CustomerData;
import com.quhong.data.RoomProgramList;
import com.quhong.data.bo.*;
import com.quhong.data.bo.host.BannerInfoBO;
import com.quhong.data.dto.CheckUserReviewStatusDTO;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.data.dto.host.ChatPriceDTO;
import com.quhong.data.room.ResourceInfoObject;
import com.quhong.data.vo.host.vest.info.HostVestVO;
import com.quhong.enums.DocLocalCategoryType;
import com.quhong.enums.RoomType;
import com.quhong.enums.TaskTriggerName;
import com.quhong.exceptions.WebException;
import com.quhong.mq.enums.MQConstant;
import com.quhong.players.ActorDerivedMgr;
import com.quhong.players.ActorMgr;
import com.quhong.redis.*;
import com.quhong.service.*;
import com.quhong.service.actor.ChatPriceService;
import com.quhong.service.actor.HostReviewInfoBaseService;
import com.quhong.service.actor.vest.info.ActorVestService;
import com.quhong.service.money.CurrencyService;
import com.quhong.service.robot.RobotService;
import com.quhong.service.user.auth.level.LevelApi;
import com.quhong.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.quhong.utils.FakeUtils.COUNTRY_CODE_AREA_MAP;

@Service
@Slf4j
public class UserOtherService {

    @Resource
    private ActorMgr actorMgr;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CurrencyService currencyService;
    @Resource
    private ActorStatusService actorStatusService;
    @Resource
    private ActorExternalDao actorExternalDao;
    @Resource
    private DocLocalConfigDao docLocalConfigDao;
    @Resource
    private BaseSubscriptionService baseSubscriptionService;
    @Resource
    private MemberDao memberDao;
    @Resource
    private HostMonthLevelService hostMonthLevelService;
    @Resource
    private FollowDao followDao;
    @Resource
    private ConfigMgrService configMgrService;
    @Resource
    private YesterdayTopRedis yesterdayTopRedis;
    @Resource
    private HostInfoDao hostInfoDao;
    @Resource
    private ChatPriceService chatPriceService;
    @Resource
    private SayHiService sayHiService;
    @Resource
    private BaseLevelService baseLevelService;
    @Resource
    private ConfigApi configApi;
    @Resource
    private HostConfigDao hostConfigDao;
    @Resource
    private BaseProfileService baseProfileService;
    @Resource
    private WhatsAppRelationDao whatsAppPurchaseDao;
    @Resource
    private HostGradeRedis hostGradeRedis;
    @Resource
    private GrayService grayService;
    @Resource
    private VestInfoRedis vestInfoRedis;
    @Resource
    private ActorVestService actorVestService;
    @Resource
    private ActorSourceDao actorSourceDao;
    @Resource
    private CdnUtils cdnUtils;
    @Resource
    private RoomDao roomDao;
    @Resource
    private HostBlockUserDao hostBlockUserDao;
    @Resource
    private LargeScalePictureDao largeScalePictureDao;
    @Resource
    private HostFakeInfoDao hostFakeInfoDao;
    @Resource
    private LordService lordService;
    @Resource
    private LevelApi levelApi;
    @Resource
    private RobotService robotService;
    @Resource
    private RoomItemsService roomItemsService;
    @Resource
    private BaseLabelService baseLabelService;
    @Resource
    private BaseInterestService baseInterestService;
    @Resource
    private RoomProgramService roomProgramService;
    @Resource
    private RoomActorRedis roomActorRedis;
    @Resource
    private ActorDerivedMgr actorDerivedMgr;
    @Resource
    private PlayerRedis playerRedis;
    @Resource
    private HostStatusNotificationRedis hostStatusNotificationRedis;
    @Resource
    private HostAppraiseLevelDao hostAppraiseLevelDao;
    @Autowired
    private HostReviewInfoBaseService hostReviewInfoBaseService;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private DirtySensitiveWordFilter dirtySensitiveWordFilter;
    @Resource
    private AppConfigChannelDao appConfigChannelDao;
    @Resource
    private AppConfigChannelService appConfigChannelService;
    @Resource
    private PrivateActorInfoDao privateActorInfoDao;
    @Resource
    private UserSignSceneCallSwitchConfigService userSignSceneCallSwitchConfigService;

    private static final String SOURCE_NO_MEDIUM = "no_medium";
    private static final String ALL_MEDIUM = "all";

    public UserOtherVO getOtherProfile(UserOtherDTO dto) {
        log.debug("record user other start origin dto={}", dto);
        long startTime = System.currentTimeMillis();
        try {
            genUserOtherVO(dto);
        } catch (WebException we) {
            log.error("record user other error dto={} we={}", dto, we.getMessage(), we);
            throw we;
        } catch (Exception e) {
            log.error("record user other error dto={} e={}", dto, e.getMessage(), e);
            throw new WebException(dto, HttpCode.SERVER_ERROR);
        }
        long endTime = System.currentTimeMillis();
//        log.debug("record user other end finally userOtherVO={} useTime={}", dto.getUserOtherVO(), endTime - startTime);
        return dto.getUserOtherVO();
    }

    private void genUserOtherVO(UserOtherDTO dto) {
        ActorData aidActorData = actorMgr.getActorData(dto.getAid());
        if (aidActorData == null) {
            throw new WebException(dto, HttpCode.USER_NOT_EXISTS);
        }
        dto.setAidActorData(aidActorData);
        if (aidActorData.getValid() != 1) {
            throw new WebException(dto, HttpCode.OTHER_BANNED_BY_ADMIN);
        }

        ActorData uidActorData = actorMgr.getActorData(dto.getUid());
        if (uidActorData == null) {
            throw new WebException(dto, HttpCode.USER_NOT_EXISTS);
        }
        dto.setUidActorData(uidActorData);

        UserOtherVO userOtherVO = new UserOtherVO();
        dto.setUserOtherVO(userOtherVO);
        fillAidBaseInfo(dto);
        fillFollowAndBlockInfo(dto);
        fillAidActorConfig(dto);
        fillAidVipInfo(dto);
        fillAidHostInfo(dto);
        fillOtherData(dto);
        fillParamsListData(dto);

        replaceRetReviewHead(dto);

        fillLordInfo(dto);
        fillLabel(dto);
        fillOtherResource(dto);
        fillOfficialRoom(dto);
        // ios马甲包private信息处理
        dealIosPremiumPrivateInfo(uidActorData, aidActorData, userOtherVO);
        otherOperation(dto);
        sendTaskMQ(dto);
    }

    /**
     * 主播的审核信息库的头像替换
     */
    private void replaceRetReviewHead(UserOtherDTO dto) {
        ActorData hostActorData = dto.getAidActorData();
        ActorData fromData = dto.getUidActorData();
        if (hostActorData == null) return;
        AppConfigChannelData appConfigChannelData = appConfigChannelDao.selectByChannel(fromData.getChannel());
        if (appConfigChannelData != null && appConfigChannelData.getDirect() == 1) {//直接包不做头像替换
            return;
        }
        if (hostReviewInfoBaseService.replaceReviewHead(hostActorData, fromData)) {
            hostActorData.setHeadIcon(hostActorData.getHeadIcon());
        }
    }

    private void fillOfficialRoom(UserOtherDTO dto) {
        String aid = dto.getAid();
        RoomData roomData = roomDao.getRoomData(RoomUtils.formatRoomId(aid, RoomType.LIVE));
        if (roomData != null && roomData.getOfficialRoom() != null && roomData.getOfficialRoom() == 1) {
            dto.getUserOtherVO().setIsOfficialRoom(1);
            RoomProgramList roomProgramList = roomProgramService.getRoomProgramList(roomData.getRoomId());
            roomProgramService.dealFollowAndInLive(roomProgramList, dto.getUid());
            dto.getUserOtherVO().setRoomProgramData(roomProgramList);
        }
    }

    private void fillOtherResource(UserOtherDTO dto) {
        UserOtherVO userOtherVO = dto.getUserOtherVO();
        RoomUserItemsBO roomUserWearItems = roomItemsService.getRoomUserWearItems(dto.getAid(), RoomItemsTypeConstant.SEAT_FRAME);
        if (roomUserWearItems != null) {
            ResourceInfoObject micFrameObject = new ResourceInfoObject();
            micFrameObject.copyFrom(roomUserWearItems);
            userOtherVO.setMicFrame(micFrameObject);
        }
        RoomUserItemsBO designationWearItems = roomItemsService.getRoomUserWearItems(dto.getAid(), RoomItemsTypeConstant.DESIGNATION);
        if (designationWearItems != null) {
            ResourceInfoObject designationObject = new ResourceInfoObject();
            designationObject.copyFrom(designationWearItems);
            userOtherVO.setDesignation(designationObject);
        }
        RoomUserItemsBO achievementDesignationWearItems = roomItemsService.getRoomUserWearItems(dto.getAid(), RoomItemsTypeConstant.ACHIEVEMENT_DESIGNATION);
        if (achievementDesignationWearItems != null) {
            ResourceInfoObject achievementDesignationObject = new ResourceInfoObject();
            achievementDesignationObject.copyFrom(achievementDesignationWearItems);
            userOtherVO.setAchievementDesignation(achievementDesignationObject);
        }
    }

    private void otherOperation(UserOtherDTO dto) {
        baseProfileService.sendDetectPicMq(dto.getAidActorData(), "head", dto.getUid() + " user/other");
        baseProfileService.dealRegisterDayMedal(dto.getAidActorData());
        hostStatusNotificationRedis.addUid(dto.getAid(), dto.getUid());

    }

    private void fillParamsListData(UserOtherDTO dto) {
        if (ParamsListUtils.validNotFillParamsList(dto.getChannel())) {
            return;
        }
        UserOtherParamsBO userOtherParamsBO = new UserOtherParamsBO();
        dto.setUserOtherParamsBO(userOtherParamsBO);
        ActorData aidActorData = dto.getAidActorData();
        UserOtherVO userOtherVO = dto.getUserOtherVO();
        userOtherParamsBO.setCountryCode(aidActorData.getCountryCode());
        userOtherParamsBO.setUserType(aidActorData.getUserType());
        userOtherParamsBO.setIsCustomer(CustomerServiceUtils.isCustomerService(dto.getAid()) ? 1 : 0);
        userOtherParamsBO.setHadSayHi(userOtherVO.getHadSayHi());
        ActorConfigCollatedBO aidActorConfigData = dto.getAidActorConfigData();
        userOtherParamsBO.setBossLevel(baseLevelService.getLevelByPoint(aidActorConfigData.getBossPoints() == null ? 0 : aidActorConfigData.getBossPoints(), LevelTypeConstant.TYPE_BOSS_LEVEL));
        if (GenderTypeEnum.USER.getType().equals(aidActorData.getGender())) {
            userOtherParamsBO.setIsMiddleEastArea(isMiddleEastArea(dto));
            userOtherParamsBO.setIsAnchorApp(ChannelEnum.isAnchorApp(aidActorData.getChannel()) ? 1 : 0);
        }
        if (GenderTypeEnum.HOST.getType().equals(aidActorData.getGender())) {
            HostConfigCollatedBO hostConfigObject = hostConfigDao.getHostConfigObject(aidActorData.getUid());
            String secondLangId = hostConfigObject.getSecondLangId();
            String translateId = dto.getUserOtherVO().getTranslateId();
            userOtherParamsBO.setLanguageList(baseProfileService.getUserLanguageName(translateId, secondLangId));
            userOtherParamsBO.setLiveHostRights(hostConfigObject.getWhiteListConfig());

            boolean lockWhatsapp = whatsAppPurchaseDao.getPurchaseRelation(dto.getUid(), aidActorData.getUid());
            userOtherParamsBO.setWhatsappStatus(lockWhatsapp ? 2 : 1);
            userOtherParamsBO.setWhatsappAccount(hostConfigObject.getWhatsappAccount());
            userOtherParamsBO.setWhatsappPrice(10);

            userOtherParamsBO.setGuaranteeHost(hostGradeRedis.decideIsGuaranteeHost(aidActorData.getUid()) ? 1 : 0);

            fillOtherUserInfo(dto);
            fillRoomData(dto);

        }

        List<ParamsDictBO> paramsListBOS = ParamsListUtils.genParamsListBO(userOtherParamsBO);
        dto.getUserOtherVO().setParamsList(paramsListBOS);

    }

    private void fillOtherUserInfo(UserOtherDTO dto) {
        AppConfigChannelData appConfigChannelData = appConfigChannelDao.selectByChannel(dto.getUidActorData().getChannel());
        if (appConfigChannelData != null && appConfigChannelData.getDirect() == 1) {//直接包不做头像昵称替换
            return;
        }
        UserOtherVO userOtherVO = dto.getUserOtherVO();
        ActorData uidActorData = dto.getUidActorData();
        ActorData aidActorData = dto.getAidActorData();
        int aidActorDataReviewStatus = getActorDataReviewStatus(aidActorData);
        int uidActorDataReviewStatus = getActorDataReviewStatus(uidActorData);
//        dto.setSource("utm_source=lsd001&utm_medium=sd001");
        String remoteIP = dto.getRemoteIP();
        int largeScaleRes = largeScalePictureDao.validLargeScalePictureFromCache(uidActorData, dto.getSource(), dto.getSession(), remoteIP, dto.getChannel(), dto.getRequestId());
        if (largeScaleRes == 0) {
            if (uidActorDataReviewStatus == 1) {
                if (aidActorDataReviewStatus == 0) {
                    Long rid = aidActorData.getRid();
                    if (StringUtils.isEmpty(aidActorData.getChannel())) {
                        return;
                    }
                    int fakeRid = FakeUtils.getFakeRid(aidActorData.getChannel(), rid);
                    HostFakeInfoData fakeInfoBy = hostFakeInfoDao.getFakeInfoBy(fakeRid, aidActorData.getGender(), 1);
                    String virtualName = aidActorData.getName();
                    String virtualHead = aidActorData.getName();
                    if (fakeInfoBy != null) {
                        virtualName = fakeInfoBy.getVirtualName();
                        virtualHead = fakeInfoBy.getVirtualHead();
                    } else {
                        log.info("rid={} fakeRid={} fakeInfoBy={}", rid, fakeRid, null);
                    }
                    userOtherVO.setBanner(new ArrayList<>());
                    userOtherVO.setName(virtualName);
                    userOtherVO.setHead(cdnUtils.replaceUrlDomain(uidActorData.getChannel(), virtualHead, 0, true));
                    userOtherVO.setDesc("");
                }
            } else {
                if (aidActorDataReviewStatus == 1) {
                    if (actorExternalDao.getReviewerStatus(dto.getAid()) != 0) {
                        String applicationName = configMgrService.getApplicationName(uidActorData);
                        userOtherVO.setName(applicationName + " Police");
                    }
                } else {
                    int aidStatus = playerRedis.getStatus(aidActorData.getUid());
                    HostVestVO vestInfo = actorVestService.getVestInfo(aidStatus, aidActorData.getUid(), uidActorData);
//                    HostVestVO vestInfo = getVestInfo(dto);
                    if (vestInfo == null) return;
                    if (!StringUtils.isEmpty(vestInfo.getName())) {
                        userOtherVO.setName(vestInfo.getName());
                    }
                    List<String> bannerList = vestInfo.getBannerList();
                    String vestInfoHead = vestInfo.getHead();
                    if (!StringUtils.isEmpty(vestInfoHead) && bannerList != null && !bannerList.isEmpty()) {
                        userOtherVO.setHead(cdnUtils.replaceUrlDomain(uidActorData.getChannel(), vestInfoHead, 0, true));
                        userOtherVO.setBanner(getUserBannerList(bannerList.subList(0, Math.min(bannerList.size(), 8)), uidActorData));    // 左闭右开
                    }
                }
            }
        } else if (largeScaleRes == 2) {
            String countryCode = aidActorData.getCountryCode();
            Integer area = COUNTRY_CODE_AREA_MAP.get(countryCode);
            if (area != null) {
                log.info("uid={} rid={} countryCode={} area={}", uidActorData.getUid(), uidActorData.getRid(), countryCode, area);
                List<HostFakeInfoData> hostPopularizeFakeListByAreaType = hostFakeInfoDao.getHostPopularizeFakeListByAreaType(HostFakeInfoDao.POPULARIZE_AREA, area);
                int hostFakeCountByAreaType = hostFakeInfoDao.getHostFakeCountByAreaType(HostFakeInfoDao.POPULARIZE_AREA, area);
                if (hostFakeCountByAreaType <= 5) {
                    return;
                }
                int fakeRid = FakeUtils.getFakeRidByMod(uidActorData.getChannel(), aidActorData.getRid(), hostFakeCountByAreaType);
                HostFakeInfoData hostFakeInfoData = hostPopularizeFakeListByAreaType.get(fakeRid);
                userOtherVO.setHead(cdnUtils.replaceUrlDomain(uidActorData.getChannel(), hostFakeInfoData.getVirtualHead(), 0, true));
                userOtherVO.setBanner(new ArrayList<>());
            }
        }

    }

    private void fillRoomData(UserOtherDTO dto) {
        UserOtherVO userOtherVO = dto.getUserOtherVO();
        String aid = dto.getAid();
        String roomId = RoomUtils.formatRoomId(aid);
        RoomData roomData = roomDao.getRoomData(roomId);
        UserOtherParamsBO userOtherParamsBO = dto.getUserOtherParamsBO();
        if (roomData == null) {
            userOtherParamsBO.setInLive(0);
            userOtherParamsBO.setLiveRoomId("");
            userOtherParamsBO.setStreamRoomId("");
            return;
        }
        if (roomData.getOfficialRoom() != null && roomData.getOfficialRoom() == 1) {//如果是官方直播间
            userOtherVO.setStatus(1);
            userOtherParamsBO.setLiveRoomId(roomId);
            int currentTime = DateHelper.getCurrentTime();
            if (roomData.getOwnerStatus() == RoomOwnerStatus.ENTER) {
                if (roomData.getChapterStartTime() != null && roomData.getChapterEndTime() != null && roomData.getChapterStartTime() <= currentTime && roomData.getChapterEndTime() >= currentTime) {
                    userOtherParamsBO.setInLive(1);
                }
            }
            return;
        }
        if (userOtherVO.getStatus() == 1) {
            int inLive = 0;
            if (roomData.getOwnerStatus() == RoomOwnerStatus.ENTER) {
                inLive = 1;
                int inWhite = hostConfigDao.getConfigIntValue(aid, HostConfigDao.WHITE_CONFIG);
                userOtherParamsBO.setStreamRoomId(roomData.getStreamRoomId());
                int onlyShow = hostConfigDao.getConfigIntValue(aid, HostConfigDao.ONLY_SHOW_LIVE);
                // 白名单 不设置仅展示才能进入live房间
                if (inWhite == 1 && onlyShow == 0) {
                    userOtherParamsBO.setLiveRoomId(roomId);
                } else {
                    userOtherParamsBO.setLiveRoomId("");
                }
                if (inWhite != 1 && dto.getChannel().equalsIgnoreCase(ChannelEnum.CDE.getName())) {
                    userOtherParamsBO.setStreamRoomId("");
                }
//                AppConfigChannelData appConfigChannelData = appConfigChannelDao.selectByChannel(dto.getChannel());//直接包用户不下发流id
//                if (appConfigChannelData != null && appConfigChannelData.getDirect() != null && appConfigChannelData.getDirect() == 1) {
//                    userOtherParamsBO.setStreamRoomId("");
//                }
//                boolean liveWindowSwitch = liveSmallWindowSwitch(aid);
//                if (!liveWindowSwitch) {
//                    userOtherParamsBO.setStreamRoomId("");
//                    userOtherParamsBO.setLiveRoomId("");
//                }
            }
            userOtherParamsBO.setInLive(inLive);
        }
    }

//    private boolean liveSmallWindowSwitch(String aid) {
//        // 判断主播是否开启live小窗
//        HostConfigData configData = hostConfigDao.getHostConfigFromCache(aid, HostConfigDao.LIVE_SMALL_WINDOW_STREAM_SWITCH);
//        // true 开  false 关
//        log.info("liveSmallWindowSwitch aid={} configData={}", aid, JSON.toJSONString(configData));
//        return configData == null || (configData.getValueInt() != null && configData.getValueInt() == 1);
//    }

    /**
     * 功能可能有问题，已废弃，再开启时需注意
     *
     * @param dto
     * @return
     */
    @Deprecated
    private HostVestVO getVestInfo(UserOtherDTO dto) {
        if (dto.getUserOtherVO().getStatus() != 1) {
            return null;
        }
        Map<String, HostVestVO> hosVestVoMap;
        ActorData uidActorData = dto.getUidActorData();
        String uid = uidActorData.getUid();
        //灰度判定
        boolean isGray = grayService.isGray(AppConfigKeyConstant.HOST_VEST_INFO_GRAY, uidActorData);
        if (!isGray) {
            log.info("user vest grayscale is not hit, uid={}, rid={}", uid, uidActorData.getRid());
            return null;
        }
        //获取配置
        ConfigDTO configDTO = new ConfigDTO();
        configDTO.setKey(AppConfigKeyConstant.SHOW_SCENE_BY_MEDIA_SOURCE);
        configDTO.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        configDTO.setUid(uid);
        Map<String, String> dictionaryMap = configApi.getJavaBeanVal(configDTO, Map.class);
        if (dictionaryMap.isEmpty()) {
            log.info("host list fill vest info config is null. uid={}", uid);
            return null;
        }
        String scene = getScene(uid, dictionaryMap);
        if (StringUtils.isEmpty(scene)) {
            log.info("host list fill vest info scene config is empty. uid={}", uid);
            return null;
        }
        if (vestInfoRedis.checkHasKey(uid)) {
            hosVestVoMap = vestInfoRedis.getHostVestInfo(uid);
            if (!hosVestVoMap.isEmpty()) {
                return hosVestVoMap.get(scene);
            }
            return null;
        }
        try {
            hosVestVoMap = actorVestService.getHosVestVoMap(uid);
        } catch (Exception e) {
            log.error("dto={} e={}", dto, e.getMessage(), e);
            return null;
        }
        return hosVestVoMap.get(scene);
    }

    private String getScene(String uid, Map<String, String> dictionaryMap) {
        String scene = dictionaryMap.getOrDefault(ALL_MEDIUM, "");
        if (StringUtils.isEmpty(scene)) {
            ActorSourceData actorSourceData = actorSourceDao.getByUid(uid);
            if (actorSourceData == null || actorSourceData.getMedium() == null) {
                log.info("host list fill vest info actorSourceData is null or medium is null. uid={}", uid);
                //return false;
                scene = dictionaryMap.getOrDefault(SOURCE_NO_MEDIUM, "");
            } else {
                scene = dictionaryMap.getOrDefault(actorSourceData.getMedium(), "");
            }
        }
        return scene;
    }

    private int isMiddleEastArea(UserOtherDTO dto) {
        ActorData aidActorData = dto.getAidActorData();
        String channel = aidActorData.getChannel();
        if (!"cdlive".equals(channel)) {
            return 0;
        }
        return getMiddleEastAreaList(aidActorData.getUid()).contains(aidActorData.getCountryCode()) ? 1 : 0;
    }


    private List<String> getMiddleEastAreaList(String uid) {
        ConfigDTO dto = new ConfigDTO();
        dto.setUid(uid);
        dto.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        dto.setKey(AppConfigKeyConstant.MIDDLE_EAST_AREA_COUNTRY_LIST);
        String strVal = configApi.getStrVal(dto);
        if (StringUtils.isEmpty(strVal)) {
            return new ArrayList<>();
        }
        String[] split = strVal.split(";");
        return new ArrayList<>(Arrays.asList(split));
    }

    /**
     * 填充关注数据
     **/
    private void fillFollowAndBlockInfo(UserOtherDTO dto) {
        UserOtherVO userOtherVO = dto.getUserOtherVO();
        String aid = dto.getAid();

        int following = followDao.getFollowingCountByUid(aid);
        int fans = followDao.getFollowedCountByUid(aid);
        int newFans = 0;
        userOtherVO.setFollowed(following);
        userOtherVO.setFollower(fans);
        userOtherVO.setNewFans(newFans);

        String uid = dto.getUid();
        // 我有没有关注你
        int isFollowed = followDao.isFollowAid(uid, aid) ? 1 : 0;
        // 别人有没有关注我
        int isFollowMe = followDao.isFollowAid(aid, uid) ? 1 : 0;
        userOtherVO.setIsFollowed(isFollowed);
        userOtherVO.setIsFollowMe(isFollowMe);

        boolean block = hostBlockUserDao.isBlock(uid, aid);
        userOtherVO.setInBlock(block ? 1 : 0);
    }

    private void fillOtherData(UserOtherDTO dto) {
        String aid = dto.getAid();
        ActorData uidActorData = dto.getUidActorData();
        UserOtherVO userOtherVO = dto.getUserOtherVO();
        userOtherVO.setHadSayHi(sayHiService.hasSayHi(dto.getUid(), dto.getAid()) ? 1 : 0);
        String roomId = roomActorRedis.getActorRoomStatus(aid);
        if (!StringUtils.isEmpty(roomId)) {
            RoomData roomData = roomDao.getRoomData(roomId);
            if (roomData != null && roomData.getRoomType().equals(RoomType.CHAT)) {
                userOtherVO.setPartyRoomId(roomId);
            }
        }
        if (GenderTypeEnum.USER.getType().equals(uidActorData.getGender())) {
            userOtherVO.setCallButtonSwitch(userSignSceneCallSwitchConfigService.getCallButtonSwitch(dto.getChannel(), dto.getUid(), aid, dto.getPosition(), false) ? 1 : 0);
        }
    }

    private void fillAidHostInfo(UserOtherDTO dto) {
        UserOtherVO userOtherVO = dto.getUserOtherVO();
        userOtherVO.setUserLevel(levelApi.getLevelByUid(userOtherVO.getAid(), GenderTypeEnum.USER.getType()));
        int gender = userOtherVO.getGender();
        if (!GenderTypeEnum.HOST.getType().equals(gender)) {
            return;
        }

        ActorData aidActorData = dto.getAidActorData();
        userOtherVO.setHostLevel(hostMonthLevelService.getHostMonthLevelByUid(aidActorData));
        userOtherVO.setHostGrade(levelApi.getLevelByUid(aidActorData.getUid(), GenderTypeEnum.HOST.getType()));
        userOtherVO.setHostQualityLevel(hostAppraiseLevelDao.getHostAppraiseLevel(aidActorData.getUid()));
        userOtherVO.setHighLight((userOtherVO.getGold() >= (userOtherVO.isPremium() ? 62 : 56)) ? 1 : 0);
        userOtherVO.setHeat(10);
        userOtherVO.setTop(yesterdayTopRedis.inYesterdayTop(aidActorData.getUid()));
        HostInfoData hostInfo = hostInfoDao.getHostInfo(aidActorData.getUid());
        int hostNew = hostInfo == null ? 0 : (hostInfo.getIsNew() == null ? 1 : hostInfo.getIsNew());
        userOtherVO.setHostNew(hostNew);
        int chatPrice = chatPriceService.getChatPrice(new ChatPriceDTO(aidActorData.getUid(), true));
        userOtherVO.setShowPrice(chatPrice);
        userOtherVO.setCallCost(chatPrice / 2);
    }

    private void fillAidVipInfo(UserOtherDTO dto) {
        UserOtherVO userOtherVO = dto.getUserOtherVO();
//        int gender = userOtherVO.getGender();
//        if (GenderTypeEnum.HOST.getType().equals(gender)) {
//            return;
//        }
        HttpEnvData data = new HttpEnvData();
        data.setUid(dto.getAid());
        SubscriptionBO subscriptionStatus = baseSubscriptionService.getSubscriptionStatus(data);
        Long vipEndAt = subscriptionStatus.getVipEndAt();
        boolean isPremium = !((vipEndAt == null) || (vipEndAt <= DateHelper.getCurrTime()));

//        boolean member = memberDao.isMember(dto.getAid());
//        log.info("uid={} aid={} isPremium={} member={}", dto.getUid(), dto.getAid(), isPremium, member);
        userOtherVO.setPremium(isPremium);
    }

    private void fillAidActorConfig(UserOtherDTO dto) {
        String aid = dto.getAid();
        ActorConfigCollatedBO actorConfigObject = actorMgr.getActorConfigCollatedBOFromActorJson(aid);

        dto.setAidActorConfigData(actorConfigObject);
        UserOtherVO userOtherVO = dto.getUserOtherVO();
        String translateId = actorExternalDao.getTranslateId(aid);
        userOtherVO.setTranslateId(translateId);
        int realAcceptCall = userOtherVO.getInReviewStatus() == 1 ? 0 : actorConfigObject.getAcceptVideo();
        userOtherVO.setAcceptTalk(realAcceptCall);
        userOtherVO.setAcceptVoice(realAcceptCall);
    }

    private void fillAidBaseInfo(UserOtherDTO dto) {
        processSensitiveWords(dto);
        UserOtherVO userOtherVO = dto.getUserOtherVO();
        ActorData aidActorData = dto.getAidActorData();
        ActorData uidActorData = dto.getUidActorData();
        fixCustomerBaseInfo(dto);
        userOtherVO.setRid(aidActorData.getRid().intValue());
        userOtherVO.setAid(aidActorData.getUid());
        userOtherVO.setAidChannel(aidActorData.getChannel());
        userOtherVO.setName(aidActorData.getName());
        userOtherVO.setHead(cdnUtils.replaceUrlDomain(uidActorData.getChannel(), aidActorData.getHeadIcon(), 0, true));
        userOtherVO.setBanner(getUserBannerList(actorMgr.getActorBanner(aidActorData), dto.getUidActorData()));
        userOtherVO.setDesc(aidActorData.getSignature());
        userOtherVO.setBirthday(aidActorData.getBirthday());
        userOtherVO.setAge(aidActorData.getAge());
        userOtherVO.setGender(aidActorData.getGender());
        userOtherVO.setSex(aidActorData.getSex() != null ? aidActorData.getSex() : 1);
        BigDecimal balance = currencyService.getRealCurrency(aidActorData);
        userOtherVO.setGold(balance.intValue());
        userOtherVO.setRealGold(balance.toString());
        userOtherVO.setStatus(getUserShowStatus(dto));
        userOtherVO.setCountry(getCountry(aidActorData, dto.getUid()));
        userOtherVO.setCountryCode(aidActorData.getCountryCode());

        int aidReviewStatus = getActorDataReviewStatus(aidActorData);
        userOtherVO.setInReviewStatus(aidReviewStatus);
        userOtherVO.setIsBrowse(aidReviewStatus);
        fillUserPickInterest(userOtherVO);
    }

    private void dealIosPremiumPrivateInfo(ActorData uidActorData, ActorData aidActorData, UserOtherVO userOtherVO) {
        boolean premium = appConfigChannelService.isPremium(uidActorData.getChannel());
        if (!premium) {
            return;
        }
        PrivateActorInfoData privateData = privateActorInfoDao.getOneByUid(aidActorData.getUid());
        if (privateData == null) {
            return;
        }
        userOtherVO.setHead(cdnUtils.replacePrivateHeadAndUrlDomain(uidActorData.getChannel(), privateData.getHeadIcon(), 0, true, privateData.getUid()));
        if (StringUtils.hasLength(privateData.getSignature())) {
            userOtherVO.setDesc(privateData.getSignature());
        }
//        if (StringUtils.hasLength(privateData.getName())) {
//            userOtherVO.setName(privateData.getName());
//        }
        if (!ObjectUtils.isEmpty(privateData.getBanners())) {
            List<BannerDictBO> privateBanners = privateData.getBanners().stream()
                    .sorted(Comparator.comparing(BannerInfoBO::getWeight).reversed())
                    .map(banner -> fillBannerDictBO(banner, uidActorData.getChannel()))
                    .collect(Collectors.toList());
            userOtherVO.setBanner(privateBanners);
        }
    }

    private BannerDictBO fillBannerDictBO(BannerInfoBO banner, String currChannel) {
        BannerDictBO bo = new BannerDictBO();
        bo.setImage(cdnUtils.replaceUrlDomain(currChannel, banner.getBannerUrl(), 0, true));
        bo.setType(0);
        bo.setUrl("");
        return bo;
    }

    private void fillUserPickInterest(UserOtherVO userOtherVO) {
        if (userOtherVO.getAid().contains("_")) {
            return;
        }
        List<InterestConfigBO> interestConfigBOS = baseInterestService.genUserAllPickList(userOtherVO.getAid());
        if (interestConfigBOS.size() > 0) {
            userOtherVO.setUserInterestList(interestConfigBOS.get(0).getInterestInfoBOList());
        }
    }

    private void fillLordInfo(UserOtherDTO dto) {
        UserOtherVO userOtherVO = dto.getUserOtherVO();
        ActorData aidActorData = dto.getAidActorData();
        ActorData uidActorData = dto.getUidActorData();
        int userLordLevel = lordService.getUserLordLevel(aidActorData.getUid());
        LordConfigData lordLevelConfig = lordService.getLordLevelConfig(userLordLevel);
        if (lordLevelConfig != null) {
            userOtherVO.setLordIcon(cdnUtils.replaceUrlDomain(uidActorData.getChannel(), lordLevelConfig.getLevelIcon(), 0));
            userOtherVO.setNewLordIcon(cdnUtils.replaceUrlDomain(uidActorData.getChannel(), lordLevelConfig.getSubscriptIcon(), 0));
        }
        userOtherVO.setLordLevel(userLordLevel);
        userOtherVO.setGoldNickname(lordService.decideLevelHaveLordAuth(userLordLevel, LordConstant.AUTH_GOLD_NICKNAME) ? 1 : 0);
    }

    private void fillLabel(UserOtherDTO dto) {
        UserOtherVO userOtherVO = dto.getUserOtherVO();
        ActorData aidActorData = dto.getAidActorData();
        userOtherVO.setVerifyStatus(baseLabelService.getVerifyLabel(aidActorData.getUid()));
        userOtherVO.setCoinSellerLabel(baseLabelService.getCoinSellerLabel(aidActorData.getUid()));
        userOtherVO.setAgencyLabel(baseLabelService.getAgencyLabel(aidActorData.getUid()));
        userOtherVO.setOfficialLabel(baseLabelService.getOfficialLabel(aidActorData.getUid()));
        userOtherVO.setLabelType(hostConfigDao.getConfigIntValue(aidActorData.getUid(), HostConfigDao.LIST_LABEL_CONFIG));
        userOtherVO.setIdentityTag(baseLabelService.getRoomListLabel(aidActorData.getUid()));
    }

    private int getActorDataReviewStatus(ActorData actorData) {
        CheckUserReviewStatusDTO checkUserReviewStatusDTO = new CheckUserReviewStatusDTO();
        checkUserReviewStatusDTO.setPlatform(actorData.getPlatform() == null ? 1 : actorData.getPlatform());
        checkUserReviewStatusDTO.setVer(actorData.getVer() == null ? 1 : actorData.getVer());
        checkUserReviewStatusDTO.setUid(actorData.getUid());
        return actorDerivedMgr.getUserReviewStatusNew(checkUserReviewStatusDTO) ? 1 : 0;
    }


    private void fixCustomerBaseInfo(UserOtherDTO dto) {
        if (!CustomerServiceUtils.isCustomerService(dto.getAid())) {
            return;
        }
        ActorData uidActorData = dto.getUidActorData();
        ActorData aidActorData = dto.getAidActorData();
        String applicationName = configMgrService.getApplicationName(uidActorData);
        CustomerData customerActorData = configMgrService.getCustomerActorData(uidActorData, applicationName);
        aidActorData.setName(customerActorData.getName());
        List<String> bannerList = customerActorData.getBanner();
        aidActorData.setBanner(JSON.toJSONString(bannerList));
        aidActorData.setHeadIcon(customerActorData.getHead());
        aidActorData.setRid(0L);
    }

    private List<BannerDictBO> getUserBannerList(List<String> actorBanner, ActorData uidActorData) {
        if (actorBanner == null) {
            return new ArrayList<>();
        }
        List<BannerDictBO> actorBannerList = new ArrayList<>();
        for (String banner : actorBanner) {
            BannerDictBO bannerDictBO = new BannerDictBO();
            bannerDictBO.setImage(cdnUtils.replaceUrlDomain(uidActorData.getChannel(), banner, 0, true));
            bannerDictBO.setType(0);
            bannerDictBO.setUrl("");
            actorBannerList.add(bannerDictBO);
        }
        return actorBannerList;
    }

    private int getUserShowStatus(UserOtherDTO dto) {
        ActorData uidActorData = dto.getUidActorData();
        ActorData aidActorData = dto.getAidActorData();
        if (robotService.decideValidRobot(aidActorData.getUid())) {
            return PlayerStatusConstant.ONLINE;
        }
        if (ChannelEnum.CDE.getName().equals(aidActorData.getChannel())) {
            return playerRedis.getStatus(aidActorData.getUid());
        }
        int showStatus = actorStatusService.getShowStatus(aidActorData.getUid());
        if (showStatus == 1) {
            return showStatus;
        }
        Integer isPayUser = uidActorData.getIsPayUser();
        if (isPayUser != 1 && aidActorData.getUserType() == 3) {
            showStatus = 1;
        }
        return showStatus;
    }

    private String getCountry(ActorData aidActorData, String uid) {
        ActorExternalData actorExternalData = actorExternalDao.getData(uid);
        String clientSysLang = actorExternalData.getClientSysLang();
        String originCountry = aidActorData.getCountry();
        return docLocalConfigDao.getDocLocalDescByApi(originCountry, clientSysLang, DocLocalCategoryType.COUNTRY);
    }

    private void processSensitiveWords(UserOtherDTO dto) {
        ActorData aidActorData = dto.getAidActorData();
        String name = aidActorData.getName();
        String signature = aidActorData.getSignature();
        String validSignature = dirtySensitiveWordFilter.replaceSensitiveWords(dto.getChannel(), SensitiveWordsSceneConstant.SIGNATURE, signature);
        if (validSignature.equals(signature)) {
            validSignature = "";
        }
        ActorData updateActor = new ActorData();
        boolean updateActorSign = false;
        if (!StringUtils.isEmpty(validSignature)) {
            updateActor.setSignature(validSignature);
            updateActorSign = true;
        }
        String validName = dirtySensitiveWordFilter.replaceSensitiveWords(dto.getChannel(), SensitiveWordsSceneConstant.NAME, name);
        if (validName.equals(name)) {
            validName = "";
        }
        if (!StringUtils.isEmpty(validName)) {
            updateActor.setName(validName);
            updateActorSign = true;
        }
        if (updateActorSign) {
            updateActor.setUid(dto.getAid());
            updateActor.setUpdateTime(DateHelper.getCurrTime());
            actorDao.updateActor(updateActor);
            dto.setAidActorData(actorMgr.getActorData(dto.getAid()));
        }
    }

    private void sendTaskMQ(UserOtherDTO dto) {
        if (dto.getUid().equals(dto.getAid())) {
            return;
        }
        TemplateTaskMqBO bo = TemplateTaskMqBO.newBuilder().triggerName(TaskTriggerName.VIEW_PROFILE).completeCount(1).uid(dto.getUid()).build();
        rabbitTemplate.convertAndSend(MQConstant.TEMPLATE_TASK_EXCHANGE, MQConstant.TEMPLATE_TASK_PRE + ".user_info", JSON.toJSONString(bo));
    }
}
