package com.quhong.service.event.unit;

import com.quhong.constant.activity.unit.EventUnitType;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.app.config.activity.unit.CurrencyUnit;
import com.quhong.dao.datas.app.config.activity.unit.EventUnit;
import com.quhong.data.dto.event.unit.BaseDTO;
import com.quhong.data.vo.event.unit.*;
import com.quhong.enums.RewardItemType;
import com.quhong.redis.event.EventCurrencyUnitRedis;
import com.quhong.service.money.CurrencyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-06-04 15:18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UnitEventPageInitService {
    private final AppConfigActivityDao appConfigActivityDao;
    private final EventCurrencyUnitRedis eventCurrencyUnitRedis;
    private final CurrencyService currencyService;

    public UnitPageInitVO pageInit(BaseDTO dto) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventCode());
        if (ObjectUtils.isEmpty(configData.getUnits())) {
            return new UnitPageInitVO()
                    .setEventCode(dto.getEventCode())
                    .setUid(dto.getUid())
                    .setStartTime(configData.getStartTime())
                    .setEndTime(configData.getEndTime());
        }
        List<CurrencyUnitVO> balances = fillBalances(dto, configData);
        List<TaskUnitVO> tasks = fillTasks(dto, configData);
        List<AwardPoolUnitVO> awardPools = fillAwardPools(dto, configData);
        List<RankUnitVO> rankConfigs = fillRankConfigs(dto, configData);

        return new UnitPageInitVO()
                .setEventCode(dto.getEventCode())
                .setUid(dto.getUid())
                .setStartTime(configData.getStartTime())
                .setEndTime(configData.getEndTime())
                .setBalances(balances)
                .setTasks(tasks)
                .setAwardPools(awardPools)
                .setRankConfigs(rankConfigs)
                ;

    }

    private List<RankUnitVO> fillRankConfigs(BaseDTO dto, AppConfigActivityData configData) {
        return configData.getUnits().stream()
                .filter(unit -> unit.getUnitType() == EventUnitType.RANK)
                .map(unit -> fillRankUnitVO(dto, unit))
                .collect(Collectors.toList());
    }

    private RankUnitVO fillRankUnitVO(BaseDTO dto, EventUnit unit) {
        return new RankUnitVO(unit);
    }

    private List<AwardPoolUnitVO> fillAwardPools(BaseDTO dto, AppConfigActivityData configData) {
        return configData.getUnits().stream()
                .filter(unit -> unit.getUnitType() == EventUnitType.AWARD_POOL)
                .map(unit -> fillAwardPoolUnitVO(dto, unit))
                .collect(Collectors.toList());
    }

    private AwardPoolUnitVO fillAwardPoolUnitVO(BaseDTO dto, EventUnit unit) {
        return new AwardPoolUnitVO(unit);
    }

    private List<TaskUnitVO> fillTasks(BaseDTO dto, AppConfigActivityData configData) {
        return configData.getUnits().stream()
                .filter(unit -> unit.getUnitType() == EventUnitType.TASK)
                .map(unit -> fillTaskUnitVO(dto, unit))
                .collect(Collectors.toList());
    }

    private TaskUnitVO fillTaskUnitVO(BaseDTO dto, EventUnit unit) {
        //TODO 任务积分
        return new TaskUnitVO(unit, 0L);
    }

    private List<CurrencyUnitVO> fillBalances(BaseDTO dto, AppConfigActivityData configData) {
        return configData.getUnits().stream()
                .filter(unit -> unit.getUnitType() == EventUnitType.CURRENCY)
                .map(unit -> fillCurrencyUnitVO(dto, unit))
                .collect(Collectors.toList());
    }

    private CurrencyUnitVO fillCurrencyUnitVO(BaseDTO dto, EventUnit unit) {
        long balance = findBalance(dto, unit);
        return new CurrencyUnitVO(unit, balance);
    }

    private long findBalance(BaseDTO dto, EventUnit unit) {
        CurrencyUnit currencyUnit = unit.getCurrencyUnit();
        long balance;
        switch (currencyUnit.getType()) {
            case RewardItemType.GOLD:
                balance = currencyService.getRealCurrency1Balance(dto.getUid()).longValue();
                break;
            case RewardItemType.DIAMOND:
                balance = currencyService.getRealCurrency2Balance(dto.getUid()).longValue();
                break;
            case RewardItemType.EVENT_CURRENCY:
            default:
                balance = eventCurrencyUnitRedis.getBalance(dto.getEventCode(), unit.getUnitId(), dto.getUid());
                break;
        }
        return balance;
    }
}
