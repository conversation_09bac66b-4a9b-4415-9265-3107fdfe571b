package com.quhong.service.event.unit;

import com.quhong.common.enums.HttpCode;
import com.quhong.constant.activity.EventGroupConstant;
import com.quhong.constant.activity.unit.EventUnitType;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.ActorExternalDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.data.dto.event.unit.RankDTO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.exceptions.WebException;
import com.quhong.players.ActorMgr;
import com.quhong.redis.event.EventRankUnitService;
import com.quhong.service.event.model.EventUserGroupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-06-04 19:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UnitEventRankService {
    private final ActorMgr actorMgr;
    private final AppConfigActivityDao appConfigActivityDao;
    private final EventRankUnitService eventRankUnitService;
    private final EventUserGroupService eventUserGroupService;
    private final ActorExternalDao actorExternalDao;

    /**
     * 排行榜查询
     *
     * @param dto 排行榜查询参数
     * @return 排行榜结果
     */
    public ModelRankVO<RankRowVO> rank(RankDTO dto) {
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventCode());
        if (DateHelper.getCurrentTime() < configData.getStartTime()) {
            throw new WebException(HttpCode.EVENT_NOT_START);
        }

        return eventRankUnitService.rank(currActor, dto.getEventCode(), dto.getUnitId());
    }

    public void increaseRankScore(int eventCode, int unitId, String uid, double incr) {
        eventRankUnitService.rankValueIncrease(eventCode, unitId, uid, incr);
    }

    public void coverRankScore(Integer eventCode, Integer unitId, String uid, long count) {
        eventRankUnitService.coverRankValue(eventCode, unitId, uid, count);
    }

    /**
     * 检查活动配置，获取目标活动配置
     *
     * @param currActor 当前用户
     * @param currTime  当前时间
     * @param rankType  排行榜类型
     * @return 活动配置列表
     */
    public List<AppConfigActivityData> checkLimitAndGetConfigs(ActorData currActor, long currTime, int rankType) {
        List<AppConfigActivityData> configList = appConfigActivityDao.getListByGroupAndValid(EventGroupConstant.UNIT_EVENT, 1);
        if (ObjectUtils.isEmpty(configList)) {
            return new ArrayList<>(0);
        }
        if (actorExternalDao.isTester(currActor.getUid())) {
            return new ArrayList<>(0);
        }
        return configList.stream()
                .filter(config -> withInEventTime(currTime, config))
                .filter(config -> !ObjectUtils.isEmpty(config.getUnits()))
                .filter(config -> hasTargetRankUnitConfig(rankType, config))
                .filter(config -> isTargetUser(currActor, config))
                .collect(Collectors.toList());
    }

    /**
     * 判定是否为目标用户
     *
     * @param currActor 当前用户
     * @param config    活动配置
     * @return 是否为目标用户 true 是 false 否
     */
    private boolean isTargetUser(ActorData currActor, AppConfigActivityData config) {
        return eventUserGroupService.checkUserGroup(currActor, config);
    }

    /**
     * 判定当前时间是否在活动时间段内
     *
     * @param currTime 当前时间
     * @param config   活动配置
     * @return 是否在活动时间段内 true 是 false 否
     */
    private static boolean withInEventTime(long currTime, AppConfigActivityData config) {
        return currTime >= config.getStartTime() && currTime < config.getEndTime();
    }

    /**
     * 判定活动配置中是否有目标排行榜组件配置
     *
     * @param rankType 排行榜类型
     * @param config   活动配置
     * @return 是否有目标排行榜配置 true 是 false 否
     */
    private static boolean hasTargetRankUnitConfig(int rankType, AppConfigActivityData config) {
        return config.getUnits().stream()
                .filter(unit -> unit.getUnitType() == EventUnitType.RANK)
                .filter(unit -> unit.getRankUnit().getRankType() == rankType)
                .findFirst().orElse(null) != null;
    }

}
