package com.quhong.service.time.box.task;

import com.quhong.common.data.BaseHttpData;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.core.datas.DayTimeData;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.core.utils.SpringUtils;
import com.quhong.dao.ActiveRewardRecordDao;
import com.quhong.dao.datas.ActiveRewardRecordData;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.dto.TimeBoxDTO;
import com.quhong.dao.vo.TimeBoxVO;
import com.quhong.dao.vo.time.box.ReceiveUserInfo;
import com.quhong.dao.vo.time.box.TimeBoxJumpGameInfo;
import com.quhong.data.appConfig.user.info.timeBox.FakeCountConfig;
import com.quhong.data.appConfig.user.info.timeBox.TimeBoxJumpGameConfig;
import com.quhong.data.bo.common.award.pool.config.RecordLastLimitQueryBO;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.RewardItemType;
import com.quhong.exceptions.WebException;
import com.quhong.moderatoin.ModerationService;
import com.quhong.players.ActorMgr;
import com.quhong.service.ConfigApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/12/22 16:42
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TimeBoxTask {

    private final ActorMgr actorMgr;

    private final CdnUtils cdnUtils;

    private final ActiveRewardRecordDao activeRewardRecordDao;

    private final ConfigApi configApi;

    private final ModerationService moderationService;

    /**
     * 根据配置生成随机数
     *
     * @param dto        时间宝箱请求体
     * @param configKey  配置key
     * @param defaultMin 默认最小值
     * @param defaultMax 默认最大值
     * @param latch      信号量对象
     * @return 随机数
     */
    @Async("1MQueueDiscard")
    public Future<Integer> genFakeCount(TimeBoxDTO dto, String configKey, int defaultMin, int defaultMax, CountDownLatch latch) {
        try {
            FakeCountConfig coinFakeCountConfig = configApi.getJavaBeanVal(new ConfigDTO(dto.getUid(), configKey, AppConfigKeyConstant.STATUS_SERVER), FakeCountConfig.class);
            if (coinFakeCountConfig == null) { // 默认配置
                coinFakeCountConfig = new FakeCountConfig(defaultMin, defaultMax);
            }
            Integer randomCount = MathUtils.randomSplitInt(coinFakeCountConfig.getMin(), coinFakeCountConfig.getMax());
            return new AsyncResult<>(randomCount);
        } catch (Exception e) {
            log.error("error to query can receive count task", e);
            throw new WebException(dto, new HttpCode(HttpCode.SERVER_ERROR).setMsg("error to query can receive count task"));
        } finally {
            latch.countDown();
        }
    }

    /**
     * 获取近一个小时可领取的用户数量
     *
     * @param currTime 当前时间
     * @param latch    信号量对象
     * @return 近一个小时领取的用户数量
     */
    @Async("1MQueueDiscard")
    public Future<Long> getLastHourUserCount(long currTime, CountDownLatch latch) {
        try {
            long count = activeRewardRecordDao.queryUserCountByEventCodeAndRewardTypeAndStatusAndDuration(ActivityTypeEnum.TIME_BOX.getCode(),
                    RewardItemType.GOLD, 2, currTime - Duration.ofHours(1).getSeconds(), currTime);
            return new AsyncResult<>(count);
        } catch (Exception e) {
            log.error("error to query last hour user count task", e);
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.SERVER_ERROR).setMsg("error to query last hour user count task"));
        } finally {
            latch.countDown();
        }
    }

    /**
     * 获取近一个小时领取的金币数量
     *
     * @param currTime 当前时间
     * @param latch    信号量对象
     * @return 近一个小时领取的金币数量
     */
    @Async("1MQueueDiscard")
    public Future<Long> getLastHourCoinCount(long currTime, CountDownLatch latch) {
        try {
            long count = activeRewardRecordDao.queryCoinCountByEventCodeAndRewardTypeAndStatusAndDuration(ActivityTypeEnum.TIME_BOX.getCode(),
                    RewardItemType.GOLD, 2, currTime - Duration.ofHours(1).getSeconds(), currTime);
            return new AsyncResult<>(count);
        } catch (Exception e) {
            log.error("error to query last hour coin count task", e);
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.SERVER_ERROR).setMsg("error to query last hour coin count task"));
        } finally {
            latch.countDown();
        }
    }


    /**
     * 获取时间宝跳转游戏信息
     *
     * @param dto   时间宝箱DTO对象
     * @param latch 信号量对象
     * @return 时间宝砸蛋游戏信息对象
     */
    @Async("1MQueueDiscard")
    public Future<TimeBoxJumpGameInfo> getTimeBoxJumpGameInfo(TimeBoxDTO dto, CountDownLatch latch, TimeBoxVO vo) {
        try {
            TimeBoxJumpGameInfo timeBoxJumpGameInfo = new TimeBoxJumpGameInfo();
            String key;
            if (CollectionUtils.isEmpty(vo.getPrizes()) || vo.getPrizes().get(0).getType().equals(RewardItemType.GOLD)) {
                key = AppConfigKeyConstant.TIME_BOX_JUMP_GAME_CONFIG;
            } else {
                key = AppConfigKeyConstant.TIME_BOX_JUMP_GAME_COIN_CONFIG;
                timeBoxJumpGameInfo.setGamePointGame(1);
            }
            TimeBoxJumpGameConfig jumpGameConfig = configApi.getJavaBeanVal(new ConfigDTO(dto.getUid(), key, AppConfigKeyConstant.STATUS_SERVER), TimeBoxJumpGameConfig.class);
            if (jumpGameConfig == null) {//默认砸蛋
                jumpGameConfig = new TimeBoxJumpGameConfig(1, 2, "https://statics.kissu.mobi/icon/smash.png", "Smash egg");
                timeBoxJumpGameInfo.setGamePointGame(0);
            }
            SpringUtils.copyPropertiesIgnoreNull(jumpGameConfig, timeBoxJumpGameInfo);
            timeBoxJumpGameInfo.setIcon(cdnUtils.replaceUrlDomain(dto.getChannel(), timeBoxJumpGameInfo.getIcon(), 0));
            return new AsyncResult<>(timeBoxJumpGameInfo);
        } catch (Exception e) {
            log.error("error to query jump game info task", e);
            throw new WebException(dto, HttpCode.createHttpCode(HttpCode.SERVER_ERROR, false, "error to query jump game info task"));
        } finally {
            // 唤醒信号量
            latch.countDown();
        }
    }

    /**
     * 填充接收用户信息列表
     *
     * @param dto       时间宝DTO对象
     * @param currActor 当前用户数据
     * @param latch     信号量对象
     * @return 未来返回接收用户信息列表
     */
    @Async("1MQueueDiscard")
    public Future<List<ReceiveUserInfo>> fillReceiveUserInfoList(TimeBoxDTO dto, ActorData currActor, CountDownLatch latch) {
        try {
            //TODO 考虑从redis出（时长宝箱当月最近30条数据）
            List<ActiveRewardRecordData> rewardRecordDataList = queryRewardRecordDataFromDb(dto);
            List<ReceiveUserInfo> receiveUserInfoList = fillReceiveUserInfo(currActor, rewardRecordDataList);
            return new AsyncResult<>(receiveUserInfoList);
        } catch (Exception e) {
            log.error("error to query user info task", e);
            throw new WebException(dto, new HttpCode(HttpCode.SERVER_ERROR).setMsg("error to query user info task"));
        } finally {
            latch.countDown();
        }
    }

    private List<ReceiveUserInfo> fillReceiveUserInfo(ActorData currActor, List<ActiveRewardRecordData> rewardRecordDataList) {
        List<ReceiveUserInfo> receiveUserInfoList = new ArrayList<>(30);
        if (!ObjectUtils.isEmpty(rewardRecordDataList)) {
            List<ReceiveUserInfo> userInfos = rewardRecordDataList.stream()
                    .sorted(Comparator.comparingInt(ActiveRewardRecordData::getId).reversed())
                    .map(data -> fillReceiveUserInfo(data, currActor))
                    .collect(Collectors.toList());
            receiveUserInfoList.addAll(userInfos);
        }
        return receiveUserInfoList;
    }

    private List<ActiveRewardRecordData> queryRewardRecordDataFromDb(TimeBoxDTO dto) {
        DayTimeData monthTime = DateHelper.UTC.getMonthStartAndEndTime(0);
        return activeRewardRecordDao.queryLastLimitBy(RecordLastLimitQueryBO.builder()
                .eventCode(ActivityTypeEnum.TIME_BOX.getCode())
                .status(2)
                .startTime((long) monthTime.getTime())
                .endTime((long) monthTime.getEndTime())
                .greaterThanRewardId(-1)
                .limit(30)
                .build());
    }


    private ReceiveUserInfo fillReceiveUserInfo(ActiveRewardRecordData recordData, ActorData currActor) {
        ActorData recordActor = actorMgr.getActorData(recordData.getUid());
        String name = "********";
        String head = "https://statics.kissu.mobi/head/default/frame_8.png_75.webp";
        if (recordActor != null) {
            name = recordActor.getName();
            if (StringUtils.hasLength(recordActor.getHeadIcon())) {
                head = moderationService.dealRankHeadModeration(recordActor);
            }
        }
        return new ReceiveUserInfo()
                .setUid(recordData.getUid())
                .setName(name)
                .setHead(cdnUtils.replacePrivateHeadAndUrlDomain(currActor.getChannel(), head, 0, recordData.getUid()))
                .setCoinCount(recordData.getNums())
                .setAwardIcon("")
                .setAwardType(recordData.getRewardType());
    }
}
