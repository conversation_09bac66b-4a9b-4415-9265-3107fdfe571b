package com.quhong.service.time.box;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.ApiResult;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.ActType;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.constant.WarnName;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.*;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.TimeBoxRewardConfigData;
import com.quhong.dao.dto.TimeBoxDTO;
import com.quhong.dao.vo.TimeBoxVO;
import com.quhong.dao.vo.smashegg.RewardInfoVo;
import com.quhong.dao.vo.time.box.ReceiveUserInfo;
import com.quhong.dao.vo.time.box.TimeBoxJumpGameInfo;
import com.quhong.data.dto.TimeBoxReceiveDTO;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.RewardItemType;
import com.quhong.exceptions.WebException;
import com.quhong.players.ActorMgr;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.TimeBoxService;
import com.quhong.service.money.CurrencyService;
import com.quhong.service.time.box.task.TimeBoxTask;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName TimeBoxReceiveService
 * <AUTHOR>
 * @date 2021/11/5 17:17
 */
@Service
public class TimeBoxReceiveService {
    private static final Logger logger = LoggerFactory.getLogger(TimeBoxReceiveService.class);
    /**
     * 活动名
     */
    private static final String ACTIVITY_NAME = "time box";

    @Autowired
    private TimeBoxDao timeBoxDao;
    @Autowired
    private TimeBoxService timeBoxService;
    @Autowired
    private RewardInfoDao rewardInfoDao;
    @Autowired
    private ActorMgr actorMgr;
    @Autowired
    private GiveOutRewardService giveOutRewardService;
    @Autowired
    private CdnUtils cdnUtils;

    @Resource(name = BaseRedisBeanConstant.OTHER_BEAN)
    protected StringRedisTemplate otherRedisTemplate;

    @Resource
    private ActorDangerAccountDao actorDangerAccountDao;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private TimeBoxRewardConfigDao timeBoxRewardConfigDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private ActorExternalDao actorExternalDao;
    @Resource
    private TimeBoxTask timeBoxTask;
    @Resource
    private CurrencyService currencyService;

    public ApiResult<TimeBoxVO> getReceiveResult(TimeBoxDTO dto) {
        if (StringUtils.isEmpty(dto.getRoomId()) || !dto.getRoomId().startsWith("r:")) {
            logger.info("param error,roomId is empty or format error, roomId={}", dto.getRoomId());
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }

        TimeBoxVO vo = new TimeBoxVO();
        vo.setTimeBoxCanReceive(0);

        String timeBoxReceiveRecordKey = timeBoxService.getRedisKey(TimeBoxService.TIME_BOX_RECEIVE_RECORD, dto.getUid());
        String timeBoxReceiveTimesKey = timeBoxService.getRedisKey(TimeBoxService.TIME_BOX_RECEIVE_TIME, dto.getUid());

        ActorData currActor = actorMgr.getActorData(dto.getUid());
        if (currActor == null) {
            return ApiResult.getError(HttpCode.USER_NOT_EXISTS);
        }

        ApiResult<TimeBoxVO> checkResult = beforeSomeChecks(dto, currActor, vo);
        if (checkResult != null) return checkResult;

        List<TimeBoxRewardConfigData> configList = timeBoxRewardConfigDao.queryBy(dto.getChannel(), 0, 1);

        // 判断是否已经领取完
        String receiveRecord = otherRedisTemplate.opsForValue().get(timeBoxReceiveRecordKey);
        if (StringUtils.isEmpty(receiveRecord)) {
            logger.error("time box receive fail. dto={}", dto);
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }
        int receiveRecordTimes = Integer.parseInt(receiveRecord);
        int maxBoxTimes = timeBoxService.getMaxBoxTimes(configList);
        if (receiveRecordTimes > maxBoxTimes) {
            // 已经领取完
            logger.debug("user was finished receiving time box. dto={} ", dto);
            return ApiResult.getOk(vo);
        }

        TimeBoxRewardConfigData currBoxConfig = configList.stream()
                .filter(config -> config.getBoxTimes() == receiveRecordTimes)
                .findFirst().orElse(null);
        if (currBoxConfig == null) {
            String detail = String.format("channel=%s receiveRecordTimes=%d  rewardConfig=%s time box config is lose", dto.getChannel(), receiveRecordTimes, "null");
            logger.error(detail);
            monitorSender.customMarkdown(WarnName.COMMON, detail);
            return ApiResult.getOk(vo);
        }
        boolean newUser = timeBoxService.decideNewUser(currActor);
        // 判断是否到领取时间
        ApiResult<TimeBoxVO> checkExpireTimeResult = checkExpireTime(dto, timeBoxReceiveTimesKey, timeBoxReceiveRecordKey, vo, currBoxConfig, newUser);
        if (checkExpireTimeResult != null) return checkExpireTimeResult;

        // 获取下一次的领取时间
        int nextReceiveCd = fillNextReceiveCd(receiveRecordTimes, configList, timeBoxReceiveRecordKey, timeBoxReceiveTimesKey, dto, newUser);
        // 更新日志表
        timeBoxDao.updateTimeBoxLog(dto.getUid(), receiveRecordTimes);
        // 查找对应的奖励

        // 生成金币奖励
        List<RewardInfoData> rewardInfoVoList = findRewards(currBoxConfig, dto, newUser);
        List<RewardInfoVo> prizes = getPrizes(rewardInfoVoList, currActor);

        // 异步发放奖励（改为同步）
        giveRewards(dto, rewardInfoVoList);
        fillVO(dto, vo, prizes, nextReceiveCd, currActor);
        return ApiResult.getOk(vo);
    }

    private void fillVO(TimeBoxDTO dto, TimeBoxVO vo, List<RewardInfoVo> prizes, int nextReceiveCd, ActorData currActor) {
        vo.setButtonType(2);//Tikko 4.5.3弃用
        vo.setPrizes(prizes);
        vo.setTimeBoxCd(nextReceiveCd);
        vo.setTimeBoxCanReceive(1);
        if (nextReceiveCd == 1) {
            vo.setTimeBoxCanReceive(0);
            vo.setTimeBoxCd(0);
        }
        long currTime = DateHelper.getCurrTime();

        fillNewTimeBoxLogic(dto, vo, currActor, currTime);
        vo.setCoinBalance(currencyService.getRealCurrency1Balance(currActor.getUid()).toString());
    }

    private void fillNewTimeBoxLogic(TimeBoxDTO dto, TimeBoxVO vo, ActorData currActor, long currTime) {
        List<ReceiveUserInfo> receiveUserInfoList;
        TimeBoxJumpGameInfo timeBoxJumpGameInfo;
        long lastHourCoinCount;
        long lastHourUserCount;
        int fakeCoinCount;
        int fakeUserCount;
        CountDownLatch latch = new CountDownLatch(6);
        Future<List<ReceiveUserInfo>> receiveUserInfoListFuture = timeBoxTask.fillReceiveUserInfoList(dto, currActor, latch);
        Future<TimeBoxJumpGameInfo> timeBoxJumpGameInfoFuture = timeBoxTask.getTimeBoxJumpGameInfo(dto, latch, vo);
        Future<Long> lastHourCoinCountFuture = timeBoxTask.getLastHourCoinCount(currTime, latch);
        Future<Long> lastHourUserCountFuture = timeBoxTask.getLastHourUserCount(currTime, latch);
        Future<Integer> fakeCoinCountFuture = timeBoxTask.genFakeCount(dto, AppConfigKeyConstant.TIME_BOX_FAKE_COIN_COUNT, 90000, 100000, latch);
        Future<Integer> fakeUserCountFuture = timeBoxTask.genFakeCount(dto, AppConfigKeyConstant.TIME_BOX_FAKE_ONLINE_USER_COUNT_CONFIG, 1000, 2000, latch);
        try {
            boolean await = latch.await(10, TimeUnit.SECONDS);
            if (!await) {
                throw new WebException(dto, HttpCode.createHttpCode(HttpCode.SERVER_ERROR, false, "time box task timeout"));
            }
            // 查询最新30条奖励领取记录
            receiveUserInfoList = receiveUserInfoListFuture.get();
            // 填充游戏跳转信息
            timeBoxJumpGameInfo = timeBoxJumpGameInfoFuture.get();
            // 拉取1小时内金币领取数量
            lastHourCoinCount = lastHourCoinCountFuture.get();
            // 拉取1小时内领取人数
            lastHourUserCount = lastHourUserCountFuture.get();
            // 模拟可领取数量
            fakeCoinCount = fakeCoinCountFuture.get();
            //模拟可领取人数
            fakeUserCount = fakeUserCountFuture.get();

        } catch (WebException e) {
            logger.error("error to fillNewTimeBoxLogic, httpCode={}", JSON.toJSONString(e.getHttpCode()), e);
            monitorSender.info(WarnName.COMMON, "error to fillNewTimeBoxLogic", JSON.toJSONString(e.getHttpCode()));
            return;
        } catch (Exception e) {
            logger.info("error to time box task,msg={}", e.getMessage(), e);
            throw new WebException(dto, HttpCode.createHttpCode(HttpCode.SERVER_ERROR, false, "error to time box task"));
        }

        vo.setReceiveUserInfoList(receiveUserInfoList);
        vo.setJumpGameInfo(timeBoxJumpGameInfo);
        vo.setReceiveCoinCount((int) lastHourCoinCount);
        vo.setReceiveUserCount((int) lastHourUserCount);
        vo.setCanReceiveCoinCount(fakeCoinCount);
        vo.setCanReceiveUserCount(fakeUserCount);
    }


    private ApiResult<TimeBoxVO> checkExpireTime(TimeBoxDTO dto, String timeBoxReceiveTimesKey, String timeBoxReceiveRecordKey, TimeBoxVO vo, TimeBoxRewardConfigData currBoxConfig, boolean newUser) {
        Boolean aBoolean = otherRedisTemplate.hasKey(timeBoxReceiveTimesKey);
        Long expire = otherRedisTemplate.getExpire(timeBoxReceiveTimesKey);
        logger.debug("expire={}", expire);
        if (aBoolean != null && aBoolean) {
            //还没有到领取时间
            logger.debug("user can not receive time box now. dto={} ", dto);
            if (expire != null) {
                vo.setTimeBoxCd(expire.intValue());
            }
            vo.setTimeBoxCanReceive(1);
            vo.setNextTimeBoxNeedTime(newUser ? currBoxConfig.getWaitDuration() : currBoxConfig.getSecWaitDuration());
            return ApiResult.getOk(vo);
        }
        return null;
    }

    private void giveRewards(TimeBoxDTO dto, List<RewardInfoData> rewardInfoVoList) {
//        BaseTaskFactory.getFactory().addSlow(new Task() {
//            @Override
//            protected void execute() throws Exception {
        Integer id = timeBoxDao.selectOneByDateAndUid(dto.getUid());
        giveOutRewardService.giveOutReward(dto.getUid(), rewardInfoVoList, id, ACTIVITY_NAME, ActType.TIME_BOX_GOLD);
//            }
//        });
    }

    private List<RewardInfoData> findRewards(TimeBoxRewardConfigData configData, TimeBoxDTO dto, boolean newUser) {
        Integer coinRandomMin = newUser ? configData.getCoinRandomMin() : configData.getSecCoinRandomMin();
        Integer coinRandomMax = newUser ? configData.getCoinRandomMax() : configData.getSecCoinRandomMax();
        Integer coin = MathUtils.randomSplitInt(coinRandomMin, coinRandomMax);
        //        List<RewardInfoData> rewardInfoVoList = getRewardInfoDataList(dto.getChannel(), receiveRecordTimes);
//        if (rewardInfoVoList == null || rewardInfoVoList.size() == 0) {
//            String detail = String.format("channel=%s receiveRecordTimes=%d rewardInfoVoList=%s time box config is lose", dto.getChannel(), receiveRecordTimes, JSON.toJSONString(rewardInfoVoList));
//            logger.error(detail);
//            monitorSender.customMarkdown(WarnName.COMMON, detail);
//            return ApiResult.getOk(vo);
//        }
        String dailyCoinMaxLimitRedisKey = timeBoxService.getDailyCoinMaxLimitRedisKey(dto.getTimeZone());
        otherRedisTemplate.opsForValue().increment(dailyCoinMaxLimitRedisKey, coin);
        otherRedisTemplate.expire(dailyCoinMaxLimitRedisKey, 1, TimeUnit.DAYS);
        int rewardType = configData.getRewardType().equals(0) ? RewardItemType.GOLD : RewardItemType.GAME_COIN;
        String rewardName = configData.getRewardType().equals(0) ? "coin" : "game coin";
        return new ArrayList<RewardInfoData>() {{
            // 金币大图标(客户端写死) 后台传会有hotchat tikko 图标不一致兼容问题
            RewardInfoData reward = new RewardInfoData(ActivityTypeEnum.TIME_BOX.getCode(), rewardName, "", coin, rewardType, 0, "");
            reward.setRoomId(dto.getRoomId());
            add(reward);
        }};
    }

    private ApiResult<TimeBoxVO> beforeSomeChecks(TimeBoxDTO dto, ActorData actorData, TimeBoxVO vo) {
        // 时长宝箱房主白名单限制判定
        String roomOwnerId = RoomUtils.getRoomOwnerId(dto.getRoomId());
        if (timeBoxService.checkRoomOwnerWhite(roomOwnerId)) {
            return ApiResult.getError(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "owner is not in time box white list"));
        }

        // 每日发放金币上限判定
        if (timeBoxService.checkDailyCoinLimit(dto.getUid(), dto.getTimeZone()))
            return ApiResult.getError(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "daily coin receive was great than limit"));

        if (ServerConfiguration.isTest()) {
            return null;
        }

        boolean dangerousActor = actorDangerAccountDao.isDangerousActor(actorData);
        int bossPoints = actorConfigDao.getConfigIntValue(actorData.getUid(), ActorConfigDao.BOSS_POINTS);
        boolean isTester = actorExternalDao.checkCurrActorIsTester(actorData.getUid());
        //防刷
        if ((check(dto) || (dangerousActor && bossPoints < 500)) && !isTester) {
            logger.info("user have error for ip or imei or imsi. uid={} ip={} imei={} imsi={}", dto.getUid(), dto.getRemoteIP(), dto.getImei(), dto.getImsi());
            return ApiResult.getOk(vo);
        }
        return null;
    }

    @Deprecated
    private List<RewardInfoData> getRewardInfoDataList(String channel, int receiveRecordTimes) {
        List<TimeBoxRewardConfigData> timeBoxRewardConfigDataList = timeBoxRewardConfigDao.queryBy(channel, receiveRecordTimes, 1);
        if (timeBoxRewardConfigDataList != null && timeBoxRewardConfigDataList.size() != 0) {
            TimeBoxRewardConfigData timeBoxRewardConfigData = timeBoxRewardConfigDataList.get(0);
            Integer id = timeBoxRewardConfigData.getId();
            return rewardInfoDao.queryListByActivityTypeAndActivityId(ActivityTypeEnum.TIME_BOX.getCode(), id);
        }
        return null;
    }


    private boolean check(TimeBoxDTO dto) {
        TimeBoxReceiveDTO timeBoxReceiveDTO = new TimeBoxReceiveDTO();
        timeBoxReceiveDTO.setUid(dto.getUid());
        timeBoxReceiveDTO.setChannel(dto.getChannel());
        timeBoxReceiveDTO.setTimeZone(dto.getTimeZone());
        timeBoxReceiveDTO.setRemoteIp(dto.getRemoteIP());
        timeBoxReceiveDTO.setImei(dto.getImei());
        timeBoxReceiveDTO.setImsi(dto.getImsi());
        return timeBoxService.checkIp(timeBoxReceiveDTO);
    }

    private int fillNextReceiveCd(int receiveRecordTimes, List<TimeBoxRewardConfigData> configList, String timeBoxReceiveRecordKey, String timeBoxReceiveTimesKey, TimeBoxDTO dto, boolean newUser) {
        int nextReceiveCd = 1;
        int nextBoxTimes = receiveRecordTimes + 1;
        TimeBoxRewardConfigData configData = configList.stream()
                .filter(data -> data.getBoxTimes() > receiveRecordTimes)
                .min(Comparator.comparingInt(TimeBoxRewardConfigData::getBoxTimes))
                .orElse(null);
        if (configData != null) {
            nextReceiveCd = newUser ? configData.getWaitDuration() : configData.getSecWaitDuration();
            nextBoxTimes = configData.getBoxTimes();
        }

        //更新缓存
        otherRedisTemplate.opsForValue().set(timeBoxReceiveRecordKey, nextBoxTimes + "", timeBoxService.getExpireTimeByTimeZone(dto.getTimeZone()), TimeUnit.SECONDS);
        int timeout = nextReceiveCd;
        otherRedisTemplate.opsForValue().set(timeBoxReceiveTimesKey, nextBoxTimes + "", timeout, TimeUnit.SECONDS);
//        switch (receiveRecordTimes + 1) {
//            case 2:
//                nextReceiveCd = TimeBoxService.SECOND_RECEIVE_CD;
//                break;
//            case 3:
//                nextReceiveCd = TimeBoxService.THIRD_RECEIVE_CD;
//                break;
//        }
        return nextReceiveCd;
    }

    private List<RewardInfoVo> getPrizes(List<RewardInfoData> rewardInfoDataList, ActorData actorData) {
        List<RewardInfoVo> prizes = new ArrayList<>();
        for (RewardInfoData data : rewardInfoDataList) {
            RewardInfoVo vo = new RewardInfoVo();
            vo.setIcon(cdnUtils.replaceUrlDomain(actorData.getChannel(), data.getBigIcon().trim(), 0));
            vo.setType(data.getType());
            vo.setNums(data.getName() + " X" + data.getNums());
            vo.setNum(data.getNums());
            vo.setText(data.getName() + " X" + "#num");
            vo.setName(data.getName());
            prizes.add(vo);
        }
        return prizes;
    }
}
