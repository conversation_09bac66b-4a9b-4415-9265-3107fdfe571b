package com.quhong.service.activity.cupid;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.WarnName;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MasterUtils;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.ActorExternalDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.config.TaskConfig;
import com.quhong.data.thData.DrawPrizesRecordLogData;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.activity.superPlayer.OtherRankingListVO;
import com.quhong.data.vo.event.cupid.CupidVO;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.RewardItemType;
import com.quhong.exceptions.WebException;
import com.quhong.moderatoin.ModerationService;
import com.quhong.mq.ActivityMQProduct;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.mq.enums.MQConstant;
import com.quhong.players.ActorMgr;
import com.quhong.redis.BaseZSetRedis;
import com.quhong.redis.base.ActivityCommonRedis;
import com.quhong.redis.base.currency.task.BaseEveryLimitCurrencyRedis;
import com.quhong.report.EventReport;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.ItemsChangeService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.common.RewardService;
import com.quhong.service.common.RoomFloatingImService;
import com.quhong.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * ustar 丘比特活动复用
 *
 * <AUTHOR>
 * @date 2023/8/23
 */
@Slf4j
@Service
public class CupidService {
    private static final boolean PROD = ServerConfiguration.isProduct();
    private static final String ACTIVITY_TITLE = "Cupid Activity";
    private static final Integer RECORD_PAGE_SIZE = 10;
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final ActivityTypeEnum EVENT_ENUM = ActivityTypeEnum.EVENT_CUPID;

    private static final String EVENT_URL = PROD ? "https://videochat.kissu.site/eros_for_cupid/" : "https://testvideochat.kissu.site/eros_for_cupid/";

    private static final String NOTICE_IMG = "https://statics.kissu.mobi/Event/Cupid/09/notice.jpg";

    private static final String BALANCE_ICON = "https://statics.kissu.mobi/Event/Cupid/09/notice_08_v1.png";

    private static final String BALANCE_NAME = "arrow";
    private static final List<RewardTaskConfig> SEND_GIFT_REWARD = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_ENUM.getCode(), 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_ENUM.getCode(), -1, RewardItemType.MEDAL, PROD ? 900041 : 900017));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 30, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 30, RewardItemType.SEAT_FRAME, PROD ? 251 : 55));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_ENUM.getCode(), 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_ENUM.getCode(), -1, RewardItemType.MEDAL, PROD ? 900042 : 90003));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 15, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 15, RewardItemType.SEAT_FRAME, PROD ? 251 : 55));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_ENUM.getCode(), 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_ENUM.getCode(), -1, RewardItemType.MEDAL, PROD ? 900043 : 90001));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 7, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 7, RewardItemType.SEAT_FRAME, PROD ? 251 : 55));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_ENUM.getCode(), 1250, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 3, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 3, RewardItemType.SEAT_FRAME, PROD ? 251 : 55));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));

    }};

    private static final List<RewardTaskConfig> RECEIVE_GIFT_REWARD = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_ENUM.getCode(), 10000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_ENUM.getCode(), -1, RewardItemType.MEDAL, PROD ? 900044 : 10086));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 30, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 30, RewardItemType.SEAT_FRAME, PROD ? 252 : 65));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_ENUM.getCode(), 7500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_ENUM.getCode(), -1, RewardItemType.MEDAL, PROD ? 900045 : 900018));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 20, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 20, RewardItemType.SEAT_FRAME, PROD ? 252 : 65));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_ENUM.getCode(), 5000, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_ENUM.getCode(), -1, RewardItemType.MEDAL, PROD ? 900046 : 90002));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 10, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 10, RewardItemType.SEAT_FRAME, PROD ? 252 : 65));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_ENUM.getCode(), 2500, RewardItemType.GOLD, 0, 1));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 7, RewardItemType.ENTER_EFFECT, PROD ? 253 : 97));
            add(new RewardInfoData(EVENT_ENUM.getCode(), 7, RewardItemType.SEAT_FRAME, PROD ? 252 : 65));
        }};
        IntStream.rangeClosed(4, 10).forEach(top -> add(new RewardTaskConfig().setCheckParams(top).setRewards(rewards4)));
    }};

    @Resource
    private ActivityCommonRedis activityCommonRedis;
    //    @Resource
//    private ResourceDistributionService distributionService;
    @Resource
    private GiveOutRewardService giveOutRewardService;
    @Resource
    private ItemsChangeService itemsChangeService;
    @Resource
    private ActivityMQProduct activityMQProduct;
    @Resource
    private EventCupidConfig eventCupidConfig;
    @Resource
    private ActorMgr actorMgr;
    @Resource
    private ModerationService moderatorService;
    @Resource
    private AppConfigActivityDao appConfigActivityDao;
    @Resource
    private BaseZSetRedis baseZSetRedis;
    @Resource(name = "cupidUserBalanceTaskLimitRedis")
    private BaseEveryLimitCurrencyRedis<TaskConfig> cupidUserBalanceTaskLimitRedis;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private OfficialNoticeService officialNoticeService;
    @Resource
    private MasterUtils masterUtils;
    @Resource
    private RewardService rewardService;
    @Resource
    private RoomFloatingImService roomFloatingImService;
    @Resource
    private EventReport eventReport;
    @Resource
    private ActorExternalDao actorExternalDao;

    private static void fillDrawRecord(String[] prizeKeySplit, Map<String, EventCupidConfig.CommonAwardConfig> rewardConfigMap, List<CupidVO.PrizeConfig> prizeConfigList) {
        String prizeKey = prizeKeySplit[0];
        String prizeTime = prizeKeySplit[1];
        EventCupidConfig.CommonAwardConfig awardConfig = rewardConfigMap.get(prizeKey);
        if (awardConfig != null) {
            CupidVO.PrizeConfig prizeConfig = new CupidVO.PrizeConfig();
            prizeConfig.setIconEn(awardConfig.getIconEn());
            prizeConfig.setNameEn(awardConfig.getNameEn());
            prizeConfig.setNameAr(awardConfig.getNameAr());
            prizeConfig.setPreviewVideoUrl(awardConfig.getPreviewVideoUrl());
            prizeConfig.setCtime(Integer.valueOf(prizeTime));
            prizeConfigList.add(prizeConfig);
        }
    }

    public String getRecordActivityKey(String activityId, String uid) {
        return String.format("event:draw_history:%s:%s", activityId, uid);
    }

    /**
     * 活动期间个人余额
     */
    public String getCupidUserBalance(String activityId) {
        return String.format("zset:event:%s", activityId);
    }

    public String getSendGiftRankKey(String activityId) {
        return String.format("sendGiftRank:%s", activityId);
    }

    public String getReceiveGiftRankingKey(String activityId) {
        return String.format("receiveGiftRanking:%s", activityId);
    }

    private void fillCupidVORanking(CupidVO vo, String rankingKey, ActorData currActor, String activityId) {
        String uid = currActor.getUid();
        List<OtherRankingListVO> rankingList = new ArrayList<>();
        OtherRankingListVO myRank = new OtherRankingListVO();
        Map<String, Integer> brainRankingMap = activityCommonRedis.getCommonRankingMap(rankingKey, 10);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : brainRankingMap.entrySet()) {
            OtherRankingListVO rankingVO = new OtherRankingListVO();
            String aid = entry.getKey();
            ActorData rankActor = actorMgr.getActorDataFromCache(aid);
            if (rankActor != null) {
                rankingVO.setHead(moderatorService.dealRankHeadModeration(rankActor));
                rankingVO.setName(rankActor.getName());
            }
            rankingVO.setUid(aid);
            rankingVO.setScore(entry.getValue());
            rankingVO.setRank(rank);
            if (aid.equals(uid)) {
                BeanUtils.copyProperties(rankingVO, myRank);
            }
            rankingList.add(rankingVO);
            rank += 1;
        }

        if (myRank.getRank() <= 0) {
            myRank.setName(currActor.getName());
            myRank.setUid(uid);
            myRank.setHead(moderatorService.dealRankHeadModeration(currActor));
            myRank.setScore(activityCommonRedis.getCommonZSetRankingScore(rankingKey, uid));
            myRank.setRank(-1);
        }

        String cupidKey = getReceiveGiftRankingKey(activityId);
        if (cupidKey.equals(rankingKey)) {
            vo.setReceiveGiftRankList(rankingList);
            vo.setMyReceiveGiftRank(myRank);
        } else {
            vo.setSendGiftRankList(rankingList);
            vo.setMySendGiftRank(myRank);
        }
    }

    public CupidVO cupidConfig(String activityId, String uid) {

        AppConfigActivityData eventConfig = appConfigActivityDao.getOneByEventCodeThrowWebException(Integer.valueOf(activityId));
        ActorData currActor = actorMgr.getCurrActorData(uid);
        CupidVO vo = new CupidVO();
        vo.setStartTime(eventConfig.getStartTime());
        vo.setEndTime(eventConfig.getEndTime());
        // 个人余额
        CountVO selfData = baseZSetRedis.getOne(getCupidUserBalance(activityId), currActor.getUid());
        vo.setBalance(selfData.getCount());

        // 设置奖池信息
        List<CupidVO.PrizeConfig> prizeConfigList = new ArrayList<>();
        List<EventCupidConfig.CommonAwardConfig> cupidConfigList = eventCupidConfig.getCupidConfigList();
        for (EventCupidConfig.CommonAwardConfig commonAwardConfig : cupidConfigList) {
            CupidVO.PrizeConfig prizeConfig = new CupidVO.PrizeConfig();
            BeanUtils.copyProperties(commonAwardConfig, prizeConfig);
            prizeConfigList.add(prizeConfig);
        }
        vo.setPrizeConfigList(prizeConfigList);

        // 设置送礼排行榜
        String sendGiftRankKey = getSendGiftRankKey(activityId);
        fillCupidVORanking(vo, sendGiftRankKey, currActor, activityId);
//        OtherRankingActivityData activityDaily = otherActivityService.getOtherRankingActivity(DAILY_ACTIVITY_ID);
//        String cupidDailyKey = getCupidDailyRankingKey(DAILY_ACTIVITY_ID, activityDaily.getRoundNum());
//        fillCupidVORanking(vo, cupidDailyKey, uid, actorData, DAILY_ACTIVITY_ID);

        // 设置收礼排行榜
        String receiveGiftRankingKey = getReceiveGiftRankingKey(activityId);
        fillCupidVORanking(vo, receiveGiftRankingKey, currActor, activityId);
        return vo;
    }

    /**
     * 抽奖
     */
    public List<CupidVO.PrizeConfig> cupidDraw(String activityId, String uid, int amount) {
        AppConfigActivityData eventConfig = appConfigActivityDao.getOneByEventCodeThrowWebException(Integer.valueOf(activityId));
        ActorData currActor = actorMgr.getCurrActorData(uid);
        long currTime = DateHelper.getCurrTime();
        checkLimit(currTime, eventConfig, currActor);

        List<CupidVO.PrizeConfig> prizeConfigList = new ArrayList<>();
        dealDrawReward(activityId, uid, amount, prizeConfigList, currTime);

        // 4、奖励下发
        if (!ObjectUtils.isEmpty(prizeConfigList)) {
            List<RewardInfoData> rewards = prizeConfigList.stream()
                    .map(prizeConfig -> this.fillRewardInfoData(prizeConfig, activityId))
                    .collect(Collectors.toList());
            giveOutRewardService.giveOutReward(uid, rewards);
            itemsChangeService.itemChangeReport(uid, rewards, EVENT_ENUM.getCode(), currTime, "draw");
            rewards.forEach(reward -> activityMQProduct.sendObjectMq(MQConstant.EVENT_COST_COUNT_ROUTE, reward));
            // 大奖全服官方通知
            prizeConfigList.forEach(prizeConfig -> this.checkAndSendGlobalNotice(prizeConfig, activityId, currActor));
            reportDrawResToThinkData(uid, amount, rewards, currActor, currTime);
        }
        return prizeConfigList;
    }

    private void reportDrawResToThinkData(String uid, int amount, List<RewardInfoData> rewards, ActorData currActor, long currTime) {
        DrawPrizesRecordLogData logData = new DrawPrizesRecordLogData();
        logData.setUid(uid);
        logData.setChannel(currActor.getChannel())
                .setCtime(currTime)
                .setScene(EVENT_ENUM.getCode())
                .setCostTicket((long) amount)
                .setDrawNums(amount)
                .setDrawResult(JSON.toJSONString(rewards));
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private void checkAndSendGlobalNotice(CupidVO.PrizeConfig prizeConfig, String activityId, ActorData currActor) {
        if (prizeConfig.getBroadcast() == null || prizeConfig.getBroadcast() != 1) {
            return;
        }
        RewardInfoData rewardInfo = Optional.of(prizeConfig)
                .map(config -> fillRewardInfoData(config, activityId))
                .orElse(null);
        rewardService.sendUniversalFullServiceNoticeMsg(currActor.getUid(), rewardInfo, EVENT_URL, EVENT_ENUM.getName(), RewardService.DEFAULT_TEXT, true);
    }

    private void dealDrawReward(String activityId, String uid, int amount, List<CupidVO.PrizeConfig> prizeConfigList, long currTime) {
//            int changed = amount == 1 ? 100 : 900;
            List<String> prizeKeyList = new ArrayList<>();
            cupidDrawDiamondCost(activityId, uid, amount);
            Map<String, EventCupidConfig.CommonAwardConfig> rewardConfigMap = eventCupidConfig.getCupidConfigList().stream()
                    .collect(Collectors.toMap(EventCupidConfig.CommonAwardConfig::getDrawType, Function.identity()));

            IntStream.range(0, amount).forEach(i -> fillPrizeConfigList(activityId, rewardConfigMap, prizeConfigList, currTime, prizeKeyList));

            // 记录抽奖历史
            String recordListKey = getRecordActivityKey(activityId, uid);
            activityCommonRedis.leftPushAllCommonList(recordListKey, prizeKeyList);
    }

    private void fillPrizeConfigList(String activityId, Map<String, EventCupidConfig.CommonAwardConfig> rewardConfigMap, List<CupidVO.PrizeConfig> prizeConfigList, long currTime, List<String> prizeKeyList) {
        initPoolSize(activityId);
        String drawKey = activityCommonRedis.leftPopCommonListKey(activityId);
        EventCupidConfig.CommonAwardConfig awardConfig = rewardConfigMap.get(drawKey);
        if (awardConfig == null) {
            return;
        }
        CupidVO.PrizeConfig prizeConfig = new CupidVO.PrizeConfig();
        BeanUtils.copyProperties(awardConfig, prizeConfig);
        prizeConfigList.add(prizeConfig);

        String drawKeyTime = drawKey + "-" + currTime;
        prizeKeyList.add(drawKeyTime);
    }

    private RewardInfoData fillRewardInfoData(CupidVO.PrizeConfig prizeConfig, String activityId) {
        return new RewardInfoData(Integer.parseInt(activityId),
                prizeConfig.getRewardNum(),
                Integer.parseInt(prizeConfig.getRewardType()),
                PROD ? prizeConfig.getSourceProdId() : prizeConfig.getSourceTestId(),
                prizeConfig.getUnitPrice())
                .setName(prizeConfig.getNameEn())
                .setIcon(prizeConfig.getIconEn())
                .setBigIcon(prizeConfig.getIconEn());
    }

    private void checkLimit(long currTime, AppConfigActivityData eventConfig, ActorData currActor) {
        if (currTime < eventConfig.getStartTime()) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("event not start"));
        }
        if (currTime > eventConfig.getEndTime()) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("event already end"));
        }
        if (StringUtils.hasLength(eventConfig.getChannel()) && !eventConfig.getChannel().equals(currActor.getChannel())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("your app not join this event"));
        }
    }

    // 1、扣费
    private void cupidDrawDiamondCost(String activityId, String uid, int changed) {
        CountVO selfData = baseZSetRedis.getOne(getCupidUserBalance(activityId), uid);
        if (selfData.getCount() < changed) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.BALANCE_NOT_ENOUGH).setMsg("balance not enough"));
        }
        baseZSetRedis.decreaseToZSet(getCupidUserBalance(activityId), uid, changed, Duration.ofDays(30));
    }

    // 2、初始化奖池
    public void commonInitPoolSize(String activityId) {

        List<String> prizeList = Stream.generate(() -> "0").limit(1000).collect(Collectors.toList());
        //索引列表
        List<String> indexList = IntStream.rangeClosed(0, 999).mapToObj(String::valueOf).collect(Collectors.toList());

        for (int i = 1; i <= 1000; i++) {

            int index = i - 1;
            boolean flag = false;
            if (i == 322) {
                prizeList.set(index, "a");
                flag = true;
            }
            if (i == 600) {
                prizeList.set(index, "d");
                flag = true;
            }

//            if (i % 250 == 0) {
//                prizeList.set(index, "b");
//                flag = true;
//            }
//
//            if ((i - 9) % 10 == 0) {
//                prizeList.set(index, "f");
//                flag = true;
//            }
//
//            if ((i - 3) % 5 == 0) {
//                prizeList.set(index, "h");
//                flag = true;
//            }

            if (flag) {
                indexList.remove(String.valueOf(index));
            }
        }

        // 打乱索引列表
        Collections.shuffle(indexList);
        List<EventCupidConfig.CommonAwardConfig> cupidConfigList = eventCupidConfig.getCupidConfigList().stream()
                .filter(item -> item.getRateNum() > 0)
                .collect(Collectors.toList());

        cupidConfigList.forEach(awardConfig ->
                IntStream.range(0, awardConfig.getRateNum())
                        .mapToObj(i -> indexList.remove(0))
                        .forEach(indexStr -> prizeList.set(Integer.parseInt(indexStr), awardConfig.getDrawType()))
        );

        activityCommonRedis.rightPushAllCommonList(activityId, prizeList);
    }

    private void initPoolSize(String activityId) {
        int poolSize = activityCommonRedis.getCommonListSize(activityId);
        if (poolSize <= 20) {
            commonInitPoolSize(activityId);
        }
    }

    public CupidVO cupidRecord(String activityId, String uid, int page) {

        CupidVO vo = new CupidVO();
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;

        String recordListKey = getRecordActivityKey(activityId, uid);
        List<String> prizeKeyTimeList = activityCommonRedis.getCommonListPageRecord(recordListKey, start, end);
        Map<String, EventCupidConfig.CommonAwardConfig> rewardConfigMap = eventCupidConfig.getCupidConfigList().stream().collect(Collectors.toMap(EventCupidConfig.CommonAwardConfig::getDrawType, Function.identity()));

        List<CupidVO.PrizeConfig> prizeConfigList = new ArrayList<>();
        prizeKeyTimeList.stream()
                .map(prizeKeyTime -> prizeKeyTime.split("-"))
                .forEach(prizeKeySplit -> fillDrawRecord(prizeKeySplit, rewardConfigMap, prizeConfigList));

        vo.setDrawRecordList(prizeConfigList);
        vo.setNextUrl(prizeConfigList.size() < RECORD_PAGE_SIZE ? -1 : page + 1);
        return vo;
    }

    // 总榜排行榜奖励
    private void distributionTotalCupid(String rankKey, List<RewardTaskConfig> rewardConfigs, String rankName) {
        StringBuilder content = new StringBuilder();
        content.append("丘比特活动【").append(rankName).append("】").append("\n")
                .append("排名\t\t统计数\t\tuid\t\t\t\t\t\t\t\t\t\trid\n");
        Map<String, Integer> dataMap = activityCommonRedis.getCommonRankingMap(rankKey, rewardConfigs.size());
        int rank = 1;
        for (Map.Entry<String, Integer> entry : dataMap.entrySet()) {
            String rankUid = entry.getKey();
            // 奖励发放 官方通知 告警
            int finalRank = rank;
            content.append(finalRank).append("\t\t\t").append(entry.getValue()).append("\t\t\t").append(rankUid).append("\t\t");
            rewardConfigs.stream()
                    .filter(rewardConfig -> rewardConfig.getCheckParams().equals(finalRank))
                    .findFirst()
                    .ifPresent(rewardConfig -> this.giveReward(rewardConfig, rankUid, rankName, finalRank, content));
            content.append("\n");
            rank++;
        }
        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, content.toString());
    }

    private void giveReward(RewardTaskConfig rewardConfig, String rankUid, String rankName, int top, StringBuilder content) {
        ActorData rankActor = actorMgr.getActorData(rankUid);
        if (rankActor == null) {
            return;
        }
        giveOutRewardService.giveOutReward(rankUid, rewardConfig.getRewards());
        itemsChangeService.itemChangeReport(rankUid, rewardConfig.getRewards(), EVENT_ENUM.getCode(), DateHelper.getCurrTime(), rankName + ",top=" + top);
        rewardConfig.getRewards().forEach(reward -> activityMQProduct.sendObjectMq(MQConstant.EVENT_COST_COUNT_ROUTE, reward));
        content.append(rankActor.getRid());
        // 推送消息
        String notice = "Top #top #rankName of Cupid's Arrow on Sep. 2024， Rewards have been sent to your backpack, remember to check >>>"
                .replace("#top", String.valueOf(top))
                .replace("#rankName", rankName);
        officialNoticeService.sendOfficialNotice(rankActor.getUid(), EVENT_ENUM.getName(), notice, NOTICE_IMG, EVENT_URL, rankActor.getChannel(),
                DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50),
                EVENT_ENUM.getCode());
    }


    public void sendGiftSuccessAction(SendGiftSuccessMsgData mqData) {
        if (mqData.fetchRealCost() <= 0) {
            return;
        }
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_ENUM.getCode());
        if (configData.getStartTime() > mqData.getTime() || configData.getEndTime() < mqData.getTime()) {
            return;
        }
        if (!configData.getDataId().contains(mqData.getGiftId().intValue())) {
            return;
        }
        sendGiftAction(mqData, configData);

        receiveGiftAction(mqData, configData);
    }

    private void receiveGiftAction(SendGiftSuccessMsgData mqData, AppConfigActivityData configData) {
        if (!configData.getChannel().equals(mqData.getToChannel())) {
            return;
        }
        boolean tester = actorExternalDao.isTester(mqData.getToUid());
        if (tester) {
            return;
        }
        //收礼榜数据统计
        String receiveGiftRankingKey = getReceiveGiftRankingKey(String.valueOf(EVENT_ENUM.getCode()));
        activityCommonRedis.incrCommonZSetRankingScore(receiveGiftRankingKey, mqData.getToUid(), mqData.getCost().intValue());
    }

    private void sendGiftAction(SendGiftSuccessMsgData mqData, AppConfigActivityData configData) {
        if (!configData.getChannel().equals(mqData.getChannel())) {
            return;
        }
        boolean tester = actorExternalDao.isTester(mqData.getUid());
        if (tester) {
            return;
        }
        // 送礼榜数据统计
        String sendGiftRankKey = getSendGiftRankKey(String.valueOf(EVENT_ENUM.getCode()));
        activityCommonRedis.incrCommonZSetRankingScore(sendGiftRankKey, mqData.getUid(), mqData.getCost().intValue());
        // 活动货币计算
        computeEventBalance(mqData);
    }

    private void computeEventBalance(SendGiftSuccessMsgData mqData) {
        int addBalance = cupidUserBalanceTaskLimitRedis.increaseAndGetRewards(mqData.getUid(), EVENT_ENUM.getCode(), mqData.getCost());
        if (addBalance > 0) {
            String cupidUserBalanceKey = getCupidUserBalance(String.valueOf(EVENT_ENUM.getCode()));
            baseZSetRedis.increaseToZSet(cupidUserBalanceKey, mqData.getUid(), addBalance, Duration.ofDays(30));
            // 推送飘屏
            ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
            roomFloatingImService.sendRoomFloatingIm(currActor, addBalance, BALANCE_ICON, BALANCE_NAME, "for event Cupid", EVENT_ENUM.getCode(), EVENT_URL);
        }
    }

//    @Async("asyncPool")
//    @Scheduled(cron = "5 0 0 16 9 ?", zone = "GMT+5:30")
    public void giveRankReward() {
        if (!masterUtils.isMaster()) {
            return;
        }
        String sendGiftRankKey = getSendGiftRankKey(String.valueOf(EVENT_ENUM.getCode()));
        distributionTotalCupid(sendGiftRankKey, SEND_GIFT_REWARD, "sender");

        String receiveGiftRankKey = getReceiveGiftRankingKey(String.valueOf(EVENT_ENUM.getCode()));
        distributionTotalCupid(receiveGiftRankKey, RECEIVE_GIFT_REWARD, "receiver");
    }
}
