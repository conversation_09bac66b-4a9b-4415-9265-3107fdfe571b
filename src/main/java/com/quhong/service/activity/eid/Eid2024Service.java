package com.quhong.service.activity.eid;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.ActType;
import com.quhong.constant.LangIdConstant;
import com.quhong.constant.OfficialUserGroup;
import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.WarnName;
import com.quhong.core.constant.money.MoneyChangeActionConstant;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorBackpackData;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.MoneyDetailData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.config.TaskConfig;
import com.quhong.data.thData.ActiveDataEvent;
import com.quhong.data.vo.activity.superPlayer.OtherRankingListVO;
import com.quhong.data.vo.event.eid.Eid2024VO;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.EventCode;
import com.quhong.enums.RewardItemType;
import com.quhong.exceptions.WebException;
import com.quhong.moderatoin.ModerationService;
import com.quhong.mq.ActivityMQProduct;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.mq.enums.MQConstant;
import com.quhong.players.ActorMgr;
import com.quhong.redis.base.ActivityCommonRedis;
import com.quhong.redis.base.currency.task.BaseEveryLimitRedis;
import com.quhong.report.EventReport;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.ItemsChangeService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.common.RewardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


@Slf4j
@Service
@RequiredArgsConstructor
public class Eid2024Service {
    private static final TaskConfig SEND_LUCY_GIFT_INC_INTEGRAL_TASK_CONFIG = new TaskConfig(TaskTypeConstant.SEND_GIFT, 100, 1, -1);

    //    private static final Integer EVENT_CODE = ActivityTypeEnum.EVENT_STAR_CARNIVAL.getCode();
//    private static final Integer EVENT_CODE = ActivityTypeEnum.EVENT_STAR_CARNIVAL_V3.getCode();
    private static final Integer EVENT_CODE = EventCode.EVENT_STAR_CARNIVAL_2504;
    private static final String ACTIVITY_ID = EVENT_CODE.toString();
    public static final boolean PROD = ServerConfiguration.isProduct();
    /**
     * 活动链接
     */
    private static final String ACTIVITY_URL = PROD ? "https://videochat.kissu.site/shining_eid2024_12/" : "https://testvideochat.kissu.site/shining_eid2024_12/";
    /**
     * 通知图片
     */
    private static final String NOTICE_IMG = "https://statics.kissu.mobi/event/sheep/202504/QHbanner.jpg";

    private static final String TOTAL_INTEGRAL = "totalIntegral";

    // 玩嘉年华任务
//    private static final List<Integer> PLAY_GAME_LEVEL_LIST = Arrays.asList(1, 200, 500, 2000);
    /**
     * 嘉年华消费达标任务奖励
     */
    private static final Map<Integer, List<RewardInfoData>> LEVEL_PLAY_GAME_MAP = new HashMap<>();

    // 玩嘉年华收集指定礼物任务
//    private static final List<Integer> GIFT_COLLECT_1_LIST = ServerConfiguration.isProduct() ? Arrays.asList(554, 640) : Arrays.asList(549, 554);
//    private static final List<Integer> GIFT_COLLECT_2_LIST = ServerConfiguration.isProduct() ? Arrays.asList(657, 660) : Arrays.asList(549, 554);
//    private static final List<Integer> GIFT_COLLECT_3_LIST = ServerConfiguration.isProduct() ? Arrays.asList(659, 658, 557) : Arrays.asList(554, 605, 640);

    private static final List<Integer> GIFT_COLLECT_4_LIST = PROD ? Arrays.asList(727, 657) : Arrays.asList(549, 554);

    private static final List<Integer> GIFT_COLLECT_5_LIST = PROD ? Arrays.asList(728, 729) : Arrays.asList(511, 642);

    //    private static final String COPPER_BOX = "copperBox";   // 铜宝箱
    private static final String BLUE_BOX = "blueBox"; // 兰宝箱
    private static final String GOLD_BOX = "goldBox"; // 金宝箱
    /**
     * 收集指定礼物达标任务奖励
     */
    private static final Map<String, List<RewardInfoData>> GIFT_COLLECT_MAP = new HashMap<>();
    private static final Map<String, String> GIFT_COLLECT_NOTICE_MAP = new HashMap<>();
    // 抓羊任务
    private static final List<Integer> CATCH_SHEEP_LEVEL_LIST = Arrays.asList(1, 10, 50, 150, 300);
    /**
     * 抓羊达标任务奖励
     */
    private static final Map<Integer, List<RewardInfoData>> LEVEL_CATCH_SHEEP_MAP = new HashMap<>();

    private static final int CATCH_ONE = 1000;
    private static final int CATCH_TEN = 9000;

    private static final int SCORE_RATE = 40;

    private static final String TOTAL_RANKING_OFFICIAL_NOTICE_TEMPLATE = "😁 Congratulations to the follows get Top 10 in [Sheep Catching Battle ] Ranking\n%s😁 The reward has been issued~";


    /**
     * 榜单奖励配置
     */
    private static final List<RewardTaskConfig> RANK_REWARDS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            if (ServerConfiguration.isProduct()) {
                add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, 230));
                add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, 222));
            } else {
                add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, 123));
                add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, 122));
            }
            add(new RewardInfoData(EVENT_CODE, 7500, RewardItemType.GOLD, 0, 1));
        }};
        add(new RewardTaskConfig().setCheckParams(1).setRewards(rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            if (ServerConfiguration.isProduct()) {
                add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, 230));
                add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.SEAT_FRAME, 222));
            } else {
                add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, 123));
                add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.SEAT_FRAME, 122));
            }
            add(new RewardInfoData(EVENT_CODE, 5000, RewardItemType.GOLD, 0, 1));
        }};
        add(new RewardTaskConfig().setCheckParams(2).setRewards(rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            if (ServerConfiguration.isProduct()) {
                add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.ENTER_EFFECT, 230));
                add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.SEAT_FRAME, 222));
            } else {
                add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.ENTER_EFFECT, 123));
                add(new RewardInfoData(EVENT_CODE, 10, RewardItemType.SEAT_FRAME, 122));
            }
            add(new RewardInfoData(EVENT_CODE, 2500, RewardItemType.GOLD, 0, 1));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            if (ServerConfiguration.isProduct()) {
                add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, 230));
                add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, 222));
            } else {
                add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.ENTER_EFFECT, 123));
                add(new RewardInfoData(EVENT_CODE, 7, RewardItemType.SEAT_FRAME, 122));
            }
            add(new RewardInfoData(EVENT_CODE, 1250, RewardItemType.GOLD, 0, 1));
        }};
        add(new RewardTaskConfig().setCheckParams(4).setRewards(rewards4));
        add(new RewardTaskConfig().setCheckParams(5).setRewards(rewards4));
        add(new RewardTaskConfig().setCheckParams(6).setRewards(rewards4));
        add(new RewardTaskConfig().setCheckParams(7).setRewards(rewards4));
        add(new RewardTaskConfig().setCheckParams(8).setRewards(rewards4));
        add(new RewardTaskConfig().setCheckParams(9).setRewards(rewards4));
        add(new RewardTaskConfig().setCheckParams(10).setRewards(rewards4));
    }};

    private static final Map<Integer, RewardTaskConfig> RANK_REWARD_MAP = RANK_REWARDS.stream()
            .collect(Collectors.toMap(RewardTaskConfig::getCheckParams, config -> config));

    /**
     * 星动嘉年华消费金币达标奖励配置
     * 礼物收集奖励配置
     * 抓羊达标奖励配置
     */
    static {
//        List<RewardInfoData> gift = Collections.singletonList(
//                new RewardInfoData(EVENT_CODE, 1, RewardItemType.SEAT_FRAME, 221));
//        List<RewardInfoData> testGift = Collections.singletonList(
//                new RewardInfoData(EVENT_CODE, 1, RewardItemType.SEAT_FRAME, 102));
//        LEVEL_PLAY_GAME_MAP.put(PLAY_GAME_LEVEL_LIST.get(0), ServerConfiguration.isProduct() ? gift : testGift);
//        LEVEL_PLAY_GAME_MAP.put(PLAY_GAME_LEVEL_LIST.get(1), Collections.singletonList(
//                new RewardInfoData(EVENT_CODE, 1, RewardItemType.STARRY_TICKET, 1, 5))
//        );
//        LEVEL_PLAY_GAME_MAP.put(PLAY_GAME_LEVEL_LIST.get(2), Collections.singletonList(
//                new RewardInfoData(EVENT_CODE, 2, RewardItemType.STARRY_TICKET, 1, 5))
//        );
//        LEVEL_PLAY_GAME_MAP.put(PLAY_GAME_LEVEL_LIST.get(3), Collections.singletonList(
//                new RewardInfoData(EVENT_CODE, 2, RewardItemType.STARRY_TICKET, 2, 20))
//        );


//        List<RewardInfoData> copperRewards = Arrays.asList(
//                new RewardInfoData(EVENT_CODE, 1, RewardItemType.BUBBLE_FRAME, 219),
//                new RewardInfoData(EVENT_CODE, 1, RewardItemType.SEAT_FRAME, 207),
//                new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, 603, 1));
//        List<RewardInfoData> testCopperRewards = Arrays.asList(
//                new RewardInfoData(EVENT_CODE, 1, RewardItemType.BUBBLE_FRAME, 119),
//                new RewardInfoData(EVENT_CODE, 1, RewardItemType.SEAT_FRAME, 65),
//                new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, 603, 1));
//        GIFT_COLLECT_MAP.put(COPPER_BOX, ServerConfiguration.isProduct() ? copperRewards : testCopperRewards);
//        GIFT_COLLECT_NOTICE_MAP.put(COPPER_BOX, "Congratulations, you have finished \"Shining Carnival Lucky King\" Collected gifts [lanterns]+[Great]\n" +
//                "task,Rewards have been sent to your backpack, more rewards >>>");

        List<RewardInfoData> blueRewards = Arrays.asList(
                new RewardInfoData(EVENT_CODE, 1, RewardItemType.SEAT_FRAME, 220),
                new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, 631, 10));
        List<RewardInfoData> testBlueRewards = Arrays.asList(
                new RewardInfoData(EVENT_CODE, 1, RewardItemType.SEAT_FRAME, 75),
                new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, 549, 10));
        GIFT_COLLECT_MAP.put(BLUE_BOX, PROD ? blueRewards : testBlueRewards);
        GIFT_COLLECT_NOTICE_MAP.put(BLUE_BOX, "\"Congratulations, you have finished \"\"New Carnival Gifts Online \"\" Collected gifts [Poison Perfume]+[Sparkling Candy] " +
                "task,Rewards have been sent to your backpack, more rewards >>>\"\n");

        List<RewardInfoData> goldReward = Arrays.asList(
                new RewardInfoData(EVENT_CODE, 1, RewardItemType.BUBBLE_FRAME, 217),
                new RewardInfoData(EVENT_CODE, 1, RewardItemType.ENTER_EFFECT, 188),
                new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, 638, 500),
                new RewardInfoData(EVENT_CODE, 1, RewardItemType.STARRY_TICKET, 1, 5));
        List<RewardInfoData> testGoldReward = Arrays.asList(
                new RewardInfoData(EVENT_CODE, 1, RewardItemType.BUBBLE_FRAME, 61),
                new RewardInfoData(EVENT_CODE, 1, RewardItemType.ENTER_EFFECT, 97),
                new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, 638, 500),
                new RewardInfoData(EVENT_CODE, 1, RewardItemType.STARRY_TICKET, 1, 5));
        GIFT_COLLECT_MAP.put(GOLD_BOX, PROD ? goldReward : testGoldReward);
        GIFT_COLLECT_NOTICE_MAP.put(GOLD_BOX, "\"Congratulations, you have finished \"\"Shining Carnival Lucky King\"\" Collected gifts [" +
                "Amazing]+[Rose box]+[Lucky castle]+[Magic castle] task,Rewards have been sent to your backpack, more rewards >>>\"\n");

        LEVEL_CATCH_SHEEP_MAP.put(CATCH_SHEEP_LEVEL_LIST.get(0), Collections.singletonList(
                new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, 612).setName("[Tell him/Tell her] gift"))
        );
        LEVEL_CATCH_SHEEP_MAP.put(CATCH_SHEEP_LEVEL_LIST.get(1), Collections.singletonList(
                new RewardInfoData(EVENT_CODE, 5, RewardItemType.GOLD, 0, 1).setName("200 coins"))
        );
        LEVEL_CATCH_SHEEP_MAP.put(CATCH_SHEEP_LEVEL_LIST.get(2), Collections.singletonList(
                new RewardInfoData(EVENT_CODE, 25, RewardItemType.GOLD, 0, 1).setName("1000 coins"))
        );
        LEVEL_CATCH_SHEEP_MAP.put(CATCH_SHEEP_LEVEL_LIST.get(3), Collections.singletonList(
                new RewardInfoData(EVENT_CODE, 40, RewardItemType.GOLD, 0, 1).setName("1600 coins"))
        );
        LEVEL_CATCH_SHEEP_MAP.put(CATCH_SHEEP_LEVEL_LIST.get(4), Collections.singletonList(
                new RewardInfoData(EVENT_CODE, 125, RewardItemType.GOLD, 0, 1).setName("5000 coins"))
        );
    }


    private final BaseEveryLimitRedis baseEveryLimitRedis;
    private final GiveOutRewardService giveOutRewardService;
    private final EventReport eventReport;
    private final AppConfigActivityDao appConfigActivityDao;
    private final ActivityCommonRedis activityCommonRedis;
    private final ActorMgr actorMgr;
    private final ModerationService moderationService;
    private final CdnUtils cdnUtils;
    private final ActivityMQProduct activityMQProduct;
    private final ItemsChangeService itemsChangeService;
    private final OfficialNoticeService officialNoticeService;
    private final MonitorSender monitorSender;
    private final RewardService rewardService;


    // 玩星动嘉年华每日key
//    private String getPlayGameKey(String activityId, String dateStr) {
//        return String.format("playGame:%s:%s", activityId, dateStr);
//    }

    private String getHashActivityId(String activityId, String uid) {
        return String.format("%s:%s", activityId, uid);
    }

    // 宝箱每日key
    private String getDailyBoxActivityId(String activityId, String uid, String dateStr) {
        return String.format("dailyBox:%s:%s:%s", activityId, uid, dateStr);
    }

    // 每日抓羊key
    private String getCatchSheepKey(String activityId, String dateStr) {
        return String.format("catchSeep:%s:%s", activityId, dateStr);
    }

    // 抓羊榜单key
    private String getTotalRankKey(String activityId) {
        return String.format("totalRank:%s", activityId);
    }

    private String getGiftStatus(String box, Integer giftId) {
        return String.format("%s:%s", box, giftId);
    }

    public Eid2024VO eid2024Config(String activityId, String uid) {
        ActorData currActor = actorMgr.getCurrActorData(uid);
        DateHelper dateHelper = DateHelper.genDateHelper(currActor.getChannel());
        AppConfigActivityData activity = appConfigActivityDao.getOneByEventCodeThrowWebException(Integer.parseInt(activityId));
        Map<String, Integer> eidConfigMap = activityCommonRedis.getCommonHashAll(getHashActivityId(activityId, uid));
        Eid2024VO vo = JSON.parseObject(JSON.toJSONString(eidConfigMap), Eid2024VO.class);
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        String currentDate = dateHelper.formatDateInDay();
//        vo.setTotalPlayBeans(activityCommonRedis.getCommonZSetRankingScore(getPlayGameKey(activityId, currentDate), uid));
        vo.setCatchSleepNum(activityCommonRedis.getCommonZSetRankingScore(getCatchSheepKey(activityId, currentDate), uid));
//        vo.setRoomId(getPopularRoomId());

        // 设置当前礼物收集情况
        setGiftStatus(activityId, uid, currentDate, vo);

        // 设置抓羊榜
        List<OtherRankingListVO> totalRankingList = new ArrayList<>();
        OtherRankingListVO mytTotalRank = new OtherRankingListVO();
        makeOtherRankingData(totalRankingList, mytTotalRank, getTotalRankKey(activityId), uid, 10);
        vo.setTotalRankingList(totalRankingList);
        vo.setMyTotalRank(mytTotalRank);

        return vo;
    }

    private void setGiftStatus(String activityId, String uid, String currentDate, Eid2024VO vo) {
        // 设置收集礼物状态
        String dailyBoxKey = getDailyBoxActivityId(activityId, uid, currentDate);
        Map<String, Integer> dailyConfigMap = activityCommonRedis.getCommonHashAll(dailyBoxKey);

//        List<Integer> copperBoxStatus = new ArrayList<>();
//        for (Integer giftId : GIFT_COLLECT_1_LIST) {
//            copperBoxStatus.add(dailyConfigMap.getOrDefault(getGiftStatus(COPPER_BOX, giftId), 0));
//        }
//        vo.setCopperBoxStatus(copperBoxStatus);
//        vo.setCopperBox(dailyConfigMap.getOrDefault(COPPER_BOX, 0));

        List<Integer> blueBoxStatus = new ArrayList<>();
        for (Integer giftId : GIFT_COLLECT_4_LIST) {
            blueBoxStatus.add(dailyConfigMap.getOrDefault(getGiftStatus(BLUE_BOX, giftId), 0));
        }
        vo.setBlueBoxStatus(blueBoxStatus);
        vo.setBlueBox(dailyConfigMap.getOrDefault(BLUE_BOX, 0));

        List<Integer> goldBoxStatus = new ArrayList<>();
        for (Integer giftId : GIFT_COLLECT_5_LIST) {
            goldBoxStatus.add(dailyConfigMap.getOrDefault(getGiftStatus(GOLD_BOX, giftId), 0));
        }
        vo.setGoldBoxStatus(goldBoxStatus);
        vo.setGoldBox(dailyConfigMap.getOrDefault(GOLD_BOX, 0));
    }

    public void makeOtherRankingData(List<OtherRankingListVO> rankingList, OtherRankingListVO myRank, String rankKey, String uid, int length) {
        ActorData currActor = actorMgr.getActorDataFromCache(uid);
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(rankKey, length);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingVO = new OtherRankingListVO();
            String aid = entry.getKey();
            ActorData rankActor = actorMgr.getActorDataFromCache(aid);
            rankingVO.setUid(aid);
            rankingVO.setScore(entry.getValue());
            rankingVO.setRank(rank);
            if (rankActor != null) {
                String head = moderationService.dealRankHeadModeration(rankActor);
                rankingVO.setHead(cdnUtils.replaceUrlDomain(currActor.getChannel(), head, 0));
                rankingVO.setName(rankActor.getName());
            }
            if (aid.equals(uid)) {
                BeanUtils.copyProperties(rankingVO, myRank);
            }
            rankingList.add(rankingVO);
            rank += 1;
        }

        if (myRank.getRank() == null || myRank.getRank() == -1) {
            myRank.setName(currActor.getName());
            myRank.setUid(uid);
            String head = moderationService.dealRankHeadModeration(currActor);
            myRank.setHead(cdnUtils.replaceUrlDomain(currActor.getChannel(), head, 0));
            myRank.setScore(activityCommonRedis.getCommonZSetRankingScore(rankKey, uid));
            myRank.setRank(-1);
        }
    }

    public void catchSheep(String activityId, String uid, int zone, int amount) {
        long currTime = DateHelper.getCurrTime();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(Integer.parseInt(activityId));
        ActorData currActor = actorMgr.getCurrActorData(uid);
        DateHelper dateHelper = DateHelper.genDateHelper(currActor.getChannel());
        checkActivityTimeAndChannel(configData, currTime, currActor);
        if (amount != 1 && amount != 10) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("amount must be 1 or 10"));
        }
        int debutNum = amount == 1 ? CATCH_ONE : CATCH_TEN;

        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> eidConfigMap = activityCommonRedis.getCommonHashAll(hashActivityId);
        int totalIntegral = eidConfigMap.getOrDefault(TOTAL_INTEGRAL, 0);
        if (totalIntegral < debutNum) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.BALANCE_NOT_ENOUGH).setMsg("your not enough"));
        }
        activityCommonRedis.incCommonHashNum(hashActivityId, TOTAL_INTEGRAL, -debutNum);
        sendActiveDataEventToThinkData(activityId, uid, zone, amount, dateHelper);
        if (zone > 0) {
            doCatchSheep(activityId, uid, amount, dateHelper, currActor, currTime);
        }
    }

    private void doCatchSheep(String activityId, String uid, int amount, DateHelper dateHelper, ActorData currActor, long currTime) {
        // 总榜单
        String totalRankKey = getTotalRankKey(ACTIVITY_ID);
        activityCommonRedis.incrCommonZSetRankingScore(totalRankKey, uid, amount);

        // 每日刷新抓羊数量
        String currentDate = dateHelper.formatDateInDay();
        String dailyCatchSheepKey = getCatchSheepKey(activityId, currentDate);
        int currentNum = activityCommonRedis.getCommonZSetRankingScore(dailyCatchSheepKey, uid);
        while (amount > 0) {
            List<Integer> tempLevelNumList = new ArrayList<>(CATCH_SHEEP_LEVEL_LIST);
            int currentLevelIndex = 0;
            if (tempLevelNumList.contains(currentNum)) {
                currentLevelIndex = tempLevelNumList.indexOf(currentNum);
            } else {
                tempLevelNumList.add(currentNum);
                tempLevelNumList.sort(Integer::compare);
                currentLevelIndex = tempLevelNumList.indexOf(currentNum) - 1;
            }

            int upLevelIndex = currentLevelIndex + 1;
            if (upLevelIndex >= CATCH_SHEEP_LEVEL_LIST.size()) {
                activityCommonRedis.incrCommonZSetRankingScore(dailyCatchSheepKey, uid, amount);
                amount = 0;
            } else {
                int upLevelNum = CATCH_SHEEP_LEVEL_LIST.get(upLevelIndex);     // 下一级的数量
                int needUpNum = upLevelNum - currentNum;                     // 需要升级到下一级的数量
                if (needUpNum <= amount) {                                      // 如果【增加的数量】大于等于【需要升级到下一级的数量】则升级, 否则不升级直接增加数量
                    currentNum = currentNum + needUpNum;
                    amount = amount - needUpNum;
                    activityCommonRedis.incrCommonZSetRankingScore(dailyCatchSheepKey, uid, needUpNum);
                    List<RewardInfoData> rewards = LEVEL_CATCH_SHEEP_MAP.get(upLevelNum);
                    if (!CollectionUtils.isEmpty(rewards)) {
                        // 奖励发放
                        giveOutRewardService.giveOutReward(uid, rewards);
                        itemsChangeService.itemChangeReport(uid, rewards, EVENT_CODE, DateHelper.getCurrTime(), "catch sheep level award,levelNum=" + upLevelNum);
                        rewards.forEach(reward -> activityMQProduct.sendObjectMq(MQConstant.EVENT_COST_COUNT_ROUTE, reward));
                        // 抓羊数量达标奖励通知
                        String notice = "You have caught #count sheep today. The reward #AwardName has been sent to your backpack. Please check it.";
                        notice = notice.replace("#count", String.valueOf(upLevelNum))
                                .replace("#AwardName", rewards.get(0).getName());
                        officialNoticeService.sendOfficialNotice(uid, "", notice, NOTICE_IMG, ACTIVITY_URL, currActor.getChannel(),
                                (int) (currTime + MathUtils.randomSplitInt(0, 50)), EVENT_CODE);
                        if (upLevelNum >= 150) {
                            rewards.forEach(reward -> rewardService.sendUniversalFullServiceNoticeMsg(uid, reward, ACTIVITY_URL,
                                    ActivityTypeEnum.getByCode(EVENT_CODE).getName(),
                                    "#name have caught" + upLevelNum + "sheep in [New Carnival Gifts Online] Event，To See>>", true));
                        }
                    }
                } else {
                    activityCommonRedis.incrCommonZSetRankingScore(dailyCatchSheepKey, uid, amount);
                    amount = 0;
                }
            }
        }
    }

    private void sendActiveDataEventToThinkData(String activityId, String uid, int zone, int amount, DateHelper dateHelper) {
        ActiveDataEvent logData = new ActiveDataEvent();
        logData.setEventCode(activityId);
        logData.setDate(dateHelper.formatDateInDay());
        logData.setUid(uid);
        logData.setActiveDataDesc(zone > 0 ? "sheep_catch_success_number" : "sheep_catch_fail_number");
        logData.setNumber(amount);
        logData.setCtime(DateHelper.getCurrTime());
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private void checkActivityTimeAndChannel(AppConfigActivityData configData, long currTime, ActorData currActor) {
        if (currTime < configData.getStartTime() || currTime > configData.getEndTime()) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("not in event time"));
        }
        if (StringUtils.hasLength(configData.getChannel()) && !configData.getChannel().equals(currActor.getChannel())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("your app can not join this event"));
        }
    }

    // 活动结束发放总榜奖励和通知
    public void distributionTotalRanking() {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(getTotalRankKey(ACTIVITY_ID), 10);

        List<ActorData> rankActors = new ArrayList<>();

        // 获取榜单用户
        for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
            ActorData rankActor = actorMgr.getActorData(entry.getKey());
            rankActors.add(rankActor);
        }

        // 按名次发放奖品
        IntStream.range(0, rankActors.size())
                .mapToObj(i -> new AbstractMap.SimpleEntry<>(i + 1, rankActors.get(i)))
                .filter(entry -> entry.getValue() != null)
                .forEach(entry -> {
                    int rank = entry.getKey();
                    ActorData actorData = entry.getValue();
                    rankSendReward(RANK_REWARD_MAP.get(rank), actorData.getUid(), rank);
                });

        // 构建全服通知信息
        String totalRankingNotice = buildTotalRankingOfficialNotice(rankActors);
        // 发送通知信息
        sendRankRewardAndNotice(totalRankingNotice, configData);
        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, totalRankingNotice);

    }

    // 发放奖励到总榜用户
    private void rankSendReward(RewardTaskConfig config, String rankUid, int finalRank) {
        giveOutRewardService.giveOutReward(rankUid, config.getRewards());
        itemsChangeService.itemChangeReport(rankUid, config.getRewards(), EVENT_CODE, DateHelper.getCurrTime(), "total rank award,rank=" + finalRank);
        config.getRewards().forEach(reward -> activityMQProduct.sendObjectMq(MQConstant.EVENT_COST_COUNT_ROUTE, reward));
    }

    // 发送官方全服通知
    public void sendRankRewardAndNotice(String notice, AppConfigActivityData configActivityData) {
        int fixTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        officialNoticeService.sendGlobalNotice("", notice, NOTICE_IMG, ACTIVITY_URL, configActivityData.getChannel(), fixTime, EVENT_CODE,
                LangIdConstant.ENGLISH, null, OfficialUserGroup.CHANNEL);
    }

    private String buildTotalRankingOfficialNotice(List<ActorData> rankActors) {
        StringBuilder noticeBuilder = new StringBuilder();

        IntStream.range(0, rankActors.size()).forEach(i -> {
            int rank = i + 1;
            ActorData actorData = rankActors.get(i);
            if (actorData == null) {
                noticeBuilder.append(String.format("Top\t\t%d\t\t%s\n", rank, ""));
            } else {
                noticeBuilder.append(String.format("Top\t\t%d\t\t%s\n", rank, actorData.getRid().toString()));
            }
        });

        return String.format(TOTAL_RANKING_OFFICIAL_NOTICE_TEMPLATE, noticeBuilder);
    }


    private void distributeGiftCollect(String fromUid, Map<String, Integer> dailyConfigMap, String dailyBoxKey, String boxType, List<Integer> giftList, ActorData currActor) {

        int total = 0;
        for (Integer giftId : giftList) {
            String giftStatusKey = getGiftStatus(boxType, giftId);
            total += dailyConfigMap.getOrDefault(giftStatusKey, 0);
        }

        if (total >= giftList.size()) {
            activityCommonRedis.setCommonHashNum(dailyBoxKey, boxType, 1);
            List<RewardInfoData> rewards = GIFT_COLLECT_MAP.get(boxType);
            if (!CollectionUtils.isEmpty(rewards)) {
                // 礼物收集 奖励下发
                giveOutRewardService.giveOutReward(fromUid, rewards);
                itemsChangeService.itemChangeReport(fromUid, rewards, EVENT_CODE, DateHelper.getCurrTime(), "gift collect award,boxType=" + boxType);
                rewards.forEach(reward -> activityMQProduct.sendObjectMq(MQConstant.EVENT_COST_COUNT_ROUTE, reward));
                // 礼物收集 官方通知
                String notice = GIFT_COLLECT_NOTICE_MAP.get(boxType);
                officialNoticeService.sendOfficialNotice(fromUid, "", notice, NOTICE_IMG, ACTIVITY_URL, currActor.getChannel(),
                        (int) (DateHelper.getCurrTime() + MathUtils.randomSplitInt(0, 50)), EVENT_CODE);
            }
        }

    }

    // 统计嘉年华收集礼物id（抽到的礼物）
    public void starryGetGiftAction(ActorBackpackData mqData) {
        String fromUid = mqData.getUid();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        ActorData currActor = actorMgr.getCurrActorData(fromUid);
        checkActivityTimeAndChannel(configData, mqData.getMtime(), currActor);

        DateHelper dateHelper = DateHelper.genDateHelper(currActor.getChannel());

        int giftId = Integer.parseInt(mqData.getDataId());
        String currentDate = dateHelper.getDayDateByTime(mqData.getMtime() * 1000L);
        String dailyBoxKey = getDailyBoxActivityId(ACTIVITY_ID, fromUid, currentDate);
        Map<String, Integer> dailyConfigMap = activityCommonRedis.getCommonHashAll(dailyBoxKey);

        if (GIFT_COLLECT_4_LIST.contains(giftId) && dailyConfigMap.getOrDefault(BLUE_BOX, 0) <= 0) {
            String giftStatusKey = getGiftStatus(BLUE_BOX, giftId);
            dailyConfigMap.put(giftStatusKey, 1);
            activityCommonRedis.setCommonHashNum(dailyBoxKey, giftStatusKey, 1);
            distributeGiftCollect(fromUid, dailyConfigMap, dailyBoxKey, BLUE_BOX, GIFT_COLLECT_4_LIST, currActor);
        }

        if (GIFT_COLLECT_5_LIST.contains(giftId) && dailyConfigMap.getOrDefault(GOLD_BOX, 0) <= 0) {
            String giftStatusKey = getGiftStatus(GOLD_BOX, giftId);
            dailyConfigMap.put(giftStatusKey, 1);
            activityCommonRedis.setCommonHashNum(dailyBoxKey, giftStatusKey, 1);
            distributeGiftCollect(fromUid, dailyConfigMap, dailyBoxKey, GOLD_BOX, GIFT_COLLECT_5_LIST, currActor);
        }
    }

    // 统计积分及下发等级奖励(星动嘉年华)
    public void moneyDetailAction(MoneyDetailData mqData) {
        if (mqData.getSingleAction() != MoneyChangeActionConstant.ACTION_DEDUCT) {
            return;
        }
        if (mqData.getSingleChange() == null || mqData.getSingleChange() < 0) {
            return;
        }
        if (mqData.getActType() != ActType.STAR_GALA) {
            return;
        }
        String fromUid = mqData.getUserid();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(Integer.parseInt(ACTIVITY_ID));
        ActorData currActor = actorMgr.getCurrActorData(fromUid);
        checkActivityTimeAndChannel(configData, mqData.getMtime(), currActor);

        // if (!whiteTestDao.isMemberByType(fromUid, WhiteTestDao.WHITE_TYPE_RID)){
        //     return;
        // }


        int value = mqData.getChanged();
        int score = value * SCORE_RATE;
        activityCommonRedis.incCommonHashNum(getHashActivityId(ACTIVITY_ID, fromUid), TOTAL_INTEGRAL, score);

        // 增加玩游戏耗钻数
//            String currentDate = dateHelper.getDayDateByTime(mqData.getMtime() * 1000L);
//            String playGameKey = getPlayGameKey(ACTIVITY_ID, currentDate);
//            int currentNum = activityCommonRedis.getCommonZSetRankingScore(playGameKey, fromUid);
//            while (value > 0) {
//                List<Integer> tempLevelNumList = new ArrayList<>(PLAY_GAME_LEVEL_LIST);
//                int currentLevelIndex = 0;
//                if (tempLevelNumList.contains(currentNum)) {
//                    currentLevelIndex = tempLevelNumList.indexOf(currentNum);
//                } else {
//                    tempLevelNumList.add(currentNum);
//                    tempLevelNumList.sort(Integer::compare);
//                    currentLevelIndex = tempLevelNumList.indexOf(currentNum) - 1;
//                }
//
//                int upLevelIndex = currentLevelIndex + 1;
//                if (upLevelIndex >= PLAY_GAME_LEVEL_LIST.size()) {
//                    activityCommonRedis.incrCommonZSetRankingScore(playGameKey, fromUid, value);
//                    value = 0;
//                } else {
//                    int upLevelNum = PLAY_GAME_LEVEL_LIST.get(upLevelIndex);     // 下一级的数量
//                    int needUpNum = upLevelNum - currentNum;                     // 需要升级到下一级的数量
//                    if (needUpNum <= value) {                                      // 如果【增加的数量】大于等于【需要升级到下一级的数量】则升级, 否则不升级直接增加数量
//                        currentNum = currentNum + needUpNum;
//                        value = value - needUpNum;
//                        activityCommonRedis.incrCommonZSetRankingScore(playGameKey, fromUid, needUpNum);
//                        List<RewardInfoData> rewards = LEVEL_PLAY_GAME_MAP.get(upLevelNum);
//                        if (!CollectionUtils.isEmpty(rewards)) {
//                            // 星动嘉年华消费金币达标 发奖励
//                            giveOutRewardService.giveOutReward(fromUid, rewards);
//                            itemsChangeService.itemChangeReport(fromUid, rewards, EVENT_CODE, mqData.getMtime(), "Play starry cost coins level reward,levelNum=" + upLevelNum);
//                            rewards.forEach(reward -> activityMQProduct.sendObjectMq(MQConstant.EVENT_COST_COUNT_ROUTE, reward));
//                            // 星动嘉年华消费金币达标 奖励通知
//                            String notice = "Congratulations, you have reached“Shining Carnival Lucky King” consumption requirement, " +
//                                    "the reward has been issued to your backpack, for more rewards, please check >>>";
//                            officialNoticeService.sendOfficialNotice(fromUid, "", notice, NOTICE_IMG, ACTIVITY_URL, currActor.getChannel(),
//                                    (int) (DateHelper.getCurrTime() + MathUtils.randomSplitInt(0, 50)), EVENT_CODE);
//                        }
//                    } else {
//                        activityCommonRedis.incrCommonZSetRankingScore(playGameKey, fromUid, value);
//                        value = 0;
//                    }
//                }
//            }

    }

    // 送幸运礼物得积分
    public void sendGiftAction(SendGiftSuccessMsgData mqData) {
        if (mqData.getCost() == null || mqData.fetchRealCost() <= 0) {
            return;
        }
        if (mqData.getIsLuck() == null || mqData.getIsLuck() == 0) {
            return;
        }
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(Integer.parseInt(ACTIVITY_ID));
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        checkActivityTimeAndChannel(configData, mqData.getTime(), currActor);

//        synchronized (stringPool.intern(ACTIVITY_ID)) {
            String fromUid = mqData.getUid();
            long increase = mqData.getCost() * SCORE_RATE;
            // 赠送幸运礼物消耗金币得到积分
            int integral = baseEveryLimitRedis.increaseAndGetRewards(currActor.getUid(), configData.getActivityCode(), increase, SEND_LUCY_GIFT_INC_INTEGRAL_TASK_CONFIG);
            if (integral < 1) {
                return;
            }
            activityCommonRedis.incCommonHashNum(getHashActivityId(ACTIVITY_ID, fromUid), TOTAL_INTEGRAL, integral);
//        }
    }

}
