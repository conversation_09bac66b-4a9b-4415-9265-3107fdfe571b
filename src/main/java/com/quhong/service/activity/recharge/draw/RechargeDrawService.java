package com.quhong.service.activity.recharge.draw;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.ActType;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.money.MoneyChangeActionConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.core.utils.SpringUtils;
import com.quhong.dao.ActivityRechargeDrawLogDao;
import com.quhong.dao.ActivityRechargeDrawUserInfoDao;
import com.quhong.dao.ActorDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.MoneyDetailData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.db.ActivityRechargeDrawUserInfoData;
import com.quhong.dao.datas.log.ActivityRechargeDrawLogData;
import com.quhong.data.bo.recharge.draw.DrawAwardBO;
import com.quhong.data.bo.recharge.draw.StepBO;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.dto.recharge.draw.DrawDTO;
import com.quhong.data.thData.DrawPrizesRecordLogData;
import com.quhong.data.thData.RechargeDigTreasureShovelEvent;
import com.quhong.data.vo.event.recharge.draw.DrawRecordVO;
import com.quhong.data.vo.event.recharge.draw.PageInfoVO;
import com.quhong.enums.CurrencyEnum;
import com.quhong.enums.EventCode;
import com.quhong.enums.RewardItemType;
import com.quhong.exceptions.WebException;
import com.quhong.mq.ActivityMQProduct;
import com.quhong.mq.enums.MQConstant;
import com.quhong.players.ActorMgr;
import com.quhong.redis.BaseZSetRedis;
import com.quhong.report.EventReport;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.ItemsChangeService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.common.RewardService;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/6/17 15:58
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RechargeDrawService {
    public static final Set<Integer> ACT_TYPE_SET = new HashSet<Integer>() {{
        add(ActType.GOOGLE_RECHARGE);
        add(ActType.MEMBER_REWARD);
        add(ActType.HMS_RECHARGE);
        add(16);
        add(81);
        add((int) ActType.MIDDLE_PAY);
        add(120);
        add((int) ActType.IOS_RECHARGE);
        add(124);
        add(125);
        add(126);
        add(127);
//        add((int) ActType.COIN_DEALERS_RECHARGE);//币商充值
    }};
    private static final boolean PROD = ServerConfiguration.isProduct();
    public static final String EVENT_URL = PROD ? "https://videochat.kissu.site/dig_treasure_202503" : "https://testvideochat.kissu.site/dig_treasure_202503";
    //    private static final int EVENT_CODE = ActivityTypeEnum.EVENT_RECHARGE_DRAW.getCode();
//    private static final int EVENT_CODE = ActivityTypeEnum.EVENT_RECHARGE_DRAW_V2.getCode();
//    private static final int EVENT_CODE = ActivityTypeEnum.EVENT_RECHARGE_DRAW_202409.getCode();
//    private static final int EVENT_CODE = EventCode.RECHARGE_DRAW_2502;
//    private static final int EVENT_CODE = EventCode.RECHARGE_DRAW_2503;
    private static final int EVENT_CODE = EventCode.EVENT_RECHARGE_DRAW_2506;
    /**
     * 产生活动货币比例 充值200金币：1活动货币
     */
    private static final int RATE = 200;
    /**
     * 刷新奖池 的消耗
     */
    private static final int REFRESH_COST = 1;
    /**
     * 步频最大值
     */
    private static final int STEP_MAX = 8;
    /**
     * 大于x金币发全频官方消息
     */
    private static final int SEND_NOTICE_REWARD_NUM_MIN = 750;
    private static final String NOTICE_IMG = "https://statics.kissu.mobi/icon/event/coins/recharge.png";
    private static final List<DrawAwardBO> SMALL_POOL = new ArrayList<DrawAwardBO>(2) {{
        add(new DrawAwardBO().setId(1L).setAwardType(RewardItemType.GOLD).setAwardName("600coins")
                .setAwardImg("https://statics.kissu.mobi/icon/event/coins/100.png").setAwardNum(15).setDataId(0).setUnitPrice(1).setRate(85));
        add(new DrawAwardBO().setId(2L).setAwardType(RewardItemType.GOLD).setAwardName("1000coins")
                .setAwardImg("https://statics.kissu.mobi/icon/event/coins/100.png").setAwardNum(25).setDataId(0).setUnitPrice(1).setRate(15));
    }};
    private static final List<DrawAwardBO> MIDDLE_POOL = new ArrayList<DrawAwardBO>(2) {{
        add(new DrawAwardBO().setId(3L).setAwardType(RewardItemType.GOLD).setAwardName("2000coins")
                .setAwardImg("https://statics.kissu.mobi/icon/event/coins/1000.png").setAwardNum(50).setDataId(0).setUnitPrice(1).setRate(85));
        add(new DrawAwardBO().setId(4L).setAwardType(RewardItemType.GOLD).setAwardName("4000coins")
                .setAwardImg("https://statics.kissu.mobi/icon/event/coins/1000.png").setAwardNum(100).setDataId(0).setUnitPrice(1).setRate(15));
    }};
    private static final List<DrawAwardBO> BIG_POOL = new ArrayList<DrawAwardBO>(4) {{
        add(new DrawAwardBO().setId(5L).setAwardType(RewardItemType.GOLD).setAwardName("6000coins")
                .setAwardImg("https://statics.kissu.mobi/icon/event/coins/1000.png").setAwardNum(150).setDataId(0).setUnitPrice(1).setRate(8500));
        add(new DrawAwardBO().setId(6L).setAwardType(RewardItemType.GOLD).setAwardName("8000coins")
                .setAwardImg("https://statics.kissu.mobi/icon/event/coins/1000.png").setAwardNum(200).setDataId(0).setUnitPrice(1).setRate(1400));
        add(new DrawAwardBO().setId(7L).setAwardType(RewardItemType.GOLD).setAwardName("16000coins")
                .setAwardImg("https://statics.kissu.mobi/icon/event/coins/10000.png").setAwardNum(400).setDataId(0).setUnitPrice(1).setRate(90));
        add(new DrawAwardBO().setId(8L).setAwardType(RewardItemType.GOLD).setAwardName("40000coins")
                .setAwardImg("https://statics.kissu.mobi/icon/event/coins/10000.png").setAwardNum(1000).setDataId(0).setUnitPrice(1).setRate(10));
    }};
    private static final List<DrawAwardBO> ALL_POOL = new ArrayList<DrawAwardBO>(8) {{
        addAll(SMALL_POOL);
        addAll(MIDDLE_POOL);
        addAll(BIG_POOL);
    }};
    private static final List<StepBO> STEP_LIST = new ArrayList<StepBO>(8) {{
        add(new StepBO().setCost(1).setAllCost(169).setStep(1));
        add(new StepBO().setCost(3).setAllCost(168).setStep(2));
        add(new StepBO().setCost(5).setAllCost(165).setStep(3));
        add(new StepBO().setCost(10).setAllCost(160).setStep(4));
        add(new StepBO().setCost(15).setAllCost(150).setStep(5));
        add(new StepBO().setCost(20).setAllCost(135).setStep(6));
        add(new StepBO().setCost(40).setAllCost(115).setStep(7));
        add(new StepBO().setCost(75).setAllCost(75).setStep(8));
    }};
    // 指定步频奖励大礼包
    private static final List<RewardTaskConfig> STEP_REWARD_LIST = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 3, RewardItemType.BUBBLE_FRAME, 169, 0).setName("Newbie"));
            add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, 612, 0).setName("Tell him"));
        }};
        add(new RewardTaskConfig().setCheckParams(3).setRewards(rewards3)
                .setNotice("\uD83E\uDD70Congratulations on getting digging treasures 3 times reward in [ recharge and dig treasures Event]." +
                        " The rewards have been issued,check more"));

        List<RewardInfoData> rewards8 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 5, RewardItemType.LORD_DAYS, 6, 0).setName("King"));
            add(new RewardInfoData(EVENT_CODE, 5, RewardItemType.SEAT_FRAME, 197, 0).setName("Sniper Master"));
            add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, 629, 250).setName("candy lover"));
        }};
        add(new RewardTaskConfig().setCheckParams(8).setRewards(rewards8)
                .setNotice("\uD83E\uDD70Congratulations on getting digging treasures 8 times reward in [ recharge and dig treasures Event]." +
                        " The rewards have been issued,check more"));
    }};
    private final AppConfigActivityDao appConfigActivityDao;

    private final ActorMgr actorMgr;

    private final ActivityRechargeDrawUserInfoDao activityRechargeDrawUserInfoDao;

    private final ActivityRechargeDrawLogDao activityRechargeDrawLogDao;

    private final BaseZSetRedis baseZSetRedis;

    private final GiveOutRewardService giveOutRewardService;

    private final ActorDao actorDao;

    private final OfficialNoticeService officialNoticeService;

    private final ActivityMQProduct activityMQProduct;

    private final EventReport eventReport;

    private final ItemsChangeService itemsChangeService;

    private final RewardService rewardService;

    private static StepBO getNextStep(ActivityRechargeDrawUserInfoData data) {
        return STEP_LIST.stream().filter(step -> data.getDrawTimes() < step.getStep())
                .min(Comparator.comparingInt(StepBO::getStep))
                .orElseThrow(() -> new WebException(new HttpEnvData(), new HttpCode(HttpCode.SERVER_ERROR).setMsg("not found next step")));
    }

    private static PageInfoVO initPageInfo(AppConfigActivityData configData) {
        StepBO initStep = STEP_LIST.get(0);
        return new PageInfoVO()
                .setStartTime(configData.getStartTime())
                .setEndTime(configData.getEndTime())
                .setDrawAwardList(ALL_POOL)
                .setNextDrawNeed(initStep.getCost())
                .setRoundAllDrawNeed(initStep.getAllCost())
                .setOnceDrawNeedTotalGold(initStep.getCost() * RATE)
                .setRoundAllDrawNeedTotalGold(initStep.getAllCost() * RATE);
    }

    private static int computeOverStep(DrawDTO dto, ActivityRechargeDrawUserInfoData data) {
        int overStep = data.getDrawTimes() + 1;
        if (dto.getDrawAll()) {
            //抽取剩余所有逻辑
            overStep = STEP_MAX;
            StepBO nextStep = getNextStep(data);
            if (data.getBalance() < nextStep.getAllCost()) {
                throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.BALANCE_NOT_ENOUGH).setMsg("your not enough"));
            }
        }
        return overStep;
    }

    private static ActivityRechargeDrawLogData genInitLogData(DrawDTO dto, ActorData currActor, Integer round, DrawAwardBO awardData, StepBO nextStep, long currTime) {
        return new ActivityRechargeDrawLogData()
                .setUid(currActor.getUid())
                .setEventCode(dto.getEventCode())
                .setRound(round)
                .setAwardType(awardData.getAwardType())
                .setAwardNum(awardData.getAwardNum())
                .setCostType(88)
                .setDataId(awardData.getDataId())
                .setCostNum(nextStep.getCost())
                .setAwardName(awardData.getAwardName())
                .setAwardImg(awardData.getAwardImg())
                .setCtime(currTime);
    }

    private static RewardInfoData fillRewardInfo(DrawDTO dto, DrawAwardBO awardData) {
        return new RewardInfoData(dto.getEventCode(), awardData.getAwardNum(), awardData.getAwardType(), awardData.getDataId(), awardData.getUnitPrice())
                .setName(awardData.getAwardName());
    }

    private static DrawAwardBO drawAwardInPool(ActivityRechargeDrawUserInfoData data) {
        List<DrawAwardBO> pool = genPool(data);
        AtomicInteger limit = new AtomicInteger();
        pool = pool.stream().peek(award -> fillRandomLimit(award, limit))
                .collect(Collectors.toList());
        Integer hit = MathUtils.randomSplitInt(0, limit.get());
        return pool.stream()
                .filter(award -> award.getLimit() > hit)
                .min(Comparator.comparingInt(DrawAwardBO::getLimit))
                .orElseThrow(() -> new WebException(new HttpEnvData(), new HttpCode(HttpCode.SERVER_ERROR).setMsg("not found award data")));
    }

    private static List<DrawAwardBO> genPool(ActivityRechargeDrawUserInfoData data) {
        List<DrawAwardBO> pool;
        switch (data.getDrawTimes()) {
            case 1:
            case 2:
                pool = SMALL_POOL;
                break;
            case 3:
            case 4:
                pool = MIDDLE_POOL;
                break;
            case 5:
            case 6:
            case 7:
            case 8:
                pool = BIG_POOL;
                break;
            default:
                throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("not support step,step=" + data.getDrawTimes()));
        }
        pool = pool.stream()
                .filter(award -> !data.getRoundRewards().contains(award.getId().intValue()))
                .map(award -> SpringUtils.copyObj(award, DrawAwardBO.class))//处理多线程问题
                .collect(Collectors.toList());
        if (pool.isEmpty()) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.SERVER_ERROR).setMsg("not found award pool data"));
        }
        return pool;
    }

    private static void fillRandomLimit(DrawAwardBO award, AtomicInteger limit) {
        limit.addAndGet(award.getRate());
        award.setLimit(limit.get());
    }

    public PageInfoVO pageInfo(Integer eventCode, String uid) {
        long currTime = DateHelper.getCurrTime();
        ActorData actorData = actorMgr.getCurrActorData(uid);
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventCode);
        PageInfoVO vo = initPageInfo(configData);
        if (currTime < configData.getStartTime()) {
            return vo;
        }
        ActivityRechargeDrawUserInfoData data = activityRechargeDrawUserInfoDao.getOneByEventCodeAndUid(eventCode, uid);
        if (data == null) {
            data = ActivityRechargeDrawUserInfoData.Factory.init(uid, actorData.getRid(), eventCode, 0, currTime);
        }
        StepBO nextStep = getNextStep(data);
        int currGoldBalance = computeCurrGoldBalance(actorData, data);
        ActivityRechargeDrawUserInfoData finalData = data;
        List<DrawAwardBO> drawList = ALL_POOL.stream()
                .map(award -> SpringUtils.copyObj(award, DrawAwardBO.class))
                .map(award -> finalData.getRoundRewards().contains(award.getId().intValue()) ? award.setIsDraw(true) : award.setIsDraw(false))
                .sorted(Comparator.comparingLong(DrawAwardBO::getId))
                .collect(Collectors.toList());
        return vo.setBalance(data.getBalance())
                .setRoundTimes(data.getDrawTimes())
                .setNextDrawNeed(nextStep.getCost())
                .setRoundAllDrawNeed(nextStep.getAllCost())
                .setOnceDrawNeedTotalGold(nextStep.getCost() * RATE)
                .setCurrGoldBalance(currGoldBalance)
                .setRoundAllDrawNeedTotalGold(nextStep.getAllCost() * RATE)
                .setDrawAwardList(drawList);
    }

    private int computeCurrGoldBalance(ActorData actorData, ActivityRechargeDrawUserInfoData data) {
        String rechargeKey = baseZSetRedis.eventRechargeDrawKey(EVENT_CODE);
        Double score = baseZSetRedis.getScore(rechargeKey, actorData.getUid());
        if (score == null) {
            score = (double) 0;
        }
        return (data.getBalance() * RATE) + score.intValue();
    }

    public List<DrawAwardBO> draw(DrawDTO dto) {
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventCode());
        if (currTime < configData.getStartTime() || currTime > configData.getEndTime()) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("not in event time"));
        }
        if (StringUtils.hasLength(configData.getChannel()) && !configData.getChannel().contains(currActor.getChannel())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("your app not join this event"));
        }
        ActivityRechargeDrawUserInfoData data = activityRechargeDrawUserInfoDao.getOneByEventCodeAndUid(dto.getEventCode(), dto.getUid());
        if (data == null) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.BALANCE_NOT_ENOUGH).setMsg("your not enough"));
        }

        List<DrawAwardBO> vo = new ArrayList<>(8);
        List<RewardInfoData> rewardInfoDataList = new ArrayList<>(8);
        int overStep = computeOverStep(dto, data);
        doDraw(dto, data, overStep, vo, rewardInfoDataList, currActor, currTime);
        //奖励集合发放
        giveOutRewardService.giveOutReward(dto.getUid(), rewardInfoDataList);
        itemsChangeService.itemChangeReport(currActor.getUid(), rewardInfoDataList, EVENT_CODE, currTime, "draw award");
        rewardInfoDataList.forEach(reward -> activityMQProduct.sendObjectMq(MQConstant.EVENT_COST_COUNT_ROUTE, reward));
        //refresh round
        refreshRound(data, currTime, currActor);
        data.setMtime(currTime);
        activityRechargeDrawUserInfoDao.updateOneSelective(data);
        return vo;
    }

    private void doDraw(DrawDTO dto, ActivityRechargeDrawUserInfoData data, int overStep, List<DrawAwardBO> vo, List<RewardInfoData> rewardInfoDataList, ActorData currActor, long currTime) {
        String drawType = dto.getDrawAll() ? "all" : "one";
        while (data.getDrawTimes() < overStep) {
            StepBO nextStep = getNextStep(data);
            //抽取一次逻辑
            if (data.getBalance() < nextStep.getCost()) {
                throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.BALANCE_NOT_ENOUGH).setMsg("your not enough"));
            }
            data.increaseDrawTimes(1);
            data.increaseBalance(-nextStep.getCost());
            DrawAwardBO awardData = drawAwardInPool(data);
            vo.add(awardData);
            data.getRoundRewards().add(awardData.getId().intValue());
            //奖励集合
            RewardInfoData rewardInfoData = fillRewardInfo(dto, awardData);
            rewardInfoDataList.add(rewardInfoData);
            int currDrawTimes = data.getDrawTimes();
            int currRound = data.getRound();
            // 奖励记录上报数数
            doDrawReportToThinkData(dto, data, currActor, currTime, nextStep, drawType, rewardInfoData);

            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    drawAfterProcess(dto, currRound, currActor, currTime, awardData, nextStep, currDrawTimes);
                }
            });
        }
    }

    private void doDrawReportToThinkData(DrawDTO dto, ActivityRechargeDrawUserInfoData data, ActorData currActor, long currTime, StepBO nextStep, String drawType, RewardInfoData rewardInfoData) {
        DrawPrizesRecordLogData logData = new DrawPrizesRecordLogData()
                .setChannel(currActor.getChannel())
                .setCtime(currTime)
                .setScene(EVENT_CODE)
                .setSceneDetail(1)//抽奖
                .setCostTicket(Long.valueOf(nextStep.getCost()))
                .setDrawNums(1)
                .setDrawDesc(data.getDrawTimes().toString())
                .setDrawDetail(drawType)
                .setDrawRound(data.getRound().toString())
                .setDrawResult(JSON.toJSONString(rewardInfoData));
        logData.setUid(dto.getUid());
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private void drawAfterProcess(DrawDTO dto, Integer round, ActorData currActor, long currTime, DrawAwardBO awardData, StepBO nextStep, int currDrawTimes) {
        //保存抽奖记录
        saveRewardLog(dto, currActor, round, awardData, nextStep, currTime);
        // 大于指定金币数，发送全频道官方消息
//        if (awardData.getAwardNum() >= SEND_NOTICE_REWARD_NUM_MIN) {
//            sendGreaterThanAndEqualsNumNotice(dto, awardData, currActor, currTime);
//        }//宣传力度过大先关闭@chengchen

        // 达到指定步频奖励发放，上报数数
        STEP_REWARD_LIST.stream()
                .filter(config -> config.getCheckParams().equals(currDrawTimes))
                .findFirst()
                .ifPresent(rewardTaskConfig -> giveStepRewards(dto, currActor, currTime, rewardTaskConfig));
    }

    private void giveStepRewards(DrawDTO dto, ActorData currActor, long currTime, RewardTaskConfig rewardTaskConfig) {
        giveOutRewardService.giveOutReward(dto.getUid(), rewardTaskConfig.getRewards());
        itemsChangeService.itemChangeReport(dto.getUid(), rewardTaskConfig.getRewards(), EVENT_CODE, currTime, rewardTaskConfig.getCheckParams().toString());
        rewardTaskConfig.getRewards().forEach(reward -> activityMQProduct.sendObjectMq(MQConstant.EVENT_COST_COUNT_ROUTE, reward));
        sendStepRewardNotice(dto, currActor, currTime, rewardTaskConfig);
    }

    private void sendStepRewardNotice(DrawDTO dto, ActorData currActor, long currTime, RewardTaskConfig rewardTaskConfig) {
        try {
            // 步频奖励官方消息
            officialNoticeService.sendOfficialNotice(dto.getUid(), "", rewardTaskConfig.getNotice(), NOTICE_IMG, EVENT_URL, currActor.getChannel(),
                    (int) (currTime + MathUtils.randomSplitInt(0, 50)), dto.getEventCode());
        } catch (Exception e) {
            log.error("notice error, msg={}", e.getMessage(), e);
        }
    }

    private void sendGreaterThanAndEqualsNumNotice(DrawDTO dto, DrawAwardBO awardData, ActorData currActor, long currTime) {
        try {
//            String notice = "Congratulations to #username for winning #awardName in the [Recharge & Dig treasure]  event. Go and watch";
//            notice = notice.replace("#username", currActor.getName())
//                    .replace("#awardName", awardData.getAwardName());
//            // 大奖全频官方消息 图片 链接
//            officialNoticeService.sendGlobalNotice("", notice, NOTICE_IMG, EVENT_URL, currActor.getChannel(),
//                    (int) (currTime + MathUtils.randomSplitInt(0, 50)), dto.getEventCode(), LangIdConstant.ENGLISH,
//                    null, OfficialUserGroup.CHANNEL);
            List<RewardInfoData> rewards = Collections.singletonList(new RewardInfoData(dto.getEventCode(), awardData.getAwardNum(), awardData.getAwardType(), awardData.getDataId())
                    .setName(awardData.getAwardName()).setIcon(awardData.getAwardImg()));
            rewardService.sendUniversalActivityPopMsg(currActor, rewards, EVENT_URL, "Recharge&Dig Event",
                    "", "Recharge to get more rewards");
        } catch (Exception e) {
            log.error("notice error,msg={}", e.getMessage(), e);
        }
    }

    private void saveRewardLog(DrawDTO dto, ActorData currActor, Integer round, DrawAwardBO awardData, StepBO nextStep, long currTime) {
        ActivityRechargeDrawLogData insertData = genInitLogData(dto, currActor, round, awardData, nextStep, currTime);
        activityRechargeDrawLogDao.insertOneSelective(insertData);
    }

    private void refreshRound(ActivityRechargeDrawUserInfoData data, long currTime, ActorData currActor) {
        if (data.getDrawTimes() >= STEP_MAX) {
            data.increaseRound(1);
            data.setDrawTimes(0);
            data.setRoundRewards(new HashSet<>(0));
            drawRefreshReportToThinkData(currTime, STEP_MAX, data.getRound() - 1, currActor, 0);
        }
    }

    public Boolean drawRefresh(DrawDTO dto) {
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventCode());
        if (currTime < configData.getStartTime() || currTime > configData.getEndTime()) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("not in event time"));
        }
        if (StringUtils.hasLength(configData.getChannel()) && !configData.getChannel().contains(currActor.getChannel())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("your app not join this event"));
        }

        ActivityRechargeDrawUserInfoData data = activityRechargeDrawUserInfoDao.getOneByEventCodeAndUid(dto.getEventCode(), dto.getUid());
        if (data == null) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.BALANCE_NOT_ENOUGH).setMsg("your not enough"));
        }
        if (data.getBalance() < REFRESH_COST) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.BALANCE_NOT_ENOUGH).setMsg("your not enough"));
        }
        Integer beforeDrawTimes = data.getDrawTimes();
        Integer beforeRound = data.getRound();
        doDrawRefresh(data, currTime);
        //奖池刷新上报数数
        drawRefreshReportToThinkData(currTime, beforeDrawTimes, beforeRound, currActor, REFRESH_COST);
        return true;
    }

    private void drawRefreshReportToThinkData(long currTime, Integer beforeDrawTimes, Integer beforeRound, ActorData currActor, long ticket) {
        DrawPrizesRecordLogData logData = new DrawPrizesRecordLogData()
                .setChannel(currActor.getChannel())
                .setCtime(currTime)
                .setScene(EVENT_CODE)
                .setSceneDetail(2)//refresh
                .setCostTicket(ticket)
                .setDrawDesc(beforeDrawTimes.toString())
                .setDrawRound(beforeRound.toString());
        logData.setUid(currActor.getUid());
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private void doDrawRefresh(ActivityRechargeDrawUserInfoData data, long currTime) {
        data.increaseBalance(-REFRESH_COST);
        data.increaseRound(1);
        data.setDrawTimes(0);
        data.setRoundRewards(new HashSet<>());
        ActivityRechargeDrawUserInfoData updateData = ActivityRechargeDrawUserInfoData.Factory.initUpdateData(data);
        updateData.setBalance(data.getBalance())
                .setRound(data.getRound())
                .setDrawTimes(data.getDrawTimes())
                .setRoundRewards(data.getRoundRewards())
                .setMtime(currTime);
        activityRechargeDrawUserInfoDao.updateOneSelective(updateData);
    }

    public List<DrawRecordVO> drawRecord(Integer eventCode, String uid) {
        long currTime = DateHelper.getCurrTime();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventCode);
        if (currTime < configData.getStartTime()) {
            return new ArrayList<>();
        }

        List<ActivityRechargeDrawLogData> dataList = activityRechargeDrawLogDao.getListFromRedisByEventCodeAndUid(eventCode, uid);
        if (ObjectUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        return dataList.stream()
                .map(this::fillDrawRecordVO)
                .collect(Collectors.toList());
    }

    public List<String> lastHistory(Integer eventCode) {
        long currTime = DateHelper.getCurrTime();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventCode);
        if (currTime < configData.getStartTime()) {
            return new ArrayList<>();
        }
        List<ActivityRechargeDrawLogData> logList = activityRechargeDrawLogDao.queryLastList(eventCode, 30);
        Set<String> uidSet = logList.stream().map(ActivityRechargeDrawLogData::getUid).collect(Collectors.toSet());
        List<ActorData> actorList = actorDao.findActorByUidSet(uidSet);
        Map<String, String> uidNameMap = actorList.stream().collect(Collectors.toMap(ActorData::getUid, ActorData::getName));
        return logList.stream()
                .map(data -> fillLastHistoryStr(data, uidNameMap))
                .collect(Collectors.toList());

    }

    private String fillLastHistoryStr(ActivityRechargeDrawLogData data, Map<String, String> uidNameMap) {
        String awardName = data.getAwardName();
        String username = uidNameMap.get(data.getUid());
        return "Congratulations to #username for winning #awardName".replace("#username", username)
                .replace("#awardName", awardName);
    }

    private DrawRecordVO fillDrawRecordVO(ActivityRechargeDrawLogData data) {
        return new DrawRecordVO()
                .setName(data.getAwardName())
                .setImg(data.getAwardImg())
                .setCost(data.getCostNum())
                .setTime(DateHelper.BEIJING.getDateByTime(data.getCtime() * 1000));
    }


    public void moneyDetailAction(MoneyDetailData mqData) {
        if (mqData.getSingleAction() != MoneyChangeActionConstant.ACTION_ADD) {
            return;
        }
        if (!CurrencyEnum.CURRENCY1.getCurrencyCode().equals(mqData.getCurrencyCode())) {
            return;
        }
        if (mqData.getSingleChange() == null || mqData.getSingleChange() < 1) {
            return;
        }
        if (!ACT_TYPE_SET.contains(mqData.getActType())) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUserid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(EVENT_CODE);
        if (StringUtils.hasLength(configData.getChannel()) && !configData.getChannel().contains(currActor.getChannel())) {
            return;
        }
        if (configData.getStartTime() > mqData.getMtime() || mqData.getMtime() > configData.getEndTime()) {
            return;
        }

        // 每次充值额外下发1天vip
        RewardInfoData timesAward = new RewardInfoData(EVENT_CODE, 1,
                RewardItemType.VIP_DAYS, 0, 0).setName("VIP");
        giveOutRewardService.giveOutReward(currActor.getUid(), Collections.singletonList(timesAward));
        itemsChangeService.itemChangeReport(currActor.getUid(), timesAward, EVENT_CODE, mqData.getMtime(), "Each recharge will receive an additional VIP day as a bonus");

        String rechargeKey = baseZSetRedis.eventRechargeDrawKey(EVENT_CODE);
        long gold = baseZSetRedis.increaseAndGet(rechargeKey, mqData.getUserid(), mqData.getSingleChange(), Duration.ofDays(30));
        if (gold < RATE) {
            return;
        }
        // 计算出需要添加的活动货币
        long addBalance = gold / RATE;
        // 剩余金币
        long goldBalance = gold % RATE;
        baseZSetRedis.addToZSet(rechargeKey, mqData.getUserid(), goldBalance, Duration.ofDays(30));
        updateBalance(mqData.getMtime(), currActor, (int) addBalance);

    }

    private void updateBalance(Long currTime, ActorData currActor, int addBalance) {
        ActivityRechargeDrawUserInfoData data = activityRechargeDrawUserInfoDao.getOneByEventCodeAndUid(RechargeDrawService.EVENT_CODE, currActor.getUid());
        int before_balance = 0;
        if (data == null) {
            data = ActivityRechargeDrawUserInfoData.Factory.init(currActor.getUid(), currActor.getRid(), RechargeDrawService.EVENT_CODE, addBalance, currTime);
            activityRechargeDrawUserInfoDao.insertOneSelective(data);
        } else {
            before_balance = data.getBalance();
            data.increaseBalance(addBalance);
            ActivityRechargeDrawUserInfoData updateData = ActivityRechargeDrawUserInfoData.Factory.initUpdateData(data);
            updateData.setBalance(data.getBalance())
                    .setMtime(currTime);
            activityRechargeDrawUserInfoDao.updateOneSelective(updateData);
        }
        // 体力值获取，上报数数
        giveBalanceReportToThinkData(currTime, currActor, addBalance, before_balance, data);
    }

    private void giveBalanceReportToThinkData(Long currTime, ActorData currActor, int addBalance, int before_balance, ActivityRechargeDrawUserInfoData data) {
        RechargeDigTreasureShovelEvent logData = new RechargeDigTreasureShovelEvent()
                .setCtime(currTime)
                .setBeforeShovelNumber(before_balance)
                .setShovelNumber(addBalance)
                .setAfterShovelNumber(data.getBalance());
        logData.setUid(currActor.getUid());
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }
}
