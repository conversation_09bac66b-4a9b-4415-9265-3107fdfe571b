package com.quhong.service.activity.plant.date.palm;

import com.alibaba.fastjson.JSON;
import com.quhong.common.enums.HttpCode;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.WarnName;
import com.quhong.core.datas.DayTimeData;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.core.utils.SpringUtils;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.data.bo.plant.date.palm.DailyUserInfoBO;
import com.quhong.data.bo.plant.date.palm.InviteUserInfoBO;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.dto.event.plant.date.palm.InviteDTO;
import com.quhong.data.dto.event.plant.date.palm.WateringDTO;
import com.quhong.data.thData.ActivityParticipationEvent;
import com.quhong.data.thData.ActivitySpecialItemsEvent;
import com.quhong.data.vo.BaseAwardVO;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.event.plant.date.palm.LastRecordVO;
import com.quhong.data.vo.event.plant.date.palm.PageInitVO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.ActorInfo;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.enums.EventCode;
import com.quhong.enums.RewardItemType;
import com.quhong.exceptions.WebException;
import com.quhong.moderatoin.ModerationService;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.mq.data.room.EnterRoomMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.redis.BaseHashSaveRedis;
import com.quhong.redis.BaseZSetRedis;
import com.quhong.redis.base.ActivityCommonRedis;
import com.quhong.redis.base.currency.task.BaseDailyOneLimitRedis;
import com.quhong.redis.base.currency.task.BaseDailyTaskLimitRedis;
import com.quhong.redis.base.reward.BaseOneLimitRewardRedis;
import com.quhong.report.EventReport;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.common.RewardService;
import com.quhong.service.common.RoomFloatingImService;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 签到数据hash redis（不做）
 * 签到提醒set（不做）
 * 水滴榜zset redis
 * 走马灯轮播逻辑 list redis（枣椰树升级）
 * 浇水5次 任务 redis
 * <p>
 * 每日进房任务 redis
 * 每日邀请任务 redis
 * 每日个人数据 hash redis
 * 每日达成枣椰树 zset redis
 *
 * <AUTHOR>
 * @since 2024/9/18 18:08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EventPlantDatePalmService {
    public static final String LEVEL_UPGRADE_NOTICE_FORMAT = "Congratulations to Completing L#level Fruit ripening，Date palm planting successfully completed， Rewards have been issued >>";
    public static final String RANK_NOTICE_FORMAT = "Top #top Water drop collection master in Plant Date Palm Event ,Rewards have been sent to your backpack, remember to check >>>";
    private static final boolean PROD = ServerConfiguration.isProduct();
    private static final int DATE_PALM_STATUS_MAX = 6;
    private static final int DAILY_ENTER_ROOM_REWARD = 10;
    private static final int DAILY_INVITE_USER_LIMIT = 5;
    private static final int DAILY_INVITE_REWARD = 10;
    private static final int EVENT_CODE = EventCode.EVENT_PLANT_DATE_PALM_2410;
    private static final String EVENT_IMG = "https://statics.kissu.mobi/Event/Welfare/Battle/Planting/notice_v1.jpg";
    private static final String EVENT_URL = PROD ? "https://videochat.kissu.site/plant_date_palm" : "https://testvideochat.kissu.site/plant_date_palm";
    private static final String WATER_ICON = "https://statics.kissu.mobi/Event/Welfare/Battle/Planting/11.png";
    /**
     * 枣椰树等级配置
     * checkParams: 等级
     * limit: 到下一等级需要的水滴克数
     */
    private static final List<RewardTaskConfig> LEVEL_CONFIGS = new ArrayList<RewardTaskConfig>() {{
        List<RewardInfoData> rewards1 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1000, RewardItemType.LEVEL_SCORE, 0, 0).setName("1000points").setIcon("https://statics.kissu.mobi/Event/Welfare/Battle/Planting/EXP2.png"));
            add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.BUBBLE_FRAME, PROD ? 266 : 57, 0).setName("Green Tree*1Day").setIcon("https://statics.kissu.mobi/room_item/icon/1727080145633/green_tree.png"));
        }};
        add(new RewardTaskConfig(EVENT_CODE, 1, 20, rewards1));

        List<RewardInfoData> rewards2 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 3, RewardItemType.SEAT_FRAME, PROD ? 268 : 55, 1).setName("Green Wing*3Days").setIcon("https://statics.kissu.mobi/room_item/icon/1727080508779/green_wing.png"));
            add(new RewardInfoData(EVENT_CODE, 5, RewardItemType.GOLD, 0, 1).setName("200coins").setIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png"));
            add(new RewardInfoData(EVENT_CODE, 1500, RewardItemType.LEVEL_SCORE, 0, 0).setName("1500points").setIcon("https://statics.kissu.mobi/Event/Welfare/Battle/Planting/EXP2.png"));
        }};
        add(new RewardTaskConfig(EVENT_CODE, 2, 50, rewards2));

        List<RewardInfoData> rewards3 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, PROD ? 517 : 23, 1).setName("Hello Rose*1").setIcon("https://statics.kissu.mobi/icon/gift/lucky/Hello_Rose.png"));
            add(new RewardInfoData(EVENT_CODE, 25, RewardItemType.GOLD, 0, 1).setName("1000coins").setIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png"));
            add(new RewardInfoData(EVENT_CODE, 2000, RewardItemType.LEVEL_SCORE, 0, 0).setName("1500points").setIcon("https://statics.kissu.mobi/Event/Welfare/Battle/Planting/EXP2.png"));
        }};
        add(new RewardTaskConfig(EVENT_CODE, 3, 50, rewards3));

        List<RewardInfoData> rewards4 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 2, RewardItemType.GIFT, PROD ? 517 : 23, 1).setName("Hello Rose*2").setIcon("https://statics.kissu.mobi/icon/gift/lucky/Hello_Rose.png"));
            add(new RewardInfoData(EVENT_CODE, 100, RewardItemType.GOLD, 0, 1).setName("4000coins").setIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png"));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.SEAT_FRAME, PROD ? 68 : 55, 0).setName("Travel Master*30Days").setIcon("https://statics.kissu.mobi/room_item/origin/123/Travel_Master.webp"));
        }};
        add(new RewardTaskConfig(EVENT_CODE, 4, 100, rewards4));

        List<RewardInfoData> rewards5 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 3, RewardItemType.GIFT, PROD ? 517 : 23, 1).setName("Hello Rose*3").setIcon("https://statics.kissu.mobi/icon/gift/lucky/Hello_Rose.png"));
            add(new RewardInfoData(EVENT_CODE, 125, RewardItemType.GOLD, 0, 1).setName("5000coins").setIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png"));
            add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, PROD ? 604 : 206, 25).setName("Romantic rose*1").setIcon("https://statics.kissu.mobi/Event/super/Romantic_rose.png"));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.LORD_DAYS, 5, 0).setName("Duke*30Days").setIcon("https://statics.kissu.mobi/icon/aristocrat/Duke.png"));
        }};
        add(new RewardTaskConfig(EVENT_CODE, 5, 150, rewards5));

        List<RewardInfoData> rewards6 = new ArrayList<RewardInfoData>() {{
            add(new RewardInfoData(EVENT_CODE, 4, RewardItemType.GIFT, PROD ? 517 : 23, 1).setName("Hello Rose*4").setIcon("https://statics.kissu.mobi/icon/gift/lucky/Hello_Rose.png"));
            add(new RewardInfoData(EVENT_CODE, 375, RewardItemType.GOLD, 0, 1).setName("15000coins").setIcon("https://statics.kissu.mobi/Event/shot/gold-more[1].png"));
            add(new RewardInfoData(EVENT_CODE, 1, RewardItemType.GIFT, 553, 25).setName("Justice Arrow*1").setIcon("https://statics.kissu.mobi/icon/diwali/v3/Arrow.png"));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.LORD_DAYS, 6, 0).setName("King*30Days").setIcon("https://statics.kissu.mobi/icon/aristocrat/King.png"));
            add(new RewardInfoData(EVENT_CODE, 30, RewardItemType.ENTER_EFFECT, PROD ? 242 : 99, 0).setName("Fighter car*30Days").setIcon("https://statics.kissu.mobi/room_item/icon/1722998467535/Red_Speed_Car_402x336.png"));
        }};
        add(new RewardTaskConfig(EVENT_CODE, 6, 150, rewards6));
    }};
    /**
     * 活动期间浇水次数达标奖励配置
     */
    private static final List<RewardInfoData> REWARD = new ArrayList<RewardInfoData>() {{
        add(new RewardInfoData(EVENT_CODE, 15, RewardItemType.ENTER_EFFECT, 203, 0));
    }};
    private static final RewardTaskConfig WATERING_REWARD_CONFIG = new RewardTaskConfig(0, 5, 1, REWARD);

    private final ActorMgr actorMgr;
    private final AppConfigActivityDao appConfigActivityDao;
    private final BaseHashSaveRedis baseHashSaveRedis;
    private final BaseZSetRedis baseZSetRedis;
    private final ActivityCommonRedis activityCommonRedis;
    private final ModerationService moderatorService;
    private final GiveOutRewardService giveOutRewardService;
    private final OfficialNoticeService officialNoticeService;
    private final RewardService rewardService;
    private final RoomFloatingImService roomFloatingImService;
    private final BaseOneLimitRewardRedis baseOneLimitRewardRedis;
    private final EventReport eventReport;
    private final MonitorSender monitorSender;

    @Resource(name = "datePalmEnterRoomTaskLimitRedis")
    private BaseDailyOneLimitRedis datePalmEnterRoomTaskLimitRedis;

    @Resource(name = "datePalmGiftCostDailyTaskRedis")
    private BaseDailyTaskLimitRedis datePalmGiftDailyCostDailyTaskRedis;

    private static List<RewardInfoData> fillRewards(List<RewardInfoData> rewards, int eventCode) {
        return rewards.stream()
                .map(reward -> SpringUtils.copyObj(reward, RewardInfoData.class))
                .peek(reward -> reward.setActivityType(eventCode))
                .collect(Collectors.toList());
    }

    private static RewardTaskConfig findCurrLevelConfig(DailyUserInfoBO userInfo) {
        if (userInfo.getDatePalmStatus() == DATE_PALM_STATUS_MAX) {
            return null;
        }
        return LEVEL_CONFIGS.stream()
                .filter(config -> config.getCheckParams().equals(userInfo.getDatePalmStatus() + 1))
                .findFirst()
                .orElseThrow(() -> new WebException(HttpCode.createHttpCode(HttpCode.SERVER_ERROR, true, "not find date palm level config")));
    }
//
//    /**
//     * 每日达成枣椰树榜单zset key
//     *
//     * @param eventCode
//     * @param date
//     * @return
//     */
//    private String dailyCompleteDatePalmRankZsetKey(Integer eventCode, String date) {
//        return "zset:event:date_palm:daily:complete_date_palm_rank:" + eventCode + ":" + date;
//    }

    private static InviteUserInfoBO findInvitedUser(InviteDTO dto, List<InviteUserInfoBO> inviteUsers) {
        InviteUserInfoBO finalInviteInfo;
        InviteUserInfoBO inviteUserInfoBO = inviteUsers.get(dto.getIndex());
        if (inviteUserInfoBO == null || !inviteUserInfoBO.getUid().equals(dto.getInvitedUid())) {
            InviteUserInfoBO realInviteUserInfo = inviteUsers.stream()
                    .filter(Objects::nonNull)
                    .filter(inviteUser -> inviteUser.getUid().equals(dto.getInvitedUid()))
                    .findFirst().orElse(null);
            if (realInviteUserInfo == null) {
                throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "The invited user is not on the invitation list"));
            }
            finalInviteInfo = realInviteUserInfo;
        } else {
            finalInviteInfo = inviteUserInfoBO;
        }
        return finalInviteInfo;
    }

    /**
     * 水滴榜key
     *
     * @param eventCode 活动码
     */
    private String waterRankZsetKey(Integer eventCode) {
        return "zset:event:date_palm:water_rank:" + eventCode;
    }

    /**
     * 每日个人数据hash key
     *
     * @param eventCode 活动码
     * @param date      日期 yyyy-MM-dd
     */
    private String dailyUserInfoHashKey(Integer eventCode, String date) {
        return "hash:event:date_palm:daily:user_info:" + eventCode + ":" + date;
    }

    public void rankRewards(int eventCode, List<RewardTaskConfig> rewardConfigs) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventCode);
        String rankKey = waterRankZsetKey(eventCode);
        List<CountVO> dataList = baseZSetRedis.getRange(rankKey, 1, 10);
        if (ObjectUtils.isEmpty(dataList)) {
            return;
        }
        StringBuilder content = new StringBuilder();
        content.append("枣椰树榜单(").append(configData.getActivityDesc()).append(")\n")
                .append("排名\t水滴数\t用户id\n");
        IntStream.range(0, dataList.size()).forEach(index -> this.rewardAndNotice(index + 1, dataList.get(index), rewardConfigs, content, eventCode));

        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, content.toString());
    }

    private void rewardAndNotice(int rankNum, CountVO data, List<RewardTaskConfig> rewardConfigs, StringBuilder content, int eventCode) {
        // 榜单奖励发放
        try {
            content.append(rankNum).append("\t").append(data.getCount()).append("\t").append(data.getUid());
            ActorData rankActor = actorMgr.getActorData(data.getUid());
            if (rankActor == null) {
                return;
            }
            rewardConfigs.stream()
                    .filter(rewardConfig -> rewardConfig.getCheckParams() == rankNum)
                    .findFirst().ifPresent(rewardConfig -> giveRewardAndSendNotice(rankNum, data, content, eventCode, rewardConfig, rankActor));
        } finally {
            content.append("\n");
        }
    }

    private void giveRewardAndSendNotice(int rankNum, CountVO data, StringBuilder content, int eventCode, RewardTaskConfig rewardConfig, ActorData rankActor) {
        List<RewardInfoData> rewards = fillRewards(rewardConfig.getRewards(), eventCode);
        giveOutRewardService.giveEventReward(data.getUid(), rewards, eventCode, "3");
        content.append("\t").append(rankActor.getRid());
        // rank notice
        int currTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        String notice = RANK_NOTICE_FORMAT.replace("#top", String.valueOf(rankNum));
        officialNoticeService.sendOfficialNotice(data.getUid(), "", notice, EVENT_IMG, genUrl(eventCode), rankActor.getChannel(), currTime, eventCode);
    }

    private String genUrl(int eventCode) {
        return EVENT_URL + "_" + eventCode + "/";
    }

    public void dailyNotice(int eventCode) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventCode);
        DateHelper dateHelper = DateHelper.genDateHelper(configData.getChannel());
        String dailyUserInfoHashKey = dailyUserInfoHashKey(eventCode, dateHelper.getToday());
        List<DailyUserInfoBO> userList = baseHashSaveRedis.getListByRedis(dailyUserInfoHashKey, DailyUserInfoBO.class);
        if (ObjectUtils.isEmpty(userList)) {
            return;
        }
        int currTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        // 每日提醒 通知
        String notice = " \uD83D\uDCA7All data of the Date Palm Tree activity will be automatically refreshed at 00:00 UTC+8:00 Next day. " +
                "Remember to water your water drops in time to get rewards, otherwise it will be cleared the next day~";
        userList.stream()
                .filter(user -> user.getCurrWaterBalance() > 0)
                .forEach(user -> officialNoticeService.sendOfficialNotice(user.getUid(), "", notice,
                        EVENT_IMG, genUrl(eventCode), configData.getChannel(), currTime, configData.getActivityCode()));

    }

    public PageInitVO pageInit(CommonDTO dto) {
        dto.checkParams();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        long currTime = DateHelper.getCurrTime();
        if (currTime < configData.getStartTime()) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "Event has not started"));
        }
        if (!configData.getChannel().equals(currActor.getChannel())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "your app not join this event"));
        }
        if (!ObjectUtils.isEmpty(configData.getUserCountryGroupStr()) && !configData.getUserCountryGroupStr().contains(currActor.getCountryCode())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "your country not join this event"));
        }
        DateHelper dateHelper = DateHelper.genDateHelper(configData.getChannel());
        DayTimeData today = dateHelper.getContinuesDays(dateHelper.getToday());
        String dailyUserInfoHashKey = dailyUserInfoHashKey(dto.getEventType(), dateHelper.getToday());
        DailyUserInfoBO userInfo = findDailyUserInfo(dto.getUid(), dailyUserInfoHashKey);
        RewardTaskConfig levelConfig = findCurrLevelConfig(userInfo);
        int needWater = 99999999;
        if (levelConfig != null) {
            needWater = levelConfig.getLimit();
        }
        return new PageInitVO()
                .setStartTime(configData.getStartTime())
                .setEndTime(configData.getEndTime())
                .setTodayEndTime((long) today.getEndTime())
                .setUid(userInfo.getUid())
                .setCurrWaterBalance(userInfo.getCurrWaterBalance())
                .setDatePalmStatus(userInfo.getDatePalmStatus())
                .setCurrWateredCount(userInfo.getCurrWateredCount())
                .setCurrRoundNeedTotalCount(needWater)
                .setDailyEnterRoomWaterCount(userInfo.getDailyEnterRoomWaterCount())
                .setDailyInviteWaterCount(userInfo.getDailyInviteWaterCount())
                .setDailySendGiftWaterCount(userInfo.getDailySendGiftWaterCount())
                .setInviteUsers(userInfo.getInviteUsers());
    }

//    private void dealDailyCompleteDatePalmRank(WateringDTO dto, DailyUserInfoBO userInfo, DateHelper dateHelper, long currTime) {
//        if (userInfo.getDatePalmStatus() == DATE_PALM_STATUS_MAX) {
//            String dailyCompleteDatePalmRankZsetKey = dailyCompleteDatePalmRankZsetKey(dto.getEventCode(), dateHelper.getToday());
//            Double score = baseZSetRedis.getScore(dailyCompleteDatePalmRankZsetKey, dto.getUid());
//            if (score == null) {
//                baseZSetRedis.increaseAndGet(dailyCompleteDatePalmRankZsetKey, dto.getUid(), currTime, Duration.ofDays(14));
//            }
//        }
//    }

    public List<BaseAwardVO> watering(WateringDTO dto) {
        dto.checkParams();
        if (dto.getWateredCount() == null || dto.getWateredCount() < 1) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "watering must be greater than 0"));
        }
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        checkLimit(currTime, configData, currActor);
        DateHelper dateHelper = DateHelper.genDateHelper(configData.getChannel());
        String dailyUserInfoHashKey = dailyUserInfoHashKey(dto.getEventType(), dateHelper.getToday());
        DailyUserInfoBO userInfo = findDailyUserInfo(dto.getUid(), dailyUserInfoHashKey);

        if (userInfo.getCurrWaterBalance() < dto.getWateredCount()) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "Water droplets is not enough"));
        }
        userInfo.decreaseCurrWaterBalance(dto.getWateredCount());
        List<RewardInfoData> rewardInfoList = new ArrayList<>(24);

        // 枣椰树浇水成长逻辑
        checkAndGetWateringRewards(dto, userInfo, rewardInfoList, currActor, configData, 6);
        //判定是否进每日榜(1期不做)
//        dealDailyCompleteDatePalmRank(dto, userInfo, dateHelper, currTime);


        if (!ObjectUtils.isEmpty(rewardInfoList)) {
            // 奖励发放
            giveOutRewardService.giveEventReward(dto.getUid(), rewardInfoList, dto.getEventType(), "1");
        }
        // 浇水消耗 上报数数
        reportWaterToThinkData(dto.getUid(), dto.getUid(), currTime, currActor.getChannel(), configData, dto.getWateredCount(), 2, "4");
        // 需要处理邀请任务
        dealInviteTask(dto, dailyUserInfoHashKey, currActor.getChannel(), configData);
        baseHashSaveRedis.saveToRedis(dailyUserInfoHashKey, userInfo.getUid(), userInfo, Duration.ofDays(14));
        // 浇水次数奖励
        dealWateringTimesRewards(dto, currActor, currTime);
        return rewardInfoList.stream()
                .map(BaseAwardVO::new)
                .collect(Collectors.toList());
    }

    private void dealWateringTimesRewards(WateringDTO dto, ActorData currActor, long currTime) {
        List<RewardInfoData> rewards = baseOneLimitRewardRedis.checkAndGetRewards(dto.getUid(), dto.getEventType(), WATERING_REWARD_CONFIG, 1);
        if (ObjectUtils.isEmpty(rewards)) {
            return;
        }
        List<RewardInfoData> finalRewards = fillRewards(rewards, dto.getEventType());
        giveOutRewardService.giveEventReward(dto.getUid(), finalRewards, dto.getEventType(), "2");
        // 官方消息 浇水累计次数奖励
        String notice = "Successfully water 5 times, Purple supercar*15day,get more rewards>>";
        long time = currTime + MathUtils.randomSplitInt(0, 50);
        officialNoticeService.sendOfficialNotice(dto.getUid(), "", notice, EVENT_IMG, genUrl(dto.getEventType()),
                currActor.getChannel(), (int) time, dto.getEventType());
    }

    private void dealInviteTask(WateringDTO dto, String dailyUserInfoHashKey, String channel, AppConfigActivityData configData) {
        if (!StringUtils.hasLength(dto.getInviterUid()) || dto.getInviterUid().equals(dto.getUid())) {
            return;
        }
        DailyUserInfoBO inviterUserInfo = findDailyUserInfo(dto.getInviterUid(), dailyUserInfoHashKey);
        InviteUserInfoBO inviteUserInfoBO = inviterUserInfo.getInviteUsers().stream()
                .filter(Objects::nonNull)
                .filter(inviterUserInfoBO -> inviterUserInfoBO.getUid().equals(dto.getUid()))
                .filter(inviterUserInfoBO -> !inviterUserInfoBO.getWatered())
                .findFirst()
                .orElse(null);
        if (inviteUserInfoBO == null) {
            return;
        }
        inviteUserInfoBO.setWatered(true);
        if (inviterUserInfo.getDailyInviteWaterCount() < DAILY_INVITE_USER_LIMIT * DAILY_INVITE_REWARD) {//每天邀请人数限制
            inviterUserInfo.increaseDailyInviteWaterCount(DAILY_INVITE_REWARD);
            // 官方消息(给邀请人发)
            int time = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
            String notice = "Congratulations on getting 10g\uD83D\uDCA7Got drop can get start your plant journey>>";
            officialNoticeService.sendOfficialNotice(inviterUserInfo.getUid(), "", notice, EVENT_IMG,
                    genUrl(dto.getEventType()), channel, time, dto.getEventType());
            //水滴榜统计
            String waterRankZsetKey = waterRankZsetKey(dto.getEventType());
            baseZSetRedis.increaseToZSet(waterRankZsetKey, inviterUserInfo.getUid(), DAILY_INVITE_REWARD, Duration.ofDays(30));
            // 上报数数 邀请浇水成功
            reportWaterToThinkData(inviterUserInfo.getUid(), dto.getUid(), DateHelper.getCurrTime(), channel, configData,
                    DAILY_INVITE_REWARD, 1, "2");
        }
        inviterUserInfo.getInviteUsers().set(inviteUserInfoBO.getIndex(), inviteUserInfoBO);
        baseHashSaveRedis.saveToRedis(dailyUserInfoHashKey, inviterUserInfo.getUid(), inviterUserInfo, Duration.ofDays(14));
    }

    private void checkAndGetWateringRewards(WateringDTO dto, DailyUserInfoBO userInfo, List<RewardInfoData> rewardInfoList, ActorData currActor, AppConfigActivityData configData, int limit) {
        if (limit < 1) {
            return;
        }
        limit--;
        RewardTaskConfig levelConfig = findCurrLevelConfig(userInfo);
        if (levelConfig == null || userInfo.getCurrWateredCount() + dto.getWateredCount() < levelConfig.getLimit()) {
            userInfo.increaseCurrWateredCount(dto.getWateredCount());
            return;
        }
        dto.decreaseWateredCount(levelConfig.getLimit() - userInfo.getCurrWateredCount());
        userInfo.increaseDatePalmStatus();
        userInfo.setCurrWateredCount(0);
        List<RewardInfoData> rewards = fillRewards(levelConfig.getRewards(), dto.getEventType());
        rewardInfoList.addAll(rewards);
        //合成椰树走马灯数据保存
        dealLastRecord(dto, userInfo, currActor, levelConfig);
        //椰树升级 上报数数
        reportDatePalmUpgradeToThinkData(dto, currActor, configData, levelConfig);
        //椰树升级官方通知
        int time = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        String notice = LEVEL_UPGRADE_NOTICE_FORMAT.replace("#level", levelConfig.getCheckParams().toString());
        officialNoticeService.sendOfficialNotice(dto.getUid(), "", notice, EVENT_IMG, genUrl(dto.getEventType()),
                currActor.getChannel(), time, dto.getEventType());
        if (dto.getWateredCount() <= 0) {
            return;
        }
        checkAndGetWateringRewards(dto, userInfo, rewardInfoList, currActor, configData, limit);
    }

    private void reportDatePalmUpgradeToThinkData(WateringDTO dto, ActorData currActor, AppConfigActivityData configData, RewardTaskConfig levelConfig) {
        ActivityParticipationEvent logData = new ActivityParticipationEvent();
        logData.setUid(dto.getUid());
        logData.setCtime(DateHelper.getCurrTime());
        logData.setChannel(currActor.getChannel());
        logData.setEventCode(configData.getActivityCode());
        logData.setActivityName(configData.getName());
        logData.setActivityStage(levelConfig.getCheckParams().toString());
        logData.setCostActivityTicket(0);
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private void dealLastRecord(WateringDTO dto, DailyUserInfoBO userInfo, ActorData currActor, RewardTaskConfig levelConfig) {
        LastRecordVO record = new LastRecordVO().setUid(userInfo.getUid())
                .setName(currActor.getName())
                .setHead(moderatorService.dealRankHeadModeration(currActor))
                .setStatus(levelConfig.getCheckParams());
        activityCommonRedis.addCommonListRecord(dto.getEventType().toString(), JSON.toJSONString(record));
    }

//    public List<RankRowVO> dailyDatePalmRank(DailyDatePalmRankDTO dto) {
//        dto.checkParams();
//        String dailyCompleteDatePalmRankZsetKey = dailyCompleteDatePalmRankZsetKey(dto.getEventCode(), dto.getDate());
//        List<CountVO> dataList = baseZSetRedis.getRange(dailyCompleteDatePalmRankZsetKey, 0, -1);
//        return fillRankRowList(dataList);
//    }

    public boolean invite(InviteDTO dto) {
        dto.checkParams();
        if (dto.getIndex() < 0 || dto.getIndex() > 4) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "The index is out of range"));
        }
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        checkLimit(currTime, configData, currActor);
        DateHelper dateHelper = DateHelper.genDateHelper(configData.getChannel());
        String dailyUserInfoHashKey = dailyUserInfoHashKey(dto.getEventType(), dateHelper.getToday());
        DailyUserInfoBO userInfo = findDailyUserInfo(dto.getUid(), dailyUserInfoHashKey);

        List<InviteUserInfoBO> inviteUsers = userInfo.getInviteUsers();
        if (inviteUsers.stream()
                .filter(Objects::nonNull)
                .anyMatch(inviteUser -> inviteUser.getUid().equals(dto.getInvitedUid()))) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "The user has already been invited today"));
        }

        InviteUserInfoBO userBO = inviteUsers.get(dto.getIndex());
        if (userBO != null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "There are already users on the invitation slot"));
        }

        ActorData inviteActor = actorMgr.getActorData(dto.getInvitedUid());
        if (inviteActor == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "The invited user does not exist"));
        }
        if (!ObjectUtils.isEmpty(configData.getUserCountryGroupStr()) && !configData.getUserCountryGroupStr().contains(currActor.getCountryCode())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "The country of the invited user will not join in this event"));
        }
        String head = moderatorService.dealRankHeadModeration(inviteActor);

        inviteUsers.set(dto.getIndex(), new InviteUserInfoBO().setIndex(dto.getIndex()).setUid(dto.getInvitedUid()).setHead(head).setWatered(false));
        userInfo.setInviteUsers(inviteUsers);
        baseHashSaveRedis.saveToRedis(dailyUserInfoHashKey, userInfo.getUid(), userInfo, Duration.ofDays(14));
        return true;
    }

    public boolean inviteDel(InviteDTO dto) {
        dto.checkParams();
        if (dto.getIndex() < 0 || dto.getIndex() > 4) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "The index is out of range"));
        }
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        checkLimit(currTime, configData, currActor);
        DateHelper dateHelper = DateHelper.genDateHelper(configData.getChannel());

        String dailyUserInfoHashKey = dailyUserInfoHashKey(dto.getEventType(), dateHelper.getToday());
        DailyUserInfoBO userInfo = findDailyUserInfo(dto.getUid(), dailyUserInfoHashKey);

        List<InviteUserInfoBO> inviteUsers = userInfo.getInviteUsers();
        if (inviteUsers.stream()
                .filter(Objects::nonNull)
                .noneMatch(inviteUser -> inviteUser.getUid().equals(dto.getInvitedUid()))) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "The invited user is not on the invitation list"));
        }

        InviteUserInfoBO invitedUserInfo = findInvitedUser(dto, inviteUsers);
        if (invitedUserInfo.getWatered()) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "The reward for the invited user has been issued and cannot be deleted"));
        }

        inviteUsers.set(invitedUserInfo.getIndex(), null);
        userInfo.setInviteUsers(inviteUsers);
        baseHashSaveRedis.saveToRedis(dailyUserInfoHashKey, userInfo.getUid(), userInfo, Duration.ofDays(14));
        return true;
    }

    public ModelRankVO<RankRowVO> waterRank(CommonDTO dto) {
        dto.checkParams();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        String waterRankZsetKey = waterRankZsetKey(dto.getEventType());
        List<RankRowVO> rankList = fillRankList(waterRankZsetKey);
        RankRowVO self = fillSelfData(currActor, waterRankZsetKey);
        return new ModelRankVO<>(self, rankList);
    }

    public List<LastRecordVO> last30Records(CommonDTO dto) {
        dto.checkParams();
        List<String> recordStrList = activityCommonRedis.getCommonListRecord(dto.getEventType().toString());
        if (ObjectUtils.isEmpty(recordStrList)) {
            return Collections.emptyList();
        }
        return recordStrList.stream()
                .map(json -> JSON.parseObject(json, LastRecordVO.class))
                .collect(Collectors.toList());
    }

    public void sendGiftSuccessAction(SendGiftSuccessMsgData mqData, int eventCode) {
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventCode);
        checkLimit(currTime, configData, currActor);
        if (mqData.fetchRealCost() <= 0) {
            return;
        }
        if (mqData.getIsLuck() > 0) {
            return;
        }
        //计算获得水滴数
        double water = datePalmGiftDailyCostDailyTaskRedis.getTicket(mqData.getUid(), mqData.getCost());
        if (water <= 0) {
            return;
        }
        waterReward(mqData.getUid(), eventCode, configData, (int) water, 2);
        // 飘屏
        roomFloatingImService.sendRoomFloatingIm(currActor, (int) water, WATER_ICON, "water", ", To plant trees", eventCode, genUrl(eventCode));
        // 数数上报 送礼获得水滴
        reportWaterToThinkData(mqData.getUid(), mqData.getUid(), currTime, currActor.getChannel(), configData, (int) water, 1, "3");
        // 水滴榜统计
        String waterRankZsetKey = waterRankZsetKey(eventCode);
        baseZSetRedis.increaseToZSet(waterRankZsetKey, mqData.getUid(), water, Duration.ofDays(30));
    }

    public void enterRoomMqAction(EnterRoomMsgData mqData, int eventCode) {
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(eventCode);
        checkLimit(currTime, configData, currActor);

        datePalmEnterRoomTaskLimitRedis.canGetAwardAfterAction(mqData.getUid(), eventCode);
        boolean canGetAward = datePalmEnterRoomTaskLimitRedis.checkCanGetAward(mqData.getUid(), eventCode);
        if (!canGetAward) {
            return;
        }
        waterReward(mqData.getUid(), eventCode, configData, DAILY_ENTER_ROOM_REWARD, 1);

        datePalmEnterRoomTaskLimitRedis.giveAwardAfterAction(mqData.getUid(), eventCode);
        // 飘屏
        roomFloatingImService.sendRoomFloatingIm(currActor, DAILY_ENTER_ROOM_REWARD, WATER_ICON, "water", ", to get rewards", eventCode, genUrl(eventCode));
        // 数数上报 进房获得水滴
        reportWaterToThinkData(mqData.getUid(), mqData.getUid(), currTime, currActor.getChannel(),
                configData, DAILY_ENTER_ROOM_REWARD, 1, "1");
        //水滴榜统计
        String waterRankZsetKey = waterRankZsetKey(eventCode);
        baseZSetRedis.increaseToZSet(waterRankZsetKey, mqData.getUid(), DAILY_ENTER_ROOM_REWARD, Duration.ofDays(30));
    }

    private RankRowVO fillSelfData(ActorData currActor, String waterRankZsetKey) {
        RankRowVO self = new RankRowVO();
        ActorInfo actorInfo = new ActorInfo(currActor);
        String head = moderatorService.dealRankHeadModeration(currActor);
        actorInfo.setHead(head);
        self.setActorInfo(actorInfo);
        Long rank = baseZSetRedis.getRank(waterRankZsetKey, currActor.getUid());
        if (rank != 0) {
            self.setRankNum(rank.toString());
            CountVO selfData = baseZSetRedis.getOne(waterRankZsetKey, currActor.getUid());
            self.setScore(selfData.getCount());
        }
        return self;
    }

    private List<RankRowVO> fillRankList(String waterRankZsetKey) {
        List<CountVO> dataList = baseZSetRedis.getRange(waterRankZsetKey, 1, 10);
        return fillRankRowList(dataList);
    }

    private List<RankRowVO> fillRankRowList(List<CountVO> dataList) {
        List<RankRowVO> rankList;
        if (!dataList.isEmpty()) {
            rankList = IntStream.range(0, dataList.size())
                    .mapToObj(index -> this.fillWaterRankRowVO(index + 1, dataList.get(index)))
                    .collect(Collectors.toList());
        } else {
            rankList = new ArrayList<>(0);
        }
        return rankList;
    }

    private RankRowVO fillWaterRankRowVO(Integer rankNum, CountVO data) {
        RankRowVO vo = new RankRowVO();
        vo.setRankNum(rankNum.toString());
        vo.setScore(data.getCount());
        ActorData rankActor = actorMgr.getActorData(data.getUid());
        ActorInfo actorInfo;
        if (rankActor != null) {
            actorInfo = new ActorInfo(rankActor);
            String head = moderatorService.dealRankHeadModeration(rankActor);
            actorInfo.setHead(head);
        } else {
            actorInfo = new ActorInfo();
        }
        vo.setActorInfo(actorInfo);
        return vo;
    }

    private void waterReward(String uid, int eventCode, AppConfigActivityData configData, int increase, int increaseType) {
        DateHelper dateHelper = DateHelper.genDateHelper(configData.getChannel());
        String date = dateHelper.getToday();

        String dailyUserInfoKey = dailyUserInfoHashKey(eventCode, date);
        DailyUserInfoBO userInfoBO = findDailyUserInfo(uid, dailyUserInfoKey);

        // 发水滴
        switch (increaseType) {
            case 1:
                userInfoBO.increaseDailyEnterRoomWaterCount(increase);
                break;
            case 2:
                userInfoBO.increaseDailySendGiftWaterCount(increase);
                break;
            default:
                return;

        }
        baseHashSaveRedis.saveToRedis(dailyUserInfoKey, userInfoBO.getUid(), userInfoBO, Duration.ofDays(14));
    }

    private void checkLimit(Long currTime, AppConfigActivityData configData, ActorData currActor) {
        if (configData.getStartTime() > currTime || configData.getEndTime() < currTime) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "not int event time"));
        }
        if (!configData.getChannel().equals(currActor.getChannel())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "your app not join this event"));
        }
        if (!ObjectUtils.isEmpty(configData.getUserCountryGroupStr()) && !configData.getUserCountryGroupStr().contains(currActor.getCountryCode())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "your country not join this event"));
        }
    }

    private DailyUserInfoBO findDailyUserInfo(String uid, String dailyUserInfoKey) {
        DailyUserInfoBO userInfoBO = baseHashSaveRedis.getDataByRedis(dailyUserInfoKey, uid, DailyUserInfoBO.class);
        if (userInfoBO == null) {
            userInfoBO = new DailyUserInfoBO();
            userInfoBO.setUid(uid);
        }
        return userInfoBO;
    }


    private void reportWaterToThinkData(String uid, String fromUid, Long time, String channel, AppConfigActivityData configData, int change, int changeAction, String fromType) {
        ActivitySpecialItemsEvent logData = new ActivitySpecialItemsEvent();
        logData.setUid(uid);
        logData.setCtime(time);
        logData.setChannel(channel);
        logData.setEventCode(configData.getActivityCode());
        logData.setActivityName(configData.getName());
        logData.setItemId("1");//水滴
        logData.setChangeAction(changeAction);
        logData.setFromType(fromType);
        logData.setFromTypeDesc("");
        logData.setFromUid(fromUid);
        ActorData fromActor = actorMgr.getActorData(fromUid);
        if (fromActor != null) {
            logData.setFromRid(fromActor.getRid());
        }
        logData.setItemCount(change);
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }
}
