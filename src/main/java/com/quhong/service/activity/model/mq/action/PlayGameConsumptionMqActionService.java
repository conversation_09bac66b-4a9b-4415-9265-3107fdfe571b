package com.quhong.service.activity.model.mq.action;

import com.quhong.common.enums.HttpCode;
import com.quhong.constant.ActType;
import com.quhong.constant.activity.EventGroupConstant;
import com.quhong.constant.activity.unit.EventUnitType;
import com.quhong.core.annotation.ActivityRankType;
import com.quhong.core.constant.money.MoneyUpdateSignConstant;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.MoneyDetailData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.app.config.activity.model.EventModeInfo;
import com.quhong.dao.datas.app.config.activity.model.ModeRankInfo;
import com.quhong.dao.datas.db.ActivityCountActorInfoData;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.exceptions.WebException;
import com.quhong.players.ActorMgr;
import com.quhong.service.activity.model.mq.action.common.SaveCountActorInfoService;
import com.quhong.service.event.model.EventUserGroupService;
import com.quhong.service.event.unit.UnitEventRankService;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 游戏消费（未指定游戏id代表所有）
 * <AUTHOR>
 * @since 2024/1/25 16:46
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PlayGameConsumptionMqActionService {
    private static final int RANK_TYPE = ActivityRankType.PLAY_GAME_CONSUMPTION;

    private static final Set<Integer> ACT_TYPE_SET = new HashSet<Integer>(){{
        add((int) ActType.HKYS_GAME_GOLD);
        add(ActType.PLAY_SLOT_TYPE);
        add((int) ActType.SMASH_EGG_GOLD);
    }};

    private final AppConfigActivityDao appConfigActivityDao;

    private final ActorMgr actorMgr;


    private final EventUserGroupService eventUserGroupService;

    private final SaveCountActorInfoService saveCountActorInfoService;

    private final UnitEventRankService unitEventRankService;

    public void mqAction(MoneyDetailData mqData) {
        checkParams(mqData);
        long currTime = mqData.getMtime();
        if (!ACT_TYPE_SET.contains(mqData.getActType())) {
            return;
        }
        if (MoneyUpdateSignConstant.UPDATE_REDUCE != mqData.getSingleAction()) {
            return;
        }

        List<AppConfigActivityData> configList = appConfigActivityDao.getListByGroupAndValid(EventGroupConstant.MODEL_EVENT, 1);
        if (ObjectUtils.isEmpty(configList)) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUserid());
        configList.stream()
                .filter(config -> currTime >= config.getStartTime() && currTime < config.getEndTime())
                .filter(config -> config.getRankType().contains(RANK_TYPE))
                .filter(config -> checkGameEventCode(config, mqData.getSegCode(), mqData.getActType()))
                .filter(config -> eventUserGroupService.checkUserGroup(currActor, config))
                .map(config -> fillSaveBO(mqData, config, currActor, currTime))
                .forEach(saveCountActorInfoService::lockSaveToDb);
    }

    private boolean checkGameEventCode(AppConfigActivityData config, Integer segCode, Integer actType) {
        EventModeInfo eventModeInfo = config.getEventModeInfo();
        if (eventModeInfo == null) {
            return true;
        }
        ModeRankInfo modeRankInfo = eventModeInfo.getModeRankInfo();
        if (modeRankInfo == null) {
            return true;
        }
        Set<Integer> eventCodeSet = modeRankInfo.getDataIdSet();
        if (ObjectUtils.isEmpty(eventCodeSet)) {
            return true;
        }
        if (eventCodeSet.contains(segCode)) {
            return true;
        }
        if (actType == ActType.SMASH_EGG_GOLD) {
            if (eventCodeSet.contains(ActivityTypeEnum.SMASH_EGG.getCode())) {
                return true;
            }
        }
        if (actType == ActType.PLAY_SLOT_TYPE) {
            if (eventCodeSet.contains(ActivityTypeEnum.LUCKY_NUMBER.getCode())) {
                return true;
            }
        }
        return false;
    }

    private static ActivityCountActorInfoData fillSaveBO(MoneyDetailData mqData, AppConfigActivityData config, ActorData currActor, long currTime) {
        ActivityCountActorInfoData bo = ActivityCountActorInfoData.InitFactory.initQueryOneBO(mqData.getUserid(),
                config.getActivityCode(),
                StringUtils.collectionToCommaDelimitedString(config.getRankType()));
        bo.setRid(currActor.getRid());
        bo.setCount(Long.valueOf(mqData.getSingleChange()));
        bo.setLastCountTime(currTime);
        return bo;
    }

    private void checkParams(MoneyDetailData mqData) {
        if (StringUtils.isEmpty(mqData.getUserid())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, uid is empty"));
        }
        if (mqData.getMtime() == null || mqData.getMtime() < 1) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, mtime is empty"));
        }
        if (mqData.getSingleAction() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, action is empty"));
        }
        if (mqData.getSingleChange() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, changed is empty"));
        }
        if (mqData.getActType() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, actType is empty"));
        }
        if (mqData.getCurrencyCode() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, currencyCode is empty"));
        }
    }

    public void unitMqAction(MoneyDetailData mqData) {
        checkParams(mqData);
        long currTime = mqData.getMtime();
        if (!ACT_TYPE_SET.contains(mqData.getActType())) {
            return;
        }
        if (MoneyUpdateSignConstant.UPDATE_REDUCE != mqData.getSingleAction()) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUserid());
        List<AppConfigActivityData> configDatas = unitEventRankService.checkLimitAndGetConfigs(currActor, currTime, RANK_TYPE);
        if (ObjectUtils.isEmpty(configDatas)) {
            return;
        }
        configDatas.stream()
                .filter(config -> checkGameEventCode(config, mqData.getSegCode(), mqData.getActType()))
                .forEach(config -> dealRankUnitRankScore(mqData, config));
    }

    private void dealRankUnitRankScore(MoneyDetailData mqData, AppConfigActivityData config) {
        config.getUnits().stream()
                .filter(unit -> unit.getUnitType() == EventUnitType.RANK)
                .filter(unit -> unit.getRankUnit().getRankType() == RANK_TYPE)
                .forEach(rankUnit -> unitEventRankService.increaseRankScore(config.getActivityCode(), rankUnit.getUnitId(), mqData.getUserid(), Long.valueOf(mqData.getSingleChange())));
    }
}
