package com.quhong.service.activity.model.mq.action;

import com.quhong.common.enums.HttpCode;
import com.quhong.constant.activity.EventGroupConstant;
import com.quhong.constant.activity.unit.EventUnitType;
import com.quhong.core.annotation.ActivityRankType;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.app.config.activity.model.EventModeInfo;
import com.quhong.dao.datas.app.config.activity.model.ModeRankInfo;
import com.quhong.dao.datas.db.ActivityCountActorInfoData;
import com.quhong.exceptions.WebException;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.service.activity.model.mq.action.common.SaveCountActorInfoService;
import com.quhong.service.event.model.EventUserGroupService;
import com.quhong.service.event.unit.UnitEventRankService;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * 送礼价值榜统计
 *
 * <AUTHOR>
 * @since 2024/1/25 16:13
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SendGiftPriceMqActionService {

    private static final int RANK_TYPE = ActivityRankType.SEND_GIFT_PRICE;

    private final AppConfigActivityDao appConfigActivityDao;

    private final ActorMgr actorMgr;

    private final EventUserGroupService eventUserGroupService;

    private final SaveCountActorInfoService saveCountActorInfoService;

    private final UnitEventRankService unitEventRankService;

    public void mqAction(SendGiftSuccessMsgData mqData) {
        checkParams(mqData);
        long currTime = mqData.getTime();
        List<AppConfigActivityData> configList = appConfigActivityDao.getListByGroupAndValid(EventGroupConstant.MODEL_EVENT, 1);
        if (ObjectUtils.isEmpty(configList)) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        configList.stream()
                .filter(config -> currTime >= config.getStartTime() && currTime < config.getEndTime())
                .filter(config -> config.getRankType().contains(RANK_TYPE))
                .filter(config -> this.checkGiftId(mqData, config))
                .filter(config -> eventUserGroupService.checkUserGroup(currActor, config))
                .map(config -> fillSaveBO(mqData, config, currActor, currTime))
                .forEach(saveCountActorInfoService::lockSaveToDb);
    }

    public void unitMqAction(SendGiftSuccessMsgData mqData) {
        checkParams(mqData);
        long currTime = mqData.getTime();
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        List<AppConfigActivityData> configDatas = unitEventRankService.checkLimitAndGetConfigs(currActor, currTime, RANK_TYPE);
        if (ObjectUtils.isEmpty(configDatas)) {
            return;
        }
        configDatas.stream()
                .filter(config -> this.checkGiftId(mqData, config))
                .forEach(config -> dealRankUnitRankScore(mqData, config));
    }

    private void dealRankUnitRankScore(SendGiftSuccessMsgData mqData, AppConfigActivityData config) {
        config.getUnits().stream()
                .filter(unit -> unit.getUnitType() == EventUnitType.RANK)
                .filter(unit -> unit.getRankUnit().getRankType() == RANK_TYPE)
                .forEach(rankUnit -> unitEventRankService.increaseRankScore(config.getActivityCode(), rankUnit.getUnitId(), mqData.getUid(), mqData.fetchRealCost()));
    }

    private static ActivityCountActorInfoData fillSaveBO(SendGiftSuccessMsgData mqData, AppConfigActivityData config, ActorData currActor, long currTime) {
        ActivityCountActorInfoData bo = ActivityCountActorInfoData.InitFactory.initQueryOneBO(mqData.getUid(),
                config.getActivityCode(),
                StringUtils.collectionToCommaDelimitedString(config.getRankType()));
        bo.setRid(currActor.getRid());
        bo.setCount(mqData.getCost());
        bo.setLastCountTime(currTime);
        return bo;
    }

    private boolean checkGiftId(SendGiftSuccessMsgData mqData, AppConfigActivityData config) {
        EventModeInfo eventModeInfo = config.getEventModeInfo();
        if (eventModeInfo == null) {
            return true;
        }
        ModeRankInfo modeRankInfo = eventModeInfo.getModeRankInfo();
        return modeRankInfo == null
                || ObjectUtils.isEmpty(modeRankInfo.getDataIdSet())
                || modeRankInfo.getDataIdSet().contains(mqData.getGiftId().intValue());
    }

    private void checkParams(SendGiftSuccessMsgData mqData) {
        if (StringUtils.isEmpty(mqData.getUid())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, uid is empty"));
        }
        if (mqData.getTime() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, time is empty"));
        }
        if (mqData.getGiftId() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, giftId is empty"));
        }
        if (mqData.getCost() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, cost is empty"));
        }
    }
}
