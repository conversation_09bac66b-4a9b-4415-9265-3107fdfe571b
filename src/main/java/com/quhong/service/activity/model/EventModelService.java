package com.quhong.service.activity.model;

import com.quhong.cache.ActorInfoCache;
import com.quhong.common.enums.HttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.ActivityCountActorInfoDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.app.config.activity.model.EventModeInfo;
import com.quhong.dao.datas.app.config.activity.model.ModeRankInfo;
import com.quhong.dao.datas.db.ActivityCountActorInfoData;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.vo.activity.model.PageInitVO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.data.vo.model.rank.row.independence.day.RankVO;
import com.quhong.exceptions.WebException;
import com.quhong.players.ActorMgr;
import com.quhong.service.event.model.EventUserGroupService;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2024/1/29 14:59
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EventModelService {

    private static final int DEFAULT_TOP = 10;

    private final AppConfigActivityDao appConfigActivityDao;

    private final ActorMgr actorMgr;

    private final EventUserGroupService eventUserGroupService;

    private final ActivityCountActorInfoDao activityCountActorInfoDao;

    private final ActorInfoCache actorInfoCache;
    public PageInitVO initPage(CommonDTO dto) {
        dto.checkParams();
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        return new PageInitVO().setEventCode(configData.getActivityCode())
                .setStartTime(configData.getStartTime())
                .setEndTime(configData.getEndTime())
                .setPageInfo(configData.getPageInfo());
    }

    public ModelRankVO<RankRowVO> ranking(CommonDTO dto) {
        dto.checkParams();
        long currTime = DateHelper.getCurrTime();
        ActorData currActor = actorMgr.getCurrActorData(dto.getUid());
        AppConfigActivityData configData = appConfigActivityDao.getOneByEventCodeThrowWebException(dto.getEventType());
        if (currTime < configData.getStartTime()) {
            throw new WebException(dto, HttpCode.EVENT_NOT_START);
        }
        if (!eventUserGroupService.checkUserGroup(currActor, configData)) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.NOT_JOIN, false, configData.getPageInfo().getNotJoinTip()));
        }

        int top = genTop(configData);
        String countGroup = StringUtils.collectionToCommaDelimitedString(configData.getRankType());

        RankRowVO self = new RankRowVO();
        self.setActorInfo(actorInfoCache.queryActorInfo(currActor));

        List<ActivityCountActorInfoData> dataList = activityCountActorInfoDao.getListByEventCodeAndCountGroupFromRedis(dto.getEventType(),
                countGroup, top);
        if (ObjectUtils.isEmpty(dataList)) {
            return new ModelRankVO<>(self, new ArrayList<>());
        }

        List<RankRowVO> rankList = IntStream.range(0, dataList.size())
                .mapToObj(index -> fillRankVO(index, dataList, self, currActor.getChannel()))
                .collect(Collectors.toList());

        if (self.getScore().equals(0L)) {
            fillSelfScore(dto, self, countGroup);
        }
        return new ModelRankVO<>(self, rankList);
    }

    private void fillSelfScore(CommonDTO dto, RankRowVO self, String countGroup) {
        ActivityCountActorInfoData data = activityCountActorInfoDao.getOneByEventCodeAndCountGroupAndUid(
                ActivityCountActorInfoData.InitFactory.initQueryOneBO(dto.getUid(), dto.getEventType(), countGroup));
        if (data != null) {
            self.setScore(data.getCount() < 0 ? 0 : data.getCount());
        }
    }

    private RankRowVO fillRankVO(int index, List<ActivityCountActorInfoData> dataList, RankRowVO self, String channel) {
        int rankNum = index + 1;
        ActivityCountActorInfoData rankData = dataList.get(index);
        checkSelfInTop(self, rankData, rankNum);
        return fillTopRankVO(rankNum, rankData, channel);
    }

    private static void checkSelfInTop(RankRowVO self, ActivityCountActorInfoData rankData, int rankNum) {
        if (rankData.getUid().equals(self.getActorInfo().getUid())) {
            self.setRankNum(Integer.toString(rankNum));
            self.setScore(rankData.getCount() < 0 ? 0 : rankData.getCount());
        }
    }

    private RankVO fillTopRankVO(int rankNum, ActivityCountActorInfoData rankData, String channel) {
        RankVO vo = new RankVO();
        vo.setRankNum(String.valueOf(rankNum));
        vo.setScore(rankData.getCount() < 0 ? 0 : rankData.getCount());
        vo.setActorInfo(actorInfoCache.queryActorInfo(rankData.getUid(), channel));
        return vo;
    }

    private static int genTop(AppConfigActivityData configData) {
        int top = DEFAULT_TOP;
        EventModeInfo eventModeInfo = configData.getEventModeInfo();
        if (eventModeInfo == null) {
            return top;
        }
        ModeRankInfo modeRankInfo = eventModeInfo.getModeRankInfo();
        if (modeRankInfo == null || modeRankInfo.getRankShowNum() == null) {
            return top;
        }
        top = modeRankInfo.getRankShowNum();
        return top;
    }
}
