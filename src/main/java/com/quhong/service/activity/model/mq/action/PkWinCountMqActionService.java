package com.quhong.service.activity.model.mq.action;

import com.quhong.common.enums.HttpCode;
import com.quhong.constant.activity.EventGroupConstant;
import com.quhong.constant.activity.unit.EventUnitType;
import com.quhong.core.annotation.ActivityRankType;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.db.ActivityCountActorInfoData;
import com.quhong.data.pk.PkInfoData;
import com.quhong.exceptions.WebException;
import com.quhong.players.ActorMgr;
import com.quhong.service.activity.model.mq.action.common.SaveCountActorInfoService;
import com.quhong.service.event.model.EventUserGroupService;
import com.quhong.service.event.unit.UnitEventRankService;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * 活动期间 pk胜利次数榜
 * <AUTHOR>
 * @since 2024/1/25 16:46
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PkWinCountMqActionService {
    private static final int RANK_TYPE = ActivityRankType.PK_WIN_COUNT;

    private final AppConfigActivityDao appConfigActivityDao;

    private final ActorMgr actorMgr;

    private final EventUserGroupService eventUserGroupService;

    private final SaveCountActorInfoService saveCountActorInfoService;

    private final UnitEventRankService unitEventRankService;

    public void mqAction(PkInfoData mqData) {
        checkParams(mqData);
        Long currTime = mqData.getPkConfrontEndTime();
        List<AppConfigActivityData> configList = appConfigActivityDao.getListByGroupAndValid(EventGroupConstant.MODEL_EVENT, 1);
        if (ObjectUtils.isEmpty(configList)) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getWinUid());
        configList.stream()
                .filter(config -> currTime >= config.getStartTime() && currTime < config.getEndTime())
                .filter(config -> config.getRankType().contains(RANK_TYPE))
                .filter(config -> eventUserGroupService.checkUserGroup(currActor, config))
                .map(config -> fillSaveBO(config, currActor, currTime))
                .forEach(saveCountActorInfoService::lockSaveToDb);
    }

    private ActivityCountActorInfoData fillSaveBO(AppConfigActivityData config, ActorData currActor, Long currTime) {
        ActivityCountActorInfoData bo = ActivityCountActorInfoData.InitFactory.initQueryOneBO(currActor.getUid(),
                config.getActivityCode(),
                StringUtils.collectionToCommaDelimitedString(config.getRankType()));
        bo.setRid(currActor.getRid());
        bo.setCount(1L);
        bo.setLastCountTime(currTime);
        return bo;
    }

    private void checkParams(PkInfoData mqData) {
        if (StringUtils.isEmpty(mqData.getWinUid())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, winUid is empty"));
        }
        if (mqData.getPkConfrontEndTime() <= 0) {
            mqData.setPkConfrontEndTime(DateHelper.getCurrTime());
        }
    }

    public void unitMqAction(PkInfoData mqData) {
        checkParams(mqData);
        Long currTime = mqData.getPkConfrontEndTime();
        ActorData currActor = actorMgr.getCurrActorData(mqData.getWinUid());
        List<AppConfigActivityData> configDatas = unitEventRankService.checkLimitAndGetConfigs(currActor, currTime, RANK_TYPE);
        if (ObjectUtils.isEmpty(configDatas)) {
            return;
        }
        configDatas.forEach(config -> dealRankUnitRankScore(mqData, config));
    }

    private void dealRankUnitRankScore(PkInfoData mqData, AppConfigActivityData config) {
        config.getUnits().stream()
                .filter(unit -> unit.getUnitType() == EventUnitType.RANK)
                .filter(unit -> unit.getRankUnit().getRankType() == RANK_TYPE)
                .forEach(rankUnit -> unitEventRankService.increaseRankScore(config.getActivityCode(), rankUnit.getUnitId(), mqData.getWinUid(), 1L));
    }
}
