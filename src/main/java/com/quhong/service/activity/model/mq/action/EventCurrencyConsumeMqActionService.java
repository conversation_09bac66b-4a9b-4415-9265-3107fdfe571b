package com.quhong.service.activity.model.mq.action;

import com.quhong.common.enums.HttpCode;
import com.quhong.constant.activity.unit.EventUnitType;
import com.quhong.core.annotation.ActivityRankType;
import com.quhong.core.constant.money.MoneyChangeActionConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.exceptions.WebException;
import com.quhong.mq.data.activity.EventUnitCurrencyMsgData;
import com.quhong.service.event.unit.UnitEventRankService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 活动货币消耗榜单统计
 * <AUTHOR>
 * @since 2025-06-11 16:50
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EventCurrencyConsumeMqActionService {

    private static final int RANK_TYPE = ActivityRankType.EVEN_CURRENCY_CONSUME_RANK;

    private final AppConfigActivityDao appConfigActivityDao;
    private final UnitEventRankService unitEventRankService;

    public void unitMqAction(EventUnitCurrencyMsgData mqData) {
        checkParams(mqData);
        if (mqData.getAction() != MoneyChangeActionConstant.ACTION_DEDUCT) {
            return;
        }
        long currTime = mqData.getTime();

        // 根据活动码获取活动配置
        AppConfigActivityData config = appConfigActivityDao.getOneByEventCodeThrowWebException(mqData.getEventCode());

        if (DateHelper.getCurrTime() - config.getEndTime() > 5) {
            return;
        }
        // 检查时间范围
        if (currTime < config.getStartTime() || currTime >= config.getEndTime()) {
            return;
        }


        // 查找对应的单元并更新分数
        config.getUnits().stream()
                .filter(unit -> unit.getUnitType() == EventUnitType.RANK)
                .filter(rankUnit -> rankUnit.getRankUnit() != null)
                .filter(unit -> unit.getRankUnit().getRankType() == RANK_TYPE)
                .filter(rankUnit -> rankUnit.getRankUnit().getCheckParamsSet() != null)
                .filter(rankUnit -> rankUnit.getRankUnit().getCheckParamsSet().contains(mqData.getUnitId().toString()))
                .forEach(rankUnit ->
                        unitEventRankService.increaseRankScore(config.getActivityCode(),
                                rankUnit.getUnitId(), mqData.getUid(), mqData.getChanged()));
    }


    private void checkParams(EventUnitCurrencyMsgData mqData) {
        if (mqData.getEventCode() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, eventCode is empty"));
        }
        if (mqData.getUnitId() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, unitId is empty"));
        }
        if (mqData.getAction() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, action is empty"));
        }
        if (mqData.getUid() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, uid is empty"));
        }
        if (mqData.getChanged() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, changed is empty"));
        }
        if (mqData.getTime() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, time is empty"));
        }
    }
}
