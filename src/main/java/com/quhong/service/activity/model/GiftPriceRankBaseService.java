package com.quhong.service.activity.model;

import com.quhong.core.constant.WarnName;
import com.quhong.dao.GiftRecordDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.data.bo.activity.QueryGiftRankBO;
import com.quhong.data.dto.RankingDTO;
import com.quhong.enums.RewardItemType;
import com.quhong.enums.TopTypeEnum;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.IntStream;

/**
 * 送礼物 价值排行榜
 *
 * <AUTHOR>
 * @since 2023/8/22 11:19
 */

public class GiftPriceRankBaseService extends ModelActivityService {
    private static final String GET_AWARD_NOTICE = "Congratulations on getting the Top #rankNum ranking reward,will be issued in 5days.";
    @Resource
    private GiftRecordDao giftRecordDao;

    @Override
    protected boolean canShowSelfData(ActorData currActor, Integer gender) {
        return true;
    }

    @Override
    public RankingDTO getSelfRankData(AppConfigActivityData configData, ActorData actorData, Integer gender) {
        QueryGiftRankBO bo = QueryGiftRankBO.fillQueryGiftPriceRankBO(configData, actorData, gender, TopTypeEnum.SELF);
        if (bo.getFromType().contains(-1)) {
            bo.setFromType(new HashSet<>());
        }
        List<RankingDTO> ranking = giftRecordDao.getActivityGiftPriceRanking(bo);
        if (ObjectUtils.isEmpty(ranking)) {
            return null;
        }
        return ranking.get(0);
    }

    @Override
    public List<RankingDTO> getRankingList(AppConfigActivityData configData, Integer gender, TopTypeEnum topTypeEnum) {
        QueryGiftRankBO bo = QueryGiftRankBO.fillQueryGiftPriceRankBO(configData, null, gender, topTypeEnum);
        if (bo.getFromType().contains(-1)) {
            bo.setFromType(new HashSet<>());
        }
        if (Objects.requireNonNull(topTypeEnum) == TopTypeEnum.AWARD) {
            return giftRecordDao.getTopGiftPriceRanking(bo);
        }
        return giftRecordDao.getActivityGiftPriceRanking(bo);
    }

    @Override
    public String getRankItemIcon(AppConfigActivityData configData, ActorData currActor, Integer gender) {
        String icon = RewardItemType.GOLD_ICON;
        if (gender == 2) {
            icon = RewardItemType.DIAMOND_ICON;
        }
        return cdnUtils.replaceUrlDomain(currActor.getChannel(), icon, 0);
    }

    @Override
    public void activityRankNotice(AppConfigActivityData configData, int rankType, String localDateStr) {
        exchangeTime(localDateStr, configData);
        List<RankingDTO> rankList = getRankingList(configData, rankType, TopTypeEnum.AWARD);
        String rankName = "送礼榜";
        if (rankType == 2) {
            rankName = "收礼榜";
        }
        //通知头部信息填充
        StringBuilder content = fillNoticeHead(configData, localDateStr, rankName);

        IntStream.rangeClosed(1, rankList.size()).forEachOrdered(rankNum -> fillContentAndSendNotice(localDateStr, rankNum, rankList, content));

        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, content.toString());
    }

    public void fillContentAndSendNotice(String localDateStr, int rankNum, List<RankingDTO> rankList, StringBuilder content) {
        RankingDTO currRankData = rankList.get(rankNum - 1);
        fillContentBody(rankNum, content, currRankData);
        sendOfficialNoticeToUser(localDateStr, rankNum, currRankData, GET_AWARD_NOTICE);
    }


}
