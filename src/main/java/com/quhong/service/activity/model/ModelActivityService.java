package com.quhong.service.activity.model;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.ActivityOfficeNoticeConstant;
import com.quhong.core.enums.GenderTypeEnum;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.ActorDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.OfficialNoticeDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.OfficialNotice;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.data.dto.ActorRankingDTO;
import com.quhong.data.dto.RankingDTO;
import com.quhong.data.dto.model.ModelActivityDTO;
import com.quhong.data.vo.BasePageInitVO;
import com.quhong.data.vo.ModelActivityRankVO;
import com.quhong.enums.TopTypeEnum;
import com.quhong.exceptions.WebException;
import com.quhong.executor.ActivityExecutorGroup;
import com.quhong.moderatoin.ModerationService;
import com.quhong.players.ActorMgr;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;


@Slf4j
@Service
public abstract class ModelActivityService implements IActivityService {

    /**
     * 活动 奖励通知 官方消息文案
     *
     * @see ActivityOfficeNoticeConstant 活动官方消息文案
     */
    private static final String OFFICE_NOTICE_TITLE = ActivityOfficeNoticeConstant.TITLE;
    @Resource
    protected ActorMgr actorMgr;
    @Resource
    protected MonitorSender monitorSender;
    @Resource
    protected CdnUtils cdnUtils;
    @Resource
    protected AppConfigActivityDao appConfigActivityDao;
    @Resource
    protected OfficialNoticeDao officialNoticeDao;
    @Resource
    protected ActorDao actorDao;
    @Resource
    protected ModerationService moderationService;
    @Resource
    private ActivityExecutorGroup activityExecutorGroup;

    private static int getShowTop(Integer gender, AppConfigActivityData configData) {
        int showTop = configData.getUserShowTop();
        if (GenderTypeEnum.HOST.getType().equals(gender)) {
            showTop = configData.getHostShowTop();
        }
        return showTop;
    }

    /**
     * 用户进入活动页面拉取
     *
     * @param dto 请求体
     * @return 活动时间 和 以及活动其他信息
     */
    public BasePageInitVO pageInit(ModelActivityDTO dto) {
        AppConfigActivityData configData = getAppConfigActivityData(dto.getActivityCode());
        return new BasePageInitVO(configData.getStartTime(), configData.getEndTime());
    }

    /**
     * @param dto 请求体
     * @return 活动榜单数据
     * @throws WebException 异常
     */
    public ModelActivityRankVO getRanking(ModelActivityDTO dto) throws WebException {
        ActorData currActor = getCurrActorData(dto.getUid());
        //获取活动配置
        AppConfigActivityData configData = getAppConfigActivityData(dto.getActivityCode());
        //替换时间(需要时重写此方法)
        exchangeTime(dto.getLocalDateStr(), configData);

        ModelActivityRankVO vo = activityExecutorGroup.get(configData.getRankType().stream().findFirst().orElse(0)).fillRanking(dto.getRankGender(), currActor, configData);
        log.debug("result to get ranking. vo={}", JSON.toJSONString(vo));
        return vo;
    }

    public void exchangeTime(String localDateStr, AppConfigActivityData configData) {
        if (!StringUtils.isEmpty(localDateStr)) {
            ZoneOffset offset = ZoneOffset.of(configData.getZoneOffset());
            LocalDate localDate = LocalDate.parse(localDateStr, DateTimeFormatter.ISO_LOCAL_DATE);
            long startSec = localDate.atTime(LocalTime.MIN).toEpochSecond(offset);
            long endSec = localDate.atTime(LocalTime.MAX).toEpochSecond(offset);
            if (startSec < configData.getStartTime()) {
                //不在活动范围时间报
                return;
            }
            configData.setStartTime(startSec);
            configData.setEndTime(endSec);
        }
    }

    private AppConfigActivityData getAppConfigActivityData(Integer activityCode) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByCodeAndValid(activityCode, 1);
        if (configData != null) {
            return configData;
        }
        log.error("activity config is not exists activity_code={}", activityCode);
        throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("param error,activity is not exists"));
    }

    @Override
    public ModelActivityRankVO fillRanking(Integer gender, ActorData currActor, AppConfigActivityData configData) {
        //榜单图标获取
        String rankItemIcon = getRankItemIcon(configData, currActor, gender);
        //活动周期标志
        String round = getRoundString(configData);
        //个人榜单数据获取
        ActorRankingDTO self = fillSelfRankData(configData, currActor, gender, rankItemIcon);
        if (configData.getStartTime() > DateHelper.getCurrTime()) {
            self.setCount("0");
            self.setUpNeed("0");
            return new ModelActivityRankVO(new ArrayList<>(), self);
        }
        //榜单数据获取
        List<RankingDTO> dataList = getRankingList(configData, gender, TopTypeEnum.DATA);
        return fillModelActivityRankVO(gender, currActor, round, self, dataList, rankItemIcon, configData);
    }

    private ActorData getCurrActorData(String uid) {
        ActorData actorData = actorMgr.getActorData(uid);
        if (actorData == null) {
            log.info("not find actor,uid={}", uid);
            throw new WebException(null, HttpCode.ACTOR_NOT_EXIST);
        }
        return actorData;
    }

    private ModelActivityRankVO fillModelActivityRankVO(Integer gender, ActorData currActor, String round, ActorRankingDTO self, List<RankingDTO> dataList, String rankItemIcon, AppConfigActivityData configData) {
        ModelActivityRankVO vo = new ModelActivityRankVO();
        List<ActorRankingDTO> ranking = new ArrayList<>();
        generateActivityRankVO(gender, currActor, self, dataList, ranking, rankItemIcon, configData);
        vo.setRanking(ranking);
        vo.setSelf(self);
        vo.setRound(round);
        return vo;
    }

    /**
     * 生成当前轮次标记
     *
     * @param configData 活动配置数据
     * @return 当前轮次标记
     */
    protected String getRoundString(AppConfigActivityData configData) {
        ZoneId zoneId = ZoneOffset.of(configData.getZoneOffset());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(configData.getDateFormat());

        String startDayStr = Instant.ofEpochSecond(configData.getStartTime())
                .atZone(zoneId)
                .format(formatter);
        String endDayStr = Instant.ofEpochSecond(configData.getEndTime())
                .atZone(zoneId)
                .format(formatter);

        return startDayStr + "~" + endDayStr;
    }

    @Override
    public List<RankingDTO> getRankingList(AppConfigActivityData configData, Integer gender, TopTypeEnum topTypeEnum) {
        return new ArrayList<>();
    }

    @Override
    public String getRankItemIcon(AppConfigActivityData configData, ActorData currActor, Integer gender) {
        return "";
    }

    protected ActorRankingDTO fillSelfRankData(AppConfigActivityData configData, ActorData currActor, Integer gender, String rankItemIcon) {
        ActorRankingDTO self;
        if (!canShowSelfData(currActor, gender)) {
            return null;
        }
        //查看者查看对应榜单
        RankingDTO selfDto = getSelfRankData(configData, currActor, gender);
        self = new ActorRankingDTO();
        self.setName(currActor.getName());
        self.setUid(currActor.getUid());
        String head = moderationService.dealRankHeadModeration(currActor);
        self.setHead(cdnUtils.replaceUrlDomain(currActor.getChannel(), head, 0));
        self.setHead(cdnUtils.replaceUrlCropOffset(self.getHead(), "?x-oss-process=image/resize,m_lfit,h_300,w_300"));
        self.setCount(selfDto != null ? String.valueOf(selfDto.getCount()) : "0");
        self.setIcon(rankItemIcon);
        return self;
    }

    protected boolean canShowSelfData(ActorData currActor, Integer gender) {
        return currActor.getGender().equals(gender);
    }

    @Override
    public RankingDTO getSelfRankData(AppConfigActivityData configData, ActorData currActor, Integer gender) {
        return null;
    }

    private void generateActivityRankVO(Integer gender, ActorData currActor, ActorRankingDTO self, List<RankingDTO> dataList, List<ActorRankingDTO> ranking, String rankItemIcon, AppConfigActivityData configData) {
        int showTop = getShowTop(gender, configData);
        if (CollectionUtils.isEmpty(dataList)) {
            if (self != null && StringUtils.isEmpty(self.getRankNum())) {
                self.setRankNum(showTop + "+");
            }
            return;
        }
        IntStream.rangeClosed(1, dataList.size()).forEach(rankNum -> {
            RankingDTO data = dataList.get(rankNum - 1);
            //检索自己是否在列表内,在列表内则填充具体排名不在则
            fillSelfRankNum(currActor, self, rankNum, data);
            if (rankNum <= showTop) {
                //排行榜信息拼装
                ActorRankingDTO dto = fillRankData(currActor, dataList, rankItemIcon, rankNum, data);
                ranking.add(dto);
            }
        });
        if (self != null && StringUtils.isEmpty(self.getRankNum())) {
            self.setRankNum(showTop + "+");
        }
    }

    private ActorRankingDTO fillRankData(ActorData currActor, List<RankingDTO> dataList, String rankItemIcon, int rankNum, RankingDTO data) {
        RankingDTO preData = null;
        if (rankNum > 1) {
            preData = dataList.get(rankNum - 2);
        }
        return fillRankUserData(currActor, rankNum, data, rankItemIcon, preData);
    }

    /**
     * 补充个人排行信息
     *
     * @param currActor 当前用户信息
     * @param self      个人排行信息
     * @param rankNum   排行数
     * @param data      排行数对应用户信息
     */
    private void fillSelfRankNum(ActorData currActor, ActorRankingDTO self, int rankNum, RankingDTO data) {
        if (self != null && data.getUid().equals(currActor.getUid())) {
            self.setRankNum(String.valueOf(rankNum));
        }
    }

    private ActorRankingDTO fillRankUserData(ActorData currActor, int rankNum, RankingDTO data, String rankItemIcon, RankingDTO preData) {
        ActorRankingDTO dto = new ActorRankingDTO();
        dto.setRankNum(String.valueOf(rankNum));
        dto.setName(data.getName());
        dto.setUid(data.getUid());
        String head = getRankActorHead(data);
        dto.setHead(cdnUtils.replaceUrlDomain(currActor.getChannel(), head, 0));
        dto.setHead(cdnUtils.replaceUrlCropOffset(dto.getHead(), "?x-oss-process=image/resize,m_lfit,h_300,w_300"));
        dto.setCount(String.valueOf(data.getCount()));
        dto.setIcon(rankItemIcon);
        if (preData != null) {
            long upNeed = preData.getCount() - data.getCount();
            dto.setUpNeed(String.valueOf(upNeed));
        }
        return dto;
    }

    private String getRankActorHead(RankingDTO data) {
        String head = data.getHead();
        ActorData rankActor = actorMgr.getActorData(data.getUid());
        if (rankActor != null) {
            head = moderationService.dealRankHeadModeration(rankActor);
        }
        return head;
    }

    @Override
    public void activityRankNotice(AppConfigActivityData configData, int rankType, String localDateStr) {
        activityExecutorGroup.get(configData.getRankType().stream().findFirst().orElse(0)).activityRankNotice(configData, rankType, localDateStr);
    }

    public StringBuilder fillNoticeHead(AppConfigActivityData configData, String localDateStr, String rankName) {
        String roundDate = getRoundString(configData);
        String rankTimeType = localDateStr != null ? "日榜" : "总榜";
        String title = "## " + rankTimeType + "(" + configData.getName() + ") \n";
        title += rankName + "\n";
        String body = "### 活动周期（UTC" + configData.getZoneOffset() + "):" + roundDate + "\n";
        String tableHead = "排名\tID\t\t\t统计数\n";
        return new StringBuilder(title + body + tableHead);
    }

    public void fillContentBody(int rankNum, StringBuilder content, RankingDTO currRankData) {
        content.append(rankNum).append("\t\t")
                .append(currRankData.getRid()).append("\t\t")
                .append(currRankData.getCount()).append("\n");
    }

    protected void sendOfficialNoticeToUser(String localDateStr, int rankNum, RankingDTO currRankData, String notice) {
        if (StringUtils.isEmpty(localDateStr)) {
            sendOfficialNoticeToUser(rankNum, currRankData.getUid(), notice, null, null, null);
        }
    }

    protected void sendOfficialNoticeToUser(int rankNum, String uid, String notice, String img, String url, String channel) {
        notice = notice.replace("#rankNum", String.valueOf(rankNum));
        sendOfficialNotice(uid, notice, img, url, channel);
    }


    public void sendOfficialNotice(String uid, String notice, String img, String url, String channel) {
        sendOfficialNotice(uid, notice, img, url, channel, DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50));
    }


    public void sendOfficialNotice(String uid, String notice, String img, String url, String channel, int currTime) {
        OfficialNotice officialNotice = new OfficialNotice();
        officialNotice.setUid(uid);
        officialNotice.setTitle(OFFICE_NOTICE_TITLE);
        officialNotice.setBody(notice);
        officialNotice.setMtime(DateHelper.UTC.formatDateInDay());
        officialNotice.setCtime(currTime);
        officialNotice.setValid(1);
        officialNotice.setUnread(1);
        officialNotice.setPicture(img);
        officialNotice.setUrl(url);
        officialNotice.setChannel(channel);

        try {
            officialNoticeDao.insert(officialNotice);
        } catch (Exception e) {
            log.error("error to send officialNotice,msg={}", e.getMessage(), e);
        }
    }


}
