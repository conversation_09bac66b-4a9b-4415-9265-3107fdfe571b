package com.quhong.service.activity.model.mq.action;

import com.quhong.common.enums.HttpCode;
import com.quhong.constant.activity.EventGroupConstant;
import com.quhong.constant.activity.unit.EventUnitType;
import com.quhong.core.annotation.ActivityRankType;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.FollowDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.db.ActivityCountActorInfoData;
import com.quhong.exceptions.WebException;
import com.quhong.mq.data.user.operation.FollowMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.service.activity.model.mq.action.common.SaveCountActorInfoService;
import com.quhong.service.event.model.EventUserGroupService;
import com.quhong.service.event.unit.UnitEventRankService;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * TODO 活动期间新增关注人数统计
 * <AUTHOR>
 * @since 2024/1/25 16:40
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NewFollowUserCountMqActionService {
    private static final int RANK_TYPE = ActivityRankType.NEW_FOLLOW_USER_COUNT;

    private final FollowDao followDao;

    private final AppConfigActivityDao appConfigActivityDao;

    private final ActorMgr actorMgr;

    private final EventUserGroupService eventUserGroupService;

    private final SaveCountActorInfoService saveCountActorInfoService;

    private final UnitEventRankService unitEventRankService;

    public void mqAction(FollowMsgData mqData) {
        checkParams(mqData);
        Long currTime = mqData.getTime();
        List<AppConfigActivityData> configList = appConfigActivityDao.getListByGroupAndValid(EventGroupConstant.MODEL_EVENT, 1);
        if (ObjectUtils.isEmpty(configList)) {
            return;
        }
        ActorData currActor = actorMgr.getCurrActorData(mqData.getAid());
        //直接获取粉丝数
        long count = followDao.getFollowedCountByUid(currActor.getUid());
        configList.stream()
                .filter(config -> currTime >= config.getStartTime() && currTime < config.getEndTime())
                .filter(config -> config.getRankType().contains(RANK_TYPE))
                .filter(config -> eventUserGroupService.checkUserGroup(currActor, config))
                .map(config -> fillSaveBO(config, currActor, currTime, count))
                .forEach(saveCountActorInfoService::lockSaveToDb);
    }

    public void unitMqAction(FollowMsgData mqData) {
        checkParams(mqData);
        Long currTime = mqData.getTime();
        ActorData currActor = actorMgr.getCurrActorData(mqData.getAid());
        //直接获取粉丝数
        long count = followDao.getFollowedCountByUid(currActor.getUid());
        List<AppConfigActivityData> configDatas = unitEventRankService.checkLimitAndGetConfigs(currActor, currTime, RANK_TYPE);
        if (ObjectUtils.isEmpty(configDatas)) {
            return;
        }
        configDatas.forEach(config -> dealRankUnitRankScore(mqData, config, count));
    }

    private void dealRankUnitRankScore(FollowMsgData mqData, AppConfigActivityData config, long count) {
        config.getUnits().stream()
                .filter(unit -> unit.getUnitType() == EventUnitType.RANK)
                .filter(unit -> unit.getRankUnit().getRankType() == RANK_TYPE)
                .forEach(rankUnit -> unitEventRankService.coverRankScore(config.getActivityCode(), rankUnit.getUnitId(), mqData.getAid(), count));
    }

    private ActivityCountActorInfoData fillSaveBO(AppConfigActivityData config, ActorData currActor, Long currTime, long count) {
        ActivityCountActorInfoData bo = ActivityCountActorInfoData.InitFactory.initQueryOneBO(currActor.getUid(),
                config.getActivityCode(),
                StringUtils.collectionToCommaDelimitedString(config.getRankType()));
        bo.setRid(currActor.getRid());
        bo.setCount(count);
        bo.setLastCountTime(currTime);
        bo.setCoverCount(true);
        return bo;
    }

    private void checkParams(FollowMsgData mqData) {
        if (StringUtils.isEmpty(mqData.getAid())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, aid is empty"));
        }
        if (mqData.getFollow() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, follow is empty"));
        }
        if (mqData.getTime() == null || mqData.getTime() <= 0) {
            mqData.setTime(DateHelper.getCurrTime());
        }
    }
}
