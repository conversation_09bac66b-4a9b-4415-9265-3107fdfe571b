package com.quhong.service.activity.model.mq.action;

import com.quhong.common.enums.HttpCode;
import com.quhong.constant.activity.EventGroupConstant;
import com.quhong.constant.activity.unit.EventUnitType;
import com.quhong.core.annotation.ActivityRankType;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.EnterRoomLogData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.db.ActivityCountActorInfoData;
import com.quhong.enums.RoomType;
import com.quhong.exceptions.WebException;
import com.quhong.players.ActorMgr;
import com.quhong.service.activity.model.mq.action.common.SaveCountActorInfoService;
import com.quhong.service.event.model.EventUserGroupService;
import com.quhong.service.event.unit.UnitEventRankService;
import com.quhong.utils.RoomUtils;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * 语聊房有效开播时长
 * <AUTHOR>
 * @since 2024/1/25 16:45
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ChatRoomEffectiveDurationMqActionService {
    private static final int RANK_TYPE = ActivityRankType.CHAT_ROOM_EFFECTIVE_DURATION;

    private final AppConfigActivityDao appConfigActivityDao;

    private final ActorMgr actorMgr;

    private final EventUserGroupService eventUserGroupService;

    private final SaveCountActorInfoService saveCountActorInfoService;

    private final UnitEventRankService unitEventRankService;


    public void unitMqAction(EnterRoomLogData mqData) {
        checkParams(mqData);
        if (mqData.getRoomType() != RoomType.CHAT) {
            return;
        }
        String roomOwnerId = RoomUtils.getRoomOwnerId(mqData.getRoomId());
        if (!mqData.getUid().equals(roomOwnerId)) {
            return;
        }
        long currTime = mqData.getExitTime();
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        List<AppConfigActivityData> configDatas = unitEventRankService.checkLimitAndGetConfigs(currActor, currTime, RANK_TYPE);
        if (ObjectUtils.isEmpty(configDatas)) {
            return;
        }
        configDatas.forEach(config -> dealRankUnitRankScore(mqData, config));
    }

    private void dealRankUnitRankScore(EnterRoomLogData mqData, AppConfigActivityData config) {
        config.getUnits().stream()
                .filter(unit -> unit.getUnitType() == EventUnitType.RANK)
                .filter(unit -> unit.getRankUnit().getRankType() == RANK_TYPE)
                .forEach(rankUnit -> unitEventRankService.increaseRankScore(config.getActivityCode(),rankUnit.getUnitId(), mqData.getUid(), mqData.getDuration()));
    }

    public void mqAction(EnterRoomLogData mqData) {
        checkParams(mqData);
        if (mqData.getRoomType() != RoomType.CHAT) {
            return;
        }
        String roomOwnerId = RoomUtils.getRoomOwnerId(mqData.getRoomId());
        if (!mqData.getUid().equals(roomOwnerId)) {
            return;
        }
        long currTime = mqData.getExitTime();
        ActorData currActor = actorMgr.getCurrActorData(mqData.getUid());
        List<AppConfigActivityData> configList = appConfigActivityDao.getListByGroupAndValid(EventGroupConstant.MODEL_EVENT, 1);
        if (ObjectUtils.isEmpty(configList)) {
            return;
        }
        configList.stream()
                .filter(config -> currTime >= config.getStartTime() && currTime < config.getEndTime())
                .filter(config -> config.getRankType().contains(RANK_TYPE))
                .filter(config -> eventUserGroupService.checkUserGroup(currActor, config))
                .map(config -> fillSaveBO(mqData, config, currActor, currTime))
                .forEach(saveCountActorInfoService::lockSaveToDb);

    }

    private ActivityCountActorInfoData fillSaveBO(EnterRoomLogData mqData, AppConfigActivityData config, ActorData currActor, long currTime) {
        ActivityCountActorInfoData bo = ActivityCountActorInfoData.InitFactory.initQueryOneBO(currActor.getUid(),
                config.getActivityCode(),
                StringUtils.collectionToCommaDelimitedString(config.getRankType()));
        bo.setRid(currActor.getRid());
        bo.setCount(Long.valueOf(mqData.getDuration()));
        bo.setLastCountTime(currTime);
        return bo;
    }

    private void checkParams(EnterRoomLogData mqData) {
        if (StringUtils.isEmpty(mqData.getUid())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, uid is empty"));
        }
        if (StringUtils.isEmpty(mqData.getRoomId())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, roomId is empty"));
        }
        if (mqData.getDuration() == null || mqData.getDuration() <= 0) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, duration is empty"));
        }
        if (mqData.getRoomType() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "param error, roomType is empty"));
        }
        if (mqData.getExitTime() == null || mqData.getExitTime() <= 0) {
            mqData.setExitTime(DateHelper.getCurrTime());
        }
    }
}
