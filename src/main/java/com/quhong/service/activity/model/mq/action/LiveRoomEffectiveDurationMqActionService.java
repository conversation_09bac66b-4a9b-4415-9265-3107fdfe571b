package com.quhong.service.activity.model.mq.action;

import com.quhong.common.enums.HttpCode;
import com.quhong.constant.activity.EventGroupConstant;
import com.quhong.constant.activity.unit.EventUnitType;
import com.quhong.core.annotation.ActivityRankType;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RoomChapterData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.db.ActivityCountActorInfoData;
import com.quhong.exceptions.WebException;
import com.quhong.players.ActorMgr;
import com.quhong.service.activity.model.mq.action.common.SaveCountActorInfoService;
import com.quhong.service.event.model.EventUserGroupService;
import com.quhong.service.event.unit.UnitEventRankService;
import com.quhong.utils.RoomUtils;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.time.Duration;
import java.util.List;

/**
 * 直播间有效开播时长
 * <AUTHOR>
 * @since 2024/1/25 16:45
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LiveRoomEffectiveDurationMqActionService {
    private static final int RANK_TYPE = ActivityRankType.LIVE_ROOM_EFFECTIVE_DURATION;
    private final AppConfigActivityDao appConfigActivityDao;

    private final ActorMgr actorMgr;

    private final EventUserGroupService eventUserGroupService;

    private final SaveCountActorInfoService saveCountActorInfoService;

    private final UnitEventRankService unitEventRankService;

    public void mqAction(RoomChapterData mqData) {
        checkParams(mqData);
        Long currTime = mqData.getMtime();
        String uid = RoomUtils.getRoomOwnerId(mqData.getRoomId());
        ActorData currActor = actorMgr.getCurrActorData(uid);
        if (mqData.getDuration() < Duration.ofHours(1).getSeconds()) {
            return;
        }
        long count = mqData.getDuration()/ Duration.ofHours(1).getSeconds();
        if (count <= 0) {
            return;
        }
        List<AppConfigActivityData> configList = appConfigActivityDao.getListByGroupAndValid(EventGroupConstant.MODEL_EVENT, 1);
        if (ObjectUtils.isEmpty(configList)) {
            return;
        }
        configList.stream()
                .filter(config -> currTime >= config.getStartTime() && currTime < config.getEndTime())
                .filter(config -> config.getRankType().contains(RANK_TYPE))
                .filter(config -> eventUserGroupService.checkUserGroup(currActor, config))
                .map(config -> fillSaveBO(config, currActor, currTime, count))
                .forEach(saveCountActorInfoService::lockSaveToDb);
    }

    private ActivityCountActorInfoData fillSaveBO(AppConfigActivityData config, ActorData currActor, Long currTime, long count) {
        ActivityCountActorInfoData bo = ActivityCountActorInfoData.InitFactory.initQueryOneBO(currActor.getUid(),
                config.getActivityCode(),
                StringUtils.collectionToCommaDelimitedString(config.getRankType()));
        bo.setRid(currActor.getRid());
        bo.setCount(count);
        bo.setLastCountTime(currTime);
        return bo;
    }

    private void checkParams(RoomChapterData mqData) {
        if (StringUtils.isEmpty(mqData.getRoomId())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR,false,"roomId is empty"));
        }
        if (mqData.getDuration() == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.PARAM_ERROR,false,"duration is empty"));
        }
        if (mqData.getMtime() == null) {
            mqData.setMtime(DateHelper.getCurrTime());
        }
    }

    public void unitMqAction(RoomChapterData mqData) {
        checkParams(mqData);
        Long currTime = mqData.getMtime();
        String uid = RoomUtils.getRoomOwnerId(mqData.getRoomId());
        ActorData currActor = actorMgr.getCurrActorData(uid);
        if (mqData.getDuration() < Duration.ofHours(1).getSeconds()) {
            return;
        }
        long count = mqData.getDuration()/ Duration.ofHours(1).getSeconds();
        if (count <= 0) {
            return;
        }
        List<AppConfigActivityData> configDatas = unitEventRankService.checkLimitAndGetConfigs(currActor, currTime, RANK_TYPE);
        if (ObjectUtils.isEmpty(configDatas)) {
            return;
        }
        configDatas.forEach(config -> dealRankUnitRankScore(mqData, config, count));
    }

    private void dealRankUnitRankScore(RoomChapterData mqData, AppConfigActivityData config, long count) {
        String uid = RoomUtils.getRoomOwnerId(mqData.getRoomId());
        config.getUnits().stream()
                .filter(unit -> unit.getUnitType() == EventUnitType.RANK)
                .filter(unit -> unit.getRankUnit().getRankType() == RANK_TYPE)
                .forEach(rankUnit -> unitEventRankService.increaseRankScore(config.getActivityCode(), rankUnit.getUnitId(), uid, count));
    }
}
