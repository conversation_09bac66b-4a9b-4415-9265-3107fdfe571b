package com.quhong.service.activity.model.mq.action.common;

import com.quhong.core.utils.DateHelper;
import com.quhong.dao.ActivityCountActorInfoDao;
import com.quhong.dao.datas.db.ActivityCountActorInfoData;
import com.quhong.utils.DistributeLockUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/1/26 18:14
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SaveCountActorInfoService {
    private static final String LOCK_KEY_PRE = "save_count_actor_info";

    private final ActivityCountActorInfoDao activityCountActorInfoDao;

    private final DistributeLockUtils distributeLockUtils;

    public void lockSaveToDb(ActivityCountActorInfoData bo) {
        String lockKey = String.format("%s:%d:%s:%s", LOCK_KEY_PRE, bo.getEventCode(), bo.getCountGroup(), bo.getUid());
        distributeLockUtils.distributeConsumer(bo, lockKey, this::saveToDb);
    }

    private void saveToDb(ActivityCountActorInfoData bo) {
        ActivityCountActorInfoData data = activityCountActorInfoDao.getOneByEventCodeAndCountGroupAndUid(bo);
        if (data == null) {
            long currTime = DateHelper.getCurrTime();
            bo.setId(null);
            bo.setValid(1);
            bo.setCtime(currTime);
            bo.setMtime(currTime);
            activityCountActorInfoDao.insertOneSelective(bo);
        } else {
            if (bo.isCoverCount()) {
                data.setCount(bo.getCount());
            }else {
                data.increaseCount(bo);
            }
            ActivityCountActorInfoData updateData = ActivityCountActorInfoData.InitFactory.initUpdateCountData(data);
            activityCountActorInfoDao.updateOneSelective(updateData);
        }
    }

    public void lockSaveDiffToDb(ActivityCountActorInfoData bo) {
        String lockKey = String.format("%s:%d:%s:%s", LOCK_KEY_PRE, bo.getEventCode(), bo.getCountGroup(), bo.getUid());
        distributeLockUtils.distributeConsumer(bo, lockKey, this::saveDiffToDb);
    }

    private void saveDiffToDb(ActivityCountActorInfoData bo) {
        ActivityCountActorInfoData data = activityCountActorInfoDao.getOneByEventCodeAndCountGroupAndUid(bo);
        if (data == null) {
            long currTime = DateHelper.getCurrTime();
            bo.setId(null);
            long initCount = 0;
            if (bo.getCount() > 0) {
                initCount = bo.getCount() - 1;
            }
            bo.setSonObj(initCount + "");
            bo.setCount(1L);
            bo.setValid(1);
            bo.setCtime(currTime);
            bo.setMtime(currTime);
            activityCountActorInfoDao.insertOneSelective(bo);
        } else {
            data.computeDiffCount(bo);
            ActivityCountActorInfoData updateData = ActivityCountActorInfoData.InitFactory.initUpdateCountData(data);
            activityCountActorInfoDao.updateOneSelective(updateData);
        }

    }


}
