package com.quhong.service.activity.week.star;

import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.GiftFromTypeConstant;
import com.quhong.core.constant.WarnName;
import com.quhong.core.date.DayTimeSupport;
import com.quhong.core.enums.ActorType;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.core.utils.SpringUtils;
import com.quhong.dao.*;
import com.quhong.dao.datas.*;
import com.quhong.dao.datas.db.ActivityWeekStarConfigData;
import com.quhong.dao.datas.log.ActivityWeekStarGiftLogData;
import com.quhong.data.vo.week.star.v1.InitPageVO;
import com.quhong.data.vo.week.star.v1.RankVO;
import com.quhong.data.vo.week.star.v1.previous.award.AwardVO;
import com.quhong.data.vo.week.star.v1.previous.gift.GiftVO;
import com.quhong.data.vo.week.star.v1.previous.top.TopListVO;
import com.quhong.data.vo.week.star.v1.previous.top.TopVO;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.RewardItemType;
import com.quhong.exceptions.WebException;
import com.quhong.moderatoin.ModerationService;
import com.quhong.players.ActorMgr;
import com.quhong.service.GiveOutRewardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 用户周星榜
 *
 * <AUTHOR>
 * @since 2023-03-14  11:08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WeekStarService {

    private static final int PRE_SHOW_TOP = 3;
    /**
     * 往期显示数量
     */
    private static final int PRE_SHOW_DURATION = 5;

    private static final int CURR_SHOW_TOP = 10;

    private final ActivityWeekStarGiftLogDao activityWeekStarGiftLogDao;

    private final ActorDao actorDao;

    private final ActorMgr actorMgr;

    private final ActivityWeekStarConfigDao activityWeekStarConfigDao;

    private final GiftListConfigDao giftListConfigDao;

    private final ResourceConfigDao resourceConfigDao;

    private final MonitorSender monitorSender;

    private final ActivityRewardConfigDao activityRewardConfigDao;

    private final RewardInfoDao rewardInfoDao;

    private final GiveOutRewardService giveOutRewardService;

    private final EventAwardRecordDao eventAwardRecordDao;

    private final CdnUtils cdnUtils;

    private final ModerationService moderationService;

    private final AppConfigActivityDao appConfigActivityDao;

    private static String getSelfTop(String uid, List<TopVO> rankList) {
        String top = null;
        List<String> topStrList = rankList.stream()
                .filter(data -> uid.equals(data.getUid()))
                .map(TopVO::getTop)
                .collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(topStrList)) {
            top = topStrList.get(0);
        }
        return top;
    }

    public static GiftVO getGiftVO(ActivityWeekStarConfigData data, GiftListConfigDao giftListConfigDao, ResourceConfigDao resourceConfigDao, String channel) {
        GiftListConfigData giftConfig = giftListConfigDao.getGiftListConfigByGiftId(data.getGiftId(), GiftFromTypeConstant.GIFT_FROM_ROOM, channel);
        if (giftConfig == null) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.OTHER_NOT_EXISTS).setMsg("gift id is not exist,giftId=" + data.getGiftId()));
        }
        ResourceConfigData resource = resourceConfigDao.getResourceByResourceId(giftConfig.getResourceId());
        if (resource == null) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.OTHER_NOT_EXISTS).setMsg("resource config is not exist,resourceId=" + giftConfig.getResourceId()));
        }
        return new GiftVO()
                .setGiftId(data.getGiftId().longValue())
                .setGiftName(giftConfig.getGiftName())
                .setGiftIcon(resource.getIconUrl())
                .setGiftScore(data.getScore().longValue());
    }

    private static void fillTopRewardMap(Map<Integer, List<RewardInfoData>> configIdRewardMap, Map<Integer, List<RewardInfoData>> topRewardMap, ActivityRewardConfigData activityRewardConfigData) {
        List<RewardInfoData> rewardInfoDataList = configIdRewardMap.get(activityRewardConfigData.getId());
        if (!ObjectUtils.isEmpty(rewardInfoDataList)) {
            topRewardMap.put(activityRewardConfigData.getTop(), rewardInfoDataList);
        }
    }

    private static void fillConfigIdRewardMap(Map<Integer, List<RewardInfoData>> configIdRewardMap, RewardInfoData reward) {
        List<RewardInfoData> topRewardList = configIdRewardMap.get(reward.getActivityId());
        if (ObjectUtils.isEmpty(topRewardList)) {
            topRewardList = new ArrayList<>(5);
        }
        topRewardList.add(reward);
        configIdRewardMap.put(reward.getActivityId(), topRewardList);
    }

    /**
     * 页面初始化
     *
     * @param activityType 活动类型
     * @return 页面初始化数据
     * @see com.quhong.enums.ActivityTypeEnum 活动类型枚举
     */
    @Cacheable(value = "str:week_star_page_init", key = "#activityType")
    public InitPageVO pageInit(Integer activityType) {
        InitPageVO vo = new InitPageVO();
        LocalDateTime currMonday = DayTimeSupport.getWeekDay(0, TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY), LocalTime.MIN);
        LocalDateTime currSunday = DayTimeSupport.getWeekDay(0, TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY), LocalTime.MAX);
        LocalDateTime lastMonday = DayTimeSupport.getWeekDay(1, TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY), LocalTime.MIN);
        LocalDateTime lastSunday = DayTimeSupport.getWeekDay(1, TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY), LocalTime.MAX);
        //获取当周开始和结束时间戳
        long currBeginTime = currMonday.toEpochSecond(ZoneOffset.UTC);
        long currEndTime = currSunday.toEpochSecond(ZoneOffset.UTC);
        vo.setCurrBeginTime(currBeginTime);
        vo.setCurrEndTime(currEndTime);

        String currMondayStr = currMonday.format(DateTimeFormatter.ISO_LOCAL_DATE);
        String currSundayStr = currSunday.format(DateTimeFormatter.ISO_LOCAL_DATE);
        String weekDuration = currMondayStr + "~" + currSundayStr;

        long lastBeginTime = lastMonday.toEpochSecond(ZoneOffset.UTC);
        long lastEndTime = lastSunday.toEpochSecond(ZoneOffset.UTC);

        //获取上周榜首数据
        String head = getLastTop1Head(activityType, vo, lastBeginTime, lastEndTime);
        vo.setLastTop1Head(head);

        //当前活动礼物定榜列表
        List<GiftVO> giftListVO = getGiftVOS(activityType, weekDuration);
        vo.setGiftList(giftListVO);

        // 获取奖励列表
        List<TopVO> awardList = getTopAwards(activityType);
        vo.setRewardList(awardList);

        // 获取往期榜单数据
        List<TopListVO> preTopList = getPreTopList(activityType);
        vo.setPreTopList(preTopList);

        return vo;
    }

    public RankVO getCurrRank(String uid, Integer activityType) {
        ActorData currActor = getCurrActor(uid);

        RankVO vo = new RankVO();
        LocalDateTime currMonday = DayTimeSupport.getWeekDay(0, TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY), LocalTime.MIN);
        LocalDateTime currSunday = DayTimeSupport.getWeekDay(0, TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY), LocalTime.MAX);
        //获取当周开始和结束时间戳
        long currBeginTime = currMonday.toEpochSecond(ZoneOffset.UTC);
        long currEndTime = currSunday.toEpochSecond(ZoneOffset.UTC);

        // 获取榜单前10数据
        List<TopVO> rankList = getCurrRankList(activityType, currBeginTime, currEndTime, currActor);
        vo.setRankList(rankList);

        // 获取个人数据
        TopVO self = getSelfTopData(uid, activityType, currActor, currBeginTime, currEndTime, rankList);
        vo.setSelf(self);
        return vo;
    }

    private ActorData getCurrActor(String uid) {
        ActorData actorData = actorMgr.getActorData(uid);
        if (actorData == null) {
            log.info("actor is not exist, uid={}", uid);
            throw new WebException(new HttpEnvData(), HttpCode.ACTOR_NOT_EXIST);
        }
        return actorData;
    }

    private List<TopVO> getCurrRankList(Integer activityType, long currBeginTime, long currEndTime, ActorData currActor) {
        List<TopVO> rankList = new ArrayList<>(CURR_SHOW_TOP);
        List<ActivityWeekStarGiftLogData> topDataList = activityWeekStarGiftLogDao.getGiftScoreTopByTopAndWeekDuration(activityType, CURR_SHOW_TOP, currBeginTime, currEndTime);
        fillTopVo(rankList, topDataList, currActor);
        return rankList;
    }

    private TopVO getSelfTopData(String uid, Integer activityType, ActorData currActor, long currBeginTime, long currEndTime, List<TopVO> rankList) {
        TopVO self = new TopVO();
        self.setTop(CURR_SHOW_TOP + "+");
        self.setUid(uid);
        String head = moderationService.dealRankHeadModeration(currActor);
        self.setHead(cdnUtils.replaceUrlDomain(currActor.getChannel(), head, 0));
        self.setName(currActor.getName());
        ActivityWeekStarGiftLogData logData = activityWeekStarGiftLogDao.getOneByUidAndWeekDuration(activityType, uid, currBeginTime, currEndTime);
        if (ObjectUtils.isEmpty(logData)) {
            self.setScore(0L);
            self.setCount(MathUtils.shortenNum(self.getScore()));
        } else {
            String top = getSelfTop(uid, rankList);
            if (StringUtils.hasLength(top)) {
                self.setTop(top);
            }
            self.setScore(logData.getScore());
            self.setCount(MathUtils.shortenNum(self.getScore()));
        }
        return self;
    }

    /**
     * 获取往期榜前3列表
     *
     * @param activityType 活动类型
     * @return 往期榜前3列表
     */
    private List<TopListVO> getPreTopList(Integer activityType) {
        List<TopListVO> preTopList = new ArrayList<>();
        IntStream.rangeClosed(1, PRE_SHOW_DURATION).forEachOrdered(i -> {
            TopListVO topListVO = new TopListVO();
            LocalDateTime monday = DayTimeSupport.getWeekDay(i, TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY), LocalTime.MIN);
            LocalDateTime sunday = DayTimeSupport.getWeekDay(i, TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY), LocalTime.MAX);
            List<TopVO> topList = fillTopVoList(activityType, monday, sunday);
            topListVO.setList(topList);

            String mondayStr = monday.format(DateTimeFormatter.ofPattern("MM/dd"));
            String sundayStr = sunday.format(DateTimeFormatter.ofPattern("MM/dd"));
            String time = mondayStr + "~" + sundayStr;
            topListVO.setTime(time);
            preTopList.add(topListVO);
        });
        return preTopList;
    }

    private List<TopVO> fillTopVoList(Integer activityType, LocalDateTime monday, LocalDateTime sunday) {
        long beginTime = monday.toEpochSecond(ZoneOffset.UTC);
        long endTime = sunday.toEpochSecond(ZoneOffset.UTC);
        List<TopVO> topList = new ArrayList<>();
        List<ActivityWeekStarGiftLogData> topDataList = activityWeekStarGiftLogDao.getGiftScoreTopByTopAndWeekDuration(activityType, PRE_SHOW_TOP, beginTime, endTime);
        fillTopVo(topList, topDataList, null);
        return topList;
    }

    private void fillTopVo(List<TopVO> topList, List<ActivityWeekStarGiftLogData> topDataList, ActorData currActor) {
        if (!ObjectUtils.isEmpty(topDataList)) {
            Map<String, ActorData> actorMap = getActorMap(topDataList);
            int bound = topDataList.size();
            IntStream.range(0, bound).forEachOrdered(j -> {
                TopVO topVO = fillTopVo(topDataList, actorMap, currActor, j);
                topList.add(topVO);
            });
        }
    }

    /**
     * 填充top数据
     *
     * @param topDataList top数据
     * @param actorMap    用户数据
     * @param currActor   当前用户
     * @param top         排行
     * @return 填充top显示数据
     */
    private TopVO fillTopVo(List<ActivityWeekStarGiftLogData> topDataList, Map<String, ActorData> actorMap, ActorData currActor, int top) {
        ActivityWeekStarGiftLogData data = topDataList.get(top);
        ActorData actorData = actorMap.get(data.getUid());
        TopVO topVO = new TopVO();
        topVO.setTop(String.valueOf(top + 1));
        topVO.setUid(data.getUid());
        topVO.setRid(data.getRid());
        if (actorData != null) {
            String head = moderationService.dealRankHeadModeration(actorData);
            if (currActor != null) {
                head = cdnUtils.replacePrivateHeadAndUrlDomain(currActor.getChannel(), head, 0, actorData.getUid());
            }
            topVO.setHead(head);
            topVO.setName(actorData.getName());
        }
        topVO.setScore(data.getScore());
        topVO.setCount(MathUtils.shortenNum(data.getScore()));
        return topVO;
    }

    private Map<String, ActorData> getActorMap(List<ActivityWeekStarGiftLogData> topDataList) {
        Set<String> uidSet = topDataList.stream().map(ActivityWeekStarGiftLogData::getUid).collect(Collectors.toSet());
        List<ActorData> actorDataList = actorDao.findActorByUidSet(uidSet);
        Map<String, ActorData> actorMap = new HashMap<>(3);
        if (!ObjectUtils.isEmpty(actorDataList)) {
            actorMap = actorDataList.stream()
                    .collect(Collectors.toMap(ActorData::getUid,
                            data -> data,
                            (a, b) -> b,
                            () -> new HashMap<>(3)));
        }
        return actorMap;
    }

    /**
     * 获取上周榜首数据
     *
     * @param activityType  活动类型
     * @param vo            页面初始化显示数据
     * @param lastBeginTime 上周开始时间戳
     * @param lastEndTime   上周结束时间戳
     * @return 上周榜首数据
     */
    private String getLastTop1Head(Integer activityType, InitPageVO vo, long lastBeginTime, long lastEndTime) {
        String head = "https://statics.kissu.mobi/default/head/default_head.png";
        List<ActivityWeekStarGiftLogData> top1DataList = activityWeekStarGiftLogDao.getGiftScoreTopByTopAndWeekDuration(activityType, 1, lastBeginTime, lastEndTime);
        if (!ObjectUtils.isEmpty(top1DataList)) {
            ActivityWeekStarGiftLogData data = top1DataList.get(0);
            vo.setLastTop1Uid(data.getUid());
            ActorData actorData = actorDao.getActorUidContainDel(data.getUid());
            if (!ObjectUtils.isEmpty(actorData)) {
                head = moderationService.dealRankHeadModeration(actorData);
            }
        }
        return head;
    }

    /**
     * 当前活动礼物定榜列表
     *
     * @param activityType 活动类型
     * @param weekDuration 周期
     * @return 活动礼物定榜列表
     */
    public List<GiftVO> getGiftVOS(Integer activityType, String weekDuration) {
        List<GiftVO> giftListVO = new ArrayList<>();
        List<ActivityWeekStarConfigData> giftConfigDataList = activityWeekStarConfigDao.getWeekStarGiftConfigByWeekDurationAndValid(activityType, weekDuration, 1);
        if (ObjectUtils.isEmpty(giftConfigDataList)) {
            // 告警
            log.info("week star current week gift not find config,weekDuration={},activityType={}", weekDuration, activityType);
            monitorSender.info("common", "week star current week gift not find config", weekDuration);
        } else {
            giftListVO = giftConfigDataList.stream()
                    .map(this::getGiftVO)
                    .sorted((a, b) -> Math.toIntExact(b.getGiftScore() - a.getGiftScore()))
                    .collect(Collectors.toList());
        }
        return giftListVO;
    }

    private List<TopVO> getTopAwards(Integer activityType) {
        List<ActivityRewardConfigData> actorRewardConfigList = activityRewardConfigDao.getConfigByTypeAndGenderAndValid(activityType, 1, 1);
        return actorRewardConfigList.stream()
                .map(this::getTopAwardVO)
                .collect(Collectors.toList());
    }

    /**
     * 获取当周奖励列表
     *
     * @param data 活动TOP配置
     * @return 当周奖励列表
     */
    public TopVO getTopAwardVO(ActivityRewardConfigData data) {
        TopVO topVO = new TopVO();
        topVO.setTop(data.getTop().toString());
        List<AwardVO> awardVoList = new ArrayList<>(8);
        List<RewardInfoData> rewardList = rewardInfoDao.queryListByActivityTypeAndActivityId(data.getActivityType(), data.getId());
        if (!ObjectUtils.isEmpty(rewardList)) {
            awardVoList = rewardList.stream()
                    .map(reward -> getAwardVO(topVO, reward))
                    .collect(Collectors.toList());
        }
        topVO.setAwardList(awardVoList);
        return topVO;
    }

    private AwardVO getAwardVO(TopVO topVO, RewardInfoData reward) {
        AwardVO awardVO = new AwardVO();
        awardVO.setTop(topVO.getTop());
        awardVO.setImg(reward.getIcon());
        awardVO.setName(reward.getName());
        awardVO.setCount(reward.getNums());
        awardVO.setUnit(getAwardUnit(reward.getType()));
        awardVO.setWeight(reward.getId());
        return awardVO;
    }

    private String getAwardUnit(Integer type) {
        String unit = "";
        switch (type) {
            case RewardItemType.GOLD:
                unit = " Coins";
                break;
            case RewardItemType.VIP_DAYS:
            case RewardItemType.SEAT_FRAME:
            case RewardItemType.BUBBLE_FRAME:
            case RewardItemType.ENTER_EFFECT:
            case RewardItemType.LORD_DAYS:
                unit = " Day";
                break;
            default:
                break;
        }
        return unit;
    }

    public GiftVO getGiftVO(ActivityWeekStarConfigData data) {
        return getGiftVO(data, giftListConfigDao, resourceConfigDao, "");
    }

    /**
     * 周奖励发放
     *
     * @param code 活动类型
     */
    public void weekReward(int code, boolean isTest) {
        LocalDateTime monday;
        LocalDateTime sunday;
        if (isTest) {
            monday = DayTimeSupport.getWeekDay(0, TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY), LocalTime.MIN);
            sunday = DayTimeSupport.getWeekDay(0, TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY), LocalTime.MAX);
        } else {
            monday = DayTimeSupport.getWeekDay(1, TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY), LocalTime.MIN);
            sunday = DayTimeSupport.getWeekDay(1, TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY), LocalTime.MAX);
        }

        String mondayStr = monday.format(DateTimeFormatter.ISO_LOCAL_DATE);
        String sundayStr = sunday.format(DateTimeFormatter.ISO_LOCAL_DATE);
        String weekDuration = mondayStr + "~" + sundayStr;

        long beginTime = monday.toEpochSecond(ZoneOffset.UTC);
        long endTime = sunday.toEpochSecond(ZoneOffset.UTC);


        List<ActivityWeekStarGiftLogData> dataList = getWeekStarGiftLogDataList(code, beginTime, endTime);
        Map<Integer, List<RewardInfoData>> topRewardMap = getTopRewardMap(code);


        int i = 0, dataListSize = dataList.size(), top = i + 1;
        while (i < dataListSize) {
            ActivityWeekStarGiftLogData data = dataList.get(i);
            List<RewardInfoData> awardList = topRewardMap.get(top);
            if (!ObjectUtils.isEmpty(awardList)) {
                EventAwardRecordData logData = saveLogData(code, weekDuration, top, data);
                giveOutRewardService.giveOutReward(data.getUid(), awardList, logData.getId());
            }
            i++;
            top = i + 1;
        }

        // 当周奖励判定与沿用
        checkAndFillCurrWeekDefaultConfig(code, weekDuration);
    }

    private void checkAndFillCurrWeekDefaultConfig(int code, String weekDuration) {
        LocalDateTime currMonday = DayTimeSupport.getWeekDay(0, TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY), LocalTime.MIN);
        LocalDateTime currSunday = DayTimeSupport.getWeekDay(0, TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY), LocalTime.MAX);
        String currMondayStr = currMonday.format(DateTimeFormatter.ISO_LOCAL_DATE);
        String currSundayStr = currSunday.format(DateTimeFormatter.ISO_LOCAL_DATE);
        long currBeginTime = currMonday.toEpochSecond(ZoneOffset.UTC);
        long currEndTime = currSunday.toEpochSecond(ZoneOffset.UTC);
        String currWeekDuration = currMondayStr + "~" + currSundayStr;
        long currTime = DateHelper.getCurrTime();
        List<ActivityWeekStarConfigData> configList = activityWeekStarConfigDao.getWeekStarGiftConfigByWeekDurationAndValid(code, currWeekDuration, 1);
        if (ObjectUtils.isEmpty(configList)) {
            List<ActivityWeekStarConfigData> lastConfigList = activityWeekStarConfigDao.getWeekStarGiftConfigByWeekDurationAndValid(code, weekDuration, 1);
            List<ActivityWeekStarConfigData> insertList = lastConfigList.stream().map(data -> fillDefaultData(data, currWeekDuration, currTime, currBeginTime, currEndTime))
                    .collect(Collectors.toList());
            activityWeekStarConfigDao.insertList(code, insertList);
            sendWeekStarFillDefaultConfigMonitor("旧周星活动", weekDuration, currWeekDuration);
        }
    }

    public void sendWeekStarFillDefaultConfigMonitor(String weekName, String weekDuration, String currWeekDuration) {
        String msg = "## 【" + weekName + "】当周礼物未配置自动沿用提醒\n"
                + "本周周期：" + currWeekDuration + "\n"
                + "上周周期：" + weekDuration + "\n"
                + "本周未更新周星礼物，已延用上周礼物@成琛@温柔";
        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, msg);
    }

    public ActivityWeekStarConfigData fillDefaultData(ActivityWeekStarConfigData data, String currWeekDuration, long currTime, long currBeginTime, long currEndTime) {
        ActivityWeekStarConfigData insertData = new ActivityWeekStarConfigData();
        SpringUtils.copyPropertiesIgnoreNull(data, insertData);
        insertData.setId(null);
        insertData.setWeekDuration(currWeekDuration);
        insertData.setBeginTime(currBeginTime);
        insertData.setEndTime(currEndTime);
        insertData.setCtime(currTime);
        insertData.setMtime(currTime);
        insertData.setOperator("auto use last");
        return insertData;
    }

    private EventAwardRecordData saveLogData(int code, String weekDuration, int top, ActivityWeekStarGiftLogData data) {
        EventAwardRecordData logData = new EventAwardRecordData();
        logData.setActivityType(code);
        logData.setRound(0);
        logData.setRoundDate(weekDuration);
        logData.setGender(ActorType.USER);
        logData.setTop(top);
        logData.setUid(data.getUid());
        logData.setRewardAmount(data.getScore().intValue());
        //只记录获奖人，不记录奖品信息
        eventAwardRecordDao.insertOne(logData);
        return logData;
    }

    private List<ActivityWeekStarGiftLogData> getWeekStarGiftLogDataList(int code, long beginTime, long endTime) {
        List<ActivityWeekStarGiftLogData> dataList = activityWeekStarGiftLogDao.getGiftScoreTopByTopAndWeekDuration(code, PRE_SHOW_TOP, beginTime, endTime);
        if (ObjectUtils.isEmpty(dataList)) {
            log.info("week star not found top data.begin={},end={}", beginTime, endTime);
            HttpCode httpCode = new HttpCode(HttpCode.SERVER_ERROR).setMsg("week star not found top data");
            throw new WebException(new HttpEnvData(), httpCode);
        }
        return dataList;
    }

    private Map<Integer, List<RewardInfoData>> getTopRewardMap(int code) {
        //数据库获取top获奖配置
        List<ActivityRewardConfigData> topConfigList = getActivityRewardConfigDataList(code);
        //获取配置id奖励映射
        Map<Integer, List<RewardInfoData>> configIdRewardMap = getConfigIdRewardMap(code, topConfigList);
        //生成top奖励映射
        Map<Integer, List<RewardInfoData>> topRewardMap = new HashMap<>(topConfigList.size());
        topConfigList.forEach(activityRewardConfigData -> fillTopRewardMap(configIdRewardMap, topRewardMap, activityRewardConfigData));
        return topRewardMap;
    }

    private List<ActivityRewardConfigData> getActivityRewardConfigDataList(int code) {
        List<ActivityRewardConfigData> topConfigList = activityRewardConfigDao.getConfigByTypeAndGenderAndValid(code, 1, 1);
        if (ObjectUtils.isEmpty(topConfigList)) {
            log.info("week star not found top config,activityCode={}", code);
            HttpCode httpCode = new HttpCode(HttpCode.SERVER_ERROR).setMsg("week star not found top config");
            throw new WebException(new HttpEnvData(), httpCode);
        }
        return topConfigList;
    }

    private Map<Integer, List<RewardInfoData>> getConfigIdRewardMap(int code, List<ActivityRewardConfigData> topConfigList) {
        Set<Integer> configIdSet = topConfigList.stream().map(ActivityRewardConfigData::getId).collect(Collectors.toSet());
        List<RewardInfoData> rewardList = getRewardInfoDataList(code, configIdSet);

        Map<Integer, List<RewardInfoData>> configIdRewardMap = new HashMap<>(3);
        rewardList.forEach(reward -> fillConfigIdRewardMap(configIdRewardMap, reward));
        return configIdRewardMap;
    }

    private List<RewardInfoData> getRewardInfoDataList(int code, Set<Integer> configIdSet) {
        List<RewardInfoData> rewardList = rewardInfoDao.queryListByActivityTypeAndActivityIdList(code, configIdSet);
        if (ObjectUtils.isEmpty(rewardList)) {
            log.info("week star not found reward list,activityCode={}", code);
            HttpCode httpCode = new HttpCode(HttpCode.SERVER_ERROR).setMsg("week star not found reward list");
            throw new WebException(new HttpEnvData(), httpCode);
        }
        return rewardList;
    }

    public void checkNextWeekConfig(ActivityTypeEnum typeEnum) {
        LocalDateTime nextMonday = DayTimeSupport.getWeekDay(-1, DayTimeSupport.currWeekMondayAdjuster, LocalTime.MIN);
        LocalDateTime nextSunday = DayTimeSupport.getWeekDay(-1, DayTimeSupport.currWeekSundayAdjuster, LocalTime.MAX);
        String nextMondayStr = nextMonday.format(DateTimeFormatter.ISO_LOCAL_DATE);
        String nextSundayStr = nextSunday.format(DateTimeFormatter.ISO_LOCAL_DATE);
        String currWeekDuration = nextMondayStr + "~" + nextSundayStr;
        List<ActivityWeekStarConfigData> configList = activityWeekStarConfigDao.getWeekStarGiftConfigByWeekDurationAndValid(typeEnum.getCode(), currWeekDuration, 1);
        if (!ObjectUtils.isEmpty(configList)) {
            return;
        }
        String msg = "## 【" + typeEnum.getDesc() + "】下周礼物未配置提醒\n"
                + "请在kissu运营平台进行配置@成琛@温柔";
        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, msg);
    }
}
