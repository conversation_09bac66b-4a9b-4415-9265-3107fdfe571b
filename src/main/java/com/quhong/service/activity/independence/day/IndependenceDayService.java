package com.quhong.service.activity.independence.day;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.ActivityOfficeNoticeConstant;
import com.quhong.core.constant.WarnName;
import com.quhong.core.enums.GenderTypeEnum;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.ActivityIndependenceActorInfoDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.OfficialNoticeDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.OfficialNotice;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.db.ActivityIndependenceActorInfoData;
import com.quhong.data.bo.activity.independence.day.QueryRankBO;
import com.quhong.data.config.RewardTaskConfig;
import com.quhong.data.config.independenceDay.PropagandaCopyRewardConfig;
import com.quhong.data.config.independenceDay.ScenicRewardConfig;
import com.quhong.data.config.independenceDay.TaskConfig;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.dto.independence.day.RankDTO;
import com.quhong.data.thData.ActivityExchangeRewardsEvent;
import com.quhong.data.thData.ActivityParticipationEvent;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.ActorInfo;
import com.quhong.data.vo.model.rank.row.independence.day.InitPageVO;
import com.quhong.data.vo.model.rank.row.independence.day.RankVO;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.event.diwali.v1.data.config.LuckyGiftPackageConfig;
import com.quhong.exceptions.WebException;
import com.quhong.moderatoin.ModerationService;
import com.quhong.mq.ActivityMQProduct;
import com.quhong.mq.enums.MQConstant;
import com.quhong.players.ActorMgr;
import com.quhong.redis.independence.day.DailyPlayYXSKGameTaskLimitRedis;
import com.quhong.redis.independence.day.EventPromotionRoomLimitRedis;
import com.quhong.redis.independence.day.IndependencePromotionRoomLimitRedis;
import com.quhong.redis.independence.day.RoomStayTaskLimitRedis;
import com.quhong.report.EventReport;
import com.quhong.service.GiveOutRewardService;
import com.quhong.service.ItemsChangeService;
import com.quhong.service.common.RewardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2023/7/31 11:48
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndependenceDayService {

    public static final String NOTICE_IMG = "https://statics.kissu.mobi/event/coins/v2/banner.png";
    private static final String COLUMN_TOTAL_TICKET = "total_ticket";
    private static final String COLUMN_GIFT_PRICE_SUM = "gift_price_sum";
    private static final String TEXT_FORMAT = "Congratulations to #name for completing last [#node] check-in and receiving #rewardName.  to see >>";
    private static final String STAR_REWARD_TEXT = "#name  Collected all the cards and get rewards in the [Happy Independence Day] event，to see>>";
    private static final String GET_AWARD_NOTICE = "Congratulations for getting top #rankNum in Happy Independence Day, the rewards will be distributed within 7 working days.";
    private static final String OFFICE_NOTICE_TITLE = ActivityOfficeNoticeConstant.TITLE;
    private final ActivityIndependenceActorInfoDao activityIndependenceActorInfoDao;
    private final DailyPlayYXSKGameTaskLimitRedis dailyPlayYXSKGameTaskLimitRedis;
    private final EventPromotionRoomLimitRedis eventPromotionRoomLimitRedis;
    private final RoomStayTaskLimitRedis roomStayTaskLimitRedis;
    private final AppConfigActivityDao appConfigActivityDao;
    private final ActorMgr actorMgr;
    private final GiveOutRewardService giveOutRewardService;
    private final ModerationService moderationService;
    private final CdnUtils cdnUtils;
    private final OfficialNoticeDao officialNoticeDao;
    private final MonitorSender monitorSender;
    private final IndependencePromotionRoomLimitRedis independencePromotionRoomLimitRedis;
    private final ActivityMQProduct activityMQProduct;
    private final ItemsChangeService itemsChangeService;
    private final EventReport eventReport;
    private final RewardService rewardService;

    /**
     * 校验限制
     *
     * @param currActor  当前用户
     * @param configData 活动配置
     */
    private static void checkLimit(ActorData currActor, AppConfigActivityData configData) {
        // 国家限制
        Set<String> countryCodeSet = getCountryCodeSet(currActor, configData);
        if (!ObjectUtils.isEmpty(countryCodeSet) && !countryCodeSet.contains(currActor.getCountryCode())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Your country cannot participate in this event"));
        }
        if (StringUtils.hasLength(configData.getChannel()) && !configData.getChannel().equals(currActor.getChannel())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Your app cannot participate in this event"));
        }
        // 活动时间限制
        long currTime = DateHelper.getCurrTime();
        if (currTime < configData.getStartTime() || currTime > configData.getEndTime()) {
            log.info("not in the event period");
            if (currTime < configData.getStartTime()) {
                throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Event not starting"));
            } else {
                throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.OTHER_NOT_EXISTS).setMsg("Event Ending"));
            }
        }
    }

    private static Set<String> getCountryCodeSet(ActorData actor, AppConfigActivityData activityData) {
        Set<String> countryCodeSet = activityData.getUserCountryGroupStr();
        if (GenderTypeEnum.HOST.getType().equals(actor.getGender())) {
            countryCodeSet = activityData.getHostCountryGroupStr();
        }
        return countryCodeSet;
    }

    /**
     * 页面初始化
     *
     * @param dto 请求体
     * @return 页面初始化数据
     */
    public InitPageVO initPage(CommonDTO dto) {
        List<ScenicRewardConfig> scenicRewardConfigs = ScenicRewardConfig.SCENIC_REWARD_CONFIGS;
        ActorData currActor = getActorData(dto.getUid());
        AppConfigActivityData configData = getAppConfigActivityData(dto.getEventType());
        checkLimit(currActor, configData);
        InitPageVO vo = new InitPageVO();
        vo.setStartTime(configData.getStartTime());
        vo.setEndTime(configData.getEndTime());
        ActivityIndependenceActorInfoData data = activityIndependenceActorInfoDao.getOneByUid(dto.getEventType(), dto.getUid());
        if (data == null) {
            return vo;
        }
        vo.copyFrom(currActor, configData, data, scenicRewardConfigs);
//        vo.setGameTicketLimit(dailyPlayYXSKGameTaskLimitRedis.getTimes(dto.getUid()));
//        vo.setRoomTicketLimit(roomStayTaskLimitRedis.getRoomStayTimes(dto.getUid()));

        //活动宣传奖励每日已领取数量
        int dailyDisseminateCount = independencePromotionRoomLimitRedis.computeReceiveCount(currActor.getUid());
        vo.setDailyDisseminateCount(dailyDisseminateCount);
//        vo.setSendCopyCount(eventPromotionRoomLimitRedis.getEventPromotionDistinctRoomIdCount(dto.getUid()));
//        PropagandaCopyRewardConfig propagandaCopyConfig = PropagandaCopyRewardConfig.PROPAGANDA_COPY_CONFIG;
//        if (vo.getCanGetSendCopyReward() == 0 && vo.getSendCopyCount() >= propagandaCopyConfig.getMinSendTimes()) {
//            vo.setCanGetSendCopyReward(1);
//        }
        return vo;
    }

    /**
     * 景点打卡
     *
     * @param dto 请求体
     * @return true成功 false失败
     */
    public boolean scenicCheckIn(CommonDTO dto) {
        List<ScenicRewardConfig> scenicRewardConfigs = ScenicRewardConfig.SCENIC_REWARD_CONFIGS;
        ActorData currActor = getActorData(dto.getUid());
        AppConfigActivityData configData = getAppConfigActivityData(dto.getEventType());
        checkLimit(currActor, configData);

        ActivityIndependenceActorInfoData data = activityIndependenceActorInfoDao.getOneByUid(dto.getEventType(), dto.getUid());
        if (data == null) {
            throw new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("not found curr actor event info"));
        }

        Integer currNode = data.getScenicNode();
        int nextNode = currNode + 1;
        if (nextNode > scenicRewardConfigs.size()) {
            throw new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("All scenic spots have checked in"));
        }

        ScenicRewardConfig scenicRewardConfig = scenicRewardConfigs.stream().filter(config -> config.getNode() == nextNode)
                .findFirst().orElse(null);
        if (scenicRewardConfig == null) {
            throw new WebException(dto, new HttpCode(HttpCode.OTHER_NOT_EXISTS).setMsg("not found scenicRewardConfig,node=" + nextNode));
        }

        if (scenicRewardConfig.getNeedTicket() > data.getTicket()) {
            throw new WebException(dto, new HttpCode(HttpCode.BALANCE_NOT_ENOUGH).setMsg("not enough tickets"));
        }

        data.scenicCheckIn(scenicRewardConfig.getNeedTicket(), scenicRewardConfig.getNode());
//        data.verifyAndGetCanGetPostcardReward(scenicRewardConfigs.size());

        ActivityIndependenceActorInfoData updateData = ActivityIndependenceActorInfoData.scenicCheckInUpdateDataFactory(data);
        activityIndependenceActorInfoDao.updateOneSelective(updateData);

        giveOutRewardService.giveOut(dto.getUid(), scenicRewardConfig.getRewards(), 0, configData.getName(), configData.getActType().byteValue());
        itemsChangeService.itemChangeReport(dto.getUid(), scenicRewardConfig.getRewards(), configData.getActivityCode(), DateHelper.getCurrTime(), "scenicCheckIn,node=" + scenicRewardConfig.getNode());
        scenicRewardConfig.getRewards().forEach(reward -> activityMQProduct.sendObjectMq(MQConstant.EVENT_COST_COUNT_ROUTE, reward));
        //上报数数
        reportScenicCheckInToThinkData(dto, currActor, configData, scenicRewardConfig);
        if (scenicRewardConfig.getNode() > 3) {//4-5-6 发全服广播
            String text = TEXT_FORMAT.replace("#node", scenicRewardConfig.getNodeName());
            rewardService.sendUniversalFullServiceNoticeMsg(dto.getUid(), scenicRewardConfig.getRewards().get(0), TaskConfig.EVENT_URL,
                    configData.getName(), text, true);
        }
        return true;
    }

    private void reportScenicCheckInToThinkData(CommonDTO dto, ActorData currActor, AppConfigActivityData configData, ScenicRewardConfig scenicRewardConfig) {
        ActivityParticipationEvent logData = new ActivityParticipationEvent();
        logData.setUid(dto.getUid());
        logData.setCtime(DateHelper.getCurrTime());
        logData.setChannel(currActor.getChannel());
        logData.setEventCode(configData.getActivityCode());
        logData.setActivityName(configData.getName());
        logData.setActivityStage(scenicRewardConfig.getNode().toString());
        logData.setCostActivityTicket(scenicRewardConfig.getNeedTicket());
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    /**
     * 独立之星大礼包兑换
     *
     * @param dto 请求体
     * @return true成功 false失败
     */
    public Integer sendIndependenceStarReward(CommonDTO dto) {
        ActorData currActor = getActorData(dto.getUid());
        AppConfigActivityData configData = getAppConfigActivityData(dto.getEventType());
        checkLimit(currActor, configData);

        ActivityIndependenceActorInfoData data = activityIndependenceActorInfoDao.getOneByUid(dto.getEventType(), dto.getUid());
        if (data == null) {
            throw new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("not found curr actor event info"));
        }
        int count = 0;
        List<RewardInfoData> rewards = LuckyGiftPackageConfig.CONFIG.getRewards();
        while (data.mergeLucky()) {
            count++;
            giveOutRewardService.giveOutReward(dto.getUid(), rewards);
            itemsChangeService.itemChangeReport(dto.getUid(), rewards, configData.getActivityCode(), DateHelper.getCurrTime(), "Independence Star Rewards");
            rewards.forEach(reward -> activityMQProduct.sendObjectMq(MQConstant.EVENT_COST_COUNT_ROUTE, reward));
            if (count >= 10) {
                break;
            }
        }

        if (count < 1) {
            throw new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("no enough letter"));
        }
        ActivityIndependenceActorInfoData updateData = ActivityIndependenceActorInfoData.updatePostcardJsonDataFactory(data);
        activityIndependenceActorInfoDao.updateOneSelective(updateData);
        //上报数数
        reportIndependenceStarRewardsToThinkData(dto, currActor, configData, count);
        //发送全服广播
        rewardService.sendUniversalFullServiceNoticeMsg(dto.getUid(), rewards.get(0), TaskConfig.EVENT_URL,
                configData.getName(), STAR_REWARD_TEXT, true);
        return count;
    }

    private void reportIndependenceStarRewardsToThinkData(CommonDTO dto, ActorData currActor, AppConfigActivityData configData, int count) {
        ActivityExchangeRewardsEvent logData = new ActivityExchangeRewardsEvent();
        logData.setUid(dto.getUid());
        logData.setCtime(DateHelper.getCurrTime());
        logData.setChannel(currActor.getChannel());
        logData.setEventCode(configData.getActivityCode());
        logData.setActivityName(configData.getName());
        logData.setRewardsName("Independence Star Rewards");
        logData.setExchangeTimes(count);
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    /**
     * 宣传奖励获取
     *
     * @param dto 请求体
     * @return true成功 false失败
     */
    @Deprecated
    public boolean promotionReward(CommonDTO dto) {
        PropagandaCopyRewardConfig propagandaCopyConfig = PropagandaCopyRewardConfig.PROPAGANDA_COPY_CONFIG;
        ActorData currActor = getActorData(dto.getUid());
        AppConfigActivityData configData = getAppConfigActivityData(dto.getEventType());
        checkLimit(currActor, configData);

        int copyToRoomCount = eventPromotionRoomLimitRedis.getEventPromotionDistinctRoomIdCount(dto.getUid());
        if (copyToRoomCount < propagandaCopyConfig.getMinSendTimes()) {
            throw new WebException(dto, new HttpCode(HttpCode.BALANCE_NOT_ENOUGH).setMsg("not enough copy to room times"));
        }

        checkAndUpdateDbInfo(dto, currActor, 3);

        giveOutRewardService.giveOut(dto.getUid(), Collections.singletonList(propagandaCopyConfig.getReward()), 0,
                configData.getName(), configData.getActType().byteValue());
        return true;
    }

    @Deprecated
    private void checkAndUpdateDbInfo(CommonDTO dto, ActorData currActor, int limit) {
        ActivityIndependenceActorInfoData data = activityIndependenceActorInfoDao.getOneByUid(dto.getEventType(), dto.getUid());
        if (data == null) {
            data = ActivityIndependenceActorInfoData.init(dto.getUid(),
                    currActor.getRid(),
                    dto.getEventType(),
                    currActor.getIsPayUser() > 0 ? -1 : 0,
                    0L);
            data.setCanGetSendCopyReward(-1);
            try {
                activityIndependenceActorInfoDao.insertOneSelective(data);
            } catch (Exception e) {
                // 递归
                limit--;
                checkAndUpdateDbInfo(dto, currActor, limit);
            }
        } else {
            if (data.getCanGetSendCopyReward() == -1) {
                throw new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Reward has been claimed"));
            }
            data.setCanGetSendCopyReward(-1);
            ActivityIndependenceActorInfoData updateData = ActivityIndependenceActorInfoData.updateCanGetSendCopyRewardDataFactory(data);
            activityIndependenceActorInfoDao.updateOneSelective(updateData);
        }
    }

    /**
     * 获取排行榜数据
     *
     * @param dto 请求体
     * @return 榜单数据
     */
    public ModelRankVO<RankVO> getRank(RankDTO dto) {
        ActorData currActor = getActorData(dto.getUid());
        AppConfigActivityData configData = getAppConfigActivityData(dto.getEventType());
        checkRankLimit(currActor, configData);

        String columnName = COLUMN_TOTAL_TICKET;
        Integer dataTop = configData.getUserDataTop();
        Integer showTop;
        if (dto.getRankType() == 2) {
            columnName = COLUMN_GIFT_PRICE_SUM;
            dataTop = configData.getHostDataTop();
            showTop = configData.getHostShowTop();
        } else {
            showTop = configData.getUserShowTop();
        }
        ModelRankVO<RankVO> vo = new ModelRankVO<>();
        RankVO self = new RankVO();
        vo.setSelf(self);

        QueryRankBO queryRankBO = QueryRankBO.builder().eventType(dto.getEventType()).columnName(columnName).limit(dataTop).build();
        List<ActivityIndependenceActorInfoData> dataList = activityIndependenceActorInfoDao.getOrderLimitListFromRedis(queryRankBO);
        if (ObjectUtils.isEmpty(dataList)) {
            return vo;
        }
        AtomicReference<RankVO> selfAtomic = new AtomicReference<>();
        List<RankVO> rankList = IntStream.range(0, dataList.size())
                .mapToObj(index -> fillRowVO(index + 1, dataList.get(index), currActor, dto, selfAtomic, showTop))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        self = fillSelfData(currActor, configData, selfAtomic, dto);

        vo.setSelf(self);
        vo.setRankList(rankList);
        return vo;
    }

    private RankVO fillSelfData(ActorData currActor, AppConfigActivityData configData, AtomicReference<RankVO> selfAtomic, RankDTO dto) {
        RankVO self = selfAtomic.get();
        if (self == null) {
            self = new RankVO();
            ActivityIndependenceActorInfoData info = activityIndependenceActorInfoDao.getOneByUid(configData.getActivityCode(), currActor.getUid());
            ActorInfo actorInfo = self.getActorInfo();
            fillActorInfo(currActor, currActor, actorInfo);
            if (info != null) {
                self.setScore(info.getTotalTicket());
                if (dto.getRankType() == 2) {
                    self.setScore(info.getGiftPriceSum());
                }
            }
        }
        return self;
    }

    private RankVO fillRowVO(int rankNum, ActivityIndependenceActorInfoData data, ActorData currActor, RankDTO dto, AtomicReference<RankVO> selfAtomic, int showTop) {
        if (rankNum > showTop && !currActor.getUid().equals(data.getUid())) {
            return null;
        }
        RankVO vo = new RankVO();
        vo.setRankNum(String.valueOf(rankNum));
        ActorInfo actorInfo = vo.getActorInfo();
        ActorData rankActor = actorMgr.getActorData(data.getUid());
        fillActorInfo(currActor, rankActor, actorInfo);
        vo.setScore(dto.getRankType() == 2 ? data.getGiftPriceSum() : data.getTotalTicket());
        if (currActor.getUid().equals(data.getUid())) {
            selfAtomic.set(vo);
        }
        return rankNum <= showTop ? vo : null;
    }

    private void fillActorInfo(ActorData currActor, ActorData rankActor, ActorInfo actorInfo) {
        if (rankActor != null) {
            actorInfo.setUid(rankActor.getUid());
            actorInfo.setRid(rankActor.getRid());
            actorInfo.setName(rankActor.getName());
            String head = rankActor.getHeadIcon();
            if (GenderTypeEnum.USER.getType().equals(rankActor.getGender())) {
                head = moderationService.dealRankHeadModeration(rankActor);
            }
            actorInfo.setHead(head);
        }
        actorInfo.setHead(cdnUtils.replaceUrlDomain(currActor.getChannel(), actorInfo.getHead(), 0));
    }

    private void checkRankLimit(ActorData currActor, AppConfigActivityData configData) {
        // 国家限制
        Set<String> countryCodeSet = getCountryCodeSet(currActor, configData);
        if (!ObjectUtils.isEmpty(countryCodeSet) && !countryCodeSet.contains(currActor.getCountryCode())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Your country cannot participate in this event"));
        }
        if (StringUtils.hasLength(configData.getChannel()) && !configData.getChannel().equals(currActor.getChannel())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Your app cannot participate in this event"));
        }
        // 活动时间限制
        long currTime = DateHelper.getCurrTime();
        if (currTime < configData.getStartTime()) {
            log.info("not in the event period");
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Event not starting"));
        }
    }

    private AppConfigActivityData getAppConfigActivityData(Integer eventType) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByCodeAndValid(eventType, 1);
        if (configData == null) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.OTHER_NOT_EXISTS));
        }
        return configData;
    }

    private ActorData getActorData(String uid) {
        ActorData currActor = actorMgr.getActorData(uid);
        if (currActor == null) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ACTOR_NOT_EXIST));
        }
        return currActor;
    }

    public void rankMonitor(int eventType, Integer rankType, List<RewardTaskConfig> rankRewards) {
        AppConfigActivityData configData = getAppConfigActivityData(eventType);
        long currTime = DateHelper.getCurrTime();
        //对于活动未开始 处理方式
        if (currTime < configData.getStartTime()) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("The activity hasn't started yet"));
        }

        int awardTop = configData.getUserAwardTop();
        String rankName = "门票榜";
        String column = COLUMN_TOTAL_TICKET;
        if (rankType == 2) {
            awardTop = configData.getHostAwardTop();
            rankName = "收礼榜";
            column = COLUMN_GIFT_PRICE_SUM;
        }
        QueryRankBO queryRankBO = QueryRankBO.builder().eventType(eventType).columnName(column).limit(awardTop).build();
        List<ActivityIndependenceActorInfoData> dataList = activityIndependenceActorInfoDao.getOrderLimitListFromRedis(queryRankBO);
        if (ObjectUtils.isEmpty(dataList)) {
//            log.info("no data--------");
            return;
        }

        // 微信群通知显示榜单
        String title = "## " + rankName + "(" + ActivityTypeEnum.getByCode(eventType).getName() + ") \n";
        String body = "### 活动周期（UTC" + configData.getZoneOffset() + "):" + getRoundString(configData) + "\n";
        String tableHead = "排名\tID\t\t\t\t\t数值\n";
        StringBuilder content = new StringBuilder(title + body + tableHead);

        for (int rankNum = 1; rankNum <= dataList.size(); rankNum++) {
            ActivityIndependenceActorInfoData currRankData = dataList.get(rankNum - 1);
            long count = currRankData.getTotalTicket();
            if (rankType == 2) {
                count = currRankData.getGiftPriceSum();
            }
            content.append(rankNum).append("\t\t").append(currRankData.getRid())
                    .append("\t\t\t").append(count)
                    .append("\n");

            int finalRankNum = rankNum;
            String finalRankName = rankName;
            rankRewards.stream()
                    .filter(rewardTaskConfig -> rewardTaskConfig.getCheckParams().equals(finalRankNum))
                    .findFirst()
                    .ifPresent(rewardTaskConfig -> giveRewardAndSendNotice(rewardTaskConfig, currRankData, finalRankNum, eventType, finalRankName));
        }

        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, content.toString());
    }

    private void giveRewardAndSendNotice(RewardTaskConfig rewardTaskConfig, ActivityIndependenceActorInfoData currRankData, int finalRankNum, int eventType, String finalRankName) {
        giveOutRewardService.giveOutReward(currRankData.getUid(), rewardTaskConfig.getRewards());
        itemsChangeService.itemChangeReport(currRankData.getUid(), rewardTaskConfig.getRewards(), eventType, DateHelper.getCurrTime(), "rank" + finalRankName + ",rankNum:" + finalRankNum);
        rewardTaskConfig.getRewards().forEach(reward -> activityMQProduct.sendObjectMq(MQConstant.EVENT_COST_COUNT_ROUTE, reward));
        //发送官方消息
        sendOfficialNotice(currRankData.getUid(), rewardTaskConfig.getNotice());
    }

    private void sendOfficialNotice(String uid, String notice) {
        OfficialNotice officialNotice = new OfficialNotice();
        try {
            officialNotice.setUid(uid);
            officialNotice.setTitle(OFFICE_NOTICE_TITLE);
            officialNotice.setBody(notice);
            officialNotice.setPicture(NOTICE_IMG);
            officialNotice.setUrl(TaskConfig.EVENT_URL);
            officialNotice.setMtime(DateHelper.UTC.formatDateInDay());
            officialNotice.setCtime(DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50));
            officialNotice.setValid(1);
            officialNotice.setUnread(1);
            officialNoticeDao.insert(officialNotice);
        } catch (Exception e) {
            log.error("sendOfficialNotice error", e);
        }
    }

    /**
     * 生成当前轮次标记
     *
     * @param configData 活动配置数据
     * @return 当前轮次标记
     */
    public String getRoundString(AppConfigActivityData configData) {
        ZoneId zoneId = ZoneOffset.of(configData.getZoneOffset());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(configData.getDateFormat());

        String startDayStr = Instant.ofEpochSecond(configData.getStartTime())
                .atZone(zoneId)
                .format(formatter);
        String endDayStr = Instant.ofEpochSecond(configData.getEndTime())
                .atZone(zoneId)
                .format(formatter);

        return startDayStr + "~" + endDayStr;
    }
}
