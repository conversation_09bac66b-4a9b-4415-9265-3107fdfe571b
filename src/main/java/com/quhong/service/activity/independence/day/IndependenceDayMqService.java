package com.quhong.service.activity.independence.day;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.ActType;
import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.constant.diwali.AwardPoolGroupConstant;
import com.quhong.core.constant.WarnName;
import com.quhong.core.constant.money.MoneyChangeActionConstant;
import com.quhong.core.enums.ActorType;
import com.quhong.core.enums.AppMsgType;
import com.quhong.core.enums.GenderTypeEnum;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.ActivityIndependenceActorInfoDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.AppConfigAwardDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.EnterRoomLogData;
import com.quhong.dao.datas.MoneyDetailData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.db.ActivityIndependenceActorInfoData;
import com.quhong.dao.datas.db.AppConfigAwardData;
import com.quhong.data.bo.common.award.pool.config.QueryBO;
import com.quhong.data.config.independenceDay.ScenicRewardConfig;
import com.quhong.data.config.independenceDay.TaskConfig;
import com.quhong.data.dto.InnerSendMsgDTO;
import com.quhong.data.dto.admin.QueryAppConfigAwardDTO;
import com.quhong.data.thData.ActivitySpecialItemsEvent;
import com.quhong.data.thData.ActivityTicketsEvent;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.CurrencyEnum;
import com.quhong.enums.RewardItemType;
import com.quhong.event.diwali.v1.data.config.LuckyGiftPackageConfig;
import com.quhong.exceptions.WebException;
import com.quhong.game.data.dto.yxsk.UpdateCoinDTO;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.mq.data.room.RoomChatMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.redis.base.currency.task.BaseEveryLimitCurrencyRedis;
import com.quhong.redis.independence.day.DailyPlayYXSKGameTaskLimitRedis;
import com.quhong.redis.independence.day.IndependencePromotionRoomLimitRedis;
import com.quhong.redis.independence.day.RoomStayTaskLimitRedis;
import com.quhong.redis.independence.day.TicketGetCountRedis;
import com.quhong.report.EventReport;
import com.quhong.service.ImApi;
import com.quhong.service.common.BaseAwardPool;
import com.quhong.service.common.RewardService;
import com.quhong.service.common.RoomFloatingImService;
import com.quhong.socket.msg.room.RoomFloatingScreenMsg;
import com.quhong.utils.DistributeLockUtils;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

/**
 * <AUTHOR>
 * @since 2023/7/31 17:39
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndependenceDayMqService {
    /**
     * pre + uid
     */
    private static final String TICKET_LOCK_KEY = "activity_independence_actor_info_key:";

    //    private static final ActivityTypeEnum ACTIVITY_TYPE_ENUM = ActivityTypeEnum.INDEPENDENCE_DAY_ACTIVITY;
    private static final ActivityTypeEnum ACTIVITY_TYPE_ENUM = ActivityTypeEnum.INDEPENDENCE_DAY_ACTIVITY_V2;
    /**
     * im消息文案
     */
    private static final String IM_TEXT = "Receive tickets  X#count for event attractions >>";

    /**
     * 门票名
     */
    private static final String TICKET_NAME = "tickets";

    private final ImApi imApi;

    private final DistributeLockUtils distributeLockUtils;

    private final ActivityIndependenceActorInfoDao activityIndependenceActorInfoDao;

    private final DailyPlayYXSKGameTaskLimitRedis dailyPlayYXSKGameTaskLimitRedis;


    private final RoomStayTaskLimitRedis roomStayTaskLimitRedis;

    private final TicketGetCountRedis ticketGetCountRedis;

    private final AppConfigActivityDao appConfigActivityDao;

    private final ActorMgr actorMgr;

    private final CdnUtils cdnUtils;

    private final MonitorSender monitorSender;

    private final IndependencePromotionRoomLimitRedis independencePromotionRoomLimitRedis;
    private final BaseAwardPool baseAwardPool;
    private final RoomFloatingImService roomFloatingImService;
    private final EventReport eventReport;
    private final RewardService rewardService;
    private final AppConfigAwardDao appConfigAwardDao;
    @Resource(name = "carnivalLuckyDrawTaskLimitRedis")
    private BaseEveryLimitCurrencyRedis<com.quhong.data.config.TaskConfig> carnivalLuckyDrawTaskLimitRedis;
    @Resource(name = "playGameTaskLimitRedis")
    private BaseEveryLimitCurrencyRedis<com.quhong.data.config.TaskConfig> playGameTaskLimitRedis;

    /**
     * 校验限制
     *
     * @param currActor
     * @param configData
     * @param currTime
     */
    private static void checkLimit(ActorData currActor, AppConfigActivityData configData, long currTime) {
        if (!ActorType.REAL_PERSON_USER_TYPE_SET.contains(currActor.getUserType())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("robot data not count"));
        }
        if (StringUtils.hasLength(configData.getChannel()) && !configData.getChannel().equals(currActor.getChannel())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("this channel not count"));
        }

        // 国家限制
        Set<String> countryCodeSet = getCountryCodeSet(currActor, configData);
        if (!ObjectUtils.isEmpty(countryCodeSet) && !countryCodeSet.contains(currActor.getCountryCode())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("Your country cannot participate in this event"));
        }
        // 活动时间限制
        if (currTime < configData.getStartTime() || currTime > configData.getEndTime()) {
            log.info("not in the event period");
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("not in the event period"));
        }
    }

    private static Set<String> getCountryCodeSet(ActorData actor, AppConfigActivityData activityData) {
        Set<String> countryCodeSet = activityData.getUserCountryGroupStr();
        if (GenderTypeEnum.HOST.getType().equals(actor.getGender())) {
            countryCodeSet = activityData.getHostCountryGroupStr();
        }
        return countryCodeSet;
    }

    public void receiveRoomChatMsgLock(RoomChatMsgData mqData) {
        checkParams(mqData);
        ActorData currActor = getActorData(mqData.getUid());
        AppConfigActivityData configData = getAppConfigActivityData(ACTIVITY_TYPE_ENUM);
        checkLimit(currActor, configData, DateHelper.getCurrTime());
        String text = "Join in Happy Independence Day Event, get tickets, check-in attractions and win big prizes";
        if (!text.equals(mqData.getMsg())) {
            return;
        }
        distributeLockUtils.distributeMethod(mqData, getTicketLockKey(mqData.getUid()), this::receiveRoomChatMsg);
    }

    private boolean receiveRoomChatMsg(RoomChatMsgData mqData) {
        checkParams(mqData);
        ActorData currActor = getActorData(mqData.getUid());

        TaskConfig taskConfig = TaskConfig.DISSEMINATE_TIMES_TASK;
        int increase = independencePromotionRoomLimitRedis.saveRoomIdAndGetTicket(mqData.getUid(), mqData.getRoomId());
        if (increase < 1) {
            return false;
        }

        giveTicket(ACTIVITY_TYPE_ENUM, currActor, increase, taskConfig);
        return true;
//        if (PropagandaCopyRewardConfig.COPY_TEXT.equals(mqData.getMsg())) {
//            eventPromotionRoomLimitRedis.addRoomId(mqData.getUid(), mqData.getRoomId());
//        }
    }

    /**
     * 集卡
     *
     * @param mqData
     */
    public void receiveSendPostcardMqLock(SendGiftSuccessMsgData mqData) {
        checkSendPostcardParams(mqData);
        distributeLockUtils.distributeMethod(mqData, getTicketLockKey(mqData.getUid()), this::receiveSendPostcardMq);
    }

    /**
     * 接收送礼mq处理
     *
     * @param mqData mqreceiveSendGiftMqLock
     */
    public void receiveSendGiftMqLock(SendGiftSuccessMsgData mqData) {
        checkParams(mqData);
        distributeLockUtils.distributeMethod(mqData, getTicketLockKey(mqData.getUid()), this::receiveSendGiftMq);
    }

    public void moneyDetailActionLock(MoneyDetailData mqData) {
        checkParams(mqData);
        ActorData currActor = getActorData(mqData.getUserid());
        AppConfigActivityData configData = getAppConfigActivityData(ACTIVITY_TYPE_ENUM);
        checkLimit(currActor, configData, DateHelper.getCurrTime());
        if (mqData.getActType() == ActType.STAR_GALA) {
            // 嘉年华消耗金币
            distributeLockUtils.distributeMethod(mqData, getTicketLockKey(mqData.getUserid()), this::carnivalTaskAction);
        } else if (ActType.HKYS_GAME_GOLD == mqData.getActType()) {
            // 玩游戏消耗金币
            distributeLockUtils.distributeMethod(mqData, getTicketLockKey(mqData.getUserid()), this::playGameTaskAction);
        }
    }

    private boolean playGameTaskAction(MoneyDetailData mqData) {
        ActorData currActor = getActorData(mqData.getUserid());
        TaskConfig taskConfig = TaskConfig.PLAY_GAME_COST_GOLD_TASK;
        int increase = playGameTaskLimitRedis.increaseAndGetRewards(mqData.getUserid(), ACTIVITY_TYPE_ENUM.getCode(), mqData.getSingleChange());
        if (increase < 1) return false;
        giveTicket(ACTIVITY_TYPE_ENUM, currActor, increase, taskConfig);
        return true;
    }

    private boolean carnivalTaskAction(MoneyDetailData mqData) {
        ActorData currActor = getActorData(mqData.getUserid());
        TaskConfig taskConfig = TaskConfig.CARNIVAL_LUCKY_DRAW_COST_GOLD_TASK;
        int increase = carnivalLuckyDrawTaskLimitRedis.increaseAndGetRewards(mqData.getUserid(), ACTIVITY_TYPE_ENUM.getCode(), mqData.getSingleChange());
        if (increase < 1) return false;
        giveTicket(ACTIVITY_TYPE_ENUM, currActor, increase, taskConfig);
        return true;
    }

    private void checkParams(MoneyDetailData mqData) {
        if (mqData.getSingleAction() != MoneyChangeActionConstant.ACTION_DEDUCT) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("this record not join event, single action must is 2"));
        }
        if (!Objects.equals(mqData.getCurrencyCode(), CurrencyEnum.CURRENCY1.getCurrencyCode())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("this record not join event, currency code must is 1"));
        }

    }

    /**
     * 接收首次付费mq处理
     *
     * @param uid 用户yid
     */
    @Deprecated
    public void receiveFirstPaymentMqLock(String uid) {
        checkParams(uid);
        distributeLockUtils.distributeMethod(uid, getTicketLockKey(uid), this::receiveFirstPayMq);
    }

    /**
     * 用户玩yxsk系列游戏mq 处理
     *
     * @param mqData mq
     */
    @Deprecated
    public void receivePlayYxskGameMqLock(UpdateCoinDTO mqData) {
        checkParams(mqData);
        distributeLockUtils.distributeMethod(mqData, getTicketLockKey(mqData.getOpenid()), this::receivePlayYxskGameMq);
    }

    /**
     * 用户离开房间mq处理
     *
     * @param mqData mq
     */
    @Deprecated
    public void receiveLeaveRoomMqLock(EnterRoomLogData mqData) {
        checkParams(mqData);
        distributeLockUtils.distributeMethod(mqData, getTicketLockKey(mqData.getUid()), this::receiveLeaveRoomMq);
    }

    private boolean receiveSendGiftMq(SendGiftSuccessMsgData mqData) {
        //发礼物的处理
        try {
            sendGiftFunc(mqData);
        } catch (WebException e) {
            log.error("error to com.quhong.service.activity.independence.day.IndependenceDayMqService.sendActorFunc, HttpCode={}", JSON.toJSONString(e.getHttpCode()));
        }
        //收礼物的处理
        try {
            receiveGiftFunc(mqData);
        } catch (WebException e) {
            log.error("error to com.quhong.service.activity.independence.day.IndependenceDayMqService.receiveActorFunc, HttpCode={}", JSON.toJSONString(e.getHttpCode()));
        }
        return true;
    }

    private boolean receiveSendPostcardMq(SendGiftSuccessMsgData mqData) {
        String node = ScenicRewardConfig.NODE_CARD_ID_MAP.getOrDefault(mqData.getGiftId().toString(), "");
        //考虑node再映射一层
        if (StringUtils.isEmpty(node)) {
            return false;
        }
        int change = mqData.getNum();
        List<String> cardName = ScenicRewardConfig.NODE_CARD_MAP.getOrDefault(mqData.getGiftId().toString(), new ArrayList<>());
        if (ObjectUtils.isEmpty(cardName)) {
            return false;
        }

        ActorData currActor = actorMgr.getCurrActorData(mqData.getToUid());
        cardName.forEach(card -> {
            updateCardAndReport(mqData, card, change, node, currActor);
        });
        return true;
    }

    private void updateCardAndReport(SendGiftSuccessMsgData mqData, String card, int change, String node, ActorData currActor) {
        //            updatePostcardCount(node, -change, mqData.getUid(), activityTypeEnum.getCode());
        updatePostcardCount(card, change, mqData.getToUid(), ACTIVITY_TYPE_ENUM.getCode());
        //活动专用道具获取记录 上报数数
        reportGetCardToThinkData(mqData, card, node, change);
        // 发飘屏
        sendRoomFloatingIm(change, card, currActor);
    }

    private void sendRoomFloatingIm(int change, String card, ActorData currActor) {
        List<AppConfigAwardData> rewardInfoList = appConfigAwardDao.getListFromRedis(new QueryAppConfigAwardDTO()
                .setEventCode(ACTIVITY_TYPE_ENUM.getCode())
                .setName(card)
                .setAwardType(RewardItemType.EVENT_CURRENCY)
                .setValid(1));
        if (ObjectUtils.isEmpty(rewardInfoList)) {
            return;
        }
        roomFloatingImService.sendRoomFloatingIm(currActor,
                change,
                rewardInfoList.get(0).getIcon(),
                card,
                "for event Card Collection>>>",
                ACTIVITY_TYPE_ENUM.getCode(),
                TaskConfig.EVENT_URL,
                false,
                "Receive cards #ticket X #count #suffix");
    }

    private void reportGetCardToThinkData(SendGiftSuccessMsgData mqData, String card, String node, int change) {
        ActivitySpecialItemsEvent logData = new ActivitySpecialItemsEvent();
        logData.setUid(mqData.getToUid());
        logData.setCtime(mqData.getTime());
        logData.setChannel(mqData.getToChannel());
        logData.setEventCode(ACTIVITY_TYPE_ENUM.getCode());
        logData.setActivityName(ACTIVITY_TYPE_ENUM.getName());
        logData.setItemId(card);
        logData.setFromType("2");
        logData.setFromTypeDesc(node);
        logData.setFromUid(mqData.getUid());
        ActorData fromActor = actorMgr.getActorData(mqData.getUid());
        if (fromActor != null) {
            logData.setFromRid(fromActor.getRid());
        }
        logData.setItemCount(change);
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private void updatePostcardCount(String node, int change, String uid, int eventType) {
//        List<ScenicRewardConfig> scenicRewardConfigs = ScenicRewardConfig.SCENIC_REWARD_CONFIGS;
        ActorData currActor = getActorData(uid);
        //查询活动用户信息
        ActivityIndependenceActorInfoData data = activityIndependenceActorInfoDao.getOneByUid(eventType, uid);
        if (data == null) {
            data = ActivityIndependenceActorInfoData.init(uid,
                    currActor.getRid(),
                    eventType,
                    currActor.getIsPayUser() == 1 ? -1 : 0,
                    0L);
            data.changePostcardCount(node, change);
//            data.verifyAndGetCanGetPostcardReward();
            activityIndependenceActorInfoDao.insertOneSelective(data);
        } else {
            data.changePostcardCount(node, change);
//            data.verifyAndGetCanGetPostcardReward();
            ActivityIndependenceActorInfoData updateData = ActivityIndependenceActorInfoData.updatePostcardJsonDataFactory(data);
            activityIndependenceActorInfoDao.updateOneSelective(updateData);
        }
    }

    private boolean receiveFirstPayMq(String uid) {
        ActivityTypeEnum activityTypeEnum = ACTIVITY_TYPE_ENUM;
        TaskConfig taskConfig = TaskConfig.FIRST_RECHARGE_TASK;
        ActorData currActor = getActorData(uid);
        AppConfigActivityData configData = getAppConfigActivityData(activityTypeEnum);
        checkLimit(currActor, configData, DateHelper.getCurrTime());

        //查询活动用户信息
        ActivityIndependenceActorInfoData data = activityIndependenceActorInfoDao.getOneByUid(activityTypeEnum.getCode(), uid);
        if (data == null) {
            data = ActivityIndependenceActorInfoData.init(uid,
                    currActor.getRid(),
                    activityTypeEnum.getCode(),
                    -1,
                    Long.valueOf(taskConfig.getTicket()));
            activityIndependenceActorInfoDao.insertOneSelective(data);
        } else {
            boolean canGetFirstRecharge = data.firstRechargeAddTicket(taskConfig.getTicket());
            if (!canGetFirstRecharge) {
                return false;
            }
            ActivityIndependenceActorInfoData updateData = ActivityIndependenceActorInfoData.firstRechargeUpdateDataFactory(data);
            activityIndependenceActorInfoDao.updateOneSelective(updateData);
        }
        // 获得门票im推送
        giveTicketSendIm(currActor, taskConfig.getTicket());
        // 首充获得门票数量统计redis
        ticketGetCountRedis.increaseTicketCount(taskConfig.getTaskType().toString(), taskConfig.getTicket());
        return true;
    }

    private boolean receivePlayYxskGameMq(UpdateCoinDTO mqData) {
        ActivityTypeEnum activityTypeEnum = ACTIVITY_TYPE_ENUM;
        TaskConfig taskConfig = TaskConfig.PLAY_YXSK_GAME_TASK;
        ActorData currActor = getActorData(mqData.getOpenid());
        AppConfigActivityData configData = getAppConfigActivityData(activityTypeEnum);
        checkLimit(currActor, configData, mqData.getTs());
        long giveTicket = (long)dailyPlayYXSKGameTaskLimitRedis.getTicket(currActor.getUid(), mqData.getCoin());

        //发放门票
        return giveTicket(activityTypeEnum, currActor, giveTicket, taskConfig);
    }

    private boolean receiveLeaveRoomMq(EnterRoomLogData mqData) {
        ActivityTypeEnum activityTypeEnum = ACTIVITY_TYPE_ENUM;
        TaskConfig taskConfig = TaskConfig.ROOM_STAY_TIME_TASK;
        ActorData currActor = getActorData(mqData.getUid());
        AppConfigActivityData configData = getAppConfigActivityData(activityTypeEnum);
        checkLimit(currActor, configData, mqData.getExitTime());
        long giveTicket = roomStayTaskLimitRedis.getTicket(currActor.getUid(), mqData.getDuration());

        //查询活动用户信息
        return giveTicket(activityTypeEnum, currActor, giveTicket, taskConfig);
    }

    /**
     * 礼物接收者的处理逻辑
     *
     * @param mqData mq
     */
    private boolean receiveGiftFunc(SendGiftSuccessMsgData mqData) {
        ActivityTypeEnum activityTypeEnum = ACTIVITY_TYPE_ENUM;
        List<TaskConfig> taskConfigs = TaskConfig.TASK_CONFIGS;
        LuckyGiftPackageConfig luckyConfig = LuckyGiftPackageConfig.CONFIG;
        ActorData currActor = getActorData(mqData.getToUid());
        AppConfigActivityData configData = getAppConfigActivityData(activityTypeEnum);
        checkLimit(currActor, configData, mqData.getTime());

        //判断是否为指定礼物
        boolean isMatch = taskConfigs.stream().anyMatch(data -> data.getCheckParams().equals(mqData.getGiftId().intValue()));
        if (!isMatch) {
            return false;
        }

        //查询活动用户信息
        ActivityIndependenceActorInfoData data = activityIndependenceActorInfoDao.getOneByUid(activityTypeEnum.getCode(), currActor.getUid());
        List<RewardInfoData> letterList;
        if (data == null) {
            letterList = saveAndGetLetterList(mqData, currActor, activityTypeEnum, luckyConfig, null);
        } else {
            letterList = updateAndGetLetterList(mqData, data, luckyConfig, null, currActor);
        }
        if (!ObjectUtils.isEmpty(letterList)) {
            // 发送LUCKY飘屏, 上报数数
            letterList.forEach(rewardInfo -> afterProcess(mqData, rewardInfo, currActor, configData));
        }
        return true;
    }

    private void afterProcess(SendGiftSuccessMsgData mqData, RewardInfoData rewardInfo, ActorData currActor, AppConfigActivityData configData) {
        roomFloatingImService.sendRoomFloatingIm(
                currActor,
                rewardInfo.getNums(),
                rewardInfo.getIcon(),
                rewardInfo.getName(),
                "for event Card Collection>>>",
                configData.getActivityCode(),
                TaskConfig.EVENT_URL,
                false,
                "Receive cards #ticket X #count #suffix");
        reportGetCardToThinkData(mqData, rewardInfo);
    }

    private void reportGetCardToThinkData(SendGiftSuccessMsgData mqData, RewardInfoData rewardInfo) {
        ActivitySpecialItemsEvent logData = new ActivitySpecialItemsEvent();
        logData.setUid(mqData.getToUid());
        logData.setCtime(mqData.getTime());
        logData.setChannel(mqData.getToChannel());
        logData.setEventCode(ACTIVITY_TYPE_ENUM.getCode());
        logData.setActivityName(ACTIVITY_TYPE_ENUM.getName());
        logData.setItemId(rewardInfo.getName());
        logData.setFromType("1");
        logData.setFromTypeDesc("");
        logData.setItemCount(1);
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    private List<RewardInfoData> updateAndGetLetterList(SendGiftSuccessMsgData mqData, ActivityIndependenceActorInfoData data, LuckyGiftPackageConfig luckyConfig, List<RewardInfoData> letterList, ActorData currActor) {
        long prePrice;
        prePrice = data.getGiftPriceSum();
        data.increaseGiftPriceSum(mqData.getCost());
        long balance = prePrice % luckyConfig.getLimit();
        if (balance + mqData.getCost() >= luckyConfig.getLimit()) {
            long sendLetterCount = ((balance + mqData.getCost()) / luckyConfig.getLimit()) * luckyConfig.getLetterCount();
            // 从奖池抽取字母
            letterList = LongStream.range(0, sendLetterCount)
                    .mapToObj(index -> getLuckyFromPool(currActor.getUid()))
                    .collect(Collectors.toList());
            letterList.forEach(rewardInfo -> data.changePostcardCount(rewardInfo.getName(), rewardInfo.getNums()));
        }
        ActivityIndependenceActorInfoData updateData = ActivityIndependenceActorInfoData.updateGiftPriceSumDataFactory(data);
        if (!ObjectUtils.isEmpty(letterList)) {
            updateData.setPostcardMapJson(data.getPostcardMapJson());
        }
        activityIndependenceActorInfoDao.updateOneSelective(updateData);
        return letterList;
    }

    private List<RewardInfoData> saveAndGetLetterList(SendGiftSuccessMsgData mqData, ActorData currActor, ActivityTypeEnum activityTypeEnum, LuckyGiftPackageConfig luckyConfig, List<RewardInfoData> letterList) {
        ActivityIndependenceActorInfoData data;
        data = ActivityIndependenceActorInfoData.init(currActor.getUid(),
                currActor.getRid(),
                activityTypeEnum.getCode(),
                currActor.getIsPayUser() == 1 ? -1 : 0,
                0L);
        data.setGiftPriceSum(mqData.getCost());
        if (data.getGiftPriceSum() >= luckyConfig.getLimit()) {
            long sendLetterCount = (data.getGiftPriceSum() / luckyConfig.getLimit()) * luckyConfig.getLetterCount();
            // 从奖池抽取字母
            letterList = LongStream.range(0, sendLetterCount)
                    .mapToObj(index -> getLuckyFromPool(currActor.getUid()))
                    .collect(Collectors.toList());
            letterList.forEach(rewardInfo -> data.changePostcardCount(rewardInfo.getName(), rewardInfo.getNums()));
        }
        activityIndependenceActorInfoDao.insertOneSelective(data);
        return letterList;
    }

    /**
     * 从奖池抽取字母
     *
     * @return L U C K Y
     */
    private RewardInfoData getLuckyFromPool(String uid) {
        return baseAwardPool.getOneAwardFromPool(new QueryBO(ACTIVITY_TYPE_ENUM.getCode(), AwardPoolGroupConstant.LUCKY_POOL, uid));
    }

    /**
     * 礼物发送者的处理逻辑
     *
     * @param mqData mq
     */
    private boolean sendGiftFunc(SendGiftSuccessMsgData mqData) throws WebException {
        ActivityTypeEnum activityTypeEnum = ACTIVITY_TYPE_ENUM;
        List<TaskConfig> taskConfigs = TaskConfig.TASK_CONFIGS;
        ActorData currActor = getActorData(mqData.getUid());
        AppConfigActivityData configData = getAppConfigActivityData(activityTypeEnum);
        checkLimit(currActor, configData, mqData.getTime());
        //获取本次获得门票数
        TaskConfig taskConfig = taskConfigs.stream().filter(data -> data.getCheckParams().equals(mqData.getGiftId().intValue()))
                .findFirst().orElse(null);
        if (taskConfig == null) {
            log.info("not found gift from task config,giftId={}", mqData.getGiftId());
            return false;
        }
        //发放门票
        return giveTicket(activityTypeEnum, currActor, (long) taskConfig.getTicket() * mqData.getNum(), taskConfig);
    }

    private void checkSendPostcardParams(SendGiftSuccessMsgData mqData) {
        checkSendGiftParams(mqData);
    }

    private void checkSendGiftParams(SendGiftSuccessMsgData mqData) {
        if (StringUtils.isEmpty(mqData.getUid())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("uid is empty"));
        }
        if (StringUtils.isEmpty(mqData.getToUid())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("toUid is empty"));
        }
        if (mqData.getTime() == null || mqData.getTime() <= 0) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("time is empty"));
        }
        if (mqData.getGiftId() == null || mqData.getGiftId() <= 0) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("gift is empty"));
        }
        if (mqData.getNum() == null || mqData.getNum() <= 0) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("num is empty"));
        }
    }

    private void checkParams(SendGiftSuccessMsgData mqData) {
        checkSendGiftParams(mqData);
        if (mqData.getCost() == null || mqData.fetchRealCost() <= 0) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("cost is empty"));
        }

    }

    private void checkParams(String uid) {
        if (StringUtils.isEmpty(uid)) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("uid is empty"));
        }
    }

    private void checkParams(RoomChatMsgData mqData) {
        if (StringUtils.isEmpty(mqData.getUid())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("uid is empty"));
        }
        if (StringUtils.isEmpty(mqData.getRoomId())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("roomId is empty"));
        }
        if (StringUtils.isEmpty(mqData.getMsg())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("msg is empty"));
        }
    }

    private void checkParams(UpdateCoinDTO mqData) {
        mqData.checkPlayGameUpdateCoinParams();
    }

    private void checkParams(EnterRoomLogData mqData) {
        if (StringUtils.isEmpty(mqData.getUid())) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("uid is empty"));
        }
        if (mqData.getDuration() == null || mqData.getDuration() < 1) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("duration is empty"));
        }
    }

    private AppConfigActivityData getAppConfigActivityData(ActivityTypeEnum activityTypeEnum) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByCodeAndValid(activityTypeEnum.getCode(), 1);
        if (configData == null) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.OTHER_NOT_EXISTS));
        }
        return configData;
    }

    private ActorData getActorData(String uid) {
        ActorData currActor = actorMgr.getActorData(uid);
        if (currActor == null) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.ACTOR_NOT_EXIST));
        }
        return currActor;
    }

    private boolean giveTicket(ActivityTypeEnum activityTypeEnum, ActorData currActor, long giveTicket, TaskConfig taskConfig) {
        ActivityIndependenceActorInfoData data = activityIndependenceActorInfoDao.getOneByUid(activityTypeEnum.getCode(), currActor.getUid());
        if (data == null) {
            data = ActivityIndependenceActorInfoData.init(currActor.getUid(),
                    currActor.getRid(),
                    activityTypeEnum.getCode(),
                    currActor.getIsPayUser() == 1 ? -1 : 0,
                    giveTicket);
            activityIndependenceActorInfoDao.insertOneSelective(data);
        } else {
            if (giveTicket < 1) {
                throw new WebException(new HttpEnvData(), HttpCode.ILLEGAL_OPERATION);
            }
            data.increaseTicket(giveTicket);
            ActivityIndependenceActorInfoData updateData = ActivityIndependenceActorInfoData.updateDataFactory(data);
            activityIndependenceActorInfoDao.updateOneSelective(updateData);
        }
        if (giveTicket < 1) {
            throw new WebException(new HttpEnvData(), HttpCode.ILLEGAL_OPERATION);
        }
        // 获得门票im推送
        giveTicketSendIm(currActor, giveTicket);
        // 获得门票redis统计
        Integer taskType = taskConfig.getTaskType();
        if (taskType == TaskTypeConstant.SEND_GIFT) {
            taskType = taskConfig.getCheckParams();
        }
        ticketGetCountRedis.increaseTicketCount(taskType.toString(), giveTicket);
        //门票获取上报数数
        reportGetTicketToThinkData(activityTypeEnum, currActor, (int) giveTicket, taskConfig);
        return true;
    }

    private void reportGetTicketToThinkData(ActivityTypeEnum activityTypeEnum, ActorData currActor, int giveTicket, TaskConfig taskConfig) {
        ActivityTicketsEvent logData = new ActivityTicketsEvent();
        logData.setUid(currActor.getUid());
        logData.setCtime(DateHelper.getCurrTime());
        logData.setChannel(currActor.getChannel());
        logData.setEventCode(activityTypeEnum.getCode());
        logData.setActivityName(activityTypeEnum.getName());
        String fromType = taskConfig.getTaskType().toString();
        if (taskConfig.getTaskType() == TaskTypeConstant.SEND_GIFT) {
            fromType += "_" + taskConfig.getCheckParams();
        }
        logData.setActivityTicketsResource(fromType);
        logData.setActivityTickets(giveTicket);
        logData.setProperties(JSON.parseObject(JSON.toJSONString(logData)));
        eventReport.track(logData);
    }

    /**
     * 获得门票im推送
     *
     * @param currActor  用户信息
     * @param giveTicket 给予的门票数
     */
    private void giveTicketSendIm(ActorData currActor, long giveTicket) {
        String url = TaskConfig.EVENT_URL;

        RoomFloatingScreenMsg msg = RoomFloatingScreenMsg.builder().uid(currActor.getUid())
                .eventType(ACTIVITY_TYPE_ENUM.getCode())
                .icon(cdnUtils.replaceUrlDomain(currActor.getChannel(), TaskConfig.TICKET_ICON_URL, 0))
                .text(IM_TEXT.replace("#count", String.valueOf(giveTicket)))
                .count(giveTicket)
                .itemName(TICKET_NAME)
                .backgroundImg(cdnUtils.replaceUrlDomain(currActor.getChannel(), TaskConfig.FLOATING_SCREEN_BACKGROUND_IMG, 0))
                .url(url).build();
        sendIm(currActor.getUid(), msg);
    }

    /**
     * 获得门票im推送
     *
     * @param uid uid
     * @param msg 消息
     */
    private void sendIm(String uid, RoomFloatingScreenMsg msg) {
        InnerSendMsgDTO imMsg = new InnerSendMsgDTO();
        imMsg.setBody(msg.toBody());
        imMsg.setToUid(uid);
        imMsg.setCmd(AppMsgType.ROOM_FLOATING_SCREEN_MSG);
        imApi.sendMsg(imMsg);
    }

    private String getTicketLockKey(String uid) {
        return TICKET_LOCK_KEY + uid;
    }

    public void monitorTaskTicketCount() {
        Map<String, String> taskTickCountMap = ticketGetCountRedis.queryTicketCount();
        String title = "## 每个任务门票发放总数(" + ACTIVITY_TYPE_ENUM.getName() + ") \n";
        String tableHead = "任务号\t\t\t数值\n";
        StringBuilder content = new StringBuilder(title + tableHead);
        taskTickCountMap.forEach((taskId, count) -> {
            content.append(taskId).append("\t\t\t\t")
                    .append(count).append("\n");
        });
        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, content.toString());
    }

}
