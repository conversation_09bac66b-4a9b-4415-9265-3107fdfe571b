package com.quhong.service.activity.send.lord.gift.award;

import com.quhong.common.data.BaseHttpData;
import com.quhong.common.enums.HttpCode;
import com.quhong.core.date.DayTimeSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.ActivitySendLordGiftAwardDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.GiftListConfigDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.GiftListConfigData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.db.ActivitySendLordGiftAwardData;
import com.quhong.data.appConfig.activity.sendLordGiftAward.SendLordGiftAwardConfig;
import com.quhong.data.dto.CommonDTO;
import com.quhong.data.vo.BaseAwardVO;
import com.quhong.data.vo.send.lord.gift.award.InitPageVO;
import com.quhong.exceptions.WebException;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.players.ActorMgr;
import com.quhong.service.GiftService;
import com.quhong.service.GiveOutRewardService;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/6 13:49
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SendLordGiftAwardService {

    private final ActivitySendLordGiftAwardDao activitySendLordGiftAwardDao;

    private final ActorMgr actorMgr;

    private final AppConfigActivityDao appConfigActivityDao;

    private final GiftListConfigDao giftListConfigDao;

    private final GiveOutRewardService giveOutRewardService;

    private final GiftService giftService;

    private static void generateInitPageVO(InitPageVO vo, ActivitySendLordGiftAwardData data, SendLordGiftAwardConfig awardConfig) {
        vo.setSendGiftCount(data.getSendGiftCount());
        vo.setLimit(awardConfig.getReceiveLimit());
        vo.setReceiveAwardCount(data.getReceiveCount());
        vo.generateStatus();
    }

    private static void checkCanGetAward(CommonDTO dto, ActivitySendLordGiftAwardData data, SendLordGiftAwardConfig awardConfig) {
        InitPageVO vo = new InitPageVO();
        generateInitPageVO(vo, data, awardConfig);
        if (vo.getButtonStatus() != 1) {
            throw new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("You do not meet the requirements for receiving rewards"));
        }
    }

    private static void checkLimit(CommonDTO dto, long currTime, AppConfigActivityData configData, ActorData currActor) {
        if (currTime < configData.getStartTime()) {
            throw new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("event not started"));
        }
        if (configData.getEndTime() < currTime) {
            throw new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("the event has ended"));
        }
        if (!configData.getChannel().equals(currActor.getChannel())) {
            throw new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("The app you are currently logged in to cannot participate in this event"));
        }
    }

    public InitPageVO initPage(CommonDTO dto) {
        checkParams(dto);
        ActorData currActor = getCurrActor(dto.getUid());
        AppConfigActivityData configData = getAppConfigActivityData(dto.getEventType());
        long currTime = DateHelper.getCurrTime();
        checkLimit(dto, currTime, configData, currActor);

        InitPageVO vo = new InitPageVO();
        vo.setStartTime(configData.getStartTime());
        vo.setEndTime(configData.getEndTime());
        ActivitySendLordGiftAwardData data = activitySendLordGiftAwardDao.getOneByEventCodeAndUid(dto.getEventType(), dto.getUid());
        if (data == null) {
            return vo;
        }
        SendLordGiftAwardConfig awardConfig = new SendLordGiftAwardConfig();
        generateInitPageVO(vo, data, awardConfig);
        return vo;
    }

    public boolean giveAward(CommonDTO dto) {
        checkParams(dto);
        ActorData currActor = getCurrActor(dto.getUid());
        AppConfigActivityData configData = getAppConfigActivityData(dto.getEventType());
        long currTime = DateHelper.getCurrTime();
        checkLimit(dto, currTime, configData, currActor);

        ActivitySendLordGiftAwardData data = activitySendLordGiftAwardDao.getOneByEventCodeAndUid(dto.getEventType(), dto.getUid());
        if (data == null) {
            throw new WebException(dto, new HttpCode(HttpCode.ILLEGAL_OPERATION).setMsg("You do not meet the requirements for receiving rewards"));
        }
        SendLordGiftAwardConfig awardConfig = new SendLordGiftAwardConfig();
        checkCanGetAward(dto, data, awardConfig);
        int awardCount = data.increaseReceiveCount();
        if (awardCount < 1) {
            throw new WebException(dto, HttpCode.SERVER_ERROR);
        }
        String currTimeStr = DayTimeSupport.INDIAN.fromTimeSeconds(currTime)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        data.getReceiveTimeList().add(currTimeStr);
        updateReceiveCountFromDb(data, currTime);
        List<RewardInfoData> rewards = generateRewards(dto, awardConfig, awardCount);
        giveOutRewardService.giveOutReward(dto.getUid(), rewards);
//        return rewards.stream().map(this::fillAwardVO).collect(Collectors.toList());
        return true;
    }

    private static List<RewardInfoData> generateRewards(CommonDTO dto, SendLordGiftAwardConfig awardConfig, int awardCount) {
        List<RewardInfoData> rewards = awardConfig.getRewards();
        rewards.forEach(reward -> {
            reward.multiNums(awardCount);
            reward.setActivityType(dto.getEventType());
        });
        return rewards;
    }

    private BaseAwardVO fillAwardVO(RewardInfoData rewardInfoData) {
        return BaseAwardVO.builder()
                .img(rewardInfoData.getIcon())
                .name(rewardInfoData.getName())
                .count(rewardInfoData.getNums())
                .type(rewardInfoData.getType())
                .build();
    }

    private void updateReceiveCountFromDb(ActivitySendLordGiftAwardData data, long currTime) {
        ActivitySendLordGiftAwardData updateData = ActivitySendLordGiftAwardData.builder()
                .id(data.getId())
                .eventCode(data.getEventCode())
                .uid(data.getUid())
                .receiveCount(data.getReceiveCount())
                .receiveTimeList(data.getReceiveTimeList())
                .mtime(currTime)
                .build();
        activitySendLordGiftAwardDao.updateOneSelective(updateData);
    }

    public void checkAndSaveSendLordGiftAwardCount(int eventCode, SendGiftSuccessMsgData mqData) {
        checkParams(mqData);
        ActorData currActor = getCurrActor(mqData.getUid());
        AppConfigActivityData configData = getAppConfigActivityData(eventCode);
        if (mqData.getTime() < configData.getStartTime() || configData.getEndTime() < mqData.getTime()) {
            log.info("Not within the activity time range, not processing data");
            return;
        }
        if (!configData.getChannel().equals(currActor.getChannel())) {
            log.info("User data that is not a designated channel will not be counted");
            return;
        }

        GiftListConfigData giftConfigData = giftService.getGiftListConfig(Math.toIntExact(mqData.getGiftId()), mqData.getFromType(), currActor.getUid());
        if (giftConfigData.getLord() != 1) {
            log.info("Not a lord gift, don't handle it");
            return;
        }
        saveCountData(eventCode, mqData, currActor);
    }

    private AppConfigActivityData getAppConfigActivityData(int eventCode) {
        AppConfigActivityData configData = appConfigActivityDao.getOneByCodeAndValid(eventCode, 1);
        if (configData == null) {
            log.info("activity config is not found");
            throw new WebException(new BaseHttpData(), HttpCode.OTHER_NOT_EXISTS);
        }
        return configData;
    }

    private void saveCountData(int eventCode, SendGiftSuccessMsgData mqData, ActorData currActor) {
        ActivitySendLordGiftAwardData data = activitySendLordGiftAwardDao.getOneByEventCodeAndUid(eventCode, mqData.getUid());
        SendLordGiftAwardConfig awardConfig = new SendLordGiftAwardConfig();

        if (data == null) {
            insertData(eventCode, mqData, currActor, awardConfig);
        } else {
            if (data.getSendGiftCount() >= awardConfig.getReceiveLimit()) {
                log.info("The number of gifts given to nobles has reached the activity limit and will no longer be recorded");
                return;
            }
            data.increaseSendGiftCount(mqData.getNum(), awardConfig.getReceiveLimit());
            updateGiftCountFromDb(mqData, data);
        }
    }

    private void updateGiftCountFromDb(SendGiftSuccessMsgData mqData, ActivitySendLordGiftAwardData data) {
        ActivitySendLordGiftAwardData updateData = ActivitySendLordGiftAwardData.builder()
                .id(data.getId())
                .eventCode(data.getEventCode())
                .uid(data.getUid())
                .sendGiftCount(data.getSendGiftCount())
                .mtime(mqData.getTime())
                .build();
        activitySendLordGiftAwardDao.updateOneSelective(updateData);
    }

    private void insertData(int eventCode, SendGiftSuccessMsgData mqData, ActorData currActor, SendLordGiftAwardConfig awardConfig) {
        ActivitySendLordGiftAwardData data;
        data = ActivitySendLordGiftAwardData.builder()
                .eventCode(eventCode)
                .uid(mqData.getUid())
                .rid(currActor.getRid())
                .sendGiftCount(Math.min(awardConfig.getReceiveLimit(), mqData.getNum()))
                .receiveCount(0)
                .ctime(mqData.getTime())
                .mtime(mqData.getTime())
                .build();
        activitySendLordGiftAwardDao.insertOneSelective(data);
    }

    private ActorData getCurrActor(String uid) {
        ActorData currActor = actorMgr.getActorData(uid);
        if (currActor == null) {
            throw new WebException(new BaseHttpData(), HttpCode.ACTOR_NOT_EXIST);
        }
        return currActor;
    }

    private void checkParams(CommonDTO dto) {
        if (StringUtils.isEmpty(dto.getUid())) {
            throw new WebException(dto, new HttpCode(HttpCode.PARAM_ERROR).setMsg("param error, uid is empty"));
        }
        if (dto.getEventType() == null) {
            throw new WebException(dto, new HttpCode(HttpCode.PARAM_ERROR).setMsg("param error, eventType is empty"));
        }
    }

    private void checkParams(SendGiftSuccessMsgData mqData) {
        if (StringUtils.isEmpty(mqData.getUid())) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("uid is empty"));
        }
        if (mqData.getTime() == null || mqData.getTime() < 1) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("time is empty"));
        }
        if (mqData.getGiftId() == null || mqData.getGiftId() < 1) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("giftId is empty"));
        }
        if (mqData.getNum() == null || mqData.getNum() < 1) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("num is empty"));
        }
        if (mqData.getFromType() == null) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("fromType is empty"));
        }
    }

}
