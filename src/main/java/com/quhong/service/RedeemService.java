package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.common.data.ApiResult;
import com.quhong.common.data.BaseHttpData;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.ActType;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.core.constant.money.MoneyChangeActionConstant;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.enums.GenderTypeEnum;
import com.quhong.core.exceptions.GoldException;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.CustomerServiceUtils;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.*;
import com.quhong.dao.data.RedeemData;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.HostInfoData;
import com.quhong.dao.datas.db.WithdrawPasswordData;
import com.quhong.dao.datas.invite.KisCoinDealersBalanceData;
import com.quhong.dao.datas.invite.KisCoinDealersBalanceLogData;
import com.quhong.dao.datas.invite.SysUserData;
import com.quhong.dao.dto.RedeemGoldDTO;
import com.quhong.dao.dto.GameCoinRedeemDTO;
import com.quhong.dao.vo.GameCoinRedeemListVO;
import com.quhong.dao.vo.RedeemGoldListVO;
import com.quhong.data.appConfig.DiamondsWalletPayoutConfig;
import com.quhong.data.appConfig.GameCoinRedeemData;
import com.quhong.data.config.RedeemLimitConfig;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.data.dto.money.CurrencyAddDTO;
import com.quhong.data.dto.money.CurrencyDeductDTO;
import com.quhong.data.thData.CoinDealerChangeRecordThData;
import com.quhong.enums.CurrencyEnum;
import com.quhong.exceptions.WebException;
import com.quhong.monitor.CmdCodeEnum;
import com.quhong.players.ActorMgr;
import com.quhong.redis.UserInfoRedis;
import com.quhong.report.EventReport;
import com.quhong.service.money.CurrencyService;
import com.quhong.service.task.TaskApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 钻石钱包页页面初始化
 *
 * <AUTHOR>
 * <AUTHOR> since 20230414
 * @date 2022/12/30 16:23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RedeemService {

    private static final String REDEEM = "Redeem";
    private static final String WITHDRAW = "Withdraw";
    private final static long MERCHANT_DEPT_ID = 113;

    private final static int WITHDRAW_BUTTON_STATUS_DEFAULT = 0;//提现按钮默认状态
    private final static int WITHDRAW_BUTTON_STATUS_OPEN = 1;//提现按钮开启状态
    private final static int WITHDRAW_BUTTON_STATUS_HIDE = 2;//提现按钮隐藏状态

    private final CurrencyService currencyService;
    private final ConfigApi configApi;
    private final HostConfigDao hostConfigDao;
    private final ActorMgr actorMgr;
    private final WithdrawPasswordDao withdrawPasswordDao;
    private final MonitorSender monitorSender;
    private final FaceAuthRecordDao faceAuthRecordDao;
    private final ActorConfigDao actorConfigDao;
    private final HostInfoDao hostInfoDao;
    private final TaskApi taskApi;
    private final SysUserDao sysUserDao;
    private final KisCoinDealersBalanceLogDao kisCoinDealersBalanceLogDao;
    private final KisCoinDealersBalanceDao kisCoinDealersBalanceDao;
    private final EventReport eventReport;
    private final UserInfoRedis userInfoRedis;
    private final SafeStrategyService safeStrategyService;

    public String getCurrency2Desc(String uid) {
        String desc = REDEEM;
        int isLiveHost = hostConfigDao.getConfigIntValue(uid, HostConfigDao.WHITE_CONFIG);
        if (HostConfigDao.ADD_WHITE_LIST == isLiveHost) {
            desc = WITHDRAW;
        }
        return desc;
    }

    public ApiResult<RedeemGoldListVO> redeemList(HttpEnvData envData) {
        if (StringUtils.isEmpty(envData.getUid())) {
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }

        ActorData curActor = actorMgr.getActorData(envData.getUid());
        if (curActor == null) {
            log.info("actor is not exist,uid={}", envData.getUid());
            throw new WebException(envData, HttpCode.ACTOR_NOT_EXIST);
        }

        RedeemGoldListVO vo = new RedeemGoldListVO();
        vo.setPhone(curActor.getPhone());
        // 获取用户钻石余额
        BigDecimal currency2Balance = currencyService.getRealCurrency2Balance(envData.getUid());
        vo.setDiamonds(currency2Balance.longValue());
        vo.setRealDiamonds(currency2Balance.toString());
        // 计算美元价值
        String usd = computeUsd(envData, currency2Balance);
        vo.setUsd(usd);
        //处理提现初始数据
        dealWithdrawInitInfo(envData, vo, curActor);
        // 拉取配置列表
        List<RedeemData> redeemList = new ArrayList<>();
        if (!CustomerServiceUtils.TIKKO_OFFICIAL_AGENT_UID.equals(curActor.getUid())) {//临时处理 官方代理没有钻石兑换金币列表
            boolean b = checkHaveRedeemList(curActor);
            if (b) {
                redeemList = getRedeemDataList(curActor);
            } else {
                int day = haveRedeemListNeedDay(curActor);
                vo.setNotHaveRedeemText(String.format("The new host can only exchange diamonds to coins after %s days", day));
            }
        }
        checkConfig(redeemList);
        vo.setRedeemList(redeemList);
        WithdrawPasswordData withdrawPasswordData = withdrawPasswordDao.queryUserRecord(curActor.getUid());
        vo.setHavePassword(withdrawPasswordData != null && !StringUtils.isEmpty(withdrawPasswordData.getWithdrawPassword()) ? 1 : 0);
        SysUserData sysUserData = sysUserDao.queryDataByRid(curActor.getRid());
        vo.setHaveBindCoinMerchant(sysUserData != null && sysUserData.getDeptId().equals(MERCHANT_DEPT_ID));
        if (vo.isHaveBindCoinMerchant()) {
            vo.setCoinSellerRedeemList(getCoinSellerRedeemDataList(curActor));
        }
        dealRedeemLimit(curActor, vo);
        return ApiResult.getOk(vo);
    }

    private void dealRedeemLimit(ActorData curActor, RedeemGoldListVO vo) {
        RedeemLimitConfig configLimit = configApi.getJavaBeanVal(new ConfigDTO(curActor.getUid(), AppConfigKeyConstant.REDEEM_DAILY_LIMIT_CONFIG, AppConfigKeyConstant.STATUS_SERVER), RedeemLimitConfig.class);
        if (configLimit != null && configLimit.getDailyMaxCount() > 0) {
            vo.setRedeemCountLimit(configLimit.getDailyMaxCount());
            //获取今日兑换次数
            String today = DateHelper.genDateHelper(curActor.getChannel()).getToday();
            int redeemCount = userInfoRedis.getRedeemCount(curActor.getUid(), today);
            vo.setTodayRedeemCount(Math.min(configLimit.getDailyMaxCount(), redeemCount));
        }
    }

    private void dealWithdrawInitInfo(HttpEnvData envData, RedeemGoldListVO vo, ActorData curActor) {
        // 判定是否是直播主播
        int isLiveHost = hostConfigDao.getConfigIntValue(envData.getUid(), HostConfigDao.WHITE_CONFIG);
        if (HostConfigDao.ADD_WHITE_LIST == isLiveHost) {
            // 显示提示按钮开关（置灰状态）
            vo.setWithdrawButtonStatus(WITHDRAW_BUTTON_STATUS_DEFAULT);
            // 设置 liveRoom数据面板入口开关
            vo.setLiveRoomDataPanelStatus(0);
            // 设置 收入分析数据面板入口开关(目前无页面不开放)
            vo.setIncomeDataPanelStatus(0);
            // 设置 提现历史记录入口
            vo.setWithdrawRecordStatus(0);
        } else {
            if (ChannelEnum.CDE.getName().equals(curActor.getChannel()) && curActor.getVer() >= 689) {
                // 显示提示按钮开关（置灰状态）
                vo.setWithdrawButtonStatus(WITHDRAW_BUTTON_STATUS_DEFAULT);
                // 设置 liveRoom数据面板入口开关
                vo.setLiveRoomDataPanelStatus(0);
                // 设置 收入分析数据面板入口开关(目前无页面不开放)
                vo.setIncomeDataPanelStatus(0);
                // 设置 提现历史记录入口
                vo.setWithdrawRecordStatus(0);
                vo.setNeedAuth(1);
            }
            if (curActor.getGender().equals(GenderTypeEnum.USER.getType())) {
                vo.setWithdrawButtonStatus(WITHDRAW_BUTTON_STATUS_HIDE);
            }
        }


        // 提现按钮和固定提示文案 判定获取
        int withDrawButtonStatus = checkAndGetWithDrawButtonStatus(envData, vo, curActor);
        vo.setWithdrawButtonStatus(withDrawButtonStatus);
    }

    private void checkConfig(List<RedeemData> redeemList) {
        if (!CollectionUtils.isEmpty(redeemList)) {
            for (RedeemData redeemData : redeemList) {
                if (redeemData.getTargetValue() / redeemData.getSourceValue() > 2) {
                    monitorSender.info(CmdCodeEnum.UNKNOWNS.getWarnName(), "钻石兑换金币配置告警", "sourValue=" + redeemData.getSourceValue() + ",targetValue=" + redeemData.getTargetValue());
                }
            }
        }
    }


    private boolean checkConfig(long targetValue, long sourceValue, RedeemLimitConfig configLimit) {
        if (configLimit.getMultiple() <= 0) {
            return false;
        }
        return targetValue / sourceValue >= configLimit.getMultiple();
    }

    private int checkAndGetWithDrawButtonStatus(HttpEnvData envData, RedeemGoldListVO vo, ActorData actorData) {
        int withDrawButtonStatus = vo.getWithdrawButtonStatus();
        if (withDrawButtonStatus == WITHDRAW_BUTTON_STATUS_HIDE) {//隐藏状态 不走以下下流程
            return withDrawButtonStatus;
        }
        // 获取配置
        DiamondsWalletPayoutConfig configBo = getConfig(envData.getUid());

        if (configBo.getPowerSwitch() == 1) {//提现按钮全局开关
            vo.setWalletPayoutTipsText(configBo.getWalletPayoutTipsText());
            vo.setWithdrawPageTipsText(configBo.getWithdrawPageTipsText());
            vo.setMinWithdrawUsd(configBo.getMinWithdrawUsd());
            vo.setMaxWithdrawUsd(configBo.getMaxWithdrawUsd());
            vo.setWithdrawRecordStatus(1);
            vo.setLiveRoomDataPanelStatus(1);

            // 提现按钮开关 是否在开放时间内
            LocalDateTime now = LocalDateTime.now(DateHelper.genDateHelper(actorData.getChannel()).getTimeZone().toZoneId());
            if (now.getDayOfWeek().getValue() == configBo.getDayOfWeek()) {
                LocalTime startTime = LocalTime.parse(configBo.getStartTimeStr());
                LocalTime endTime = LocalTime.parse(configBo.getEndTimeStr());
                LocalTime nowLocalTime = now.toLocalTime();
                boolean isAfter = nowLocalTime.isAfter(startTime);
                boolean isBefore = nowLocalTime.isBefore(endTime);
                if (isAfter && isBefore) {
                    // 提示按钮开关（可用状态）
                    withDrawButtonStatus = WITHDRAW_BUTTON_STATUS_OPEN;
                }
            }
        } else {
            //设置提现按钮为隐藏状态
            withDrawButtonStatus = WITHDRAW_BUTTON_STATUS_HIDE;
        }
        return withDrawButtonStatus;
    }

    private DiamondsWalletPayoutConfig getConfig(String uid) {
        ConfigDTO dto = new ConfigDTO();
        dto.setUid(uid);
        dto.setKey(AppConfigKeyConstant.DIAMONDS_WALLET_PAYOUT_CONFIG);
        dto.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        DiamondsWalletPayoutConfig bo = configApi.getJavaBeanVal(dto, DiamondsWalletPayoutConfig.class);
        if (bo == null) {
            bo = new DiamondsWalletPayoutConfig();
        }
        return bo;
    }


    private String computeUsd(HttpEnvData envData, BigDecimal currency2Balance) {
        ConfigDTO dto = new ConfigDTO();
        dto.setUid(envData.getUid());
        dto.setKey(AppConfigKeyConstant.CONVERTIBLE_PROPORTION);
        dto.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        Integer rate = configApi.getIntegerVal(dto);
        if (rate == null) {
            rate = 200;
        }
        return MathUtils.division(currency2Balance.longValue(), rate);
    }

    private List<RedeemData> getRedeemDataList(ActorData actorData) {
        ConfigDTO dto = new ConfigDTO();
        dto.setUid(actorData.getUid());
        dto.setKey(AppConfigKeyConstant.REDEEM_GOLD_LIST_CONFIG);
        dto.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        String strVal = configApi.getStrVal(dto);
        List<RedeemData> redeemList = null;
        if (!StringUtils.isEmpty(strVal)) {
            redeemList = JSONObject.parseArray(strVal, RedeemData.class);
        }
        return redeemList;
    }

    private List<RedeemData> getCoinSellerRedeemDataList(ActorData actorData) {
        ConfigDTO dto = new ConfigDTO();
        dto.setUid(actorData.getUid());
        dto.setKey(AppConfigKeyConstant.COIN_SELLER_REDEEM_GOLD_LIST_CONFIG);
        dto.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        String strVal = configApi.getStrVal(dto);
        List<RedeemData> redeemList = null;
        if (!StringUtils.isEmpty(strVal)) {
            redeemList = JSONObject.parseArray(strVal, RedeemData.class);
        }
        return redeemList;
    }

    private boolean checkHaveRedeemList(ActorData actorData) {
        if (actorData.getRegisterTime() >= 1719772200//7月1号后注册的女主播
                && ChannelEnum.CDE.getName().equals(actorData.getChannel())
                && GenderTypeEnum.HOST.getType().equals(actorData.getGender())
                && actorData.getSex().equals(ActorData.SEX_FEMALE)) {
            boolean notHaveList = checkHaveRedeemDataList(actorData);
            return !notHaveList;
        }
        return true;
    }

    /**
     * 判断是否有兑换列表
     *
     * @param actorData 用户信息
     * @return ture没有兑换也列表  false不做限制
     */
    private boolean checkHaveRedeemDataList(ActorData actorData) {
        HostInfoData hostInfo = hostInfoDao.getHostInfo(actorData.getUid());
        if (hostInfo == null) {
            return false;
        }
        Long activateTime = hostInfo.getActivateTime();
        if (activateTime == null || activateTime == 0) {
            return false;
        }
        long seconds = TimeUnit.DAYS.toSeconds(30);
        return activateTime + seconds >= DateHelper.getCurrentTime();
    }

    private int haveRedeemListNeedDay(ActorData actorData) {
        HostInfoData hostInfo = hostInfoDao.getHostInfo(actorData.getUid());
        Long activateTime = hostInfo.getActivateTime();
        long seconds = TimeUnit.DAYS.toSeconds(30);
        return (int) ((seconds - (DateHelper.getCurrentTime() - activateTime)) / TimeUnit.DAYS.toSeconds(1));
    }

    public ApiResult<String> redeemGold(RedeemGoldDTO dto) {
        if (StringUtils.isEmpty(dto.getUid())) {
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorMgr.getActorData(dto.getUid());
        if (actorData == null) {
            throw new WebException(dto, HttpCode.USER_NOT_EXISTS);
        }
        RedeemLimitConfig configLimit = configApi.getJavaBeanVal(new ConfigDTO(actorData.getUid(), AppConfigKeyConstant.REDEEM_DAILY_LIMIT_CONFIG, AppConfigKeyConstant.STATUS_SERVER), RedeemLimitConfig.class);
        //判断配置
        if (!decideConfig(dto, actorData, configLimit)) {
            return ApiResult.getError(HttpCode.PARAM_ERROR);
        }
        //判断限制
        decideLimit(dto, actorData, configLimit);
        SysUserData sysUserData = null;
        if (dto.getRedeemType() == 1) {
            //判断身份
            sysUserData = sysUserDao.queryDataByRid(actorData.getRid());
            if (sysUserData == null || sysUserData.getDeptId() != MERCHANT_DEPT_ID) {
                throw new WebException(dto, HttpCode.ILLEGAL_OPERATION);
            }
        }
        // 获取钻石余额
        BigDecimal currency2Balance = currencyService.getRealCurrency2Balance(dto.getUid());
        // 判断钻石余额是否大于兑换所需余额
        if (currency2Balance.longValue() < dto.getSourceValue()) {
            return ApiResult.getError(HttpCode.BALANCE_NOT_ENOUGH);
        }
        //获取钻石倍率
        Integer diamondMultiplier = configApi.getIntegerVal(new ConfigDTO(actorData.getUid(), AppConfigKeyConstant.DIAMOND_COIN_MULTIPLIER, AppConfigKeyConstant.STATUS_ALL));
        //获取金币倍率
        Integer coinMultiplier = configApi.getIntegerVal(new ConfigDTO(actorData.getUid(), AppConfigKeyConstant.GOLD_COIN_MULTIPLIER, AppConfigKeyConstant.STATUS_ALL));
        try {
            String desc = "Redeem " + dto.getTargetValue() * coinMultiplier + " coins using " + dto.getSourceValue() * diamondMultiplier + " diamonds";
            // 减去钻石
            deductDiamonds(dto.getUid(), dto.getSourceValue(), desc);
            // 增加金币
            if (dto.getRedeemType() == 1) {
                addGoldToCoinMerchantAccount(actorData, sysUserData, dto.getTargetValue());
            } else {
                addGold(dto.getUid(), dto.getTargetValue(), desc);
            }
        } catch (Exception e) {
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }
        String today = DateHelper.genDateHelper(actorData.getChannel()).getToday();
        userInfoRedis.incRedeemCount(dto.getUid(), today);
        userInfoRedis.incRedeemGold(dto.getUid(), today, dto.getTargetValue());
        if (configLimit.getDailyTotalNums() > 0) {
            int redeemGold = userInfoRedis.getRedeemGold(dto.getUid(), today);
            if (redeemGold >= configLimit.getDailyTotalNums()) {
                monitorSender.info(CmdCodeEnum.ACTOR_BALANCE_NOTICE.getWarnName(), "钻石兑换金币配置告警单账号当日兑换金币数量＞" + configLimit.getDailyTotalNums(), "rid=" + actorData.getRid() + ",redeemGold=" + redeemGold);
            }
        }
        return ApiResult.getOk();
    }

    private void decideLimit(RedeemGoldDTO dto, ActorData actorData, RedeemLimitConfig configLimit) {
        if (configLimit.getDailyMaxCount() <= 0) {
            return;
        }
        int redeemCount = userInfoRedis.getRedeemCount(actorData.getUid(), DateHelper.genDateHelper(actorData.getChannel()).getToday());
        if (redeemCount < configLimit.getDailyMaxCount()) {
            return;
        }
        throw new WebException(dto, HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "Your exchange times has reached limits, please try again tomorrow"));
    }

    private boolean decideConfig(RedeemGoldDTO dto, ActorData actorData, RedeemLimitConfig configLimit) {
        List<RedeemData> redeemDataList = new ArrayList<>();
        if (dto.getRedeemType() == 1) {
            redeemDataList = getCoinSellerRedeemDataList(actorData);
        } else {
            boolean b = checkHaveRedeemList(actorData);
            if (b) {
                redeemDataList = getRedeemDataList(actorData);
            }
        }
        if (CollectionUtils.isEmpty(redeemDataList)) {
            return false;
        }
        Map<String, String> redeemDataMap = new HashMap<>();
        for (RedeemData redeemData : redeemDataList) {
            redeemDataMap.put(String.valueOf(redeemData.getSourceValue()), String.valueOf(redeemData.getTargetValue()));
        }

        String s = redeemDataMap.get(String.valueOf(dto.getSourceValue()));
        if (StringUtils.isEmpty(s) || !s.equals(String.valueOf(dto.getTargetValue()))) {
            return false;
        }
        if (checkConfig(dto.getTargetValue(), dto.getSourceValue(), configLimit)) {
            //
            log.info("redeem decide config target max than source dto={}", dto);
            throw new WebException(dto, HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, false, "The server is upgrading, please try again later"));
        }
        return true;
    }

    private void deductDiamonds(String uid, long sourceValue, String desc) throws GoldException {
        CurrencyDeductDTO currencyDeductDTO = new CurrencyDeductDTO();
        currencyDeductDTO.setUid(uid);
        currencyDeductDTO.setChangeNum(sourceValue);
        currencyDeductDTO.setCurrencyCode(CurrencyEnum.CURRENCY2.getCurrencyCode());
        currencyDeductDTO.setAction(MoneyChangeActionConstant.ACTION_DEDUCT);
        currencyDeductDTO.setOperator("redeem gold");
        currencyDeductDTO.setActDesc(desc);
        currencyDeductDTO.setActType(ActType.REDEEM);
        currencyDeductDTO.setActId(0);
        currencyDeductDTO.setSegmentCode(1);
        currencyDeductDTO.setChangeRelation(MDC.get(BaseHttpData.REQUEST_ID));
        currencyService.deduct(currencyDeductDTO);
    }

    private void addGold(String uid, long targetValue, String desc) throws GoldException {
        CurrencyAddDTO currencyAddDTO = new CurrencyAddDTO();
        currencyAddDTO.setUid(uid);
        currencyAddDTO.setChangeNum(targetValue);
        currencyAddDTO.setCurrencyCode(CurrencyEnum.CURRENCY1.getCurrencyCode());
        currencyAddDTO.setAction(MoneyChangeActionConstant.ACTION_ADD);
        currencyAddDTO.setOperator("redeem gold");
        currencyAddDTO.setActDesc(desc);
        currencyAddDTO.setActType(ActType.REDEEM);
        currencyAddDTO.setActId(0);
        currencyAddDTO.setSegmentCode(2);
        currencyAddDTO.setChangeRelation(MDC.get(BaseHttpData.REQUEST_ID));
        currencyService.add(currencyAddDTO);
    }

    private void addGoldToCoinMerchantAccount(ActorData actorData, SysUserData sysUserData, long targetValue) {
        KisCoinDealersBalanceData balanceData = kisCoinDealersBalanceDao.queryDataByUserIdAndCurrencyType(sysUserData.getUserId().intValue(), CurrencyEnum.CURRENCY1.getCurrencyCode());
        String receiveNewBalance = addBalance(balanceData.getBalance(), String.valueOf(targetValue));
        updateBalance(balanceData, sysUserData, receiveNewBalance);
        KisCoinDealersBalanceLogData logData = saveBalanceLog(sysUserData, balanceData, targetValue);
        CoinDealerChangeRecordThData thData = new CoinDealerChangeRecordThData();
        thData.copyFrom(logData, logData.getUserId(), logData.getUserId(), CoinDealerChangeRecordThData.REDEEM);
        eventReport.track(thData);
    }

    private KisCoinDealersBalanceLogData saveBalanceLog(SysUserData sysUserData, KisCoinDealersBalanceData balanceData, long targetValue) {
        KisCoinDealersBalanceLogData data = new KisCoinDealersBalanceLogData();
        data.setUserId(sysUserData.getUserId().intValue());
        data.setCurrencyType(CurrencyEnum.CURRENCY1.getCurrencyCode());
        data.setBeforeBalance(balanceData.getBalance());
        data.setChangeNum(String.valueOf(targetValue));
        data.setActType(MoneyChangeActionConstant.ACTION_ADD);
        data.setToRid(0L);
        data.setCtime(DateHelper.getCurrTime());
        data.setMtime(DateHelper.getCurrTime());
        data.setValid(1);
        data.setOperator("Redeem");
        data.setDeptId(balanceData.getDeptId());
        data.setReceiveType(3);
        data.setChannel(balanceData.getChannel());
        data.setCoinRate(balanceData.getCoinRate());
        data.setRelateId(sysUserData.getUserId().intValue());
        data.setFromType(5);
        kisCoinDealersBalanceLogDao.save(data);
        return data;
    }

    private void updateBalance(KisCoinDealersBalanceData balanceData, SysUserData sysUserData, String sendNewBalance) {
        if (balanceData == null) {
            balanceData = new KisCoinDealersBalanceData();
            balanceData.setUserId(sysUserData.getUserId().intValue());
            balanceData.setCurrencyType(CurrencyEnum.CURRENCY1.getCurrencyCode());
            balanceData.setBalance(sendNewBalance);
            balanceData.setCtime(DateHelper.getCurrTime());
            balanceData.setMtime(DateHelper.getCurrTime());
            balanceData.setOperator(sysUserData.getUserName());
            balanceData.setDeptId(sysUserData.getDeptId());
            balanceData.setChannel(sysUserData.getChannel());
            balanceData.setCoinRate(sysUserData.getCoinRate());
            kisCoinDealersBalanceDao.save(balanceData);
        } else {
            KisCoinDealersBalanceData newData = new KisCoinDealersBalanceData();
            newData.setId(balanceData.getId());
            newData.setBalance(sendNewBalance);
            newData.setMtime(DateHelper.getCurrTime());
            newData.setOperator(sysUserData.getUserName());
            kisCoinDealersBalanceDao.updateData(newData);
        }

    }

    private String addBalance(String v1, String v2) {
        return new BigDecimal(v1).add(new BigDecimal(v2)).toString();
    }

    /**
     * 获取游戏币兑换金币列表
     */
    public GameCoinRedeemListVO gameCoinRedeemList(HttpEnvData envData) {
        if (StringUtils.isEmpty(envData.getUid())) {
            throw new WebException(envData, HttpCode.PARAM_ERROR);
        }
        ActorData curActor = actorMgr.getActorData(envData.getUid());
        if (curActor == null) {
            log.info("actor is not exist,uid={}", envData.getUid());
            throw new WebException(envData, HttpCode.ACTOR_NOT_EXIST);
        }
        // 判断是否是游戏核心用户
        boolean isGameCoreUser = safeStrategyService.isGameCurrencyCoreUser(curActor);
        if (!isGameCoreUser) {
            log.info("user is not game core user, uid={}", envData.getUid());
            throw new WebException(envData, HttpCode.ILLEGAL_OPERATION);
        }
        GameCoinRedeemListVO vo = new GameCoinRedeemListVO();
        //获取倍率
        Integer goldCoinMultiplier = configApi.getIntegerVal(new ConfigDTO(envData.getUid(), AppConfigKeyConstant.GOLD_COIN_MULTIPLIER, -1));
        Integer gameCoinMultiplier = configApi.getIntegerVal(new ConfigDTO(envData.getUid(), AppConfigKeyConstant.GAME_COIN_MULTIPLIER, -1));
        // 获取游戏币余额
        BigDecimal gameBalance = currencyService.getRealCurrency4Balance(envData.getUid());
        vo.setGameBalance(gameBalance.multiply(new BigDecimal(gameCoinMultiplier)).longValue());
        // 获取金币余额
        BigDecimal goldBalance = currencyService.getRealCurrency1Balance(envData.getUid());
        vo.setGoldBalance(goldBalance.multiply(new BigDecimal(goldCoinMultiplier)).longValue());
        // 获取游戏币兑换配置列表
        String strVal = configApi.getStrVal(new ConfigDTO(envData.getUid(), AppConfigKeyConstant.GAME_COIN_REDEEM_GOLD_LIST_CONFIG, AppConfigKeyConstant.STATUS_SERVER));
        List<GameCoinRedeemData> redeemList = new ArrayList<>();
        if (!StringUtils.isEmpty(strVal)) {
            redeemList = JSONObject.parseArray(strVal, GameCoinRedeemData.class);
        }
        if (!CollectionUtils.isEmpty(redeemList)) {
            for (GameCoinRedeemData data : redeemList) {
                data.setSourceValue(data.getSourceValue() * gameCoinMultiplier);
                data.setTargetValue(data.getTargetValue() * goldCoinMultiplier);
            }
        }
        vo.setRedeemList(redeemList);
        return vo;
    }

    /**
     * 游戏币兑换金币
     */
    public void gameCoinRedeem(GameCoinRedeemDTO dto) {
        if (StringUtils.isEmpty(dto.getUid()) || dto.getSourceValue() == 0 || dto.getTargetValue() == 0) {
            throw new WebException(dto, HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorMgr.getActorData(dto.getUid());
        if (actorData == null) {
            log.info("actor is not exist,uid={}", dto.getUid());
            throw new WebException(dto, HttpCode.ACTOR_NOT_EXIST);
        }
        // 判断是否是游戏核心用户
        boolean isGameCoreUser = safeStrategyService.isGameCurrencyCoreUser(actorData);
        if (!isGameCoreUser) {
            log.info("user is not game core user, uid={}", dto.getUid());
            throw new WebException(dto, HttpCode.ACTOR_NOT_EXIST);
        }
        //获取倍率
        Integer goldCoinMultiplier = configApi.getIntegerVal(new ConfigDTO(dto.getUid(), AppConfigKeyConstant.GOLD_COIN_MULTIPLIER, -1));
        Integer gameCoinMultiplier = configApi.getIntegerVal(new ConfigDTO(dto.getUid(), AppConfigKeyConstant.GAME_COIN_MULTIPLIER, -1));
        dto.setTargetValue(dto.getTargetValue() / goldCoinMultiplier);
        dto.setSourceValue(dto.getSourceValue() / gameCoinMultiplier);
        // 校验兑换比例是否合法
        if (noValidateExchangeRate(dto)) {
            log.info("exchange rate invalid, dto={}", dto);
            throw new WebException(dto, HttpCode.PARAM_ERROR);
        }
        // 检查游戏币余额
        BigDecimal gameBalance = currencyService.getRealCurrency4Balance(dto.getUid());
        if (gameBalance.compareTo(new BigDecimal(dto.getSourceValue())) < 0) {
            log.info("game points balance not enough, balance={}, need={}", gameBalance, dto.getSourceValue());
            throw new WebException(dto, HttpCode.BALANCE_NOT_ENOUGH);
        }
        try {
            // 扣减游戏币
            deductGamePoints(dto);
            // 增加金币
            addGold(dto);
        } catch (Exception e) {
            log.error("game points redeem error, dto={}", dto, e);
            throw new WebException(dto, HttpCode.SERVER_ERROR);
        }
    }

    /**
     * 校验兑换比例是否合法
     */
    private boolean noValidateExchangeRate(GameCoinRedeemDTO dto) {
        ConfigDTO configDTO = new ConfigDTO();
        configDTO.setUid(dto.getUid());
        configDTO.setKey(AppConfigKeyConstant.GAME_COIN_REDEEM_GOLD_LIST_CONFIG);
        configDTO.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        String strVal = configApi.getStrVal(configDTO);
        if (StringUtils.isEmpty(strVal)) {
            return true;
        }
        List<GameCoinRedeemData> redeemList = JSONObject.parseArray(strVal, GameCoinRedeemData.class);
        if (CollectionUtils.isEmpty(redeemList)) {
            return true;
        }
        for (GameCoinRedeemData data : redeemList) {
            if (data.getSourceValue() == dto.getSourceValue() && data.getTargetValue() == dto.getTargetValue()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 扣减游戏币
     */
    private void deductGamePoints(GameCoinRedeemDTO dto) {
        CurrencyDeductDTO currencyDeductDTO = new CurrencyDeductDTO();
        currencyDeductDTO.setUid(dto.getUid());
        currencyDeductDTO.setRealChangeNum(String.valueOf(dto.getSourceValue()));
        currencyDeductDTO.setCurrencyCode(CurrencyEnum.CURRENCY4.getCurrencyCode());
        currencyDeductDTO.setAction(MoneyChangeActionConstant.ACTION_DEDUCT);
        currencyDeductDTO.setOperator("game coin redeem");
        currencyDeductDTO.setActDesc("Pcoins to Coins");
        currencyDeductDTO.setActType(ActType.GAME_COIN_EXCHANGE_GOLD);
        currencyDeductDTO.setActId(0);
        currencyDeductDTO.setSegmentCode(1);
        currencyDeductDTO.setChangeRelation(MDC.get(BaseHttpData.REQUEST_ID));
        currencyService.deduct(currencyDeductDTO);
    }

    /**
     * 增加金币
     */
    private void addGold(GameCoinRedeemDTO dto) {
        CurrencyAddDTO currencyAddDTO = new CurrencyAddDTO();
        currencyAddDTO.setUid(dto.getUid());
        currencyAddDTO.setRealChangeNum(String.valueOf(dto.getTargetValue()));
        currencyAddDTO.setCurrencyCode(CurrencyEnum.CURRENCY1.getCurrencyCode());
        currencyAddDTO.setAction(MoneyChangeActionConstant.ACTION_ADD);
        currencyAddDTO.setOperator("game coin redeem");
        currencyAddDTO.setActDesc("Pcoins to Coins");
        currencyAddDTO.setActType(ActType.GAME_COIN_EXCHANGE_GOLD);
        currencyAddDTO.setActId(0);
        currencyAddDTO.setSegmentCode(2);
        currencyAddDTO.setChangeRelation(MDC.get(BaseHttpData.REQUEST_ID));
        currencyService.add(currencyAddDTO);
    }
}
