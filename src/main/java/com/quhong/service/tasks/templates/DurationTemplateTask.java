package com.quhong.service.tasks.templates;

import com.alibaba.fastjson.JSON;
import com.quhong.common.enums.HttpCode;
import com.quhong.core.constant.WarnName;
import com.quhong.dao.datas.ActorData;
import com.quhong.data.bo.TemplateTaskBO;
import com.quhong.data.bo.TemplateTaskProgressBO;
import com.quhong.data.dto.TemplateTaskListDTO;
import com.quhong.data.dto.TemplateTaskRewardDTO;
import com.quhong.data.prop.*;
import com.quhong.data.reports.TaskCompleteRecordData;
import com.quhong.entity.TemplateTaskRecordData;
import com.quhong.enums.PeriodType;
import com.quhong.enums.TaskTemplateType;
import com.quhong.enums.TemplateTaskRecordStatus;
import com.quhong.exceptions.WebException;
import com.quhong.players.ActorMgr;
import com.quhong.socket.msg.IProto;
import com.quhong.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class DurationTemplateTask extends AbstractTemplateTask {
    @Resource
    private ActorMgr actorMgr;
    @Resource
    private TemplateTaskPeriodService templateTaskPeriodService;

    public DurationTemplateTask() {
        super(TaskTemplateType.DURATION, "");
    }

    public DurationTemplateTask(String taskName){
        super(TaskTemplateType.DURATION, taskName);
    }

    @Override
    public IProto<?> show(TemplateTaskListDTO dto, TemplateTaskBO bo) {
        TemplateTaskRecordData recordData = templateTaskRecordService.getAndCreateRecordData(dto.getUid(), bo.getData(), "", DurationTaskProp.class);
        DurationTaskProp prop = JSON.parseObject(recordData.getRecordJson(), DurationTaskProp.class);
        if (prop.getItem() == null || prop.getItem().isEmpty()) {
            log.error("item is empty. taskName={}", bo.getData().getTaskName());
            monitorSender.info(WarnName.COMMON, "配置节点为空.", "taskName=" + bo.getData().getTaskName());
            return null;
        }
        // 检查并重置
        templateTaskPeriodService.checkAndReset(dto.getUid(), recordData, prop, this::report);
        return prop;
    }

    @Override
    public int completeCount(TemplateTaskListDTO dto, TemplateTaskBO bo) {
        TemplateTaskRecordData recordData = templateTaskRecordService.getAndCreateRecordData(dto.getUid(), bo.getData(), "", DurationTaskProp.class);
        DurationTaskProp prop = JSON.parseObject(recordData.getRecordJson(), DurationTaskProp.class);
        if (prop.getItem() == null || prop.getItem().isEmpty()) {
            log.error("item is empty. taskName={}", bo.getData().getTaskName());
            monitorSender.info(WarnName.COMMON, "配置节点为空.", "taskName=" + bo.getData().getTaskName());
            return 0;
        }
        int count = 0;
        for(DurationNodeItemProp item : prop.getItem()){
            if(item.getTaskStatus() == TemplateTaskRecordStatus.COMPLETED){
                count ++;
            }
        }
        return count;
    }

    @Override
    public IProto<?> reward(TemplateTaskRewardDTO dto) {
        TemplateTaskRecordData recordData = templateTaskRecordService.getAndCreateRecordData(dto.getUid(), dto.getData(), "");
        if (recordData.getRecordStatus() == TemplateTaskRecordStatus.REWARDED) {
            log.error("has rewarded, taskName={}", dto.getData().getTaskName());
            throw new WebException(dto, HttpCode.REPEATED_GET_REWARDS);
        }
        DurationTaskProp prop = JSON.parseObject(recordData.getRecordJson(), DurationTaskProp.class);
        if (prop.getItem() == null || prop.getItem().isEmpty()) {
            log.error("item is empty. taskName={}", dto.getData().getTaskName());
            monitorSender.info(WarnName.COMMON, "配置节点为空.", "taskName=" + dto.getData().getTaskName());
            throw new WebException(dto, HttpCode.PARAM_ERROR);
        }
        DurationNodeItemProp itemProp = prop.getItemByNodeId(dto.getNodeId(), dto.getDay());
        if (itemProp == null) {
            log.error("do not find nodeId. taskName={} uid={}", dto.getData().getTaskName(), dto.getUid());
            throw new WebException(dto, HttpCode.PARAM_ERROR);
        }
        if (itemProp.getTaskStatus() == TemplateTaskRecordStatus.REWARDED) {
            log.error("has rewarded, taskName={} nodeId={}", dto.getData().getTaskName(), dto.getNodeId());
            throw new WebException(dto, HttpCode.REPEATED_GET_REWARDS);
        }
        if (itemProp.getTaskStatus() != TemplateTaskRecordStatus.COMPLETED) {
            log.error("can not receive reward. taskName={} uid={}", dto.getData().getTaskName(), dto.getUid());
            throw new WebException(dto, HttpCode.CAN_NOT_RECEIVE_REWARD);
        }
        itemProp.setTaskStatus(TemplateTaskRecordStatus.REWARDED);
        templateTaskRecordService.updateRecordData(recordData, prop);
        // 发送奖励
        log.info("reward success. taskName={} nodeId={} uid={}", dto.getData().getTaskName(), dto.getNodeId(), dto.getUid());
        templateTaskService.sendRewards(recordData, "", itemProp.getAward(), itemProp.getDataSign(), itemProp.getGoldDesc());
        report(recordData, prop, itemProp);
        return prop;
    }

    @Override
    public void reset(String uid, TemplateTaskBO bo) {
        TemplateTaskRecordData recordData = templateTaskRecordService.getAndCreateRecordData(uid, bo.getData(), "");
        recordData.setRecordStatus(TemplateTaskRecordStatus.PROGRESS);
        DurationTaskProp prop = JSON.parseObject(recordData.getRecordJson(), DurationTaskProp.class);
        if (prop.getItem() == null || prop.getItem().isEmpty()) {
            log.error("item is empty. taskName={}", bo.getData().getTaskName());
            monitorSender.info(WarnName.COMMON, "配置节点为空.", "taskName=" + bo.getData().getTaskName());
            return;
        }
        for (DurationNodeItemProp item : prop.getItem()) {
            item.setCompleteCount(0);
            item.setCompleteSeconds(0);
            item.setTaskStatus(TemplateTaskRecordStatus.NONE);
        }
        prop.setStartTime(null);
        prop.setCurDay(null);
        prop.setTimeZone(null);
        templateTaskRecordService.updateRecordData(recordData, prop);
    }

    @Override
    public boolean checkComplete(TemplateTaskProgressBO bo) {
        return false;
    }

    @Override
    public void updateCompleteCount(TemplateTaskProgressBO bo, Integer value, boolean check, boolean set) {
        //TODO
    }

    @Override
    public void updateCompleteTime(TemplateTaskProgressBO bo, int seconds, boolean check) {

    }

    @Override
    public void updateCompleteDay(TemplateTaskProgressBO bo, int seconds, boolean check) {
        //TODO
    }

    @Override
    public Class<? extends TaskProp> getTaskPropClass() {
        return DurationTaskProp.class;
    }

    public void report(TemplateTaskRecordData recordData, DurationTaskProp prop, DurationNodeItemProp itemProp){
        TaskCompleteRecordData data = new TaskCompleteRecordData();
        ActorData actorData = actorMgr.getActorDataFromCache(recordData.getUid());
        data.copyFrom(actorData.getChannel(), recordData);
        data.setNode_name(itemProp.getNodeId());
        data.setTask_progress(itemProp.getCompleteCount() + "/" + itemProp.getTotalCount());
        data.setDay(itemProp.getDay());
        data.setTask_date(templateTaskPeriodService.getDateSign(actorData.getUid(), PeriodType.DAILY).getSign());
        data.setTask_status(itemProp.getTaskStatus());
        templateTaskRecordService.report(itemProp.getEventId(), data);
    }

    @Override
    public ReportFunction<DurationTaskProp, DurationNodeItemProp> getReportFunction() {
        return this::report;
    }
}
