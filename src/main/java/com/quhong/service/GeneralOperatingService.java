package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.ApiResult;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.ABSideConstant;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.constant.LangIdConstant;
import com.quhong.constant.SafeStrategyTypeConstant;
import com.quhong.constant.general.operating.GeneralOperatingSceneConstant;
import com.quhong.constant.general.operating.GeneralOperatingTypeConstant;
import com.quhong.core.constant.RedisCacheManagerConstant;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.*;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.ActorSourceData;
import com.quhong.dao.datas.GeneralOperatingConfigData;
import com.quhong.dao.datas.OfficialNotice;
import com.quhong.dao.dto.OperatingConfigDTO;
import com.quhong.dao.dto.OperationConfigBO;
import com.quhong.dao.protobuf.OperatingConfigResp;
import com.quhong.dao.slave.mapper.db.RewardInfoSlaveMapper;
import com.quhong.dao.vo.OperatingConfigVo;
import com.quhong.data.appConfig.payment.RepurchaseConfig;
import com.quhong.data.bo.generate.operating.GenerateOperatingConfigBO;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.enums.OfficialNoticeId;
import com.quhong.players.ActorMgr;
import com.quhong.service.payment.RepurchaseService;
import com.quhong.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import com.quhong.core.cache.CacheMap;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Description: 通用运营位配置service层
 *
 * <AUTHOR>
 * @date 2021/10/30 16:43
 */
@Service
@Slf4j
public class GeneralOperatingService {
    private final Set<Integer> h5Type = new LinkedHashSet<>();
    private final Set<String> channelSet = new LinkedHashSet<>();
    private final static String APPSTORE_YOKU_CHANNEL = "appstore_yoku";

    @Resource
    private GeneralOperatingConfigDao generalOperatingConfigDao;
    @Resource
    private ActorMgr actorMgr;
    @Resource
    private RewardInfoSlaveMapper rewardInfoSlaveMapper;
    @Resource
    private ConfigApi configApi;
    @Resource
    private ActorSourceDao actorSourceDao;
    @Resource
    private GrayService grayService;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private CdnUtils cdnUtils;
    @Resource
    private ActorExternalDao actorExternalDao;
    @Resource
    private SafeStrategyService safeStrategyService;
    @Resource
    private GeneralOperatingService generalOperatingService;
    @Resource
    private RepurchaseService repurchaseService;
    @Resource
    private OfficialNoticeService officialNoticeService;
    @Resource
    private OfficialNoticeDao officialNoticeDao;
    /**
     * 长期缓存用户复购条件检查结果，跨请求使用
     * 缓存时间设置为60秒，避免过长时间缓存导致用户状态变化后仍使用旧数据
     */
    private final CacheMap<String, Boolean> repurchaseConditionLongCache = new CacheMap<>(30 * 1000);
    /**
     * 发送过通知记录
     */
    private final CacheMap<String, Boolean> repurchaseNoticeCache = new CacheMap<>(60 * 1000);

    public GeneralOperatingService() {
        h5Type.add(GeneralOperatingTypeConstant.EGG_TYPE);
        h5Type.add(GeneralOperatingTypeConstant.EGG_WEB_TYPE);
        h5Type.add(GeneralOperatingTypeConstant.WEB_TYPE);
        h5Type.add(GeneralOperatingTypeConstant.POPUP_WEB_TYPE);
        h5Type.add(GeneralOperatingTypeConstant.JUMP_OUTER_BROWSER_H5);
        channelSet.add("appstore_rakoo");
        channelSet.add("appstore_dada");
        channelSet.add("appstore_nici");
        channelSet.add("appstore_chacha");
//        mediumSet.add("restricted");
//        mediumSet.add("googleadwords_int");
        repurchaseConditionLongCache.start(); // 启动缓存清理任务
    }

    /**
     * Description: 返回通用运营位列表
     *
     * @param dto 公参集
     * @return com.quhong.common.data.ApiResult<com.quhong.data.protobuf.OperatingConfigResp>
     * <AUTHOR>
     * @date 2021/10/30 16:54
     */
    public ApiResult<OperatingConfigResp> getOperatingConfigResp(OperatingConfigDTO dto) {
        boolean isAdmin = actorExternalDao.isReviewer(dto.getUid()) || ABSideConstant.SIDE_A.equals(actorConfigDao.getConfigStringValue(dto.getUid(), ActorConfigDao.LAST_AB_SIDE));
        if (ABSideConstant.SIDE_B.equals(actorConfigDao.getConfigStringValue(dto.getUid(), ActorConfigDao.LAST_AB_SIDE))) {
            isAdmin = false;
        }
        ActorData actorData = actorMgr.getActorDataFromCache(dto.getUid());
        if (actorData == null) {
            return ApiResult.getError(HttpCode.ACTOR_NOT_EXIST);
        }
        if (APPSTORE_YOKU_CHANNEL.equals(actorData.getChannel())) {
            //appstore_yoku不下发banner
            return ApiResult.getOk();
        }
        log.debug("start to getOperatingConfigResp method. isAdmin={} dto={} requestId={}", isAdmin, dto, dto.getRequestId());
        OperatingConfigResp resp = new OperatingConfigResp();
        List<GeneralOperatingConfigData> dataList = generalOperatingConfigDao.queryListByChannel(actorData, dto.getChannel(), dto.getScene(), 1);
        String clientSysLang = getClientSysLang(actorData);
        OperationConfigBO operationConfigBO = new OperationConfigBO();
        operationConfigBO.setAdmin(isAdmin);
        //数据拼装
        boolean finalIsAdmin = isAdmin;
        List<OperatingConfigVo> datas = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            datas = dataList.parallelStream()
                    .filter(this::isUp)
                    .filter(data -> !verControl(operationConfigBO, actorData, data))
                    .filter(data -> data.getType() != GeneralOperatingTypeConstant.SLOT_TYPE || !channelSet.contains(actorData.getChannel()))
                    .map(data -> genVO(dto, data, actorData, clientSysLang))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        resp.setDatas(datas);
        //下发所有icon
        List<String> icons = generalOperatingService.queryIcon();
        List<String> newIcons = new ArrayList<>();
        for (String icon : icons) {
            String replace = cdnUtils.replaceUrlDomain(actorData.getChannel(), icon, 0);
            newIcons.add(replace);
        }
        resp.setIcons(newIcons);
        return ApiResult.getOk(resp);
    }

    @Cacheable(value = "str:genOperating:queryIcon", key = "methodName", cacheManager = RedisCacheManagerConstant.CACHE_5_MINUTE_MANAGER)
    public List<String> queryIcon() {
        return rewardInfoSlaveMapper.getIcons();
    }

    private OperatingConfigVo genVO(OperatingConfigDTO dto, GeneralOperatingConfigData data, ActorData actorData, String clientSysLang) {
        OperatingConfigVo vo = new OperatingConfigVo();
        vo.setIcon(getReplaceCdnIcon(dto, data));
        vo.setType(data.getType());
        vo.setTitle(data.getDesc());
        vo.setUrl(data.getUrl());
        vo.setNewLabel(data.getIsNew());
        try {
            if (checkConfig(dto, data, vo, actorData))
                return null;
        } catch (Exception e) {
            log.error("error to check Config,dto={},msg={}", dto, e.getMessage(), e);
            return null;
        }
        replaceUrlParams(dto, data, vo, actorData.getRid(), clientSysLang);
        return vo;
    }

    private String getClientSysLang(ActorData actorData) {
        String clientSysLang = actorExternalDao.getClientSysLang(actorData.getUid());
        if (StringUtils.isEmpty(clientSysLang)) {
            clientSysLang = LangIdConstant.ENGLISH;
        }
        return clientSysLang;
    }

    private String getReplaceCdnIcon(OperatingConfigDTO dto, GeneralOperatingConfigData data) {
        String icon = data.getIcon();
        icon = cdnUtils.replaceUrlDomain(dto.getChannel(), icon, 0);
        return icon;
    }

    /**
     * 判定是否处于上架时间区间内
     *
     * @param data 通用运营位配置数据
     * @return true 可上架 false 不可上架
     */
    private boolean isUp(GeneralOperatingConfigData data) {
        boolean isUp = true;
        if (data.getDownTime() != 0) {//下架时间为0 则判定为永久上架项
            long currentTime = DateHelper.getCurrTime();
            if (currentTime < data.getUpTime() || currentTime > data.getDownTime()) {
                isUp = false;
            }
        }
        return isUp;
    }

    /**
     * 链接参数填充
     *
     * @param dto
     * @param data
     * @param vo
     * @param rid
     * @param clientSysLang
     */
    private void replaceUrlParams(OperatingConfigDTO dto, GeneralOperatingConfigData data, OperatingConfigVo vo, Long rid, String clientSysLang) {
//        if (vo.getUrl().contains("#uid")) {
//            vo.setUrl(vo.getUrl().replace("#uid", dto.getUid()));
//        }
        if (vo.getUrl().contains("#rid")) {
            vo.setUrl(vo.getUrl().replace("#rid", rid.toString()));
        }
        if (vo.getUrl().contains("#isapp")) {
            vo.setUrl(vo.getUrl().replace("#isapp", "1"));
        }

        if (h5Type.contains(data.getType())) {
            vo.setUrl(mergeUrl(vo.getUrl(), dto.getUid(), dto.getSession(), dto.getChannel(), clientSysLang, dto.getVer(), dto.getRoomId()));
        }
    }

    /**
     * 拉取配置并处理
     *
     * @param dto
     * @param data
     * @param vo
     * @param actorData
     * @return true:不显示 false:显示
     */
    private boolean checkConfig(OperatingConfigDTO dto, GeneralOperatingConfigData data, OperatingConfigVo vo, ActorData actorData) throws Exception {
        if (!StringUtils.isEmpty(data.getConfigKey())) {
            GenerateOperatingConfigBO configBO = getGenerateOperatingConfigBO(dto, data);
            log.debug("operating config bo ={}", JSON.toJSONString(configBO));
            return checkConfig(dto, vo, actorData, configBO);
        }
        return false;
    }

    /**
     * 拉取配置并处理
     *
     * @param dto
     * @param configBO
     * @param vo
     * @param actorData
     * @return true:不显示 false:显示
     */
    private boolean checkConfig(OperatingConfigDTO dto, OperatingConfigVo vo, ActorData actorData, GenerateOperatingConfigBO configBO) throws Exception {
        if (configBO != null) {
            if (configBO.getPowerSwitch() != null && configBO.getPowerSwitch() == 0) {
                return true;
            }
            //灰度判定
            boolean gray = grayService.isGray(actorData, configBO);
            if (!gray) {
                return true;
            }
            //AB面展示逻辑
            if (!checkShowUser(dto, configBO))
                return true;

            if (!StringUtils.isEmpty(configBO.getIcon())) {
                vo.setIcon(configBO.getIcon());
            }
            if (!StringUtils.isEmpty(configBO.getUrl())) {
                vo.setUrl(configBO.getUrl());
            }
            if (!StringUtils.isEmpty((configBO.getUrl()))) {
                vo.setTitle(configBO.getTitle());
            }
        }
        return false;
    }

    /**
     * 确定指定来源用户展示
     *
     * @param dto
     * @param configBO
     * @return
     */
    private boolean checkShowUser(OperatingConfigDTO dto, GenerateOperatingConfigBO configBO) throws Exception {
        boolean isAFace = checkIsAFaceUser(dto, configBO);
        boolean isShow = (configBO.getShowMedium() > 0) == isAFace;
        if (isShow) {
            //最后特定逻辑处理
            if (configBO.getMinBossPoint() != 0 || configBO.getMinRechargeTimes() != 0) {
                int bossPoints = actorConfigDao.getConfigIntValue(dto.getUid(), ActorConfigDao.BOSS_POINTS);
                int rechargeTimes = actorConfigDao.getConfigIntValue(dto.getUid(), ActorConfigDao.HAD_TIMES_RECHARGE);
                log.debug("uid={},bossPoints={},rechargeTimes={},configBo={}",
                        dto.getUid(), bossPoints, rechargeTimes, JSON.toJSONString(configBO));
                if (configBO.getIsAnd() == 1) {
                    isShow = bossPoints >= configBO.getMinBossPoint() && rechargeTimes >= configBO.getMinRechargeTimes();
                } else {
                    isShow = bossPoints >= configBO.getMinBossPoint() || rechargeTimes >= configBO.getMinRechargeTimes();
                }
            }
        }
        return isShow;
    }

    /**
     * 判定是否是A面用户
     *
     * @param dto
     * @param configBO
     * @return
     */
    private boolean checkIsAFaceUser(OperatingConfigDTO dto, GenerateOperatingConfigBO configBO) throws Exception {
        //钱包页 第三方支付banner 面向用户判定
        if (ObjectUtils.isEmpty(configBO.getMediumList())) {
            return false;
        }
        ActorSourceData sourceData = actorSourceDao.getOneByUid(dto.getUid());
        return sourceData != null && configBO.getMediumList().contains(sourceData.getMedium());
    }

    private GenerateOperatingConfigBO getGenerateOperatingConfigBO(OperatingConfigDTO dto, GeneralOperatingConfigData data) throws Exception {
        ConfigDTO configDTO = new ConfigDTO();
        configDTO.setKey(data.getConfigKey());
        configDTO.setUid(dto.getUid());
        configDTO.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        return configApi.getJavaBeanVal(configDTO, GenerateOperatingConfigBO.class);
    }

    /**
     * 版本控制
     *
     * @param configBO
     * @param actorData
     * @param data
     * @return true 不显示， false 显示
     */
    private boolean verControl(OperationConfigBO configBO, ActorData actorData, GeneralOperatingConfigData data) {
        if (data.getType() == GeneralOperatingTypeConstant.REPURCHASE_RECOMMEND) {
            return !isRepurchaseConditionMet(configBO, actorData);
        }
        if (configBO.isAdmin() && data.getType() != GeneralOperatingTypeConstant.ACTOR_SIGN_TYPE) {
            return true;
        }
        //签到新版本下架悬浮位
        if (checkHasSignIcon(actorData, data)) {
            return true;
        }
        //运营位用户群体判断
        if (safeStrategyService.decideUserTypeFromGeneral(actorData, data, SafeStrategyTypeConstant.GAME_SHOW_STRATEGY)) {
            return true;
        }
        //充值优惠版本控制
        if (checkHasRechargeDiscount(actorData, data)) {
            return true;
        }
        return false;
    }

    /**
     * 检查用户是否满足复购条件，使用缓存优化性能
     *
     * @param configBO  配置对象
     * @param actorData 用户ID
     * @return true 满足复购条件， false 不满足
     */
    private boolean isRepurchaseConditionMet(OperationConfigBO configBO, ActorData actorData) {
        String uid = actorData.getUid();
        if (configBO.getRepurchase() != null) {
            log.info("isRepurchaseConditionMet: uid={}, configBO={}", uid, configBO);
            return configBO.getRepurchase();
        }

        // 2. 查长期缓存（多次请求间共享）
        Boolean cachedResult = repurchaseConditionLongCache.getData(uid);
        if (cachedResult != null) {
            configBO.setRepurchase(cachedResult);
            return cachedResult;
        }
        RepurchaseConfig repurchaseConfig = repurchaseService.getRepurchaseConfig(uid);
        // 3. 实际执行复购条件检查
        int result = repurchaseService.doCheckRepurchaseCondition(uid, repurchaseConfig);
        log.info("isRepurchaseConditionMet: uid={}, result={}", uid, result);
        configBO.setRepurchase(result > 0);
        repurchaseConditionLongCache.cacheData(uid, result > 0);
        String cacheKey = uid + "_" + result;
        if (result > 0 && (!repurchaseNoticeCache.hasData(cacheKey))) {
            RepurchaseConfig.RepurchaseItem repurchaseItem = repurchaseConfig.getItems().get(result - 1);
            int noticeId = OfficialNoticeId.REPURCHASE_GOODS_NOTICE_1 + result - 1;
            OfficialNotice officialNotice = officialNoticeDao.getData(uid, noticeId);
            if (officialNotice == null) {
                String title = repurchaseItem.getNoticeTitle();
                String img = repurchaseItem.getNoticeImg();
                String content = repurchaseItem.getNoticeContent();
                officialNoticeService.sendOfficialNotice(uid, title, content, img, "https://ibb.co/7xWGJQNM", actorData.getChannel(), DateHelper.getCurrentTime(), noticeId);
                log.info("send notice. uid={}", uid);
            } else {
                log.info("notice has send. uid={}", uid);
            }
            repurchaseNoticeCache.cacheData(cacheKey, true);
        }
        return result > 0;
    }

    /**
     * 优惠充值 版本控制
     *
     * @param actorData
     * @param data
     * @return true 不显示， false 显示
     */
    private boolean checkHasRechargeDiscount(ActorData actorData, GeneralOperatingConfigData data) {
        //优惠充值banner位 版本控制
        if (data.getType() == GeneralOperatingTypeConstant.RECHARGE_DISCOUNT) {
            if (ChannelEnum.APPSTORE_HARA.getName().equals(actorData.getChannel()) && actorData.getVer() >= 289) {
                //显示
                return false;
            } else if (ChannelEnum.APPSTORE_NICI.getName().equals(actorData.getChannel()) && actorData.getVer() >= 291) {
                return false;
            } else if (ChannelEnum.CDD.getName().equals(actorData.getChannel()) && actorData.getVer() >= 382) {
                return false;
            } else if (ChannelEnum.CDE.getName().equals(actorData.getChannel()) && actorData.getVer() >= 378) {
                return false;
            } else if (ChannelEnum.CDC.getName().equals(actorData.getChannel()) && actorData.getVer() >= 380) {
                return false;
            } else if (ChannelEnum.APPSTORE_CAMCHAT.getName().equals(actorData.getChannel()) && actorData.getVer() >= 293) {
                return false;
            } else if (ChannelEnum.HUAWEI_CHACHA.getName().equals(actorData.getChannel()) && actorData.getVer() >= 406) {
                return false;
            } else {
                return true;
            }
        }
        //显示
        return false;
    }

    /**
     * 校验popular页面悬浮运营位是否下发签到位
     *
     * @param actorData
     * @param data
     * @return
     */
    private boolean checkHasSignIcon(ActorData actorData, GeneralOperatingConfigData data) {
        if (GeneralOperatingSceneConstant.POPULAR_PAGE.equals(data.getScene())) {
            if (data.getType() == GeneralOperatingTypeConstant.ACTOR_SIGN_TYPE) {
                if (ChannelEnum.APPSTORE_HARA.getName().equals(actorData.getChannel()) && actorData.getVer() >= 289) {
                    return true;
                } else if (ChannelEnum.APPSTORE_NICI.getName().equals(actorData.getChannel()) && actorData.getVer() >= 291) {
                    return true;
                } else if (ChannelEnum.CDD.getName().equals(actorData.getChannel()) && actorData.getVer() >= 382) {
                    return true;
                } else if (ChannelEnum.CDE.getName().equals(actorData.getChannel()) && actorData.getVer() >= 378) {
                    return true;
                } else if (ChannelEnum.CDC.getName().equals(actorData.getChannel()) && actorData.getVer() >= 380) {
                    return true;
                } else if (ChannelEnum.APPSTORE_CAMCHAT.getName().equals(actorData.getChannel()) && actorData.getVer() >= 293) {
                    return true;
                } else if (ChannelEnum.HUAWEI_CHACHA.getName().equals(actorData.getChannel()) && actorData.getVer() >= 406) {
                    return true;
                } else if (ChannelEnum.CD_LIVE.getName().equals(actorData.getChannel()) && actorData.getVer() >= 458) {
                    return true;
                } else {
                    return false;
                }
            }
        }
        return false;
    }

    /**
     * 拼url
     */
    private String mergeUrl(String url, String uid, String session, String channel, String lang, int ver, String roomId) {
        log.debug("merge url. url={} uid={} session={} channel={} lang={} ver={}", url, uid, session, channel, lang, ver);
        url += url.contains("?") ? "&" : "?";
        url += "uid=" + uid + "&session=" + session + "&channel=" + channel + "&lang_id=" + lang + "&ver=" + ver + "&room_id=" + roomId;

        // 为复购推荐类型添加额外参数
        if (url.contains("repurchase=1")) {
            url += "&isRepurchase=1";
        }

        return url;
    }
}
