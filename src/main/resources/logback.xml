<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="log.base" value="logs"/>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger:%L %M -%msg%n</pattern>
        </encoder>
    </appender>

    <appender name="logfile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${log.base}/info.log</File>
        <encoder>
            <pattern>%d{MM-dd HH:mm:ss.SSS} [%X{request_id}] [%thread] %-5level %logger{36}:%L %M -%msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.base}/info-%d{yyyyMMdd}-%i.log
            </fileNamePattern>
            <MaxHistory>1</MaxHistory>
            <TimeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <MaxFileSize>2GB</MaxFileSize>
            </TimeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <appender name="errorLogFile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${log.base}/error.log</File>
        <encoder>
            <pattern>%d{MM-dd HH:mm:ss.SSS} [%X{request_id}] [%thread] %-5level %logger{36}:%L %M -%msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.base}/error-%d{yyyyMMdd}-%i.log
            </fileNamePattern>
            <MaxHistory>7</MaxHistory>
            <TimeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <MaxFileSize>2GB</MaxFileSize>
            </TimeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter"><!-- 只打印错误日志 -->
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="messageLogFile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${log.base}/message.log</File>
        <encoder>
            <pattern>%d{MM-dd HH:mm:ss.SSS} -%msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.base}/message-%d{yyyyMMddHH}-%i.log
            </fileNamePattern>
            <MaxHistory>24</MaxHistory>
            <TimeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <MaxFileSize>2GB</MaxFileSize>
            </TimeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>
    <appender name="async" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>1000000</queueSize>
        <appender-ref ref="logfile"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <appender name="asyncError" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>1000000</queueSize>
        <appender-ref ref="errorLogFile"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <appender name="asyncMessage" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>1000000</queueSize>
        <appender-ref ref="messageLogFile"/>
        <includeCallerData>true</includeCallerData>
    </appender>
    <!-- messageLog 现网日志 additivity要改为false -->
    <logger name="messageLog" additivity="true">
        <level value="info"/>
        <appender-ref ref="asyncMessage"/>
    </logger>
    <!-- mongodb debug -->
    <!--      <logger name="com.quhong" additivity="true">-->
    <!--      <level value="debug" />-->
    <!--      <appender-ref ref="asyncMessage" />-->
    <!--      </logger>-->

    <root level="info">
<!--        <appender-ref ref="stdout"/>-->
<!--        <appender-ref ref="logfile"/>-->
        <appender-ref ref="async"/>
        <appender-ref ref="asyncError"/>
    </root>
</configuration>