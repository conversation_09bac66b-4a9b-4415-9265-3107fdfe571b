package com.quhong.admin.service.sys;

import com.quhong.dao.data.AdminAuthData;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.*;

/**
 * 权限继承逻辑测试类
 * 测试权限继承的两种情况：
 * 1. 同时拥有父权限和子权限：只展示父权限和用户实际拥有的子权限
 * 2. 只有父权限：展示父权限下的所有子权限（继承逻辑）
 */
@SpringBootTest
@ActiveProfiles("test")
public class LocalAuthInheritanceTest {

    @Resource
    private AdminAuthService adminAuthService;

    /**
     * 测试权限继承逻辑
     */
    @Test
    public void testAuthInheritanceLogic() {
        System.out.println("=== 权限继承逻辑测试 ===");

        // 获取所有权限，分析权限结构
        List<AdminAuthData> allAuths = adminAuthService.getAllValidAuths();
        Map<Integer, List<AdminAuthData>> parentChildMap = buildParentChildMap(allAuths);

        System.out.println("权限结构分析:");
        printAuthStructure(allAuths, parentChildMap);

        // 测试场景1：只有父权限的情况
        testOnlyParentAuth(parentChildMap, allAuths);

        // 测试场景2：同时拥有父权限和部分子权限的情况
        testParentAndPartialChildAuth(parentChildMap, allAuths);
    }

    /**
     * 测试场景1：只有父权限的情况
     */
    private void testOnlyParentAuth(Map<Integer, List<AdminAuthData>> parentChildMap, List<AdminAuthData> allAuths) {
        System.out.println("\n=== 测试场景1：只有父权限 ===");

        // 找一个有子权限的父权限
        Integer parentAuthId = findParentWithChildren(parentChildMap);
        if (parentAuthId == null) {
            System.out.println("未找到有子权限的父权限，跳过测试");
            return;
        }

        String parentAuthName = allAuths.stream()
                .filter(auth -> auth.getAuthId().equals(parentAuthId))
                .map(AdminAuthData::getAuthName)
                .findFirst()
                .orElse("未知");

        System.out.println("测试父权限: " + parentAuthName + " (ID: " + parentAuthId + ")");

        // 模拟用户只有父权限
        List<AdminAuthData> userAuths = Arrays.asList(createAuthData(parentAuthId, parentAuthName, 0));

        // 构建权限树
        List<AdminAuthData> authTree = adminAuthService.getAuthTree(userAuths);

        System.out.println("权限树结果:");
        printAuthTree(authTree, 0);

        // 验证：应该包含父权限下的所有子权限
        List<AdminAuthData> expectedChildren = parentChildMap.get(parentAuthId);
        if (expectedChildren != null) {
            System.out.println("预期子权限数量: " + expectedChildren.size());
            int actualChildrenCount = countChildrenInTree(authTree, parentAuthId);
            System.out.println("实际子权限数量: " + actualChildrenCount);

            if (actualChildrenCount >= expectedChildren.size()) {
                System.out.println("✅ 权限继承逻辑正确：只有父权限时显示所有子权限");
            } else {
                System.out.println("❌ 权限继承逻辑错误：子权限数量不足");
            }
        }
    }

    /**
     * 测试场景2：同时拥有父权限和部分子权限的情况
     */
    private void testParentAndPartialChildAuth(Map<Integer, List<AdminAuthData>> parentChildMap, List<AdminAuthData> allAuths) {
        System.out.println("\n=== 测试场景2：父权限+部分子权限 ===");

        // 找一个有多个子权限的父权限
        Integer parentAuthId = findParentWithMultipleChildren(parentChildMap);
        if (parentAuthId == null) {
            System.out.println("未找到有多个子权限的父权限，跳过测试");
            return;
        }

        String parentAuthName = allAuths.stream()
                .filter(auth -> auth.getAuthId().equals(parentAuthId))
                .map(AdminAuthData::getAuthName)
                .findFirst()
                .orElse("未知");

        List<AdminAuthData> children = parentChildMap.get(parentAuthId);
        System.out.println("测试父权限: " + parentAuthName + " (ID: " + parentAuthId + ")");
        System.out.println("总子权限数量: " + children.size());

        // 模拟用户拥有父权限和部分子权限（取前一半）
        List<AdminAuthData> userAuths = new ArrayList<>();
        userAuths.add(createAuthData(parentAuthId, parentAuthName, 0));

        int partialCount = Math.max(1, children.size() / 2);
        for (int i = 0; i < partialCount; i++) {
            AdminAuthData child = children.get(i);
            userAuths.add(createAuthData(child.getAuthId(), child.getAuthName(), parentAuthId));
        }

        System.out.println("用户拥有的子权限数量: " + partialCount);

        // 构建权限树
        List<AdminAuthData> authTree = adminAuthService.getAuthTree(userAuths);

        System.out.println("权限树结果:");
        printAuthTree(authTree, 0);

        // 验证：应该只包含用户实际拥有的子权限
        int actualChildrenCount = countChildrenInTree(authTree, parentAuthId);
        System.out.println("实际显示的子权限数量: " + actualChildrenCount);

        if (actualChildrenCount == partialCount) {
            System.out.println("✅ 权限继承逻辑正确：同时拥有父权限和子权限时只显示拥有的子权限");
        } else {
            System.out.println("❌ 权限继承逻辑错误：显示的子权限数量不正确");
        }
    }

    /**
     * 构建父子权限映射
     */
    private Map<Integer, List<AdminAuthData>> buildParentChildMap(List<AdminAuthData> allAuths) {
        Map<Integer, List<AdminAuthData>> parentChildMap = new HashMap<>();
        for (AdminAuthData auth : allAuths) {
            if (auth.getParentId() != 0) {
                parentChildMap.computeIfAbsent(auth.getParentId(), k -> new ArrayList<>()).add(auth);
            }
        }
        return parentChildMap;
    }

    /**
     * 打印权限结构
     */
    private void printAuthStructure(List<AdminAuthData> allAuths, Map<Integer, List<AdminAuthData>> parentChildMap) {
        System.out.println("总权限数量: " + allAuths.size());
        System.out.println("有子权限的父权限数量: " + parentChildMap.size());

        for (Map.Entry<Integer, List<AdminAuthData>> entry : parentChildMap.entrySet()) {
            if (entry.getValue().size() > 1) { // 只显示有多个子权限的
                Integer parentId = entry.getKey();
                String parentName = allAuths.stream()
                        .filter(auth -> auth.getAuthId().equals(parentId))
                        .map(AdminAuthData::getAuthName)
                        .findFirst()
                        .orElse("未知");
                System.out.println("  " + parentName + " (ID: " + parentId + ") -> " + entry.getValue().size() + " 个子权限");
            }
        }
    }

    /**
     * 找一个有子权限的父权限
     */
    private Integer findParentWithChildren(Map<Integer, List<AdminAuthData>> parentChildMap) {
        return parentChildMap.entrySet().stream()
                .filter(entry -> !entry.getValue().isEmpty())
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(null);
    }

    /**
     * 找一个有多个子权限的父权限
     */
    private Integer findParentWithMultipleChildren(Map<Integer, List<AdminAuthData>> parentChildMap) {
        return parentChildMap.entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(null);
    }

    /**
     * 创建权限数据对象
     */
    private AdminAuthData createAuthData(Integer authId, String authName, Integer parentId) {
        AdminAuthData auth = new AdminAuthData();
        auth.setAuthId(authId);
        auth.setAuthName(authName);
        auth.setParentId(parentId);
        auth.setStatus(1);
        return auth;
    }

    /**
     * 统计权限树中指定父权限的子权限数量
     */
    private int countChildrenInTree(List<AdminAuthData> authTree, Integer parentAuthId) {
        for (AdminAuthData auth : authTree) {
            if (auth.getAuthId().equals(parentAuthId)) {
                return auth.getSonAuths() != null ? auth.getSonAuths().size() : 0;
            }
            if (auth.getSonAuths() != null) {
                int count = countChildrenInTree(auth.getSonAuths(), parentAuthId);
                if (count > 0) return count;
            }
        }
        return 0;
    }

    /**
     * 递归打印权限树结构
     */
    private void printAuthTree(List<AdminAuthData> authTree, int level) {
        if (authTree == null) return;

        for (AdminAuthData auth : authTree) {
            StringBuilder indent = new StringBuilder();
            for (int i = 0; i < level; i++) {
                indent.append("  ");
            }

            String childrenInfo = "";
            if (auth.getSonAuths() != null && !auth.getSonAuths().isEmpty()) {
                childrenInfo = " [" + auth.getSonAuths().size() + " 个子权限]";
            }

            System.out.println(indent + "├─ " + auth.getAuthName() +
                              " (ID: " + auth.getAuthId() + ")" + childrenInfo);

            if (auth.getSonAuths() != null && !auth.getSonAuths().isEmpty()) {
                printAuthTree(auth.getSonAuths(), level + 1);
            }
        }
    }
}
