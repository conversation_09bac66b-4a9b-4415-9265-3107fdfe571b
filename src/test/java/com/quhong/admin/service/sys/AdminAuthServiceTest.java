package com.quhong.admin.service.sys;

import com.quhong.dao.data.AdminAuthData;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * AdminAuthService 测试类
 * 用于验证权限树构建的性能优化效果
 */
@SpringBootTest
@ActiveProfiles("test")
public class AdminAuthServiceTest {

    @Resource
    private AdminAuthService adminAuthService;

    /**
     * 测试权限树构建性能
     */
    @Test
    public void testAuthTreePerformance() {
        // 模拟用户权限ID集合
        Set<Integer> roleIds = new HashSet<>();
        roleIds.add(1);
        roleIds.add(2);

        // 获取用户权限
        List<AdminAuthData> userAuths = adminAuthService.queryByRoleIdSet(roleIds);

        // 测试多次调用权限树构建，验证缓存效果
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < 10; i++) {
            List<AdminAuthData> authTree = adminAuthService.getAuthTree(userAuths);
            System.out.println("第" + (i + 1) + "次调用，权限树根节点数量: " +
                             (authTree != null ? authTree.size() : 0));
        }

        long endTime = System.currentTimeMillis();
        System.out.println("10次权限树构建总耗时: " + (endTime - startTime) + "ms");
        System.out.println("平均每次耗时: " + (endTime - startTime) / 10.0 + "ms");
    }

    /**
     * 测试缓存效果
     */
    @Test
    public void testCacheEffect() {
        System.out.println("=== 测试缓存效果 ===");

        // 第一次调用，会查询数据库并缓存
        long startTime1 = System.currentTimeMillis();
        List<AdminAuthData> allAuths1 = adminAuthService.getAllValidAuths();
        long endTime1 = System.currentTimeMillis();
        System.out.println("第一次调用getAllValidAuths耗时: " + (endTime1 - startTime1) + "ms");
        System.out.println("获取到权限数量: " + allAuths1.size());

        // 第二次调用，应该从缓存获取
        long startTime2 = System.currentTimeMillis();
        List<AdminAuthData> allAuths2 = adminAuthService.getAllValidAuths();
        long endTime2 = System.currentTimeMillis();
        System.out.println("第二次调用getAllValidAuths耗时: " + (endTime2 - startTime2) + "ms");
        System.out.println("获取到权限数量: " + allAuths2.size());

        // 验证两次结果一致
        assert allAuths1.size() == allAuths2.size() : "两次调用结果不一致";
        System.out.println("缓存测试通过！");
    }

    /**
     * 测试权限树结构正确性
     */
    @Test
    public void testAuthTreeStructure() {
        System.out.println("=== 测试权限树结构正确性 ===");

        // 模拟用户权限
        Set<Integer> roleIds = new HashSet<>();
        roleIds.add(1);

        List<AdminAuthData> userAuths = adminAuthService.queryByRoleIdSet(roleIds);
        List<AdminAuthData> authTree = adminAuthService.getAuthTree(userAuths);

        if (authTree != null && !authTree.isEmpty()) {
            System.out.println("用户权限树根节点数量: " + authTree.size());

            // 递归打印权限树结构
            for (AdminAuthData rootAuth : authTree) {
                printAuthTree(rootAuth, 0);
            }
        } else {
            System.out.println("用户权限树为空");
        }
    }

    /**
     * 测试完整权限树（管理界面使用）
     */
    @Test
    public void testCompleteAuthTree() {
        System.out.println("=== 测试完整权限树 ===");

        // 获取所有权限（模拟管理界面的调用）
        List<AdminAuthData> allAuths = adminAuthService.queryAll(new AdminAuthData());
        List<AdminAuthData> completeAuthTree = adminAuthService.getAuthTree(allAuths);

        if (completeAuthTree != null && !completeAuthTree.isEmpty()) {
            System.out.println("完整权限树根节点数量: " + completeAuthTree.size());

            // 递归打印权限树结构
            for (AdminAuthData rootAuth : completeAuthTree) {
                printAuthTree(rootAuth, 0);
            }
        } else {
            System.out.println("完整权限树为空");
        }
    }

    /**
     * 测试子权限显示
     */
    @Test
    public void testChildAuthDisplay() {
        System.out.println("=== 测试子权限显示 ===");

        // 模拟用户只有部分权限
        Set<Integer> roleIds = new HashSet<>();
        roleIds.add(1);

        List<AdminAuthData> userAuths = adminAuthService.queryByRoleIdSet(roleIds);
        System.out.println("用户拥有的权限数量: " + userAuths.size());

        // 打印用户拥有的权限ID
        Set<Integer> userAuthIds = userAuths.stream()
                .map(AdminAuthData::getAuthId)
                .collect(Collectors.toSet());
        System.out.println("用户权限ID: " + userAuthIds);

        // 构建权限树
        List<AdminAuthData> authTree = adminAuthService.getAuthTree(userAuths);

        if (authTree != null && !authTree.isEmpty()) {
            System.out.println("权限树根节点数量: " + authTree.size());

            // 统计权限树中的总节点数
            int totalNodes = countTotalNodes(authTree);
            System.out.println("权限树总节点数: " + totalNodes);

            // 递归打印权限树结构
            for (AdminAuthData rootAuth : authTree) {
                printAuthTree(rootAuth, 0);
            }
        } else {
            System.out.println("权限树为空");
        }
    }

    /**
     * 统计权限树中的总节点数
     */
    private int countTotalNodes(List<AdminAuthData> authTree) {
        int count = 0;
        for (AdminAuthData auth : authTree) {
            count++; // 当前节点
            if (auth.getSonAuths() != null && !auth.getSonAuths().isEmpty()) {
                count += countTotalNodes(auth.getSonAuths()); // 递归统计子节点
            }
        }
        return count;
    }

    /**
     * 递归打印权限树结构
     */
    private void printAuthTree(AdminAuthData auth, int level) {
        StringBuilder indent = new StringBuilder();
        for (int i = 0; i < level; i++) {
            indent.append("  ");
        }

        System.out.println(indent + "├─ " + auth.getAuthName() +
                          " (ID: " + auth.getAuthId() +
                          ", ParentID: " + auth.getParentId() +
                          ", Order: " + auth.getOrderNum() + ")");

        if (auth.getSonAuths() != null && !auth.getSonAuths().isEmpty()) {
            for (AdminAuthData child : auth.getSonAuths()) {
                printAuthTree(child, level + 1);
            }
        }
    }
}
