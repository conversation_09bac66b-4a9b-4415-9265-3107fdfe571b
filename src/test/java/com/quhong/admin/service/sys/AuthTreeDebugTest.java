package com.quhong.admin.service.sys;

import com.quhong.dao.data.AdminAuthData;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 权限树调试测试类
 * 专门用于调试和验证权限树构建逻辑
 */
@SpringBootTest
@ActiveProfiles("test")
public class AuthTreeDebugTest {

    @Resource
    private AdminAuthService adminAuthService;

    /**
     * 对比原始逻辑和优化逻辑的结果
     */
    @Test
    public void compareAuthTreeResults() {
        System.out.println("=== 权限树构建结果对比 ===");
        
        // 模拟用户权限
        Set<Integer> roleIds = new HashSet<>();
        roleIds.add(1);
        
        List<AdminAuthData> userAuths = adminAuthService.queryByRoleIdSet(roleIds);
        System.out.println("用户拥有的权限数量: " + userAuths.size());
        
        // 打印用户权限详情
        System.out.println("\n用户拥有的权限列表:");
        for (AdminAuthData auth : userAuths) {
            System.out.println("  - " + auth.getAuthName() + " (ID: " + auth.getAuthId() + 
                             ", ParentID: " + auth.getParentId() + ")");
        }
        
        // 使用优化后的方法构建权限树
        List<AdminAuthData> optimizedTree = adminAuthService.getAuthTree(userAuths);
        
        System.out.println("\n=== 优化后的权限树结构 ===");
        if (optimizedTree != null && !optimizedTree.isEmpty()) {
            System.out.println("根节点数量: " + optimizedTree.size());
            int totalNodes = countTotalNodes(optimizedTree);
            System.out.println("总节点数: " + totalNodes);
            
            for (AdminAuthData rootAuth : optimizedTree) {
                printAuthTree(rootAuth, 0);
            }
        } else {
            System.out.println("权限树为空");
        }
        
        // 验证子权限是否正确显示
        validateChildrenDisplay(optimizedTree);
    }

    /**
     * 验证子权限显示是否正确
     */
    private void validateChildrenDisplay(List<AdminAuthData> authTree) {
        System.out.println("\n=== 验证子权限显示 ===");
        
        boolean hasChildren = false;
        for (AdminAuthData rootAuth : authTree) {
            if (hasChildrenRecursive(rootAuth)) {
                hasChildren = true;
                break;
            }
        }
        
        if (hasChildren) {
            System.out.println("✅ 权限树包含子权限，显示正常");
        } else {
            System.out.println("❌ 权限树不包含子权限，可能存在问题");
        }
    }

    /**
     * 递归检查是否有子权限
     */
    private boolean hasChildrenRecursive(AdminAuthData auth) {
        if (auth.getSonAuths() != null && !auth.getSonAuths().isEmpty()) {
            return true;
        }
        return false;
    }

    /**
     * 测试特定权限的子权限获取
     */
    @Test
    public void testSpecificAuthChildren() {
        System.out.println("=== 测试特定权限的子权限获取 ===");
        
        // 获取所有权限
        List<AdminAuthData> allAuths = adminAuthService.getAllValidAuths();
        System.out.println("所有有效权限数量: " + allAuths.size());
        
        // 找出有子权限的父权限
        Map<Integer, List<AdminAuthData>> parentChildMap = new HashMap<>();
        for (AdminAuthData auth : allAuths) {
            if (auth.getParentId() != 0) {
                parentChildMap.computeIfAbsent(auth.getParentId(), k -> new ArrayList<>()).add(auth);
            }
        }
        
        System.out.println("有子权限的父权限数量: " + parentChildMap.size());
        
        // 打印父子关系
        for (Map.Entry<Integer, List<AdminAuthData>> entry : parentChildMap.entrySet()) {
            Integer parentId = entry.getKey();
            List<AdminAuthData> children = entry.getValue();
            
            // 找到父权限名称
            String parentName = allAuths.stream()
                    .filter(auth -> auth.getAuthId().equals(parentId))
                    .map(AdminAuthData::getAuthName)
                    .findFirst()
                    .orElse("未知");
            
            System.out.println("父权限: " + parentName + " (ID: " + parentId + ") 有 " + children.size() + " 个子权限");
            for (AdminAuthData child : children) {
                System.out.println("  └─ " + child.getAuthName() + " (ID: " + child.getAuthId() + ")");
            }
        }
    }

    /**
     * 统计权限树中的总节点数
     */
    private int countTotalNodes(List<AdminAuthData> authTree) {
        int count = 0;
        for (AdminAuthData auth : authTree) {
            count++; // 当前节点
            if (auth.getSonAuths() != null && !auth.getSonAuths().isEmpty()) {
                count += countTotalNodes(auth.getSonAuths()); // 递归统计子节点
            }
        }
        return count;
    }

    /**
     * 递归打印权限树结构
     */
    private void printAuthTree(AdminAuthData auth, int level) {
        StringBuilder indent = new StringBuilder();
        for (int i = 0; i < level; i++) {
            indent.append("  ");
        }
        
        String childrenInfo = "";
        if (auth.getSonAuths() != null && !auth.getSonAuths().isEmpty()) {
            childrenInfo = " [" + auth.getSonAuths().size() + " 个子权限]";
        }
        
        System.out.println(indent + "├─ " + auth.getAuthName() + 
                          " (ID: " + auth.getAuthId() + 
                          ", ParentID: " + auth.getParentId() + 
                          ", Order: " + auth.getOrderNum() + ")" + childrenInfo);
        
        if (auth.getSonAuths() != null && !auth.getSonAuths().isEmpty()) {
            for (AdminAuthData child : auth.getSonAuths()) {
                printAuthTree(child, level + 1);
            }
        }
    }
}
