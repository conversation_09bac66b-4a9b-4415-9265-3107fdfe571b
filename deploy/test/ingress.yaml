kind: HTTPRoute
apiVersion: gateway.networking.k8s.io/v1beta1
metadata:
  name: kissu-server-user-v2
spec:
  parentRefs:
    - kind: Gateway
      name: tikko-internal-http
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /java_actor
      backendRefs:
        - kind: Service
          name: kissu-server-user-v2
          port: 8080
---
kind: HTTPRoute
apiVersion: gateway.networking.k8s.io/v1beta1
metadata:
  name: kissu-server-inner-user-v2
spec:
  parentRefs:
    - kind: Gateway
      name: tikko-internal-http
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /inner/java_actor
      backendRefs:
        - kind: Service
          name: kissu-server-user-v2
          port: 8080