kind: HTTPRoute
apiVersion: gateway.networking.k8s.io/v1beta1
metadata:
  name: kissu-server-activity-v2
spec:
  parentRefs:
    - kind: Gateway
      name: tikko-internal-http
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /activity
      backendRefs:
        - kind: Service
          name: kissu-server-activity-v2
          port: 8080
---
kind: HTTPRoute
apiVersion: gateway.networking.k8s.io/v1beta1
metadata:
  name: kissu-server-inner-activity-v2
spec:
  parentRefs:
    - kind: Gateway
      name: tikko-internal-http
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /inner/activity
      backendRefs:
        - kind: Service
          name: kissu-server-activity-v2
          port: 8080