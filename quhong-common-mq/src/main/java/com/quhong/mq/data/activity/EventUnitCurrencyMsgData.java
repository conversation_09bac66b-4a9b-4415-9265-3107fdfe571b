package com.quhong.mq.data.activity;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 活动组件货币消息
 * <AUTHOR>
 * @since 2025-06-11 16:38
 */
@Data
@Accessors(chain = true)
public class EventUnitCurrencyMsgData {
    /**
     * 活动码
     */
    private Integer eventCode;
    /**
     * 组件id
     */
    private Integer unitId;
    /**
     * 用户id
     */
    private String uid;
    /**
     * 货币操作类型 1:增加 2:减少
     */
    private Integer action;
    /**
     * 变动金额
     */
    private Integer changed;
    /**
     * 时间
     */
    private Long time;
}
