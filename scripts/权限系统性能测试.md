# 权限系统性能测试脚本

## 测试目的
验证权限系统优化后的性能提升效果，对比优化前后的数据库访问次数和响应时间。

## 测试环境准备

### 1. 数据库监控
在测试前，开启数据库查询日志监控：

```sql
-- MySQL 开启查询日志
SET GLOBAL general_log = 'ON';
SET GLOBAL general_log_file = '/tmp/mysql_query.log';

-- 或者使用慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 0;
SET GLOBAL slow_query_log_file = '/tmp/mysql_slow.log';
```

### 2. 应用日志配置
在 `logback.xml` 中添加数据库查询日志：

```xml
<!-- 添加MyBatis SQL日志 -->
<logger name="com.quhong.dao.slave.mapper.db.AdminAuthSlaveMapper" level="DEBUG"/>
<logger name="org.springframework.cache" level="DEBUG"/>
```

## 性能测试步骤

### 1. 基准测试
```bash
# 运行基准测试
mvn test -Dtest=AdminAuthServiceTest#testAuthTreePerformance

# 查看数据库查询日志
tail -f /tmp/mysql_query.log | grep admin_auth
```

### 2. 缓存效果测试
```bash
# 测试缓存命中率
mvn test -Dtest=AdminAuthServiceTest#testCacheEffect

# 观察第一次和后续调用的性能差异
```

### 3. 并发测试
使用JMeter或类似工具进行并发测试：

```xml
<!-- JMeter测试计划示例 -->
<TestPlan>
  <ThreadGroup>
    <numThreads>50</numThreads>
    <rampTime>10</rampTime>
    <loops>100</loops>
  </ThreadGroup>
  <HTTPSampler>
    <domain>localhost</domain>
    <port>8080</port>
    <path>/admin/login</path>
    <method>POST</method>
  </HTTPSampler>
</TestPlan>
```

## 性能指标监控

### 1. 数据库查询次数
监控以下SQL的执行次数：
- `SELECT * FROM admin_auth WHERE status = 1`
- `SELECT * FROM admin_auth WHERE parent_id = ?`
- `SELECT * FROM admin_role_auth WHERE role_id IN (?)`

### 2. 响应时间
监控以下接口的响应时间：
- `/admin/login` - 登录接口
- `/admin/auth/list` - 权限列表接口
- `/admin/role/create` - 角色创建页面

### 3. 缓存指标
监控缓存相关指标：
- 缓存命中率
- 缓存大小
- 缓存过期次数

## 预期测试结果

### 优化前性能基线
- 数据库查询次数：1 + N次（N为权限节点数）
- 平均响应时间：200-500ms
- 缓存命中率：0%

### 优化后性能目标
- 数据库查询次数：首次1次，后续0次
- 平均响应时间：50-100ms
- 缓存命中率：95%以上

## 测试命令集合

```bash
# 1. 清理缓存，重启应用
docker-compose restart app

# 2. 预热测试
curl -X POST http://localhost:8080/admin/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","pwd":"password","curTime":1234567890}'

# 3. 性能测试
for i in {1..10}; do
  echo "第${i}次测试"
  time curl -X POST http://localhost:8080/admin/login \
    -H "Content-Type: application/json" \
    -d '{"username":"admin","pwd":"password","curTime":1234567890}'
done

# 4. 权限树构建测试
for i in {1..10}; do
  echo "第${i}次权限树测试"
  time curl -X GET http://localhost:8080/admin/auth/list \
    -H "Authorization: Bearer ${TOKEN}"
done

# 5. 查看数据库查询统计
mysql -e "SHOW STATUS LIKE 'Com_select';"
mysql -e "SHOW STATUS LIKE 'Questions';"
```

## 性能分析工具

### 1. JProfiler 分析
```bash
# 启动应用时添加JProfiler参数
java -agentpath:/path/to/jprofiler/bin/linux-x64/libjprofilerti.so=port=8849 \
  -jar kissu-server-admin.jar
```

### 2. Spring Boot Actuator
在 `application.yml` 中启用监控端点：
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,metrics,caches
  endpoint:
    metrics:
      enabled: true
    caches:
      enabled: true
```

访问监控端点：
```bash
# 查看缓存统计
curl http://localhost:8080/actuator/caches

# 查看JVM指标
curl http://localhost:8080/actuator/metrics/jvm.memory.used

# 查看数据库连接池
curl http://localhost:8080/actuator/metrics/hikaricp.connections
```

### 3. 自定义性能监控
在代码中添加性能监控：

```java
@Component
public class PerformanceMonitor {
    private final MeterRegistry meterRegistry;
    
    @EventListener
    public void handleAuthTreeBuild(AuthTreeBuildEvent event) {
        Timer.Sample sample = Timer.start(meterRegistry);
        // ... 业务逻辑
        sample.stop(Timer.builder("auth.tree.build.time").register(meterRegistry));
    }
}
```

## 测试报告模板

### 性能测试报告
- **测试时间**：YYYY-MM-DD HH:mm:ss
- **测试环境**：开发/测试/生产
- **数据规模**：权限数量、用户数量、角色数量

### 测试结果对比
| 指标 | 优化前 | 优化后 | 提升比例 |
|------|--------|--------|----------|
| 数据库查询次数 | 1+N | 1 | 90%+ |
| 平均响应时间 | 300ms | 60ms | 80% |
| 缓存命中率 | 0% | 95% | - |
| 内存使用 | 100MB | 105MB | +5% |

### 结论和建议
- 性能提升效果显著
- 缓存策略有效
- 建议在生产环境部署
- 需要监控缓存使用情况
