package com.quhong.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 方法监控注解
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface MethodMonitor {

    /**
     * 监控Key，用于关联监控配置
     * 格式为：monitor-{活动码}-含义后缀
     */
    String value();

    /**
     * 是否榜单方法，用于榜单空置率监控
     */
    boolean isRankMethod() default false;

}
