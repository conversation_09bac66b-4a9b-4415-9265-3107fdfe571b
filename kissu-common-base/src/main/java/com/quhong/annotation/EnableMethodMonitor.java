package com.quhong.annotation;

import com.quhong.aop.MethodMonitorAop;
import com.quhong.service.activity.event.monitor.core.strategy.MonitorStrategyFactory;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @since 2025/4/29 16:05
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import({MethodMonitorAop.class, MonitorStrategyFactory.class})
public @interface EnableMethodMonitor {
}
