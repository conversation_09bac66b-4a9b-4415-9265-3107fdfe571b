package com.quhong.enums;

import com.quhong.core.annotation.ConstantFiledDesc;

/**
 * 事件码
 *
 * @see ActivityTypeEnum 活动码
 */
public interface EventCode {
    @ConstantFiledDesc(name = "所有(0)")
    int DEFAULT = 0;
    @ConstantFiledDesc(name = "验证码")
    int VERIFICATION_CODE = 1;

    @ConstantFiledDesc(name = "adx广告任务(1098)")
    int ADX_TASK = 1098;
    @ConstantFiledDesc(name = "任务(1099)")
    int TASK = 1099;
    @ConstantFiledDesc(name = "排灯节(10005)")
    int ACTIVITY_DIWALI = 10005;
    @ConstantFiledDesc(name = "双旦活动(10008)")
    int ACTIVITY_XMAS = 10008;
    @ConstantFiledDesc(name = "独立日(10024)")
    int EVENT_INDEPENDENCE_DAY_2024_08 = 10024;
    @ConstantFiledDesc(name = "周年庆(10025)")
    int EVENT_ANNIVERSARY = 10025;
    @ConstantFiledDesc(name = "玫瑰小姐（10026）")
    int EVENT_MISS_ROSE = 10026;
    @ConstantFiledDesc(name = "丘比特之箭（10027）")
    int EVENT_CUPID = 10027;
    /**
     * 福利大乱斗
     */
    @ConstantFiledDesc(name = "福利大乱斗2409（10028）")
    int EVENT_WELFARE_2409 = 10028;

    @ConstantFiledDesc(name = "充值抽奖（10029）")
    int EVNET_RECHARGE_DRAW_202409 = 10029;

    @ConstantFiledDesc(name = "枣椰树活动（10030）菲律宾")
    int EVENT_PLANT_DATE_PALM = 10030;

    @ConstantFiledDesc(name = "枣椰树活动（10031）巴基斯坦")
    int EVENT_PLANT_DATE_PALM_2 = 10031;

    @ConstantFiledDesc(name = "射门活动2410（10032）")
    int EVENT_SHOOT_2410 = 10032;

    @ConstantFiledDesc(name = "排灯节2410（10033）")
    int EVENT_DIWALI_2410 = 10033;

    @ConstantFiledDesc(name = "万圣节2410（10034）")
    int EVENT_HALLOWEEN_2410 = 10034;
    @ConstantFiledDesc(name = "枣椰树活动2410（10035)")
    int EVENT_PLANT_DATE_PALM_2410 = 10035;

    @ConstantFiledDesc(name = "PK大王活动2411（10036）")
    int EVENT_RANK_PK_2411 = 10036;
    @ConstantFiledDesc(name = "赛马活动2411（10037）")
    int EVENT_HORSE_RACE_2411 = 10037;

    @ConstantFiledDesc(name = "嘉年华之星2024-12（10038）")
    int EVENT_STAR_CARNIVAL_V3 = 10038;

    @ConstantFiledDesc(name = "女房主争霸赛（10039）")
    int EVENT_RANK_PK_2412 = 10039;

    @ConstantFiledDesc(name = "庆典房（10040）")
    int EVENT_GALA_ROOM_2412 = 10040;

    @ConstantFiledDesc(name = "游戏大师(10042)")
    int EVENT_GAME_MASTER_2501 = 10042;

    @ConstantFiledDesc(name = "房主争霸赛(10043)")
    int EVENT_RANK_PK_2501 = 10043;

    @ConstantFiledDesc(name = "情人节活动(10044)")
    int EVENT_LOVER_2501 = 10044;

    @ConstantFiledDesc(name = "组队驯龙2502(10045)")
    int EVENT_DRAGON_TRAINING_2502 = 10045;

    @ConstantFiledDesc(name = "直播pk(10046)")
    int EVENT_PK_2502 = 10046;

    @ConstantFiledDesc(name = "25洒红节(10047)")
    int EVENT_HOLI_2503 = 10047;

    @ConstantFiledDesc(name = "房主争霸赛(10048)")
    int EVENT_RANK_PK_2503 = 10048;

    @ConstantFiledDesc(name = "游戏大师(10049)")
    int EVENT_GAME_MASTER_2504 = 10049;

    @ConstantFiledDesc(name = "直播pk(10050)")
    int EVENT_PK_2504 = 10050;

    @ConstantFiledDesc(name = "房主争霸赛(10051)")
    int EVENT_RANK_PK_2504 = 10051;
    @ConstantFiledDesc(name = "嘉年华之星2025-04（10052）")
    int EVENT_STAR_CARNIVAL_2504 = 10052;
    @ConstantFiledDesc(name = "房主争霸赛2505（10053）")
    int EVENT_RANK_PK_2505 = 10053;
    @ConstantFiledDesc(name = "赛马活动2505（10054）")
    int EVENT_HORSE_RACE_2505 = 10054;

    /**
     * 圣诞节2024-12
     */
    @ConstantFiledDesc(name = "圣诞节2024-12（10101）")
    int EVENT_XMAS_2024 = 10101;
    @ConstantFiledDesc(name = "射门活动2025-01（10102）")
    int EVENT_SHOOT_2501 = 10102;
    @ConstantFiledDesc(name = "极速挑战2025-01（10103）")
    int EVENT_SPEED_2501 = 10103;

    @ConstantFiledDesc(name = "充值抽奖2025-02（10104）")
    int RECHARGE_DRAW_2502 = 10104;

    @ConstantFiledDesc(name = "充值抽奖2025-03（10105）")
    int RECHARGE_DRAW_2503 = 10105;

    @ConstantFiledDesc(name = "竞技摩托2025-03（10106）")
    int EVENT_SPEED_2503 = 10106;
    @ConstantFiledDesc(name = "愚人节2503（10107）")
    int EVENT_FOOL_2503 = 10107;
    @ConstantFiledDesc(name = "充值抽卡活动2504（10108）")
    int EVENT_CARD_DRAW_2504 = 10108;
    @ConstantFiledDesc(name = "Ios tikko送Iphone（10109）")
    int EVENT_IPHONE_2504 = 10109;
    @ConstantFiledDesc(name = "融合礼物宣传活动2504（10110）")
    int EVENT_FUSION_GIFT_2504 = 10110;
    @ConstantFiledDesc(name = "极速赛车2025-05（10111）")
    int EVENT_SPEED_2505 = 10111;
    @ConstantFiledDesc(name = "充值节点任务2505（10112）")
    int EVENT_RECHARGE_NODE_TASK_2505 = 10112;
    @ConstantFiledDesc (name = "Ios tikko送Iphone2505（10113）")
    int EVENT_IPHONE_2505 = 10113;
    @ConstantFiledDesc(name = "充值抽奖2506（10114）")
    int EVENT_RECHARGE_DRAW_2506 = 10114;
    @ConstantFiledDesc(name = "神灯活动2506（10115）")
    int EVENT_MAGIC_LAMP_2506 = 10115;
}
