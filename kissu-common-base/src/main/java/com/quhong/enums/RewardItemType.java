package com.quhong.enums;

import com.quhong.core.annotation.ConstantFiledDesc;

import java.util.Arrays;
import java.util.List;

/**
 * 奖励类型 常量类
 *
 * <AUTHOR>
 */
public interface RewardItemType {
    /**
     * ALL占位
     */
    int DEFAULT = 0;
    /**
     * 金币
     */
    @ConstantFiledDesc(name = "金币", enName = "Coin")
    int GOLD = 1;
    String GOLD_ICON = "https://statics.kissu.mobi/icon/egg_golds.png";
    String DIAMOND_ICON = "https://statics.kissu.mobi/icon/diamonds.png";
    /**
     * 礼物
     */
    @ConstantFiledDesc(name = "礼物", enName = "Gift")
    int GIFT = 2;
    String ROSE_GIFT_ICON = "https://statics.kissu.mobi/icon/egg_rose.png";
    /**
     * 匹配卡
     */
    @ConstantFiledDesc(name = "通话卡", enName = "Call Card")
    int MATCH_CARD = 3;
    String CALL_CARD_ICON = "https://statics.kissu.mobi/icon/egg_call_card.png";
    /**
     * vip天数
     */
    @ConstantFiledDesc(name = "VIP", enName = "VIP")
    int VIP_DAYS = 4;
    String VIP_ICON = "https://statics.kissu.mobi/icon/egg_vip.png";
    /**
     * 免费游戏卡
     */
    @Deprecated
    int FREE_GAME_CARD = 5;
    /**
     * 贵族天数
     */
    @ConstantFiledDesc(name = "贵族天数", enName = "Lord")
    int LORD_DAYS = 6;
    /**
     * 等级积分
     * dataId  3用户等级
     * 4主播等级
     *
     * @see com.quhong.constant.levelConfig.LevelTypeConstant 目前只支持3和4
     */
    @ConstantFiledDesc(name = "等级积分", enName = "Level score")
    int LEVEL_SCORE = 7;
    /**
     * 勋章 num==0/-1为永久
     */
    @ConstantFiledDesc(name = "勋章", enName = "Medal")
    int MEDAL = 8;
    @ConstantFiledDesc(name = "钻石", enName = "Diamond")
    int DIAMOND = 9;
    /**
     * 仅hotchat使用
     */
    @ConstantFiledDesc(name = "礼物钻石(模拟送礼)仅Hotchat可用", enName = "Gift")
    int GIFT_DIAMONDS = 10;
    @ConstantFiledDesc(name = "游戏货币",enName = "Game Coin")
    int GAME_COIN = 11;
    String GAME_COIN_ICON = "https://statics.kissu.mobi/icon/game/gcoins2.png";

    /**
     * 麦位框<br/>
     * roomItemsType = rewardItemType % 1000   num==0为永久
     */
    @ConstantFiledDesc(name = "头像框", enName = "Frame")
    int SEAT_FRAME = 1001;
    /**
     * 气泡框<br/>
     * roomItemsType = rewardItemType % 1000
     */
    @ConstantFiledDesc(name = "气泡框", enName = "Bubble")
    int BUBBLE_FRAME = 1002;
    /**
     * 座驾<br/>
     * roomItemsType = rewardItemType % 1000
     */
    @ConstantFiledDesc(name = "座驾", enName = "Ride")
    int ENTER_EFFECT = 1003;
    /**
     * 进场特效<br/>
     * roomItemsType = rewardItemType % 1000
     */
    @ConstantFiledDesc(name = "进场特效", enName = "Ride")
    int ENTRY_EFFECT = 1004;
    /**
     * 称号<br/>
     * roomItemsType = rewardItemType % 1000
     */
    @ConstantFiledDesc(name = "称号", enName = "designation")
    int DESIGNATION = 1008;
    /**
     * 官方推荐标签
     * roomItemsType = rewardItemType % 1000
     */
    @ConstantFiledDesc(name = "官方推荐标签", enName = "Official Label")
    int HOST_LABEL = 1009;
    /**
     * 资料卡披肩
     * roomItemsType = rewardItemType % 1000
     */
    @ConstantFiledDesc(name = "资料卡披肩", enName = "Profile Card")
    int PROFILE_CARD = 1010;
    /**
     * 星动嘉年华 初级场抽奖券
     * ticketType = rewardItemType % 2000
     */
    @ConstantFiledDesc(name = "星动嘉年华抽奖券", enName = "starry ticket")
    int STARRY_TICKET = 2001;


    /**
     * 活动货币
     */
    @ConstantFiledDesc(name = "活动货币", enName = "Event currency")
    int EVENT_CURRENCY = 9999;

    @ConstantFiledDesc(name = "其他物品", enName = "Other")
    int OTHER_ITEM = 10000;
    static String getUnit(int rewardType, String lang) {
        switch (rewardType) {
            case VIP_DAYS:
            case LORD_DAYS:
            case SEAT_FRAME:
            case MEDAL:
            case BUBBLE_FRAME:
            case HOST_LABEL:
            case ENTER_EFFECT:
            case ENTRY_EFFECT:
                return "Days";
        }
        return "";
    }

    List<Integer> roomItemsTypeList = Arrays.asList(SEAT_FRAME, BUBBLE_FRAME, ENTER_EFFECT, ENTRY_EFFECT);

    static boolean isRoomItemsType(int rewardType) {
        return roomItemsTypeList.contains(rewardType);
    }
}

