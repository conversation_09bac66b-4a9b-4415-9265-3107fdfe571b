package com.quhong.enums;

import com.quhong.core.annotation.ConstantFiledDesc;

public interface UserSignType {
    @ConstantFiledDesc(name = "通用")
    String DEFAULT = "default";
    @ConstantFiledDesc(name = "匹配")
    String MATCH = "match";
    @ConstantFiledDesc(name = "策略")
    String AUTO_MESSAGE = "auto_message";
    @ConstantFiledDesc(name = "任务")
    String TASK = "task";
    @ConstantFiledDesc(name = "用户推荐列表")
    String RECOMMENDED_LIST = "recommended_list";
    @ConstantFiledDesc(name = "复购")
    String REPURCHASE = "repurchase";
    @ConstantFiledDesc(name = "拨打按钮")
    String CALL_BUTTON = "callButton";
    @ConstantFiledDesc(name = "live房间推荐列表")
    String LIVE_RECOMMENDED_LIST = "live_recommended_list";
    @ConstantFiledDesc(name = "party房间推荐列表")
    String PARTY_RECOMMENDED_LIST = "party_recommended_list";
    @ConstantFiledDesc(name = "主播一筛")
    String HOST_FIRST_SCREENING = "host_first_screening";

    public static String getTypeByRoomType(int roomType){
        switch (roomType){
            case RoomType.LIVE:
                return LIVE_RECOMMENDED_LIST;
            case RoomType.CHAT:
                return PARTY_RECOMMENDED_LIST;
            case RoomType.NO_ROOM:
                return RECOMMENDED_LIST;
            default:
                return null;
        }
    }
}
