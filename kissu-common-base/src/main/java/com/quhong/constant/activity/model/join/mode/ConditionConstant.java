package com.quhong.constant.activity.model.join.mode;

import com.quhong.core.annotation.ConstantFiledDesc;

/**
 * <AUTHOR>
 * @since 2024/1/5 14:29
 */
public interface ConditionConstant {

    @ConstantFiledDesc(name = "=")
    String EQUALS = "=";
    @ConstantFiledDesc(name = ">")
    String GREATER_THAN = ">";

    @ConstantFiledDesc(name = "<")
    String LESS_THAN = "<";

    @ConstantFiledDesc(name = ">=")
    String GREATER_THAN_OR_EQUALS = ">=";

    @ConstantFiledDesc(name = "<=")
    String LESS_THAN_OR_EQUALS = "<=";

    @ConstantFiledDesc(name = "in")
    String IN = "in";
}
