package com.quhong.constant.activity.model.join.mode;

import com.quhong.core.annotation.ConstantFiledDesc;

/**
 * 条件方式
 * <AUTHOR>
 * @since 2024/1/5 14:28
 */
public interface ConditionModeConstant {
    /**
     * 注册时间
     */
    @ConstantFiledDesc(name = "注册时间")
    int REGISTER_TIME = 1;
    /**
     * 用户类型 1：用户 2：主播
     */
    @ConstantFiledDesc(name = "用户类型")
    int GENDER = 2;
    /**
     * 性别 1：男 2：女
     */
    @ConstantFiledDesc(name = "性别")
    int SEX = 3;
    /**
     * 贵族
     */
    @ConstantFiledDesc(name = "贵族")
    int LORD = 4;
    /**
     * 等级
     */
    @ConstantFiledDesc(name = "用户等级")
    int USER_LEVEL = 5;
    /**
     * rid列表
     */
    @ConstantFiledDesc(name = "rid")
    int RID = 6;
    @ConstantFiledDesc(name = "是否VIP")
    int IS_VIP = 7;

    @ConstantFiledDesc(name = "主播等级")
    int HOST_LEVEL = 8;
}
