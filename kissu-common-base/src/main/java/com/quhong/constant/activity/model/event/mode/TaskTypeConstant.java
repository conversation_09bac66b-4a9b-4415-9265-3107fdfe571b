package com.quhong.constant.activity.model.event.mode;

import com.quhong.core.annotation.ConstantFiledDesc;

/**
 * 任务类型
 */
public interface TaskTypeConstant {
    /**
     * 送礼
     */
    @ConstantFiledDesc(name = "送礼物")
    int SEND_GIFT = 1;
    /**
     * 首充
     */
    @ConstantFiledDesc(name = "首充")
    int FIRST_RECHARGE = 2;
    /**
     * yxsk游戏金币消耗x金币
     */
    @ConstantFiledDesc(name = "yxsk游戏金币消耗x金币")
    int PLAY_YXSK_GAME = 3;
    /**
     * 直播间停留超x秒
     */
    @ConstantFiledDesc(name = "直播间停留超x秒")
    int ROOM_STAY_TIME = 4;
    /**
     * 活动期间第一次充值
     */
    @ConstantFiledDesc(name = "活动期间第一次充值")
    int EVENT_FIRST_RECHARGE = 5;

    /**
     * 宣传
     */
    @ConstantFiledDesc(name = "宣传")
    int DISSEMINATE_TIMES = 6;
    /**
     * 主播partyRoom停留时长
     */
    @ConstantFiledDesc(name = "主播partyRoom停留时长")
    int HOST_PARTY_ROOM_STAY_TIME = 7;
    /**
     * 上麦时长
     */
    @ConstantFiledDesc(name = "上麦时长")
    int MIC_STAY_TIME = 8;
    /**
     * 游戏次数
     */
    @ConstantFiledDesc(name = "游戏次数")
    int GAME_PLAY_TIMES = 9;
    /**
     * 语聊房送礼价值
     */
    @ConstantFiledDesc(name = "语聊房送礼价值")
    int PARTY_ROOM_GIFT_PRICE = 10;

    /**
     * 星动嘉年华消费金币
     */
     @ConstantFiledDesc(name = "星动嘉年华消费金币")
    int CARNIVAL_LUCKY_DRAW_COST_GOLD = 11;
    /**
     * 游戏消耗金币
     */
    @ConstantFiledDesc(name = "游戏消耗金币")
    int PLAY_GAME_COST_GOLD = 12;
    /**
     * 送幸运礼物消费金币
     */
    @ConstantFiledDesc(name = "送幸运礼物消费金币")
    int LUCKY_GIFT_COST_GOLD = 13;
    /**
     * 邀请用户
     */
    @ConstantFiledDesc(name = "邀请用户")
    int INVITE_USER_COUNT = 14;
    /**
     * 收礼累计
     */
    @ConstantFiledDesc(name = "收礼累计")
    int RECEIVE_COST_GOLD = 15;
    /**
     * 线上充值金币
     */
    @ConstantFiledDesc(name = "线上充值金币")
    int REAL_RECHARGE_GOLD = 16;
    /**
     * 收礼钻石数
     */
    @ConstantFiledDesc(name = "收礼钻石数")
    int RECEIVE_GIFT_DIAMONDS = 17;
}
