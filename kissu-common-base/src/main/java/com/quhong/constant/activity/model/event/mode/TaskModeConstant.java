package com.quhong.constant.activity.model.event.mode;

import com.quhong.core.annotation.ConstantFiledDesc;

/**
 * 任务方式
 * <AUTHOR>
 * @since 2024/1/5 15:57
 */
public interface TaskModeConstant {
    /**
     * 全局任务
     */
    @ConstantFiledDesc(name = "单节点奖励任务")
    int ONE_TASK = 1;
    /**
     * 每日任务
     */
    @ConstantFiledDesc(name = "多节点奖励任务")
    int NODE_TASK = 2;

    @ConstantFiledDesc(name = "每达到条件奖励任务")
    int CONDITION_TASK = 3;

    @ConstantFiledDesc(name = "去重达标奖励任务")
    int ROOM_MESSAGE_TASK = 4;
}
