package com.quhong.constant;

import com.quhong.core.annotation.ConstantFiledDesc;

/**
 * 复购类型常量
 */
public interface RepurchaseTypeConstant {
    
    /**
     * 复购配置键名
     */
    @ConstantFiledDesc(name = "复购配置键名")
    String REPURCHASE_KEY = "repurchase";
    
    /**
     * 复购开启
     */
    @ConstantFiledDesc(name = "复购开启")
    boolean REPURCHASE_OPEN = true;
    
    /**
     * 复购关闭
     */
    @ConstantFiledDesc(name = "复购关闭")
    boolean REPURCHASE_CLOSE = false;
}
