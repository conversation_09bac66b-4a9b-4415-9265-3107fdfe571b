package com.quhong.constant.event.monitor;

/**
 * 告警等级
 * <AUTHOR>
 * @since 2025/4/29 14:05
 */
public interface MonitorLevelConstant {
    /**
     * 重要
     */
    int HIGH = 1;
    /**
     * 普通
     */
    int LOW = 2;

    static String getDesc(int level) {
        switch (level) {
            case HIGH:
                return "重要";
            case LOW:
                return "普通";
            default:
                return "未知";
        }
    }
}
