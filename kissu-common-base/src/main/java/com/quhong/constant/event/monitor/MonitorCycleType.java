package com.quhong.constant.event.monitor;

/**
 * <AUTHOR>
 * @since 2025/4/29 13:57
 */
public interface MonitorCycleType {
    /**
     * 活动期间
     */
    int EVENT_DURATION = 1;

    /**
     * 每日监控
     */
    int DAILY_MONITOR = 2;

    /**
     * 周期监控（小于一天的周期）
     */
    int CYCLE_MONITOR = 3;

    /**
     * 定点周期监控（指定一个时间范围进行监控）
     */
    int POINT_CYCLE_MONITOR = 4;

    static String getDesc(int type){
        switch (type){
            case EVENT_DURATION:
                return "活动期间";
            case DAILY_MONITOR:
                return "每日监控";
            case CYCLE_MONITOR:
                return "周期监控";
            case POINT_CYCLE_MONITOR:
                return "定点周期监控";
            default:
                return "未知周期类型";
        }
    }

}
