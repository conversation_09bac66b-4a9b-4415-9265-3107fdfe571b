package com.quhong.constant;

import com.quhong.data.appConfig.LiveMultipleMicSwitchConfig;
import com.quhong.data.appConfig.MigrateCodeWindowsConfig;
import com.quhong.data.appConfig.PayNameSortData;
import com.quhong.data.appConfig.RewardSendImConditionConfig;
import com.quhong.data.appConfig.user.auth.level.OldLevelMappingConfig;

/**
 * Description:
 *
 * <AUTHOR>
 * @date 2021/12/20 10:19
 */
public interface AppConfigKeyConstant {
    /**
     * 客户端可用
     */
    int STATUS_CLIENT = 1;
    /**
     * 服务端可用
     */
    int STATUS_SERVER = 2;
    /**
     * 双端可用
     */
    int STATUS_ALL = 3;
    /**
     * 主播列表大尺度开关
     * 1开 0关
     */
    String HOST_LIST_SEXY_SWITCH = "host_list_sexy_switch";
    /**
     * 通话实时字幕开关列表
     *
     * @see com.quhong.data.appConfig.chat.ChatSttSwitchConfig 语音实时字幕开关配置
     */
    String CHAT_STT_SWITCH = "chat_stt_switch";
    /**
     * 质量等级描述页相关配置
     */
    String QUALITY_LEVEL_DESC_CONFIG = "quality_level_desc_config";

    /**
     * 安卓马甲包web登录接口映射
     */
    String WEB_GAME_LOGIN_MAPPING = "web_game_login_mapping";

    /**
     * 游戏排行榜跳转房间游戏参数配置
     */
    String GAME_RANK_JUMP_ROOM_GAME_PARAMS = "game_rank_jump_room_game_params";
    /**
     * 时长宝箱每日金币领取上限
     *
     * @see Integer
     */
    String TIME_BOX_DAILY_COIN_MAX_LIMIT = "time_box_daily_coin_max_limit";
    /**
     * 时长宝箱弹窗跳转游戏配置
     *
     * @see com.quhong.data.appConfig.user.info.timeBox.TimeBoxJumpGameConfig
     */
    String TIME_BOX_JUMP_GAME_CONFIG = "time_box_jump_game_config";

    /**
     * 时长宝箱弹窗跳转游戏币游戏配置
     *
     * @see com.quhong.data.appConfig.user.info.timeBox.TimeBoxJumpGameConfig
     */
    String TIME_BOX_JUMP_GAME_COIN_CONFIG = "time_box_jump_game_coin_config";

    /**
     * 时长宝箱模拟在线人数
     *
     * @see com.quhong.data.appConfig.user.info.timeBox.FakeCountConfig
     */
    String TIME_BOX_FAKE_ONLINE_USER_COUNT_CONFIG = "time_box_fake_online_user_count_config";
    /**
     * 时长宝箱模拟可领取金币数量
     *
     * @see com.quhong.data.appConfig.user.info.timeBox.FakeCountConfig
     */
    String TIME_BOX_FAKE_COIN_COUNT = "time_box_fake_coin_count";
    /**
     * 时长宝箱白名单逻辑开关
     */
    String TIME_BOX_OWNER_WHITE_SWITCH = "time_box_owner_white_switch";

    /**
     * 匹配卡配置（非付费用户）
     */
    String MATCH_CARD_CONFIG = "match_card_config";

    /**
     * 短时间的匹配卡配置（所有用户）
     */
    String MIN_MATCH_CARD_CONFIG = "min_match_card_config";

    /**
     * 匹配流程广告位
     */
    String AD_MATCH_PROCESS = "ad_match_process";
    /**
     * 资料页相册广告位
     */
    String AD_PROFILE_PAGE_ALBUM = "ad_profile_page_album";
    /**
     * 消息列表广告位
     */
    String AD_MESSAGE_LIST = "ad_message_list";
    /**
     * 开屏广告位
     */
    String AD_OPEN_SCREEN = "ad_open_screen";
    /**
     * 激励广告
     */
    String AD_INCENTIVE = "ad_incentive";

    /**
     * 广告全局兜底金额
     */
    String AD_GLOBAL_SWITCH_MONEY = "ad_global_switch_money";
    /**
     * 广告老用户低消费意愿判定金额
     */
    String AD_IS_OLD_LOW_MONEY = "ad_is_old_low_money";
    /**
     * 广告奖励每日领取限额
     */
    String AD_EVERY_DAY_AWARD_COUNT = "ad_every_day_award_count";
    /**
     * max广告奖励每日领取限额
     */
    String AD_EVERY_DAY_AWARD_COUNT_MAX = "ad_every_day_award_count_max";

    /**
     * 优质主播保收逻辑配置
     */
    String HOST_GUARANTEE_LIMIT = "host_guarantee_limit";

    /**
     * 网络质量配置
     */
    String NETWORK_QUALITY = "network_quality";
    /**
     * 用户删除账号原因（列表）
     */
    String ACTOR_DEL_ACCOUNT_REASON = "actor_del_account_reason";
    /**
     * 签到面向用户配置
     */
    String SIGN_IN_FOR_USER_CONFIG = "sign_in_for_user_config";
    /**
     * vip签到额外奖励开关
     */
    String SUBSCRIBE_SIGN_SWITCH = "subscribe_sign_switch";
    /**
     * vip签到额外奖励金币数量
     */
    @Deprecated
    String SUBSCRIBE_SIGN_REWARD_COIN = "subscribe_sign_reward_coin";
    /**
     * 订阅后，签到的奖励配置
     * validityType 的值就是生效的类型
     * rewardNums奖励的金币数量 30 就是多送30金币
     * rewardMultiple奖励的倍数 1就是多赠送1倍的金币，只有金币生效
     */
    String SUBSCRIBE_SIGN_REWARD_CONFIG = "subscribe_sign_reward_config";
    /**
     * 打开通知奖励任务配置
     */
    String OPEN_NOTIFICATION_REWARD_TASK_CONFIG = "open_notification_reward_task_config";
    /**
     * 筛选主播配置
     */
    String PROBATION_HOST_CONFIG = "probation_host_config";

    /**
     * 新转正主播配置
     */
    String NEW_POSITIVE_HOST_CONFIG = "new_positive_host_config";

    /**
     * 主播 钻石:美元 汇率
     * eg. 200    200:$1
     */
    String CONVERTIBLE_PROPORTION = "convertible_proportion";

    /**
     * 通话卡抵扣时长 单位/s
     */
    String CALL_CARD_DURATION = "call_card_duration";

    /**
     * min抵扣时长 单位/s
     */
    String MIN_CALL_CARD_DURATION = "min_call_card_duration";

    /**
     * 每日首充转盘玩法奖励配置
     */
    String DAILY_FIRST_RECHARGE_CAROUSEL_REWARD_CONFIG = "daily_first_recharge_carousel_reward_config";
    /**
     * 每日首充转盘玩法 自动启动开关
     */
    String DAILY_FIRST_RECHARGE_CAROUSEL_AUTO_SWITCH = "daily_first_recharge_carousel_auto_switch";
    /**
     * 每日首充转盘玩法 灰度比例配置
     */
    String DAILY_FIRST_RECHARGE_CAROUSEL_GRAYSCALE = "daily_first_recharge_carousel_grayscale";
    /**
     * 每日首充转盘玩法 每日首次启动app拉起页面开关
     */
    String DAILY_FIRST_RECHARGE_CAROUSEL_OPEN_GET = "daily_first_recharge_carousel_open_get";


    /**
     * 匹配通话等待时长过长反馈配置
     */
    String MATCH_CARD_FEEDBACK_CONFIG = "match_card_feedback_config";

    /**
     * 曝光次数过少反馈配置
     */
    String CLICK_FEEDBACK_CONFIG = "click_feedback_config";

    /**
     * 系统推荐弹窗配置
     */
    String RECOMMEND_POPUP_CONFIG = "recommend_popup_config";

    /**
     * 收入保收池配置
     */
    String INCOME_GUARANTEE_CONFIG = "income_guarantee_pool";

    /**
     * 时长保收池配置
     */
    String TIME_GUARANTEE_CONFIG = "time_guarantee_pool";
    /**
     * 金币倍率
     */
    String GOLD_COIN_MULTIPLIER = "gold_coin_multiplier";

    /**
     * 钻石倍率
     */
    String DIAMOND_COIN_MULTIPLIER = "diamond_coin_multiplier";

    /**
     * 游戏币倍率配置
     */
    String GAME_COIN_MULTIPLIER = "game_coin_multiplier";
    /**
     * 是否展示游戏房或者真主播直播是否展示游戏列表
     */
    String SHOW_GAME_SWITCH = "show_game_switch";
    /**
     * 获得主播抢单后的冻结时间
     * 不为空且大于0则设置有冻结时间
     */
    String HOST_GRAB_COOL_TIME_CONFIG = "host_grab_cool_time";
    /**
     * 是否给主播的抢单列表推送匹配用户
     */
    String PUSH_USER_TO_GRAB_LIST = "push_user_to_grab_list";
    /**
     * 是否给给哪些高质量主播推送用户
     */
    String PUSH_USER_TO_HIGH_QUALITY_LIST = "push_user_to_high_quality_list";
    /**
     * 获得主播当天最大可抢单次数
     */
    String HOST_GRAB_MAX_TIMES_DAILY = "host_grab_max_times";
    /**
     * 获得单个主播 每天最多的优质匹配推送次数
     */
    String HOST_HIGH_QUALITY_MAX_TIMES_DAILY = "host_high_quality_max_times_daily";
    /**
     * 考核所需用户数
     */
    String EXAMINE_NEED_COUNT = "examine_need_count";
    /**
     * 不服务率配置
     */
    String NOT_SERVE_CONFIG = "not_serve_config";
    /**
     * 考核期转正评级分数保持配置
     */
    String EXAMINE_APPRAISE_SCORE_KEEP_CONFIG = "examine_appraise_score_keep_config";
    /**
     * 用户的电话被定义为考核通话最少需要多少可拨打分钟数
     */
    String EXAMINE_USER_MIN_CALL_DURATION = "examine_user_min_call_duration";
    /**
     * 考核电话超过X秒配置
     */
    String EXAMINE_CALL_EXCEED_CALL_TIME = "examine_call_exceed_call_time";
    /**
     * 主播在线数据上报数数开关 int (0，null)-为关 1-开
     */
    String HOST_ONLINE_LOG_UPLOAD_TH_SWITCH = "host_online_log_upload_th_switch";
    /**
     * 付费用户领取匹配卡的配置
     */
    String MATCH_CARD_PAY_GET_CONFIG = "match_card_pay_get_config";
    /**
     * 匹配价格/每分钟
     */
    String MATCH_PRICE_PER_MIN = "match_price_per_min";
    /**
     * nsfw鉴黄判定指数
     */
    String DETECT_NFSW_CHECK_INDEX_VALUE = "detect_nfsw_check_index_value";
    /**
     * 优质匹配池最短通话间隔
     */
    String HIGH_QUALITY_HOST_MAXIMUM_CALL_INTERVAL_SECONDS = "high_quality_host_maximum_call_interval_seconds";


    //*******主播banner 开关
    /**
     * 主播banner  问卷调查开关
     */
    String HOST_BANNER_QUESTIONNAIRE_SWITCH = "host_banner_questionnaire_switch";

    /**
     * 主播马甲信息 key
     */
    String SHOW_SCENE_BY_MEDIA_SOURCE = "show_scene_by_media_source";
    /**
     * 用户在线数据上报数数开关 key
     */
    String USER_ONLINE_LOG_UPLOAD_TH_SWITCH = "user_online_log_upload_th_switch";

    /**
     * 主播列表新排序规则灰度
     */
    String HOST_LIST_SHOW_GRAY = "host_list_show_gray";

    String WALLET_PAGE_BANNER_THREE_PARTY_PAYMENT = "wallet_page_banner_three_party_payment";
    /**
     * 屏蔽的国家码组
     */
    String SHIELD_COUNTRY_CODE = "shield_country_code";
    /**
     * 屏蔽的时区地区组
     */
    String SHIELD_TIMEZONE_AREA = "shield_timezone_area";
    /**
     * 屏蔽的系统语言组
     */
    String SHIELD_CLIENT_SYS_LANG = "shield_client_sys_Lang";
    /**
     * 屏蔽跳过的系统语言组
     */
    String SHIELD_SKIP_CLIENT_SYS_LANG = "shield_skip_client_sys_Lang";
    /**
     * 屏蔽跳过的rid组
     */
    String SHIELD_SKIP_RID = "shield_skip_rid";
    /**
     * 屏蔽跳过的设备组
     */
    String SHIELD_SKIP_DEVICE = "shield_skip_device";
    /**
     * 屏蔽手机运营商组
     */
    String SHIELD_SIM_ISP = "shield_sim_isp";
    /**
     * 屏蔽总开关
     */
    String SHIELD_TOTAL_SWITCH = "shield_total_switch";
    /**
     * A B 面灰度测试
     *
     * @see com.quhong.data.GrayData
     */
    String HOST_VEST_INFO_GRAY = "host_vest_info_gray";

    /**
     * 机器人消息开关key
     */
    String ROBOT_MESSAGE_BY_MEDIA_SOURCE = "robot_message_by_media_source";

    /**
     * 解锁发送消息金币key
     */
    String UNLOCK_SEND_MESSAGE_GOLD = "unlock_send_message_gold";

    /**
     * 机器人消息每天推送X套Key
     */
    String ROBOT_MESSAGE_LIMIT = "robot_message_limit";

    /**
     * 机器人消息每天推送X套Key
     */
    String ROBOT_MESSAGE_LIMIT_NEW = "robot_message_limit_new";

    /**
     * 机器人消息灰度
     */
    String ROBOT_MESSAGE_GRAY = "robot_message_gray";

    /**
     * 消息限制开关
     */
    String SEND_MESSAGE_LIMIT_SWITCH = "send_message_limit_switch";

    /**
     * 用户列表地区配置
     */
    String USER_LIST_AREA_CONFIG = "user_list_area_config";

    /**
     * 中东地区国家列表
     */
    String MIDDLE_EAST_AREA_COUNTRY_LIST = "middle_east_area_country_list";

    /**
     * 消息限制灰度
     *
     * @see com.quhong.data.GrayData
     */
    String SEND_MESSAGE_LIMIT_GRAY = "send_message_limit_gray";

    /**
     * 图片变清晰原图最大大小 默认80K=81920
     */
    String PIC_WISH_LIMIT_SIZE = "pic_wish_limit_size";

    /**
     * 奖励滚屏广播配置
     *
     * @see RewardSendImConditionConfig
     */
    String REWARD_LOOP_BROADCAST = "reward_loop_broadcast";
    /**
     * 砸蛋 首次玩砸蛋奖励id配置
     *
     * @see com.quhong.dao.datas.RewardInfoData 奖励信息.id
     */
    String SMASH_EGG_FIRST_PLAY_REWARD = "smash_egg_first_play_reward";
    /**
     * 房间内玩游戏奖励消息发送配置
     *
     * @see RewardSendImConditionConfig
     */
    String ROOM_REWARD_SEND_IM_CONFIG = "room_reward_send_im_config";
    /**
     * 幸运礼物 首次发送幸运礼物倍率奖励配置
     */
    String LUCKY_GIFT_FIRST_PLAY_REWARD_MULTI = "lucky_gift_first_play_reward_multi";


    /**
     * 中台支付开关
     */
    String MIDDLE_PAY_SWITCH = "middle_pay_switch";

    /**
     * 系统推荐电话灰度
     */
    String SYSTEM_CALL_GRAY = "system_call_gray";

    /**
     * 系统推荐电话配置
     */
    String SYSTEM_CALL_CONFIG = "system_call_config";

    /**
     * 余额不足用户充值时间限制
     */
    String ROBOT_MESSAGE_INSUFFICIENT_RECHARGE_TIME_LIMIT = "robot_message_Insufficient_recharge_time_limit";

    /**
     * charming地区周榜奖励key
     */
    String CHARMING_AREA_WEEKLY_RANK_REWARD_CONFIG = "charming_area_weekly_rank_reward_config";
    /**
     * charming周榜奖励key
     */
    String CHARMING_WEEKLY_RANK_REWARD_CONFIG = "charming_weekly_rank_reward_config";
    /**
     * 礼物地区日榜奖励key
     */
    String GIFT_AREA_DAILY_RANK_REWARD_CONFIG = "gift_area_daily_rank_reward_config";
    /**
     * recharge日榜称号奖励配置
     */
    String RECHARGE_DAILY_RANK_DESIGNATION_REWARD_CONFIG = "recharge_daily_rank_designation_reward_config";
    /**
     * recharge周榜称号奖励配置
     */
    String RECHARGE_WEEKLY_RANK_DESIGNATION_REWARD_CONFIG = "recharge_weekly_rank_designation_reward_config";

    /**
     * 渠道cdn列表
     */
    String CHANNEL_CDU_LIST_CONFIG = "channel_cdu_list_config";

    /**
     * 推广成本渠道列表
     */
    String AD_COST_CHANNEL_LIST = "ad_cost_channel_list";

    /**
     * 机器人虚拟视频/来电界面推送次数
     */
    String ROBOT_CALL_PUSH_COUNT_CONFIG = "robot_call_push_count_config";

    /**
     * 评价配置
     */
    String EVALUATE_CONFIG = "evaluate_config";
    /**
     * 用户端 显示的【通过通话历史记录排序的主播列表】限制显示数量配置
     */
    String CHAT_HISTORY_HOST_LIST_LIMIT = "chat_history_host_list_limit";

    /**
     * 支付facebook回传渠道列表
     */
    String SEND_POST_TO_FACEBOOK_CHANNEL_LIST = "send_post_to_facebook_channel_list";

    /**
     * 金币流水变动配置
     */
    String BALANCE_CHANGE_MONITOR_CONFIG = "balance_change_monitor_config";

    /**
     * 主播评分等级计算配置
     */
    String HOST_APPRAISE_LEVEL_CALCULATE_CONFIG = "host_appraise_level_calculate_config";

    /**
     * 钻石兑换金币列表配置
     */
    String REDEEM_GOLD_LIST_CONFIG = "redeem_gold_list";
    /**
     * 币商钻石兑换金币列表配置
     */
    String COIN_SELLER_REDEEM_GOLD_LIST_CONFIG = "coin_seller_redeem_gold_list_config";

    /**
     * 钻石兑换金币每日限制配置
     */
    String REDEEM_DAILY_LIMIT_CONFIG = "redeem_daily_limit_config";
    /**
     * 进入语聊房的boss等级的配置
     */
    String ENTER_ROOM_BOSS_LEVEL_CONFIG = "create_room_lowest_level";
    /**
     * 房间的stream类型
     */
    String ENTER_ROOM_STREAM = "enter_room_stream";
    /**
     * 安卓马甲包渠道列表
     */
    String ANDROID_VEST_CHANNEL = "android_vest_channel";

    /**
     * 鉴黄时间间隔配置
     */
    String DETECT_IMG_CONFIG = "detect_img_config";

    /**
     * 全局配置悬浮弹窗权限弹框开关字段  默认1 打开悬浮弹窗权限
     * 以前在mongo的 jasmin_config 里，迁移到java时放到配置字典里面
     */
    String FLOATING_WINDOW_SWITCH = "floating_window_switch";

    /**
     * 主播提现配置
     */
    String WITHDRAWAL_CONFIG = "withdrawal_config";

    /**
     * 邀请链接前缀（不带参数值的链接）
     */
    String INVITE_PRE_URL = "invite_pre_url";

    /**
     * 邀请链接有效时间
     */
    String INVITE_URL_EFFECTIVE_DURATION = "invite_url_effective_duration";

    /**
     * 全服广播配置
     */
    String SEND_FULL_SERVICE_IM_CONFIG = "send_full_service_im_config";
    /**
     * 游戏全服公屏通知配置
     */
    String GAME_ALL_ROOM_IM_CONFIG = "game_all_room_im_config";
    /**
     * 钻石钱包页主播提现相关配置
     *
     * @see com.quhong.data.appConfig.DiamondsWalletPayoutConfig
     */
    String DIAMONDS_WALLET_PAYOUT_CONFIG = "diamonds_wallet_payout_config";
    /**
     * 提现币商渠道
     * str
     */
    String WITHDRAW_PARTNER_CHANNEL_CONFIG = "withdraw_partner_channel_config";

    /**
     * 马赛克开关    1-开 2-关
     */
    String TOTAL_MOSAIC_SWITCH = "total_mosaic_switch";

    /**
     * 蓝光短信控制
     */
    String CAN_SEND_SMS_COUNTRY = "can_send_sms_country";

    /**
     * 蓝光不支持支持发送短信的国际码
     */
    String PROHIBIT_SEND_MSG_CODE = "prohibit_send_msg_code";

    /**
     * 推荐视频的配置
     */
    String CALL_VIDEO_EDC_CONFIG = "call_video_edc_config";

    /**
     * 添加到屏幕
     */
    String ADD_TO_SCREEN = "web_addscreen_reward";

    /**
     * 用户live鉴黄配置
     */
    String USER_LIVE_DETECT_CONFIG = "user_live_detect_config";
    /**
     * 等级得分配置
     * type: 等级得分相关
     *
     * @see com.quhong.data.appConfig.user.auth.level.LevelScoreRateConfig 等级得分配置
     */
    String LEVEL_SCORE_RATE_CONFIG = "level_score_rate_config";
    /**
     * 使用tikko等级开关
     */
    String TIKKO_LEVEL_SWITCH = "use_tikko_level_switch";

    /**
     * 马甲包开发主播列表固定配置
     */
    String HOST_LIST_DEV_CONFIG = "host_list_dev_config";


    /**
     * 弹幕消息配置
     */
    String BARRAGE_MESSAGE_CONFIG = "barrage_message_config";

    /**
     * 新等级映射旧等级权益配置
     *
     * @see OldLevelMappingConfig 新旧等级映射配置
     */
    String OLD_LEVEL_MAPPING_CONFIG = "old_level_mapping_config";

    /**
     * 旧等级points替换为新等级映射配置
     *
     * @see OldLevelMappingConfig 新旧等级映射配置
     */
    String OLD_LEVEL_POINTS_TO_NEW_CONFIG = "old_level_points_to_new_config";
    /**
     * 用户旧等级映射新等级开关<br/>
     * 0 等级只做旧等级映射新等级不入库不发补偿<br/>
     * 1 旧等级映射新等级，入库且发起补偿<br/>
     */
    String USER_OLD_LEVEL_TO_NEW_LEVE_SWITCH = "user_old_level_to_new_leve_switch";

    /**
     * 旧等级补偿配置
     */
    String OLD_LEVEL_COMPENSATE_CONFIG = "old_level_compensate";

    /**
     * pk步骤时长
     */
    String PK_STEP_DURATION = "pk_step_duration";
    /**
     * 生成机器人配置
     *
     * @see com.quhong.data.appConfig.robot.GenerateRobotConfig 生成机器人配置
     */
    String GENERATE_ROBOT_CONFIG = "generate_robot_config";

    /**
     * 房间机器人配置
     */
    String ROOM_ROBOT_CONFIG = "room_robot_config";

    /**
     * 免费发送消息主播人数限制配置
     */
    String FREE_MESSAGE_HOST_COUNT = "free_message_host_count";
    /**
     * 充值优惠的配置
     */
    String WEB_RECHARGE_BONUS = "web_recharge_bonus";
    /**
     * 允许web通知奖励
     */
    String ALLOW_NOTIFICATION_REWARD = "web_notifi_reward";
    /**
     * 兑换比例 配置
     */
    String WAHO_CURRENCY_RATE = "waho_hotchat_currency_ratio";

    /**
     * 弹窗免费发送礼物配置
     */
    String FREE_REWARD_NOTIFY_CONFIG = "free_reward_notify_config";


    /**
     * Google analytics配置
     */
    String GOOGLE_ANALYTICS_CONFIG = "google_analytics_config";

    /**
     * 官方账号配置
     */
    String OFFICIAL_CERTIFICATION_ACCOUNT = "official_certification_account";

    /**
     * 币商账号标签配置
     */
    String COIN_SELLER_ACCOUNT_CONFIG = "coin_seller_account_config";

    /**
     * 公会长账号标签配置
     */
    @Deprecated
    String AGENCY_ACCOUNT_CONFIG = "agency_account_config";
    /**
     * 公会标签自动下发条件配置
     *
     * @see com.quhong.data.appConfig.agency.AgencyLabelConditionConfig
     */
    String AGENCY_LABEL_CONDITION = "agency_label_condition";

    /**
     * web facebook回传配置
     */
    String WEB_FACEBOOK_ANALYTICS_CONFIG = "web_facebook_analytics_config";

    /**
     * 礼物渠道单独配置
     */
    String GIFT_VEST_CHANNEL_CONFIG = "gift_vest_channel_config";

    /**
     * 马甲包上报meta渠道配置
     */
    String SEND_META_CHANNEL_CONFIG = "send_meta_channel_config";
    /**
     * 推送的接听电话
     */
    String PUSH_CALLING_POPUP = "push_calling_popup";

    /**
     * 主播匹配的间隔时长
     */
    String HOST_MATCH_INTERVAL = "host_match_interval";

    /**
     * 免费通话时长
     */
    String FREE_CALL_DURATION = "free_call_duration";

    /**
     * 主播每天最大匹配次数
     */
    String HOST_MAX_MATCH_TIMES_DAILY = "host_max_match_times_daily";

    /**
     * 资源尺度
     */
    String CALL_VIDEO_TYPE = "call_video_type";

    /**
     * 勋章开关
     */
    String MEDAL_SWITCH = "medal_switch";
    /**
     * 发送贵族礼物获得奖励活动配置
     *
     * @see com.quhong.data.appConfig.activity.sendLordGiftAward.SendLordGiftAwardConfig 发送贵族礼物获得奖励配置
     */
    String ACTIVITY_SEND_LORD_GIFT_AWARD_CONFIG = "activity_send_lord_gift_award_config";

    /**
     * joyGame key配置
     */
    String JOY_GAME_KEY_CONFIG = "joy_game_key_config";

    String MATCH_TYPE_CONFIG = "match_type_config";

    /**
     * 房间标签配置
     */
    String ROOM_TAG_CONFIG = "room_tag_config";
    /**
     * 鉴黄上麦限制
     */
    String ROOM_VIDEO_MIC_BLOCK_CONFIG = "room_videomic_block";

    /**
     * 游戏邀请分成比例配置
     *
     * @see com.quhong.data.appConfig.GameInviteDivideRateConfig
     */
    String GAME_INVITE_DIVIDE_RATE_CONFIG = "game_invite_divide_rate_config";

    /**
     * 游戏邀请分成场景配置
     */
    String GAME_INVITE_DIVIDE_SCENE_CONFIG = "game_invite_divide_scene_config";
    /**
     * 非邀请用户邀请分成场景配置
     */
    String NOT_INVITE_DIVIDE_SCENE_CONFIG = "not_invite_divide_scene_config";
    /**
     * 游戏邀请分成最低金额配置
     */
    String GAME_INVITE_DIVIDE_MINIMUM_CONFIG = "game_invite_divide_minimum_config";

    /**
     * 游戏邀请分成有效期配置
     */
    String GAME_INVITE_DIVIDE_VALIDITY_CONFIG = "game_invite_divide_validity_config";

    /**
     * 游戏结算最低金额配置
     */
    String GAME_INVITE_SETTLEMENT_MINIMUM_CONFIG = "game_invite_settlement_minimum_config";
    /**
     * 游戏最低任务和比例配置
     */
    String GAME_INVITE_TASK_AND_RATE_CONFIG = "game_invite_task_and_rate_config";
    /**
     * 签到充值的奖励翻倍
     */
    String SIGNIN_REWARD_BONUS = "signin_reward_bonus";
    /**
     * 首充礼包资格注册时间
     */
    String FIRST_CHARGE_QUALIFICATION_REGISTER_TIME = "first_charge_qualification_register_time";
    /**
     * 图灵盾设备注册限制
     */
    String TURING_SHIELD_DEVICE = "turing_shield_device";
    /**
     * 用户来源进入app初始页面开关配置
     */
    String SOURCE_ACTOR_INIT_PAGE_CONFIG = "source_actor_init_page_config";
    /**
     * 用户来源进入app初始页面配置
     */
    String SOURCE_ACTOR_INIT_DETAIL_PAGE_CONFIG = "source_actor_init_detail_page_config";
    /**
     * 语聊房麦位视频开关等级限制配置
     */
    String PARTY_ROOM_MIC_CAMERA_LEVEL_CONFIG = "party_room_mic_camera_level_config";
    /**
     * 礼物列表道具配置
     */
    String GIFT_LIST_PROP_CONFIG = "gift_list_prop_config";
    /**
     * 好友审核功能开关
     */
    String YOUTUBE_REVIEW_SWITCH = "youtube_review_switch";
    /**
     * 亲密值开关
     */
    String VIDEO_CHAT_CLOSE_SWITCH = "video_chat_close_switch";

    /**
     * 代理分成比例配置
     */
    String AGENT_COMMISSION_RATE_CONFIG = "agent_commission_rate_config";

    /**
     * 币商充值列表配置
     */
    String COIN_SELLER_CONFIG = "coin_seller_config";
    /**
     * 邀请充值返佣配置
     */
    String INVITE_RECHARGE_REWARD_CONFIG = "invite_recharge_reward_config";
    /**
     * 新邀请充值返佣配置
     */
    String INVITE_RECHARGE_REWARD_NEW_CONFIG = "invite_recharge_reward_new_config";
    /**
     * 邀请直播返佣配置
     */
    String INVITE_LIVE_REWARD_CONFIG = "invite_live_reward_config";
    /**
     * 邀请收礼返佣配置
     */
    String INVITE_GIFT_REWARD_CONFIG = "invite_gift_reward_config";
    /**
     * 邀请成为充值用户返佣配置
     */
    String INVITE_BE_RECHARGE_REWARD_CONFIG = "invite_be_recharge_reward_config";

    /**
     * 客服自动回复配置
     */
    String AUTO_CUSTOMER_REPLY_CONFIG = "auto_customer_reply_config";

    /**
     * 开播通知推送次数限制
     */
    String OPEN_LIVE_PUSH_COUNT = "open_live_push_count";

    /**
     * 开播接受FCM推送次数限制
     */
    String OPEN_LIVE_RECEIVE_FCM_COUNT = "open_live_receive_fcm_count";

    /**
     * 开播接受IM推送次数限制
     */
    String OPEN_LIVE_RECEIVE_IM_COUNT = "open_live_receive_im_count";

    /**
     * 非Tikko渠道的礼物分成固定配置
     */
    String HOST_GIFT_RAIN_CONFIG = "host_gift_rain_config";
    /**
     * 游戏幸运奖事件限制配置
     *
     * @see com.quhong.data.appConfig.activity.event.game.lucky.EventGameLuckyLimitConfig 幸运奖领取限制配置
     */
    String EVENT_GAME_LUCKY_LIMIT_CONFIG = "event_game_lucky_limit_config";

    /**
     * 游戏积分风险控制配置
     */
    String GAME_POINTS_RISK_CONTROL_CONFIG = "game_points_risk_control_config";

    /**
     * 游戏积分抽奖配置
     */
    String PRIZE_DRAW_PRICE_CONFIG = "prize_draw_price_config";

    /**
     * 游戏积分抽奖风控配置
     */
    String PRIZE_DRAW_RISK_CONTROL_CONFIG = "prize_draw_risk_control_config";
    /**
     * 主播守护等级配置
     */
    String HOST_GUARD_LEVEL = "host_guard_level";

    /**
     * 游戏积分 获取开关控制
     */
    String GAME_POINTS_GAIN_SWITCH_CONFIG = "game_points_gain_switch_config";

    /**
     * 游戏积分 获取开关控制
     */
    String SAY_HI_REWARD_CONFIG = "say_hi_reward_config";

    /**
     * 录制视频开关
     */
    String PATROL_VIDEO_RECORD = "patrol_video_record";

    /**
     * 星动嘉年华抽奖券配置
     */
    String STARRY_CARNIVAL_TICKET_CONFIG = "starry_carnival_ticket_config";

    /**
     * ios包下架触发审核版本更新
     */
    String IOS_APP_REMOVED_ADD_REVIEW = "ios_app_removed_add_review";

    /**
     * ios包定时设置审核版本
     */
    String IOS_APP_PERIODICALLY_SET_REVIEW = "ios_app_periodically_set_review";

    /**
     * live露脸检测配置
     */
    String LIVE_SHOW_FACE_CONFIG = "live_show_face_config";

    /**
     * feedback 标题配置
     */
    String FEEDBACK_TITLE_CONFIG = "feedback_title_config";
    /**
     * feedback 奖励数量
     */
    String FEEDBACK_REWARD_NUMBER = "feedback_reward_number";

    /**
     * feedback 奖励支付失败次数
     */
    String FEEDBACK_REWARD_FAIL_COUNT = "feedback_reward_fail_count";
    /**
     * 培训视频配置key
     */
    String TRAIN_VIDEO_CONFIG = "train_video_config";
    /**
     * ab面包打开可以访问国家的key
     */
    String AB_SIDE_OPEN_LIMIT_COUNTRY_KEY = "ab_side_open_limit_country_key";
    /**
     * ab面 包打开可以访问的国家的时区地区的key
     */
    String AB_SIDE_OPEN_LIMIT_COUNTRY_TIMEZONE_AREA_KEY = "ab_side_open_limit_country_timezone_area_key";

    /**
     * same_sex_can_call_channel_switch相同性别可以打电话的渠道开关
     */
    String SAME_SEX_CAN_CALL_CHANNEL_SWITCH = "same_sex_can_call_channel_switch";

    /**
     * 房间火箭开关配置
     */
    String ROOM_ROCKET_SWITCH_CONFIG = "room_rocket_switch_config";
    /**
     * adx 三方广告相关配置
     *
     * @see com.quhong.data.appConfig.activity.adx.AdxConfig
     */
    String ADX_CONFIG = "adx_config";

    /**
     * 金币倍率
     */
    String THIRD_PRODUCTS_GOLD_ICON = "third_products_gold_icon";

    /**
     * 第一次分享邀请链接被点击后获得的钻石数
     */
    String FIRST_SHARE_REWARD_DIAMONDS = "first_share_reward_diamonds";

    /**
     * 币商转账最小值配置
     */
    String COIN_MERCHANT_TRANSFER_LIMIT_CONFIG = "coin_merchant_transfer_limit_config";

    /**
     * 充值币商国家列表配置
     */
    String COIN_SELLER_COUNTRY_LIST_CONFIG = "coin_seller_country_list_config";

    /**
     * 幸运礼物公屏消息中奖金币配置
     */
    String LUCKY_GIFT_SCREEN_MSG_REWARD_LIMIT_CONFIG = "lucky_gift_screen_msg_reward_limit_config";

    /**
     * 迁移反馈文字消息内容
     */
    String MIGRATE_FEED_MSG = "migrate_feed_msg";
    /**
     * 等级权益页配置
     *
     * @see com.quhong.data.appConfig.user.auth.level.LevelRightPageConfig 等级权益页配置
     */
    String LEVEL_RIGHTS_PAGE_CONFIG = "level_rights_page_config";

    /**
     * 房间列表推荐配置
     */
    String ROOM_LIST_RECOMMEND_CONFIG = "room_list_recommend_config";

    /**
     * 房间列表推荐和自动领取时长宝箱新用户判断配置
     */
    String ROOM_LIST_RECOMMEND_AND_AUTO_RECEIVE_TIME_BOX_NEW_USER_CONFIG = "room_list_recommend_and_auto_receive_time_box_new_user_config";

    /**
     * ab面包customData_syslang过滤
     */
    String AB_SIDE_OPEN_LIMIT_CUSTOMDATA_SYSLANG = "ab_side_open_limit_customData_syslang";


    /**
     * HOST_LIST_DAILY_RECOMMENDED_CONFIG
     */
    String HOST_LIST_DAILY_RECOMMENDED_CONFIG = "host_list_daily_recommended_config";


    /**
     * 名字和签名的敏感词检测
     */
    String NAME_SIGNATURE_SENSITIVE_WORDS_CONFIG = "name_signature_sensitive_words_config";
    /**
     * 房间红包配置
     */
    String ROOM_RED_ENVELOPE_CONFIG = "room_red_envelope_config";

    /**
     * 红包全服广播金币配置
     */
    String ROOM_RED_ENVELOPE_NOTIFY_ALL_ROOM = "room_red_envelope_notify_all_room";
    /**
     * 红包等级限制
     */
    String ROOM_RED_ENVELOPE_LEVEL_LIMIt = "room_red_envelope_level_limit";
    /**
     * 红包等级限制
     */
    String ROOM_RED_ENVELOPE_COMMISSION = "room_red_envelope_commission";

    String HIGH_NET_WORTH_INVITE_CONFIG = "high_net_worth_invite_config";

    /**
     * 任务领取前置分享次数配置
     */
    String TASK_SHARE_COUNT_LIMIT = "task_share_count_limit";

    /**
     * 中台UPI开关
     */
    String MIDDLE_PAY_UPI_SWITCH = "middle_pay_upi_switch";

    /**
     * tab
     */
    String VIEWER_LIST_FILTER_CONFIG_NEW = "viewer_list_filter_config_new";

    /**
     * 乱序房间列表渠道配置
     */
    String SHUFFLE_ROOM_LIST_CONFIG = "shuffle_room_list_config";

    /**
     * 迁移码弹窗配置 {}
     *
     * @see MigrateCodeWindowsConfig
     */
    String MIGRATE_CODE_WINDOWS_CONFIG = "migrate_code_windows_config";

    /**
     * 迁移码弹窗配置 {}
     *
     * @see PayNameSortData
     */
    String PAY_LIST_CONFIG = "pay_list_config";

    /**
     * 定义会员等级描述配置的常量字符串
     */
    String MEMBERSHIP_LEVEL_DESC_CONFIG = "membership_level_desc_config";

    /**
     * 定义会员等级描述配置的常量字符串
     * 该常量用于标识是否为新的会员等级描述配置
     * 1：是
     * 0：否
     */
    String MEMBERSHIP_LEVEL_NEW_WHETHER = "membership_level_new_whether";


    /**
     * 列表页绿色显示规则配置
     */
    String LIST_GREEN_DISPLAY_RULE_CONFIG = "list_green_display_rule_config";

    /**
     * 复购商品配置
     *
     * @see com.quhong.data.appConfig.payment.RepurchaseConfig 复购商品配置
     */
    String REPURCHASE_CONFIG = "repurchase_config";

    /**
     * 手机号格式校验
     */
    String CHECK_PHONE = "check_phone";

    /**
     * 评分奖励数量
     */
    String RATING_REWARD_NUMBER_CONFIG = "rating_reward_number_config";


    /**
     * 评分奖励渠道
     */
    String RATING_REWARD_CHANNEL = "rating_reward_channel";

    /**
     * 拨打按钮开关
     */
    String CALL_BUTTON_CONFIG_SWITCH = "call_button_config_switch";
    /**
     * 页面点击跳转私密视频开关
     */
    String JUMP_PRIVATE_VIDEO = "jump_private_video";
    /**
     * 评分奖励信息图片
     */
    String RATING_REWARD_MESSAGE_IMAGE = "rating_reward_message_image";

    /**
     * 游戏币兑换金币列表配置
     */
    String GAME_COIN_REDEEM_GOLD_LIST_CONFIG = "game_coin_redeem_gold_list_config";

    /**
     * 复购和ab面逻辑配置
     */
    String REPURCHASE_AND_AB_LOGIC_CONFIG = "repurchase_and_ab_logic_config";

    /**
     * 直播多人麦位开关
     *
     * @see LiveMultipleMicSwitchConfig
     */
    String LIVE_MULTIPLE_MIC_SWITCH_CONFIG = "live_multiple_mic_switch_config";
}
