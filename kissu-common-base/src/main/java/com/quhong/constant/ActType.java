package com.quhong.constant;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Description: 金币操作类型
 *
 * <AUTHOR>
 * @date 2021/10/29 10:14
 * @see com.quhong.dao.datas.TitleForMoneyDetailData 金币流水明显标题配置表
 */
public interface ActType {
    /**
     * 谷歌充值
     */
    int GOOGLE_RECHARGE = 2;
    /**
     * 首充
     */
    int FIRST_PAY = 3;
    /**
     * say hi 相关的
     */
    int SAY_HI = 7;
    /**
     * 赠送的vip
     */
    int REWARD_VIP = 8;
    /**
     * 奖励
     */
    int REWARD = 13;
    /**
     * 华为内购
     */
    int HMS_RECHARGE = 15;
    /**
     * 订阅奖励
     */
    int MEMBER_REWARD = 14;
    /**
     * 邀请人获得金币
     */
    int INVITE_COMMISSION = 17;
    /**
     * 被邀请人绑定邀请码获得奖励
     */
    int INVITE_AWARD = 110;
    /**
     * say hi 回复奖励
     */
    byte TYPE_SAY_HI_REPLY_REWARD = 57;
    /**
     * 老虎机奖励
     */
    byte SLOT_AWARD = 59;
    /**
     * 音频通话
     */
    int AUDIO_CHAT = 60;
    /**
     * 视频通话
     */
    int VIDEO_CHAT = 10;

    /**
     * 签到金币奖励
     */
    byte SIGN_GOLD = 38;
    /**
     * 砸蛋金币奖励
     */
    int SMASH_EGG_GOLD = 39;
    /**
     * 时长宝箱金币奖励
     */
    byte TIME_BOX_GOLD = 35;
    /**
     * 活动奖励金币
     */
    byte ACTIVITY_REWARD = 88;
    /**
     * 广告奖励
     */
    byte AD_REWARD = 34;
    /**
     * 结算
     */
    byte CASH_OUT = 80;
    /**
     * 打开通知奖励
     */
    byte OPEN_NOTIFICATION_REWARD = 86;
    /**
     * 第三方游戏金币操作
     */
    int HKYS_GAME_GOLD = 99;
    /**
     * 苹果支付
     */
    byte IOS_RECHARGE = 123;
    /**
     * 每日首充奖励
     */
    @Deprecated
    byte DAILY_RECHARGE_CAROUSEL = -99;

    /**
     * 送礼物
     */
    byte SEND_GIFT = 20;
    /**
     * 获得礼物
     */
    int GAIN_GIFT = 21;
    /**
     * 中台支付
     */
    byte MIDDLE_PAY = 119;
    /**
     * 苹果支付
     */
    byte APPLE_APY = 123;
    /**
     * 兑换
     */
    byte EXCHANGE = 118;

    /**
     * 评价奖励
     */
    byte EVALUATION_REWARD = 71;

    /**
     * 迁移账号奖励
     */
    byte MIGRATION_REWARD = 73;

    /**
     * 房间购买
     */
    byte ROOM_PURCHASE = 6;

    /**
     * 消耗钻石兑换金币
     */
    byte REDEEM = 5;
    /**
     * 提现
     */
    byte WITHDRAW = 12;
    /**
     * 币商充值
     */
    byte COIN_DEALERS_RECHARGE = 19;
    /**
     * 发送弹幕扣费
     */
    byte BARRAGE = 27;
    /**
     * 贵族等级购买
     */
    byte LORD = 28;
    /**
     * 移动web端，添加主屏幕奖励
     */
    byte ADD_TO_SCREEN = 87;
    /**
     * 购买视频
     */
    byte BUY_VIDEO = 50;
    /**
     * 主播视频收益
     */
    byte RECEIVE_VIDEO = 51;

    /**
     * web奖励
     */
    int WEB_PUSH_SUBSCRIBE = 128;
    /**
     * 游戏邀请结算
     */
    int GAME_INVITE_SETTLEMENT = 26;

    /**
     * 玩老虎机
     */
    int PLAY_SLOT_TYPE = 61;
    /**
     * 老虎机奖励
     */
    int SLOT_REWARD = 59;

    int OTHER = 85;

    /**
     * 代理佣金下发
     */
    int AGENT_COMMISSION = 30;
    /**
     * 任务下发奖励
     */
    int TASK_REWARD = 18;

    /**
     * 被邀请方 封号扣除钻石
     */
    int DEDUCTION_OF_INVITATION_REWARDS = 52;
    /**
     * 主播守护奖励
     */
    int HOST_GUARD_REWARDS = 53;

    /**
     * 积分过期扣除
     */
    int DEDUCTION_OF_EXPIRED = 222;

    /**
     * 游戏积分抽奖
     */
    int GAME_POINTS_PRIZE_DRAW = 99;

    /**
     * 游戏积分 消费金币赠送的
     */
    int GAME_POINTS_CONSUMER_GOLD_GIVEAWAY = 155;

    /**
     * 星动嘉年华抽奖扣费
     */
    int STAR_GALA = 42;

    /**
     * TG支付
     */
    int TELEGRAM_PAY = 129;

    /**
     * 邀请人数档位
     */
    int INVITEE_NUM_GEAR_TYPE = 55;
    /**
     * adx广告奖励
     */
    int ADX_GAIN = 43;

    /**
     * 房间火箭奖励
     */
    int ROOM_ROCKET_REWARDS = 56;

    /**
     * 首次分享点击奖励钻石
     */
    int REWARD_FIRST_SHARE_REWARD_DIAMONDS = 130;

    /**
     * 邀请嘉年华
     */
    int INVITATION_WHEEL_REWARD = 131;

    /**
     * 房间 红包
     */
    int SEND_ROOM_RED_ENVELOPE = 132;
    /**
     * 抢红包
     */
    int GRAB_ROOM_RED_ENVELOPE = 133;
    /**
     * 返还红包
     */
    int REFUND_ROOM_RED_ENVELOPE = 134;

    /**
     * 迁移
     */
    int MIGRATION_CHANGE = 135;

    /**
     * 游戏大厅绑定手机奖励
     */
    int GAME_LOBBY_BIND_PHONE_REWARD = 136;

    /**
     * 非会员发送私信文字消息价格
     */
    int NOT_MEMBERSHIP_SEND_PRIVATE_TEXT_MESSAGE_PRICE = 137;

    /**
     * 贵族金币返币
     */
    int ARISTOCRAT_REWARDS = 138;

    /**
     * 评分奖励
     */
    int RATING_REWARD = 139;

    /**
     * 游戏兑换金币
     */
    int GAME_COIN_EXCHANGE_GOLD = 140;

    /**
     * 游戏积分抽奖流水
     */
    int GAME_PRIZE_DRAW = 141;

    List<Integer> CHAT_LISt = Arrays.asList(VIDEO_CHAT, AUDIO_CHAT);

    List<Integer> AGENT_PERFORMANCE_DEAL_ACT_LIST = Arrays.asList(GAIN_GIFT, VIDEO_CHAT);
    /**
     * 玩游戏ActType
     */
    Set<Integer> PLAY_GAME_ACT_TYPE = new HashSet<Integer>(3) {{
        add(HKYS_GAME_GOLD);
        add(SMASH_EGG_GOLD);
        add(PLAY_SLOT_TYPE);
    }};
    /**
     * 游戏获得奖励ActType
     */
    Set<Integer> GAME_REWARD_ACT_TYPE = new HashSet<Integer>(3) {{
        add(HKYS_GAME_GOLD);
        add(SMASH_EGG_GOLD);
        add(SLOT_REWARD);
    }};
    /**
     * 平台奖励actType
     */
    Set<Integer> USER_PLATFORM_REWARD_ACT_SET = new HashSet<Integer>(2) {{
        add(TASK_REWARD);
        add((int) ACTIVITY_REWARD);
    }};

    Set<Integer> COMMISSION_ACT_SET = new HashSet<Integer>(3) {{
        add(AGENT_COMMISSION);
        add(INVITE_COMMISSION);
        add(GAME_INVITE_SETTLEMENT);
    }};

    Set<Integer> STATEMENT_ACT_LIST = new HashSet<Integer>(6) {{
        add(GAIN_GIFT);
        addAll(COMMISSION_ACT_SET);
        add(TASK_REWARD);
        add((int) REDEEM);
    }};

    Set<Integer> GAME_ACT_LIST = new HashSet<Integer>(3) {{
        add(PLAY_SLOT_TYPE);
        add(SMASH_EGG_GOLD);
        add(HKYS_GAME_GOLD);
    }};


    Set<Integer> MONITOR_COMMISSION_ACT_SET = new HashSet<Integer>(3) {{
        add(AGENT_COMMISSION);
        add(GAME_INVITE_SETTLEMENT);
    }};
}
