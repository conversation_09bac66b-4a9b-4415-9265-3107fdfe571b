package com.quhong.dao;

import com.quhong.dao.datas.db.EventMonitorConfigData;
import com.quhong.dao.mapper.db.EventMonitorConfigMapper;
import com.quhong.utils.StringUtils;
import com.quhong.utils.TKUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * 事件监控配置(event_monitor_config)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-28 11:25:48
 */
@Lazy
@Slf4j
@Component
@RequiredArgsConstructor
public class EventMonitorConfigDao {

    private static final String ONE_KEY = "str:event_monitor_config";

    private final EventMonitorConfigMapper mapper;

    @CachePut(value = ONE_KEY, key = "#data.monitorKey")
    public EventMonitorConfigData insertOneSelective(EventMonitorConfigData data) {
        int code = mapper.insertSelective(data);
        if(code < 0) return null;
        return getOneByKey(data.getMonitorKey());
    }

    @CachePut(value = ONE_KEY, key = "#data.monitorKey")
    public EventMonitorConfigData updateOneSelective(EventMonitorConfigData data) {
        int code = mapper.updateByPrimaryKeySelective(data);
        if(code < 0) return null;
        return getOneByKey(data.getMonitorKey());
    }

    @Cacheable(value = ONE_KEY, key = "#monitorKey")
    public EventMonitorConfigData getOneByKey(String monitorKey){
        Example example = TKUtils.creatExample(EventMonitorConfigData.class);
        example.createCriteria().andEqualTo("monitorKey", monitorKey);
        return mapper.selectOneByExample(example);
    }


    /**
     * 运营平台使用
     */
    public List<EventMonitorConfigData> queryListByCondition(String monitorKey, String eventCode, String name, Integer valid){
        Example example = TKUtils.creatExample(EventMonitorConfigData.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.hasLength(monitorKey)) {
            criteria.andLike("monitorKey", "%" + monitorKey + "%");
        }
        if (StringUtils.hasLength(eventCode)) {
            criteria.andEqualTo("eventCode", eventCode);
        }
        if (StringUtils.hasLength(name)) {
            criteria.andLike("name", "%" + name + "%");
        }
        if (valid != null) {
            criteria.andEqualTo("valid", valid);
        }
        return mapper.selectByExample(example);
    }
}

