package com.quhong.dao;

import com.quhong.dao.datas.AdCostData;
import com.quhong.dao.mapper.log.AdCostMapper;
import com.quhong.dao.slave.mapper.log.AdCostSlaveMapper;
import com.quhong.data.thData.LogAdCostData;
import com.quhong.report.EventReport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName AdCostDao
 * <AUTHOR>
 * @date 2022/9/16 11:39
 */
@Component
@Lazy
public class AdCostDao {
    private static final Logger logger = LoggerFactory.getLogger(AdCostDao.class);

    @Autowired
    private AdCostMapper adCostMapper;
    @Autowired
    private AdCostSlaveMapper adCostSlaveMapper;
    @Autowired
    private EventReport eventReport;

    public void insert(AdCostData adCostData, String uid) {
        try {
            adCostMapper.insertSelective(adCostData);
            saveToThirdDB(adCostData, uid);
        } catch (Exception e) {
            logger.error("save ad cost to db error. e={} {} {}", e.getMessage(), e, e.getStackTrace());
        }
    }

    public void saveToThirdDB(AdCostData adCostData, String uid) {
        LogAdCostData logData = new LogAdCostData();
        logData.copyFrom(adCostData, uid);
        eventReport.track(logData);
    }

    public List<AdCostData> queryList(String username) {
        return adCostSlaveMapper.selectList(username);
    }

    public AdCostData getDataById(Integer id) {
        return adCostSlaveMapper.selectDataById(id);
    }

    public void delData(Integer id, String uid) {
        AdCostData adCostData = getDataById(id);
        adCostData.setAdCostMoney("-" + adCostData.getAdCostMoney());
        saveToThirdDB(adCostData, uid);
        adCostMapper.delData(id);
    }

    public List<AdCostData> queryListByTime(int startTime, int endTime) {
        return adCostSlaveMapper.selectListByTime(startTime, endTime);
    }
}
