package com.quhong.dao;

import com.quhong.constant.ActType;
import com.quhong.constant.SegCodeConstant;
import com.quhong.core.datas.DayTimeData;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.DateUtils;
import com.quhong.dao.datas.MoneyDetailData;
import com.quhong.dao.doris.DorisMoneyDetailDao;
import com.quhong.dao.mapper.db.MoneyDetailMapper;
import com.quhong.dao.slave.mapper.db.MoneyDetailSlaveMapper;
import com.quhong.data.CountData;
import com.quhong.data.bo.AgentCountBO;
import com.quhong.data.dto.QueryActorRankFromDbDTO;
import com.quhong.data.utils.SubTableSupport;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.HostIncomeVO;
import com.quhong.data.vo.HostIncomeWithActTypeVO;
import com.quhong.data.vo.MoneyHistoryVO;
import com.quhong.utils.TKUtils;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;

public class MoneyDetailDao extends TestUserBaseDao {
    private static final Logger logger = LoggerFactory.getLogger(MoneyDetailDao.class);
    /**
     * 用户充值相关的ActType
     */
    public static final Set<Integer> RECHARGE_ACT_TYPE_SET = new LinkedHashSet<>();
    @Resource
    protected MoneyDetailMapper moneyDetailMapper;

    @Resource
    private MoneyDetailSlaveMapper moneyDetailSlaveMapper;

    private static final String BASE_TABLE_NAME = "t_money_detail";

    private static String dbName = "kissu";

    @Value("${server.type:dev}")
    private String type;

    protected DateHelper dateHelper;

    public MoneyDetailDao() {
//        logger.info("MoneyDetailDao init {}", type);
//        if(type.equals(ServerType.PRODUCT)){
//            dateHelper = DateHelper.INDIAN;
//        }else{
        dateHelper = DateHelper.UTC;
//        }
        RECHARGE_ACT_TYPE_SET.add(2);
        RECHARGE_ACT_TYPE_SET.add(14);
        RECHARGE_ACT_TYPE_SET.add(15);
        RECHARGE_ACT_TYPE_SET.add(16);
        RECHARGE_ACT_TYPE_SET.add(81);
        RECHARGE_ACT_TYPE_SET.add(119);
        RECHARGE_ACT_TYPE_SET.add(120);
        RECHARGE_ACT_TYPE_SET.add(123);
        RECHARGE_ACT_TYPE_SET.add(124);
        RECHARGE_ACT_TYPE_SET.add(125);
        RECHARGE_ACT_TYPE_SET.add(126);
        RECHARGE_ACT_TYPE_SET.add(127);
    }

    /**
     * actor的金币变化相关统计
     *
     * @param days       日期列表
     * @param uidSet     uid列表
     * @param actTypeSet 金币操作类型
     * @param action     1增加金币 2扣除金币 null不做判定
     * @return 金币变化相关统计 map<uid,金币数>
     */
    public Map<String, Double> getCoinsCountBy(List<DayTimeData> days, Set<String> uidSet, Set<Integer> actTypeSet,
                                               Integer action, Integer currencyCode) {
        //日期和分表后缀处理
        DayTimeData allDateTime = DateUtils.getAllDateTime(days);
        List<String> suffixes = getSuffixes(allDateTime);

        //获取数据
        List<CountData> coinsCountList = moneyDetailSlaveMapper.getCoinsCount(suffixes, allDateTime.getTime(),
                allDateTime.getEndTime(), uidSet, actTypeSet, action, currencyCode);
        Map<String, Double> coinsCountMap = new LinkedHashMap<>();
        if (!CollectionUtils.isEmpty(coinsCountList)) {
            coinsCountList.forEach(data -> coinsCountMap.put(data.getKey(), data.fetchRealCount()));
        }
        return coinsCountMap;
    }

    /**
     * 获取近半年的金币消耗
     *
     * @param uid 用户id
     * @return 消耗的金币数
     */
    public Double getCostGold(String uid) {
        //日期和分表后缀处理
        List<String> suffixList = DateHelper.UTC.getTableSuffixByTime(DateHelper.UTC.getDayOffset(-90), System.currentTimeMillis());
        Double count = moneyDetailSlaveMapper.getCostGold(uid, suffixList);
        return count == null ? 0 : count;
    }

    private List<String> getSuffixes(DayTimeData allDateTime) {
        return getSuffixes(allDateTime.getTime(), allDateTime.getEndTime());
    }

    private List<String> getSuffixes(long startTime, long endTime) {
        List<String> tableSuffixList = dateHelper.getTableSuffixByTime(startTime * 1000L, endTime * 1000L);
        //表名判定
        List<String> suffixes = new ArrayList<>();
        if (!CollectionUtils.isEmpty(tableSuffixList)) {
            tableSuffixList.forEach(suffix -> {
                String currentTableName = BASE_TABLE_NAME + "_" + suffix;
                if (SubTableSupport.checkExist(moneyDetailSlaveMapper, currentTableName, dbName)) {
                    suffixes.add(suffix);
                }
            });
        }
        return suffixes;
    }

    /**
     * 获取用户总收益
     *
     * @param timeData
     * @param uid
     * @return
     */
    public double getGainGold(DayTimeData timeData, String uid) {
        String tableSuffix = SubTableSupport.getTableSuffix(timeData);
        String currentTableName = BASE_TABLE_NAME + "_" + tableSuffix;
        if (!(SubTableSupport.checkExist(moneyDetailSlaveMapper, currentTableName, dbName))) {
            logger.error("current table not exist ;table name = {},date = {}", currentTableName, timeData.getDate());
            return 0;
        }
        Double value = moneyDetailSlaveMapper.getGainGold(tableSuffix, uid, timeData.getTime(), timeData.getEndTime());
        return value == null ? 0 : value;
    }

    /**
     * 获取用户总收益
     *
     * @param timeData
     * @param uid
     * @return
     */
    public double getGainGold(DayTimeData timeData, String uid, int deleted) {
        String tableSuffix = SubTableSupport.getTableSuffix(timeData);
        String currentTableName = BASE_TABLE_NAME + "_" + tableSuffix;
        if (!(SubTableSupport.checkExist(moneyDetailSlaveMapper, currentTableName, dbName))) {
            logger.error("current table not exist ;table name = {},date = {}", currentTableName, timeData.getDate());
            return 0;
        }
        Double value = moneyDetailSlaveMapper.getGainGold(tableSuffix, uid, timeData.getTime(), timeData.getEndTime());
        return value == null ? 0 : value;
    }

    public List<MoneyDetailData> getPerformanceHistory(long startTime, long endTime, int start, int size, String suffix) {
        return moneyDetailSlaveMapper.selectPerformanceHistory(startTime, endTime, start, size, suffix);
    }

    public MoneyDetailData getItem(String userId, int actType, int actId) {
        String suffix = dateHelper.getTableSuffix();
        MoneyDetailData data = getItem(suffix, userId, actType, actId);
        if (data != null) {
            return data;
        }
        suffix = dateHelper.getTableSuffix(new Date(dateHelper.getDayOffset(-1)));
        return getItem(suffix, userId, actType, actId);
    }

    public MoneyDetailData getItem(String suffix, String userId, int actType, int actId) {
        //检查分表是否存在
        String tableName = BASE_TABLE_NAME + "_" + suffix;
        boolean isExist = SubTableSupport.checkExist(moneyDetailMapper, tableName, dbName);
        if (isExist) {
            Example example = TKUtils.creatExample(MoneyDetailData.class);
            example.setTableName(tableName);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("userid", userId);
            if (actType != -1) {
                criteria.andEqualTo("actType", actType);
            }
            if (actId != -1) {
                criteria.andEqualTo("actId", actId);
            }
            List<MoneyDetailData> list = moneyDetailMapper.selectByExampleAndRowBounds(example, new RowBounds(0, 1));
            if (list.isEmpty()) {
                logger.error("can not find host info data. uid={}", userId);
                return null;
            }
            return list.get(0);
        }
        logger.error("find error current table not exist;table name = {}", tableName);
        return null;
    }

    public void insert(MoneyDetailData data) {
        String suffix = SubTableSupport.getTableSuffix(data.getCtime());
        String tableName = BASE_TABLE_NAME + "_" + suffix;
        SubTableSupport.checkAndCreate(moneyDetailMapper, tableName, BASE_TABLE_NAME, dbName);
        //插入操作
        data.setSuffix(suffix);
        moneyDetailMapper.insertSelective(data);
    }

    public List<MoneyDetailData> getList(String suffix, int start, int size) {
        boolean b = SubTableSupport.checkExist(moneyDetailMapper, BASE_TABLE_NAME + "_" + suffix, dbName);
        if (!b) {
            return new ArrayList<>();
        }
        return moneyDetailSlaveMapper.selectList(suffix, start, size);
    }

    public List<MoneyDetailData> getFixList(String suffix, long startTime, long endTime, int start, int size) {
        boolean b = SubTableSupport.checkExist(moneyDetailMapper, BASE_TABLE_NAME + "_" + suffix, dbName);
        if (!b) {
            return new ArrayList<>();
        }
        return moneyDetailSlaveMapper.selectFixList(suffix, startTime, endTime, start, size);
    }

    public void update(MoneyDetailData data) {
        String suffix = SubTableSupport.getTableSuffix(data.getCtime());
        String tableName = BASE_TABLE_NAME + "_" + suffix;
        SubTableSupport.checkAndCreate(moneyDetailMapper, tableName, BASE_TABLE_NAME, dbName);
        //插入操作
        data.setSuffix(suffix);
        moneyDetailMapper.updateByPrimaryKey(data);
    }


    /**
     * 获取交易金币：多表查询
     *
     * @param start    开始时间戳
     * @param end      结束时间戳
     * @param action   金币操作1获取金币2扣除金币
     * @param typeList
     * @return
     */
    public Double getChangeGold(int start, int end, int action, List<Integer> typeList, Set<String> uidSet) {
        List<DayTimeData> list = DateHelper.UTC.getMonTypeDayTimeData(start * 1000L, end * 1000L);
        double gold = 0;
        for (DayTimeData dayTimeData : list) {
            Double changeGold = getChangeGold(dayTimeData, action, typeList, uidSet);
            gold = gold + changeGold;
        }
        return gold;
    }

    /**
     * 获取交易金币数
     *
     * @param timeData
     * @param action
     * @param typeList
     * @return
     */
    public Double getChangeGold(DayTimeData timeData, int action, List<Integer> typeList, Set<String> uidSet) {
        String tableSuffix = SubTableSupport.getTableSuffix(timeData);
        String currentTableName = BASE_TABLE_NAME + "_" + tableSuffix;
        if (!(SubTableSupport.checkExist(moneyDetailMapper, currentTableName, dbName))) {
            logger.error("current table not exist ;table name = {},date = {}", currentTableName, timeData.getDate());
            return 0.0;
        }
        int start = timeData.getTime();
        int end = timeData.getEndTime();
        //uidSet 过滤测试账号
        uidSet = getNormalUserSet(uidSet);
        Double res = moneyDetailMapper.selectChangeGold(currentTableName, start, end, action, typeList, uidSet);
        return res == null ? 0.0 : res;

    }

    /**
     * 获取在特定时间内的充值用户
     */
    public Set<String> getPaidUserSet(String date) {
        Integer[] timeArr = DateHelper.UTC.getTimeWhereRange(date);
        return getChangeGoldUserList(timeArr[0], --timeArr[1], 1, Arrays.asList(2, 81, 125, 126, 127, 123, 124, 14, 15, 120, 16, 119));
    }

    /**
     * 获取在特定时间内的交易金币用户
     */
    public Set<String> getChangeGoldUserList(int start, int end, int action, List<Integer> typeList) {
        Set<String> result = new HashSet<>();
        List<DayTimeData> list = DateHelper.UTC.getMonTypeDayTimeData(start * 1000L, end * 1000L);
        for (DayTimeData timeData : list) {
            String tableSuffix = SubTableSupport.getTableSuffix(timeData);
            String currentTableName = BASE_TABLE_NAME + "_" + tableSuffix;
            if (!(SubTableSupport.checkExist(moneyDetailMapper, currentTableName, dbName))) {
                logger.error("current table not exist, table name={}, date={}", currentTableName, timeData.getDate());
                return result;
            }
            result.addAll(moneyDetailSlaveMapper.selectChangeGoldUserList(currentTableName, timeData.getTime(), timeData.getEndTime(), action, typeList));
        }
        return result;
    }

    public List<HostIncomeVO> getIncomeFromCallAndGift(Set<String> hostList, DayTimeData dayTimeData) {
        String tableSuffix = SubTableSupport.getTableSuffix(dayTimeData);
        String currentTableName = BASE_TABLE_NAME + "_" + tableSuffix;
        boolean result = SubTableSupport.checkExist(moneyDetailSlaveMapper, currentTableName, dbName);
        if (!result) {
            return new ArrayList<>();
        }
        return moneyDetailSlaveMapper.selectIncomeFromCallAndGift(hostList, dayTimeData.getTime(), dayTimeData.getEndTime(), tableSuffix);
    }

    /**
     * new
     *
     * @see DorisMoneyDetailDao#getHostTotalIncome(Set, DayTimeData)
     */
    public List<HostIncomeVO> getHostTotalIncome(Set<String> hostList, DayTimeData dayTimeData) {
        String tableSuffix = SubTableSupport.getTableSuffix(dayTimeData);
        String currentTableName = BASE_TABLE_NAME + "_" + tableSuffix;
        boolean result = SubTableSupport.checkExist(moneyDetailSlaveMapper, currentTableName, dbName);
        if (!result) {
            return new ArrayList<>();
        }
        return moneyDetailSlaveMapper.selectHostTotalIncome(hostList, dayTimeData.getTime(), dayTimeData.getEndTime(), tableSuffix);
    }

    public List<HostIncomeWithActTypeVO> getHostTotalIncomeWithActType(Set<String> hostList, DayTimeData dayTimeData, Integer actType) {
        List<String> suffixs = getSuffixes(dayTimeData);
        List<HostIncomeWithActTypeVO> res = new ArrayList<>();
        for (String suffix : suffixs) {
            res.addAll(moneyDetailSlaveMapper.selectHostTotalIncomeWithActType(hostList, dayTimeData.getTime(), dayTimeData.getEndTime(), actType, suffix));
        }
        return res;
    }

    public HostIncomeVO getHostIncome(String uid, DayTimeData dayTimeData) {
        String tableSuffix = SubTableSupport.getTableSuffix(dayTimeData);
        String currentTableName = BASE_TABLE_NAME + "_" + tableSuffix;
        boolean result = SubTableSupport.checkExist(moneyDetailSlaveMapper, currentTableName, dbName);
        if (!result) {
            return null;
        }
        return moneyDetailSlaveMapper.selectHostIncome(uid, dayTimeData.getTime(), dayTimeData.getEndTime(), tableSuffix);
    }

    public HostIncomeVO getHostIncome(String uid, int startTime, int endTime) {
        DayTimeData dayTimeData = new DayTimeData();
        dayTimeData.setTime(startTime);
        dayTimeData.setEndTime(endTime);
        List<String> suffixList = getSuffixes(dayTimeData);
        return moneyDetailSlaveMapper.selectHostTotalIncomeByUid(uid, dayTimeData.getTime(), dayTimeData.getEndTime(), suffixList);
    }

    /**
     * new
     *
     * @see com.quhong.dao.doris.DorisMoneyDetailDao#getHostTotalDiamondIncome(java.lang.String, long, long)
     */
    public HostIncomeVO getHostTotalDiamondIncome(String uid, long startTime, long endTime) {
        DayTimeData dayTimeData = new DayTimeData();
        dayTimeData.setTime((int) startTime);
        dayTimeData.setEndTime((int) endTime);
        List<String> suffixList = getSuffixes(dayTimeData);
        return moneyDetailSlaveMapper.selectHostTotalDiamondIncomeByUid(uid, dayTimeData.getTime(), dayTimeData.getEndTime(), suffixList);
    }


    /**
     * new
     *
     * @see com.quhong.dao.doris.DorisMoneyDetailDao#getHostTotalDiamondIncomeByTypeSeg(java.lang.String, int, int, java.util.List, java.util.List)
     */
    public HostIncomeVO getHostTotalDiamondIncomeByTypeSeg(String uid, int startTime, int endTime, List<Integer> actTypeList, List<Integer> segmentCodeList) {
        DayTimeData dayTimeData = new DayTimeData();
        dayTimeData.setTime(startTime);
        dayTimeData.setEndTime(endTime);
        List<String> suffixList = getSuffixes(dayTimeData);
        if (CollectionUtils.isEmpty(suffixList)) {
            HostIncomeVO hostIncomeVO = new HostIncomeVO();
            hostIncomeVO.setUid(uid);
            hostIncomeVO.setIncome(0);
            hostIncomeVO.setRealIncome(0);
            return hostIncomeVO;
        }
        return moneyDetailMapper.selectHostTotalDiamondIncomeByUidActType(uid, dayTimeData.getTime(), dayTimeData.getEndTime(), actTypeList, suffixList, segmentCodeList);
    }

    public HostIncomeVO getHostIncomeFromCallAndGift(String uid, DayTimeData dayTimeData) {
        String tableSuffix = SubTableSupport.getTableSuffix(dayTimeData);
        String currentTableName = BASE_TABLE_NAME + "_" + tableSuffix;
        boolean result = SubTableSupport.checkExist(moneyDetailSlaveMapper, currentTableName, dbName);
        if (!result) {
            return null;
        }
        return moneyDetailSlaveMapper.selectHostIncomeFromCallAndGift(uid, dayTimeData.getTime(), dayTimeData.getEndTime(), tableSuffix);
    }

    public HostIncomeVO getHostIncomeFromMessageGiftSharding(String uid, DayTimeData allDateTime, String channel) {
        HostIncomeVO incomeData = new HostIncomeVO();
        incomeData.setUid(uid);
        List<String> suffixes = getSuffixes(allDateTime);
        if (CollectionUtils.isEmpty(suffixes)) {
            return incomeData;
        }
        double income = suffixes.stream()
                .map(suffix -> moneyDetailSlaveMapper.selectHostIncomeByActTypeAndSegCode(uid, allDateTime.getTime(), allDateTime.getEndTime(), suffix, ActType.GAIN_GIFT, SegCodeConstant.GIFT_MESSAGE))
                .filter(Objects::nonNull)
                .mapToDouble(HostIncomeVO::fetchRealIncome).sum();
        incomeData.setIncome((int) income);
        incomeData.setRealIncome(income);
        return incomeData;
    }

    public HostIncomeVO getHostIncomeFromGameInvite(String uid, DayTimeData allDateTime) {
        HostIncomeVO incomeData = new HostIncomeVO();
        incomeData.setUid(uid);
        List<String> suffixes = getSuffixes(allDateTime);
        if (CollectionUtils.isEmpty(suffixes)) {
            return incomeData;
        }
        double income = suffixes.stream()
                .map(suffix -> moneyDetailSlaveMapper.selectHostIncomeByActTypeAndSegCode(uid, allDateTime.getTime(), allDateTime.getEndTime(), suffix, ActType.GAME_INVITE_SETTLEMENT, 0))
                .filter(Objects::nonNull)
                .mapToDouble(HostIncomeVO::fetchRealIncome)
                .sum();
        incomeData.setIncome((int) income);
        incomeData.setRealIncome(income);
        return incomeData;
    }

    public HostIncomeVO getHostIncomeFromPartyRoomSharding(String uid, DayTimeData allDateTime, String channel) {
        HostIncomeVO incomeData = new HostIncomeVO();
        incomeData.setUid(uid);
        List<String> suffixes = getSuffixes(allDateTime);
        if (CollectionUtils.isEmpty(suffixes)) {
            return incomeData;
        }

        double income = suffixes.stream()
                .map(suffix -> moneyDetailSlaveMapper.selectHostIncomeFromPartyRoom(uid, allDateTime.getTime(), allDateTime.getEndTime(), suffix))
                .filter(Objects::nonNull)
                .mapToDouble(HostIncomeVO::fetchRealIncome).sum();
        incomeData.setIncome((int) income);
        incomeData.setRealIncome(income);
        return incomeData;
    }

    public HostIncomeVO getGiftIncome(String uid, DayTimeData dayTimeData) {
        String tableSuffix = SubTableSupport.getTableSuffix(dayTimeData);
        String currentTableName = BASE_TABLE_NAME + "_" + tableSuffix;
        boolean result = SubTableSupport.checkExist(moneyDetailSlaveMapper, currentTableName, dbName);
        if (!result) {
            return null;
        }
        return moneyDetailSlaveMapper.selectGiftIncome(uid, dayTimeData.getTime(), dayTimeData.getEndTime(), tableSuffix);
    }

    /**
     * new
     *
     * @see DorisMoneyDetailDao#getHostIncomeByTime(Set, int, int)
     */
    public List<HostIncomeVO> getHostIncomeByTime(Set<String> highQualityHost, int startTime, int endTime) {
//        List<String> suffixList = SubTableSupport.getSuffixList(moneyDetailSlaveMapper, BASE_TABLE_NAME, startTime, endTime);
        DayTimeData dayTimeData = new DayTimeData();
        dayTimeData.setTime(startTime);
        dayTimeData.setEndTime(endTime);
        List<String> suffixList = getSuffixes(dayTimeData);
        if (CollectionUtils.isEmpty(suffixList)) {
            return new ArrayList<>();
        }
        return moneyDetailSlaveMapper.selectHostIncomeByTime(suffixList, highQualityHost, startTime, endTime);
    }

    /**
     * new
     *
     * @see DorisMoneyDetailDao#getDiamondIncomeByUidSetAndActTypeSetAndIgnoreActTypeSet(Set, Set, Set, long, long)
     */
    public List<CountVO> getDiamondIncomeByUidSetAndActTypeSetAndIgnoreActTypeSet(Set<String> uidSet,
                                                                                  Set<Integer> actTypeSet,
                                                                                  Set<Integer> ignoreActTypeSet,
                                                                                  long startTime,
                                                                                  long endTime) {
        if (ObjectUtils.isEmpty(uidSet)) {
            return new ArrayList<>(0);
        }
        List<String> suffixes = getSuffixes(startTime, endTime);
        if (ObjectUtils.isEmpty(suffixes)) {
            return new ArrayList<>(0);
        }
        List<CountVO> dataList = moneyDetailSlaveMapper.selectDiamondSumIncomeByUidSetAndActTypeSetAndIgnoreActTypeSet(suffixes, uidSet, actTypeSet, ignoreActTypeSet, startTime, endTime);
        if (ObjectUtils.isEmpty(dataList)) {
            return new ArrayList<>(0);
        }
        return dataList;
    }

    public CountVO getDiamondIncomeByUidAndActTypeSetAndIgnoreActTypeAndSegCode(String uid,
                                                                                Set<Integer> actTypeSet,
                                                                                Set<Integer> ignoreActTypeSet,
                                                                                Integer segCode,
                                                                                long startTime,
                                                                                long endTime) {
        if (StringUtils.isEmpty(uid)) {
            return new CountVO();
        }
        List<String> suffixes = getSuffixes(startTime, endTime);
        if (CollectionUtils.isEmpty(suffixes)) {
            return new CountVO();
        }
        return moneyDetailSlaveMapper.selectDiamondIncomeByUidAndActTypeSetAndIgnoreActTypeAndSegCode(suffixes, uid, actTypeSet, ignoreActTypeSet, segCode, startTime, endTime);
    }

    /**
     * new
     *
     * @see DorisMoneyDetailDao#getHostGiftIncomeByTime(Set, int, int)
     */
    public List<HostIncomeVO> getHostGiftIncomeByTime(Set<String> highQualityHost, int startTime, int endTime) {
//        List<String> suffixList = SubTableSupport.getSuffixList(moneyDetailSlaveMapper, BASE_TABLE_NAME, startTime, endTime);
        DayTimeData dayTimeData = new DayTimeData();
        dayTimeData.setTime(startTime);
        dayTimeData.setEndTime(endTime);
        List<String> suffixList = getSuffixes(dayTimeData);
        if (CollectionUtils.isEmpty(suffixList)) {
            return new ArrayList<>();
        }
        return moneyDetailSlaveMapper.selectHostGiftIncomeByTime(suffixList, highQualityHost, startTime, endTime);
    }

    /**
     * new
     *
     * @see DorisMoneyDetailDao#getTaskIncomeByTime(Set, int, int)
     */
    public List<HostIncomeVO> getTaskIncomeByTime(Set<String> highQualityHost, int startTime, int endTime) {
        DayTimeData dayTimeData = new DayTimeData();
        dayTimeData.setTime(startTime);
        dayTimeData.setEndTime(endTime);
        List<String> suffixList = getSuffixes(dayTimeData);
        if (CollectionUtils.isEmpty(suffixList)) {
            return new ArrayList<>();
        }
        return moneyDetailSlaveMapper.selectTaskIncomeByTime(suffixList, highQualityHost, startTime, endTime);
    }

    /**
     * new
     *
     * @see DorisMoneyDetailDao#getHostTotalCallIncomeByTime(Set, int, int)
     */
    public List<HostIncomeVO> getHostTotalCallIncomeByTime(Set<String> highQualityHost, int startTime, int endTime) {
        DayTimeData dayTimeData = new DayTimeData();
        dayTimeData.setTime(startTime);
        dayTimeData.setEndTime(endTime);
        List<String> suffixList = getSuffixes(dayTimeData);
        if (CollectionUtils.isEmpty(suffixList)) {
            return new ArrayList<>();
        }
        return moneyDetailSlaveMapper.selectHostTotalCallIncomeByTime(suffixList, highQualityHost, startTime, endTime);
    }

    public List<HostIncomeVO> getHostCallAndGiftIncomeByTime(Set<String> highQualityHost, int startTime, int endTime) {
        DayTimeData dayTimeData = new DayTimeData();
        dayTimeData.setTime(startTime);
        dayTimeData.setEndTime(endTime);
        List<String> suffixList = getSuffixes(dayTimeData);
//        List<String> suffixList = SubTableSupport.getSuffixList(moneyDetailSlaveMapper, BASE_TABLE_NAME, startTime, endTime);
        if (CollectionUtils.isEmpty(suffixList)) {
            return new ArrayList<>();
        }
        return moneyDetailSlaveMapper.selectHostCallAndGiftIncomeByTime(suffixList, highQualityHost, startTime, endTime);
    }

    /**
     * 查询主播收入/用户充值榜单
     *
     * @param dto
     * @return
     */
    public List<HostIncomeVO> getTotalHostIncomeOrActorRecharge(QueryActorRankFromDbDTO dto) {
        List<String> suffixList = getSuffixes(dto.getStart(), dto.getEnd());
        if (CollectionUtils.isEmpty(suffixList)) {
            return new ArrayList<>();
        }
        dto.setSuffixes(suffixList);
        return moneyDetailSlaveMapper.selectTotalHostIncomeOrActorRecharge(dto);
    }

    public List<HostIncomeVO> getRealTimeRankTotalHostIncomeOrActorRecharge(QueryActorRankFromDbDTO dto) {
        List<String> suffixList = getSuffixes(dto.getStart(), dto.getEnd());
        if (CollectionUtils.isEmpty(suffixList)) {
            return new ArrayList<>();
        }
        dto.setSuffixes(suffixList);
        return moneyDetailSlaveMapper.selectRealTimeRankTotalHostIncomeOrActorRecharge(dto);
    }

    public List<HostIncomeVO> getTotalAgentIncome(QueryActorRankFromDbDTO dto) {
        List<String> suffixList = getSuffixes(dto.getStart(), dto.getEnd());
        if (CollectionUtils.isEmpty(suffixList)) {
            return new ArrayList<>();
        }
        dto.setSuffixes(suffixList);
        return moneyDetailSlaveMapper.selectTotalAgentIncome(dto);
    }

    public List<HostIncomeVO> getTotalAgentIncomeOrActorRecharge(QueryActorRankFromDbDTO dto) {
        List<String> suffixList = getSuffixes(dto.getStart(), dto.getEnd());
        if (CollectionUtils.isEmpty(suffixList)) {
            return new ArrayList<>();
        }
        dto.setSuffixes(suffixList);
        return moneyDetailSlaveMapper.selectTotalHostIncomeOrActorRecharge(dto);
    }

    public List<HostIncomeVO> getHostGiftIncome(String uid, Integer startTime, Integer endTime) {
        DayTimeData dayTimeData = new DayTimeData();
        dayTimeData.setTime(startTime);
        dayTimeData.setEndTime(endTime);
        List<String> suffixList = getSuffixes(dayTimeData);
        if (CollectionUtils.isEmpty(suffixList)) {
            return null;
        }
        return moneyDetailSlaveMapper.selectHostGiftIncome(uid, startTime, endTime, suffixList);
    }

    /**
     * 获取
     *
     * @param agentIdSet
     * @param startTime
     * @param endTime
     * @return
     */
    public Map<Integer, Integer> getAgentHostTotalIncomeMap(Set<Integer> agentIdSet, Integer startTime, Integer endTime, Set<Integer> actTypeSet) {
        Map<Integer, Integer> dataMap = new LinkedHashMap<>();
        DayTimeData dayTimeData = new DayTimeData();
        dayTimeData.setTime(startTime);
        dayTimeData.setEndTime(endTime);
        List<String> suffixList = getSuffixes(dayTimeData);
        if (CollectionUtils.isEmpty(suffixList)) {
            return dataMap;
        }
        List<AgentCountBO> agentCountBOList = moneyDetailSlaveMapper.selectHostTotalIncomeByAgentIdSet(agentIdSet, startTime, endTime, suffixList, actTypeSet);
        if (CollectionUtils.isEmpty(agentCountBOList)) {
            return dataMap;
        }
        agentCountBOList.forEach(data -> dataMap.put(data.getAgentId(), data.getCount()));
        return dataMap;
    }

    public Double getOneIncomeByActId(String uid, DayTimeData dayTimeData, int actType) {
        List<String> suffixList = getSuffixes(dayTimeData);
        if (CollectionUtils.isEmpty(suffixList)) {
            return 0.0;
        }
        Double income = moneyDetailSlaveMapper.selectOneIncomeByActId(uid, actType, dayTimeData.getTime(), dayTimeData.getEndTime(), suffixList);
        return income != null ? income : 0;
    }

    public Double getOneIncomeByActIdAndSegCode(String uid, DayTimeData dayTimeData, int actType, List<Integer> segCodeList) {
        List<String> suffixList = getSuffixes(dayTimeData);
        if (CollectionUtils.isEmpty(suffixList)) {
            return 0.0;
        }
        Double value = moneyDetailSlaveMapper.selectOneIncomeByActIdAndSegCode(uid, actType, segCodeList, dayTimeData.getTime(), dayTimeData.getEndTime(), suffixList);
        return value == null ? 0 : value;
    }

    public Set<String> getActiveUser(DayTimeData dayTimeData, int gold) {
        List<String> suffixList = getSuffixes(dayTimeData);
        if (CollectionUtils.isEmpty(suffixList)) {
            return null;
        }
        return moneyDetailSlaveMapper.selectActiveUser(dayTimeData.getTime(), dayTimeData.getEndTime(), gold, suffixList);
    }

    public int queryPlayGameCount(int startTime, int endTime, String uid, List<String> tableSuffixList, int actType, List<Integer> segCodeList, int action) {
        return moneyDetailSlaveMapper.queryPlayGameCount(uid, startTime, endTime, actType, action, tableSuffixList, segCodeList);
    }

    public Double querySumBy(int startTime, int endTime, String uid, List<String> tableSuffixList, Set<Integer> actTypeSet, List<Integer> segCodeList, int action) {
        return moneyDetailSlaveMapper.querySumBy(uid, startTime, endTime, actTypeSet, action, tableSuffixList, segCodeList);
    }


    public List<MoneyHistoryVO> queryMoneyHistory(int startTime, int endTime, Integer rid, Integer actType, Integer action, Integer currencyCode, Integer segmentCode) {
        DayTimeData dayTimeData = new DayTimeData();
        dayTimeData.setTime(startTime);
        dayTimeData.setEndTime(endTime);
        List<String> suffixList = getSuffixes(dayTimeData);
        if (CollectionUtils.isEmpty(suffixList)) {
            return new ArrayList<>();
        }
        return moneyDetailSlaveMapper.selectMoneyHistory(startTime, endTime, rid, actType, action, currencyCode, suffixList, segmentCode);
    }

    public List<MoneyHistoryVO> queryMoneyHistory(long startTime, long endTime, String uid, Set<Integer> actType, Set<Integer> ignoreActType, Integer action, Integer currencyCode, Integer segmentCode) {
        List<String> suffixList = getSuffixes(startTime, endTime);
        if (CollectionUtils.isEmpty(suffixList)) {
            return new ArrayList<>();
        }
        return moneyDetailMapper.selectMoneyHistoryByUid(startTime, endTime, uid, actType, ignoreActType, action, currencyCode, suffixList, segmentCode);
    }

    public void delMoneyDetail(int id, int ctime) {
        String tableSuffix = SubTableSupport.getTableSuffix(ctime);
        moneyDetailMapper.deleteMoneyDetail(id, tableSuffix);
    }

    public List<HostIncomeVO> getAftChangeByTime(int startTime, int endTime, Set<String> uidList) {
        DayTimeData dayTimeData = new DayTimeData();
        dayTimeData.setTime(startTime);
        dayTimeData.setEndTime(endTime);
        List<String> suffixList = getSuffixes(dayTimeData);
        if (CollectionUtils.isEmpty(suffixList)) {
            return new ArrayList<>();
        }
        if (suffixList.size() > 1) {
            return moneyDetailSlaveMapper.selectAftChangeByTimes(startTime, endTime, uidList, suffixList);
        }
        String suffix = suffixList.get(0);
        return moneyDetailSlaveMapper.selectAftChangeByTime(startTime, endTime, uidList, suffix);
    }

    public MoneyDetailData query4thDetailByAction(String uid) {
        return moneyDetailSlaveMapper.select4thDetailByAction(uid);
    }

    public List<MoneyDetailData> queryMoneyDetailFixList() {
        return moneyDetailSlaveMapper.selectMoneyDetailFixList();
    }
}
