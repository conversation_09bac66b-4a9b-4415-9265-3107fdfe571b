package com.quhong.dao;

import com.quhong.dao.datas.db.AppEventData;
import com.quhong.dao.mapper.db.AppEventMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 事件(app_event)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-05 14:04:03
 */
@Lazy
@Slf4j
@Component
@RequiredArgsConstructor
public class AppEventDao {

    private static final String ONE_KEY = "app_event";
    
    private final AppEventMapper mapper;

    @CachePut(value = ONE_KEY, key = "#data.eventCode")
    public AppEventData insertOneSelective(AppEventData data) {
        int code = mapper.insertSelective(data);
        if(code < 0) return null;
        return getOneByEventCode(data.getEventCode());
    }

    @CachePut(value = ONE_KEY, key = "#data.eventCode")
    public AppEventData updateOneSelective(AppEventData data) {
        int code = mapper.updateByPrimaryKeySelective(data);
        if(code < 0) return null;
        return getOneByEventCode(data.getEventCode());
    }
    
    @Cacheable(value = ONE_KEY, key = "#eventCode")
    public AppEventData getOneByEventCode(Long eventCode){
        return mapper.selectByPrimaryKey(eventCode);
    }
    
    
}

