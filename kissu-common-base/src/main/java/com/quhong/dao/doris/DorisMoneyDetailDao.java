package com.quhong.dao.doris;

import com.quhong.core.constant.WarnName;
import com.quhong.core.datas.DayTimeData;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.MoneyDetailDao;
import com.quhong.dao.datas.MoneyDetailData;
import com.quhong.dao.datas.doris.DorisMoneyDetailData;
import com.quhong.dao.mapper.doris.DorisMoneyDetailMapper;
import com.quhong.data.utils.SubTableSupport;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.HostIncomeVO;
import com.quhong.data.vo.MoneyHistoryVO;
import com.quhong.utils.TKUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * @ClassName DorisMoneyDetailDao
 * <AUTHOR>
 * @date 2024/11/28 15:09
 */
@Component
@Lazy
@Slf4j
public class DorisMoneyDetailDao extends AbstractDorisDao {
    @Resource
    private DorisMoneyDetailMapper mapper;
    @Resource
    private MonitorSender monitorSender;

    public void saveOne(MoneyDetailData moneyDetailData) {
        try {
            String dbName = getDBName();
            DorisMoneyDetailData data = new DorisMoneyDetailData();
            data.copy(moneyDetailData, moneyDetailData.getSuffix());
            mapper.insertOne(data, dbName);
        } catch (Exception e) {
            log.error("money detail save error. data={} e={}", moneyDetailData, e.getMessage(), e);
            monitorSender.info(WarnName.COMMON, "doris 插入失败", String.format("doris insert error e=%s es=%s", e.getMessage(), e));
        }
    }

    public void saveList(List<DorisMoneyDetailData> dataList) {
        String dbName = getDBName();
        mapper.insertList(dataList, dbName);
    }

    public List<MoneyHistoryVO> queryMoneyHistoryFromDoris(long startTime, long endTime, String uid, Set<Integer> actType,
                                                           Set<Integer> ignoreActType, Integer action, Integer currencyCode, Integer segmentCode,
                                                           int start, int size) {
        String dbName = getDBName();
        List<String> partitionIndexList = getPartitionIndex(startTime, endTime);
        if (CollectionUtils.isEmpty(partitionIndexList)) {
            return new ArrayList<>();
        }
        return mapper.selectMoneyHistoryFromDoris(startTime, endTime, uid, actType, ignoreActType, action, currencyCode,
                segmentCode, dbName, partitionIndexList, start, size);
    }

    public List<CountVO> getDiamondIncomeFromDoris(Set<String> uidSet,
                                                   Set<Integer> actTypeSet,
                                                   Set<Integer> ignoreActTypeSet,
                                                   Set<Integer> sgeCodeSet,
                                                   long startTime,
                                                   long endTime) {
        if (ObjectUtils.isEmpty(uidSet)) {
            return new ArrayList<>(0);
        }
        String dbName = getDBName();
        List<String> partitionIndexList = getPartitionIndex(startTime, endTime);
        if (CollectionUtils.isEmpty(partitionIndexList)) {
            return new ArrayList<>();
        }
        List<CountVO> dataList = mapper.getDiamondIncomeFromDoris(uidSet, actTypeSet, ignoreActTypeSet, sgeCodeSet, startTime, endTime, dbName, partitionIndexList);
        if (ObjectUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        return dataList;
    }

    public List<CountVO> getDiamondCostFromDoris(Set<String> uidSet,
                                                 Set<Integer> actTypeSet,
                                                 Set<Integer> ignoreActTypeSet,
                                                 Set<Integer> sgeCodeSet,
                                                 long startTime,
                                                 long endTime) {
        if (ObjectUtils.isEmpty(uidSet)) {
            return new ArrayList<>(0);
        }
        String dbName = getDBName();
        List<String> partitionIndexList = getPartitionIndex(startTime, endTime);
        if (CollectionUtils.isEmpty(partitionIndexList)) {
            return new ArrayList<>();
        }
        List<CountVO> dataList = mapper.getDiamondCostFromDoris(uidSet, actTypeSet, ignoreActTypeSet, sgeCodeSet, startTime, endTime, dbName, partitionIndexList);
        if (ObjectUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        return dataList;
    }

    public CountVO getDiamondIncomeByUidAndActTypeSetAndIgnoreActTypeAndSegCode(String uid,
                                                                                Set<Integer> actTypeSet,
                                                                                Set<Integer> ignoreActTypeSet,
                                                                                Integer segCode,
                                                                                long startTime,
                                                                                long endTime) {
        if (StringUtils.isEmpty(uid)) {
            return new CountVO();
        }
        List<String> partitionIndex = getPartitionIndex(startTime, endTime);
        String dbName = getDBName();
        if (StringUtils.isEmpty(partitionIndex)) {
            return new CountVO();
        }
        return mapper.selectDiamondIncomeByUidAndActTypeSetAndIgnoreActTypeAndSegCode(dbName, partitionIndex, uid, actTypeSet, ignoreActTypeSet, segCode, startTime, endTime);
    }

    public CountVO getStatValue(DayTimeData timeData, int action, Set<Integer> typeSet, Set<String> testUidSet) {
        long start = timeData.getTime();
        long end = timeData.getEndTime();
        List<String> partitionIndex = getPartitionIndex(start, end);
        String dbName = getDBName();
        if (StringUtils.isEmpty(partitionIndex)) {
            return new CountVO();
        }
        return mapper.selectStatValue(dbName, partitionIndex, start, end, action, typeSet, testUidSet);
    }

    /**
     * 最近180天
     *
     * @param uid
     * @param start
     * @param end
     * @return
     */
    public CountVO getConsumeValue(String uid, long start, long end) {
        if (StringUtils.isEmpty(uid)) {
            return new CountVO();
        }
        List<String> partitionIndex = getPartitionIndex(start, end);
        String dbName = getDBName();
        if (StringUtils.isEmpty(partitionIndex)) {
            return new CountVO();
        }
        return mapper.selectConsumeValue(dbName, partitionIndex, uid, start, end);
    }

    public double queryTotalDiamondIncomeByUidSetAndTimeAndActType(Set<String> uidSet, long startTime, long endTime, List<Integer> actType) {
        if (CollectionUtils.isEmpty(uidSet)) {
            return 0;
        }
        List<String> partitionIndex = getPartitionIndex(startTime, endTime);
        if (CollectionUtils.isEmpty(partitionIndex)) {
            return 0;
        }
        String dbName = getDBName();
        Double v = mapper.selectTotalDiamondIncomeByUidSetAndTime(dbName, partitionIndex, uidSet, startTime, endTime, actType);
        return v == null ? 0 : v;
    }

    public List<HostIncomeVO> getHostTotalCallIncomeByTime(Set<String> uidSet, int startTime, int endTime) {
        if (CollectionUtils.isEmpty(uidSet)) {
            return new ArrayList<>();
        }
        List<String> partitionIndex = getPartitionIndex(startTime, endTime);
        if (CollectionUtils.isEmpty(partitionIndex)) {
            return new ArrayList<>();
        }
        String dbName = getDBName();
        return mapper.selectHostTotalCallIncomeByTime(uidSet, startTime, endTime, partitionIndex, dbName);
    }

    public List<HostIncomeVO> getHostGiftIncomeByTime(Set<String> uidSet, int startTime, int endTime) {
        if (CollectionUtils.isEmpty(uidSet)) {
            return new ArrayList<>();
        }
        List<String> partitionIndex = getPartitionIndex(startTime, endTime);
        if (CollectionUtils.isEmpty(partitionIndex)) {
            return new ArrayList<>();
        }
        String dbName = getDBName();
        return mapper.selectHostGiftIncomeByTime(uidSet, startTime, endTime, partitionIndex, dbName);
    }

    public List<HostIncomeVO> getTaskIncomeByTime(Set<String> uidSet, int startTime, int endTime) {
        if (CollectionUtils.isEmpty(uidSet)) {
            return new ArrayList<>();
        }
        List<String> partitionIndex = getPartitionIndex(startTime, endTime);
        if (CollectionUtils.isEmpty(partitionIndex)) {
            return new ArrayList<>();
        }
        String dbName = getDBName();
        return mapper.selectTaskIncomeByTime(uidSet, startTime, endTime, partitionIndex, dbName);
    }

    public List<HostIncomeVO> getHostIncomeByTime(Set<String> uidSet, int startTime, int endTime) {
        if (CollectionUtils.isEmpty(uidSet)) {
            return new ArrayList<>();
        }
        List<String> partitionIndex = getPartitionIndex(startTime, endTime);
        if (CollectionUtils.isEmpty(partitionIndex)) {
            return new ArrayList<>();
        }
        String dbName = getDBName();
        return mapper.selectHostIncomeByTime(uidSet, startTime, endTime, partitionIndex, dbName);
    }

    //待验证
    public List<HostIncomeVO> getAfterChangeByTime(Set<String> uidSet, int startTime, int endTime) {
        if (CollectionUtils.isEmpty(uidSet)) {
            return new ArrayList<>();
        }
        List<String> partitionIndex = getPartitionIndex(startTime, endTime);
        if (CollectionUtils.isEmpty(partitionIndex)) {
            return new ArrayList<>();
        }
        String dbName = getDBName();
        return mapper.selectAfterChangeByTime(uidSet, startTime, endTime, partitionIndex, dbName);
    }

    /**
     * origin
     *
     * @see MoneyDetailDao#getHostTotalDiamondIncome(String, long, long)
     */
    public HostIncomeVO getHostTotalDiamondIncome(String uid, long startTime, long endTime) {
        if (StringUtils.isEmpty(uid)) {
            return new HostIncomeVO();
        }
        List<String> partitionIndex = getPartitionIndex(startTime, endTime);
        if (CollectionUtils.isEmpty(partitionIndex)) {
            return new HostIncomeVO(uid, 0, 0);
        }
        String dbName = getDBName();
        return mapper.selectHostTotalDiamondIncome(dbName, partitionIndex, uid, startTime, endTime);
    }

    /**
     * origin
     *
     * @see MoneyDetailDao#getDiamondIncomeByUidSetAndActTypeSetAndIgnoreActTypeSet(Set, Set, Set, long, long)
     */
    public List<CountVO> getDiamondIncomeByUidSetAndActTypeSetAndIgnoreActTypeSet(Set<String> uidSet,
                                                                                  Set<Integer> actTypeSet,
                                                                                  Set<Integer> ignoreActTypeSet,
                                                                                  long startTime,
                                                                                  long endTime) {
        if (CollectionUtils.isEmpty(uidSet)) {
            return new ArrayList<>(0);
        }
        List<String> partitionIndex = getPartitionIndex(startTime, endTime);
        if (CollectionUtils.isEmpty(partitionIndex)) {
            return new ArrayList<>(0);
        }
        String dbName = getDBName();
        List<CountVO> list = mapper.selectDiamondSumIncomeByUidSetAndActTypeSetAndIgnoreActTypeSet(dbName, partitionIndex, uidSet, actTypeSet, ignoreActTypeSet, startTime, endTime);
        if (ObjectUtils.isEmpty(list)) {
            return new ArrayList<>(0);
        }
        return list;
    }

    /**
     * origin
     *
     * @see MoneyDetailDao#getHostTotalDiamondIncomeByTypeSeg(String, int, int, List, List)
     */
    public HostIncomeVO getHostTotalDiamondIncomeByTypeSeg(String uid, int startTime, int endTime, List<Integer> actTypeList, List<Integer> segmentCodeList) {
        if (StringUtils.isEmpty(uid)) {
            return new HostIncomeVO();
        }
        List<String> partitionIndex = getPartitionIndex(startTime, endTime);
        if (CollectionUtils.isEmpty(partitionIndex)) {
            return new HostIncomeVO(uid, 0, 0);
        }
        String dbName = getDBName();
        return mapper.selectHostTotalDiamondIncomeByTypeSeg(dbName, partitionIndex, uid, startTime, endTime, actTypeList, segmentCodeList);
    }

    @Cacheable(value = "str:game_record_by_time#20", key = "'uid:'+#uid+':startTime:'+#startTime+':endTime:'+#endTime+':limit:'+#limit")
    public List<DorisMoneyDetailData> getGameRecordByTime(String uid, long startTime, long endTime, int limit) {
        if (StringUtils.isEmpty(uid)) {
            return new ArrayList<>();
        }
        List<String> partitionIndex = getPartitionIndex(startTime, endTime);
        if (CollectionUtils.isEmpty(partitionIndex)) {
            return new ArrayList<>(0);
        }
        String dbName = getDBName();
        List<DorisMoneyDetailData> list = mapper.selectGameRecordByTime(dbName, partitionIndex, uid, startTime, endTime, limit);
        if (ObjectUtils.isEmpty(list)) {
            return new ArrayList<>(0);
        }
        return list;
    }

    public List<DorisMoneyDetailData> getGameCoinDetail(String uid, long startTime, long endTime, Integer searchType, int page) {
        if (StringUtils.isEmpty(uid)) {
            return new ArrayList<>();
        }
        List<String> partitionIndex = getPartitionIndex(startTime, endTime);
        if (CollectionUtils.isEmpty(partitionIndex)) {
            return new ArrayList<>();
        }
        String dbName = getDBName();
        // 计算分页参数
        int pageSize = 20;
        int offset = (page - 1) * pageSize;
        List<DorisMoneyDetailData> list = mapper.selectGameCoinDetail(dbName, partitionIndex, uid, startTime, endTime, searchType, offset, pageSize);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }

    public List<HostIncomeVO> getGiftIncomeByTimeAndUidSet(Set<String> uidSet, long startTime, long endTime) {
        if (CollectionUtils.isEmpty(uidSet)) {
            return new ArrayList<>();
        }
        List<String> partitionIndex = getPartitionIndex(startTime, endTime);
        if (CollectionUtils.isEmpty(partitionIndex)) {
            return new ArrayList<>();
        }
        String dbName = getDBName();
        return mapper.selectGiftIncomeByTimeAndUidSet(dbName, partitionIndex, uidSet, startTime, endTime);
    }


    /**
     * Origin
     *
     * @see MoneyDetailDao#getHostTotalIncome(Set, DayTimeData)
     */
    public List<HostIncomeVO> getHostTotalIncome(Set<String> hostList, DayTimeData dayTimeData) {
        List<String> partitionIndex = getPartitionIndex(dayTimeData.getTime(), dayTimeData.getEndTime());
        if (CollectionUtils.isEmpty(partitionIndex)) {
            return new ArrayList<>();
        }
        String dbName = getDBName();
        return mapper.selectHostTotalIncome(hostList, dayTimeData.getTime(), dayTimeData.getEndTime(), partitionIndex, dbName);
    }
}
