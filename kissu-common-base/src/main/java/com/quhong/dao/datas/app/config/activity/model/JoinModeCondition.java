package com.quhong.dao.datas.app.config.activity.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/1/5 14:21
 */
@Data
public class JoinModeCondition {
    /**
     * 条件方式
     * 常量接口com.quhong.constant.activity.model.join.mode.ConditionModeConstant
     *
     * @see com.quhong.constant.activity.model.join.mode.ConditionModeConstant 条件方式
     */
    private Integer mode;
    /**
     * 连接方式 AND OR DEFAULT
     * 常量接口com.quhong.constant.activity.model.join.mode.RelationConstant
     *
     * @see com.quhong.constant.activity.model.join.mode.RelationConstant 条件连接方式
     */
    private String relation;
    /**
     * 条件符号 = > < >= <= in
     * 常量接口com.quhong.constant.activity.model.join.mode.ConditionConstant
     *
     * @see com.quhong.constant.activity.model.join.mode.ConditionConstant 条件符号
     */
    private String condition;
    /**
     * 条件值
     */
    private String value;
}
