package com.quhong.dao.datas.log;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 礼包奖励发放记录实体类
 *
 * <AUTHOR>
 * @since 2024/4/15
 */
@Data
@Accessors(chain = true)
@Table(name = "awards_key_record")
public class AwardsKeyRecordData {

    /**
     * 自增主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 礼包key标识
     */
    private String awardsKey;

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 变动描述
     */
    private String changeDesc;

    /**
     * 活动码
     */
    private Integer eventCode;

    /**
     * 创建时间（秒）
     */
    private Long ctime;

    /**
     * 操作人
     */
    private String operator;
}
