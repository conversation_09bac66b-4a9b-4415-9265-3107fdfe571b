package com.quhong.dao.datas.log;

import com.quhong.dao.datas.AbstractShardingData;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @ClassName RoomMicApplyData
 * <AUTHOR>
 * @date 2023/5/9 20:26
 */
@Table(name = "room_mic_apply")
@Data
public class RoomMicApplyData {

    @Id
    private Long id;

    private String uid;

    private String roomId;

    private String chapterId;

    private Long ctime;

    private Integer valid;

    /**
     * @see com.quhong.room.mic.MicConstant
     */
    private Integer reason;

    private Integer micType;//麦位类型 0视频+音频上麦 1音频上麦
}
