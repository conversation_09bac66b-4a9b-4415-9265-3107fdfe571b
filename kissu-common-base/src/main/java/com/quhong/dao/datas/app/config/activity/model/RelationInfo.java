package com.quhong.dao.datas.app.config.activity.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/1/19 18:09
 */
@Data
@Accessors(chain = true)
public class RelationInfo {
    /**
     * 连接方式 AND OR DEFAULT
     * 常量接口com.quhong.constant.activity.model.join.mode.RelationConstant
     *
     * @see com.quhong.constant.activity.model.join.mode.RelationConstant 条件连接方式
     */
    private String relation;
    /**
     * 是否命中
     */
    private Boolean hit;
}
