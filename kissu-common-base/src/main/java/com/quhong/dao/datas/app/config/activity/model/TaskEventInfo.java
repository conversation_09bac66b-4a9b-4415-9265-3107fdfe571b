package com.quhong.dao.datas.app.config.activity.model;

import com.quhong.dao.datas.RewardInfoData;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/5 15:36
 */
@Data
public class TaskEventInfo {
    /**
     * 任务类型
     * 常量接口com.quhong.constant.activity.model.event.mode.TaskTypeConstant
     *
     * @see com.quhong.constant.activity.model.event.mode.TaskTypeConstant 任务类型常量类
     */
    private Integer type;
    /**
     * 任务组
     * 常量接口com.quhong.constant.activity.model.event.mode.TaskGroupConstant
     *
     * @see com.quhong.constant.activity.model.event.mode.TaskGroupConstant 任务组常量类
     */
    private String taskGroup;
    /**
     * 任务名字
     */
    private String name;
    /**
     * 中文名
     */
    private String cnName;
    /**
     * 任务描述
     */
    private String taskDesc;
    /**
     * 任务方式
     * 常量接口com.quhong.constant.activity.model.event.mode.TaskModeConstant
     *
     * @see com.quhong.constant.activity.model.event.mode.TaskModeConstant 任务方式常量类
     */
    private Integer mode;

    /**
     * 每人可领取次数
     */
    private Integer eachClaimTimes;
    /**
     * 总共可领取次数（全局限制）
     */
    private Integer totalClaimTimes;
    /**
     * 校验参数
     * eg. 礼物id,游戏消费金币数量达标值,宣传文案发送次数等
     */
    private Integer checkParams;
    /**
     * 权重，值越大排的越前
     */
    private Integer weight;
    /**
     * 奖励列表
     * 考虑换为app_config_award + num
     */
    private List<RewardInfoData> rewards;

}
