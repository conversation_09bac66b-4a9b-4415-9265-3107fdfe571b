package com.quhong.dao.datas.db;

import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.dao.datas.handler.impl.common.rewards.key.AwardInfoArrayTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.List;

/**
 * 礼包key配置实体类
 *
 * <AUTHOR>
 * @since 2024/4/15
 */
@Data
@Accessors(chain = true)
@Table(name = "awards_key_config")
public class AwardsKeyConfigData {

    /**
     * 自增主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 礼包key标识
     */
    private String awardsKey;

    /**
     * 礼包key类型：1普通类、2概率类
     */
    private Integer awardsKeyType;

    /**
     * 礼包名称
     */
    private String awardsName;

    /**
     * 礼包图标URL
     */
    private String awardsIcon;

    /**
     * 奖励列表JSON
     */
    @ColumnType(typeHandler = AwardInfoArrayTypeHandler.class)
    private List<AwardInfo> awardInfoList;

    /**
     * 创建时间（秒）
     */
    private Long ctime;

    /**
     * 修改时间（秒）
     */
    private Long mtime;

    /**
     * 是否有效：1有效，0无效
     */
    private Integer valid;

    /**
     * 操作人
     */
    private String operator;

}
