package com.quhong.dao.datas.app.config.activity.unit;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Set;

/**
 * 通用奖池组件信息
 * <AUTHOR>
 * @since 2025-05-30 15:39
 */
@Data
@Accessors(chain = true)
public class AwardPoolUnit {
    /**
     * 可作为消耗的货币组件ID
     * @see EventUnit unitId
     */
    private Set<Integer> costUnitId;
    /**
     * 抽奖次数汇率 eg. 200 - 200后台金币:1次抽奖
     */
    private Integer timesRate;
    /**
     * 礼包资源key
     * @see com.quhong.dao.datas.db.AwardsKeyConfigData awardsKey
     */
    private String awardsKey;


}
