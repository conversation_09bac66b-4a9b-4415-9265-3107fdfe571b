package com.quhong.dao.datas.db;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * @ClassName LordExperienceCardData
 * <AUTHOR>
 * @date 2025/3/11 15:10
 */
@Table(name = "lord_experience_card")
@Data
public class LordExperienceCardData {
    @Id
    private Long id;

    private String uid;

    private String cardId;

    private Integer level;

    private Long expireTime;

    private Integer duration;

    /**
     * 状态 0未使用 1使用 2删除
     */
    private Integer status;

    private Long ctime;

    private Long mtime;

    @Transient
    private String lordName;
    @Transient
    private String lordIcon;
}
