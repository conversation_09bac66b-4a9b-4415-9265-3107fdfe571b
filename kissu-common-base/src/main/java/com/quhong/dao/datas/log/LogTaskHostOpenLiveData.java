package com.quhong.dao.datas.log;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "log_task_host_open_live")
public class LogTaskHostOpenLiveData {

    @Id
    private Long id;

    private String uid;

    private String date;

    private Integer taskLevel;

    private Long rewardNums;

    private Integer status;

    private Integer levelNode;

    private Long ctime;

    private Long mtime;


}
