package com.quhong.dao.datas.log;

import com.quhong.dao.datas.AbstractShardingData;
import com.quhong.dao.datas.doris.DorisAgentPerformanceDetailLogData;
import com.quhong.utils.StringUtils;
import lombok.*;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;

/**
 * @ClassName AgentPerformanceDetailLogData
 * <AUTHOR>
 * @date 2024/2/19 14:28
 */
@EqualsAndHashCode(callSuper = true)
@Table(name = "agent_performance_detail_log")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentPerformanceDetailLogData extends AbstractShardingData implements Serializable {

    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 链路关联id
     */
    private String linkId;

    /**
     * 身份 0主播 1代理
     */
    private Integer identity;

    /**
     * 产生业绩类型 对比money_detail
     *
     * @see com.quhong.constant.ActType
     */
    private Integer actType;

    /**
     * 代理id 如果为代理身份
     */
    private String agentId;

    /**
     * uid 主播uid或代理绑定的uid
     */
    private String uid;

    /**
     * 业绩
     */
    private Long performance;

    /**
     * 佣金比例
     */
    private String rate;

    /**
     * 佣金
     */
    private String commission;

    /**
     * 产生业绩uid
     */
    private String relationUid;

    /**
     * 产生业绩关联id
     */
    private Long recordId;

    /**
     * 业绩变化原因 0业绩变化起点  1业绩变化由旗下主播产生 2业绩变化由旗下代理产生
     */
    private Integer changeReason;

    /**
     * 创建时间戳
     */
    private Long ctime;

    /**
     * 修改时间戳
     */
    private Long mtime;

    /**
     * 上一层变动的(下级代理或主播带来的业绩变动)
     */
    private String subChange;

    /**
     * 是否有效 0无效 1有效
     */
    private Integer valid;

    @Transient
    private String relationAgentId;

    /**
     * 业绩
     */
    @Transient
    private String realPerformance;

    public String fetchRealPerformance() {
        if (StringUtils.hasLength(realPerformance)) {
            return realPerformance;
        }
        return performance.toString();
    }

    public void copyFrom(DorisAgentPerformanceDetailLogData src) {
        this.linkId = src.getLinkId();
        this.identity = src.getIdentity();
        this.actType = src.getActType();
        this.agentId = src.getAgentId();
        this.uid = src.getUid();
        this.performance = src.getPerformance();
        this.rate = src.getRate();
        this.commission = src.getCommission();
        this.relationUid = src.getRelationUid();
        this.recordId = src.getRecordId();
        this.changeReason = src.getChangeReason();
        this.ctime = src.getCtime();
        this.mtime = src.getMtime();
        this.subChange = src.getSubChange();
        this.valid = src.getValid();
        this.suffix = src.getPartitionIndex();
    }

}
