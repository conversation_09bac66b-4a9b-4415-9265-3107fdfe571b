package com.quhong.dao.datas.app.config.activity.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/1/5 15:31
 */
@Data
public class EventModeInfo {
    /**
     * 活动方式
     * 常量接口：com.quhong.constant.activity.model.event.mode.EventModeConstant
     *
     * @see com.quhong.constant.activity.model.event.mode.EventModeConstant 活动方式
     */
    private Integer mode;
    /**
     * 榜单活动方式信息
     */
    private ModeRankInfo modeRankInfo;
    /**
     * 任务信息列表（暂时不写， 具体逻辑还没确定）
     */
//    private List<TaskEventInfo> taskEventInfoList;
}
