package com.quhong.dao.datas.db;

import com.quhong.constant.RoomItemsTypeConstant;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * @ClassName RoomItemsConfig
 * <AUTHOR>
 * @date 2022/12/2 15:24
 */
@Data
@Table(name = "room_items_config")
public class RoomItemsConfigData {

    @Id
    private Long id;

    /**
     * 物品id
     */
    private Integer roomItemId;

    /**
     * 商品id
     */
    private Integer roomSkuId;

    /**
     * 物品名字
     */
    private String roomItemName;

    /**
     * 物品描述
     */
    private String roomItemDesc;

    /**
     * 物品标注
     */
    private String roomItemTag;

    /**
     * 资源id
     */
    private Integer resourceId;

    /**
     * 获取途径  1商城 2活动 3boss等级
     *
     * @see RoomItemAccessConstant
     */
    private Integer access;

    /**
     * boss等级 access为3时使用
     */
    private Integer bossLevel;

    /**
     * 物品价格
     */
    private Long price;

    /**
     * 资源购买时间 单位s
     */
    private Integer purchaseTime;

    /**
     * 排序id
     */
    private Integer orderId;

    /**
     * 物品类型  1座位框 2气泡框 3进场特效
     *
     * @see RoomItemsTypeConstant
     */
    private Integer roomItemType;

    /**
     * 融合开关 0关1开
     */
    private Integer fusionSwitch;

    private Integer fusionIdSwitch;

    private Integer fusionNameSwitch;

    /**
     * 是否有效  1有效 0无效
     */
    private Integer valid;

    /**
     * 创建时间戳
     */
    private Long ctime;

    /**
     * 修改时间戳
     */
    private Long mtime;

    /**
     * 上架时间
     */
    private Long upTime;

    /**
     * 下架时间
     */
    private Long downTime;

    /**
     * 操作人
     */
    private String operator;

    @Transient
    private String createDate;
    @Transient
    private String iconUrl;
    @Transient
    private Integer len;
    @Transient
    private String iconUrlPath;
    @Transient
    private String resourceUrlPath;
    @Transient
    private String originUrlPath;
    @Transient
    private String md5;
    @Transient
    private String resourceUrl;
    @Transient
    private String originUrl;
    @Transient
    private String md5Add;
    @Transient
    private String showUrl;
    @Transient
    private String originAddUrl;
    @Transient
    private String originAddMd5;
    @Transient
    private String originAddUrlPath;
    @Transient
    private String fusionUrl;
    @Transient
    private String fusionMd5;
    @Transient
    private String fusionUrlPath;
    @Transient
    private String idColor;
    @Transient
    private String nameColor;
}
