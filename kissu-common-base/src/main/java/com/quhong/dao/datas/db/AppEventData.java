package com.quhong.dao.datas.db;

import com.quhong.dao.datas.BaseOperationData;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 事件(app_event)实体类
 *
 * <AUTHOR>
 * @since 2024-01-05 14:04:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "app_event")
public class AppEventData extends BaseOperationData {
    /**
     * 事件码
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long eventCode;
    /**
     * 事件类别 0一般事件  1活动事件  2游戏事件  3运营平台操作事件
     */
    private Integer category;
    /**
     * 事件分组 0未分组 1PK活动
     */
    private Integer eventGroup;
    /**
     * 名字
     */
    private String name;
    /**
     * 事件描述
     */
    private String eventDesc;

}

