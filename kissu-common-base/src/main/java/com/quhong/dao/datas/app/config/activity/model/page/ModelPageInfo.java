package com.quhong.dao.datas.app.config.activity.model.page;

import lombok.Data;

/**
 * 页面元素
 *
 * <AUTHOR>
 * @since 2024/1/5 16:20
 */
@Data
public class ModelPageInfo {
    /**
     * 规则页相关元素
     */
    public RulePage rulePage;
    /**
     * 活动头图
     */
    private String headImg;
    /**
     * 活动头部背景图
     */
    private String headBackgroundImg;
    /**
     * 活动方式头图
     */
    private String modelHeadImg;
    /**
     * 兑换按钮图
     */
    private String claimButtonImg;
    /**
     * 兑换弹窗头部图
     */
    private String claimPopHeadImg;
    /**
     * 兑换弹窗关闭按钮图
     */
    private String claimPopCancelButtonImg;
    /**
     * 不参与活动用户提示
     */
    private String notJoinTip;
    /**
     * 不参与活动用户提示文案颜色
     */
    private String notJoinTipColor;
    /**
     * 背景色
     */
    private BackgroundColors backgroundColors;


}
