package com.quhong.dao.datas.db;

import com.quhong.dao.datas.handler.impl.activity.event.monitor.EventMonitorArrayTypeHandler;
import com.quhong.data.bo.activity.event.monitor.EventMonitorBO;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.List;

/**
 * 事件监控配置(event_monitor_config)实体类
 *
 * <AUTHOR>
 * @since 2025-04-28 11:25:48
 */
@Data
@Table(name = "event_monitor_config")
public class EventMonitorConfigData {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;
    /**
     * 监控key
     */
    private String monitorKey;
    /**
     * 活动码
     */
    private String eventCode;
    /**
     * 告警名
     */
    private String warnName;
    /**
     * 监控名
     */
    private String name;
    /**
     * 监控列表
     */
    @ColumnType(typeHandler = EventMonitorArrayTypeHandler.class)
    private List<EventMonitorBO> monitorList;
    /**
     * 是否有效 1有效 0无效
     */
    private Integer valid;

    private Long ctime;

    private Long mtime;
    /**
     * 操作人
     */
    private String operator;
}

