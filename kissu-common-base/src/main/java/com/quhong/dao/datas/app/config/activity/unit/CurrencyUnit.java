package com.quhong.dao.datas.app.config.activity.unit;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 货币组件信息
 * <AUTHOR>
 * @since 2025-05-30 15:18
 */
@Data
@Accessors(chain = true)
public class CurrencyUnit {
    /**
     * 货币图标
     */
    private String icon;
    /**
     * 货币类型
     * 1金币 9钻石 9999活动货币 暂时只支持这些货币
     * @see com.quhong.enums.RewardItemType 只支持里面的这些货币
     */
    private Integer type;
    /**
     * 货币显示倍率 金币和钻石40
     */
    private Integer showRate = 1;
}
