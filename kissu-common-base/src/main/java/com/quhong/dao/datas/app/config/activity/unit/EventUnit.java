package com.quhong.dao.datas.app.config.activity.unit;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 活动组件
 *
 * <AUTHOR>
 * @since 2025-05-30 15:05
 */
@Data
@Accessors(chain = true)
public class EventUnit {
    /**
     * 活动组件id
     */
    private Integer unitId;
    /**
     * 组件类型
     * @see com.quhong.constant.activity.unit.EventUnitType 组件类型
     */
    private Integer unitType;
    /**
     * 组件名称(英文名）
     */
    private String unitName;
    /**
     * 组件别名（中文名）
     */
    private String unitAka;
    /**
     * 描述
     */
    private String unitDesc;
    /**
     * 货币组件
     * unitType==1时必填
     */
    private CurrencyUnit currencyUnit;
    /**
     * 通用奖池组件
     * unitType==2时必填
     */
    private AwardPoolUnit awardPoolUnit;
    /**
     * 任务组件
     * unitType==3时必填
     */
    private TaskUnit taskUnit;
    /**
     * 排行组件
     * unitType==4时必填
     */
    private RankUnit rankUnit;


}
