package com.quhong.dao.datas.app.config.activity.model;

import lombok.Data;

import java.util.List;

/**
 * 参与群体信息
 *
 * <AUTHOR>
 * @since 2024/1/5 14:20
 */
@Data
public class JoinModeInfo {
    /**
     * 参与群体
     * 常量接口com.quhong.constant.activity.model.join.mode.JoinModeConstant
     *
     * @see com.quhong.constant.activity.model.join.mode.JoinModeConstant 参与群体常量
     */
    private Integer mode;
    /**
     * 条件列表, 第一个条件的逻辑关系为""
     */
    private List<JoinModeCondition> conditions;
}
