package com.quhong.dao.datas.app.config.activity.unit;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-05-30 17:02
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class TaskUnitConfig {
    /**
     * 判定值
     */
    private Long checkParams;
    /**
     * 判定文本 宣传文案任务需要使用
     */
    private String checkText;
    /**
     * 限制值 -1/null 不限制
     */
    private Integer limit = -1;
    /**
     * 礼包资源key
     * @see com.quhong.dao.datas.db.AwardsKeyConfigData awardsKey
     */
    private String awardsKey;
    /**
     * 通知消息
     */
    private String notice;


    public TaskUnitConfig(RankRewardConfig config) {
        this.checkParams = config.getMinTop();
        this.awardsKey = config.getAwardsKey();
    }
}
