package com.quhong.dao.datas;

import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.dao.datas.db.AppConfigAwardData;
import com.quhong.dao.datas.db.AppConfigAwardPoolData;
import com.quhong.utils.StringUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 奖品信息表(reward_info)实体类
 *
 * <AUTHOR>
 * @since 2021-10-27 10:19:01
 */
@Data
@Accessors(chain = true)
@Table(name = "reward_info")
public class RewardInfoData implements Serializable {
    private static final long serialVersionUID = 675399531000773974L;

    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;
    /**
     * 奖品名
     */
    private String name;
    /**
     * 奖品描述
     */
    @Column(name = "award_desc")
    private String awardDesc;
    /**
     * 活动类型（1签到奖励 2时长宝箱 3砸蛋 4live房间玫瑰花竞赛）
     *
     * @see com.quhong.enums.ActivityTypeEnum 活动类型
     */
    @Column(name = "activity_type")
    private Integer activityType;

    /**
     * 活动id(签到奖励配置表check_in_reward_config.id,活动配置表id)
     */
    @Column(name = "activity_id")
    private Integer activityId;
    /**
     * 图标url
     */
    private String icon;
    /**
     * 大图标url
     */
    @Column(name = "big_icon")
    private String bigIcon;
    /**
     * 是否为大奖 1是 0不是
     */
    @Column(name = "is_grand_prize")
    private Integer isGrandPrize;
    /**
     * 数量
     */
    private Integer nums;
    /**
     * 奖品类型 默认0（1 金币，2 礼物，3 匹配卡，4 vip天数）
     *
     * @see com.quhong.enums.RewardItemType 奖励类型
     */
    private Integer type;
    /**
     * 奖品id(礼物id,匹配卡id)
     */
    @Column(name = "data_id")
    private Integer dataId;
    /**
     * 是否有效：1有效，0无效
     */
    private Integer valid;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer mtime;

    /**
     * 操作人
     */
    private String operator;

    @Transient
    private Integer rewardMultiplier;
    @Transient
    private String roomId;
    @Transient
    private String gameExposure;
    /**
     * 场景
     *
     * @see com.quhong.constant.GiftFromTypeConstant
     */
    @Transient
    private Integer scene;
    /**
     * 单个成本
     */
    @Transient
    private Integer unitCost;
    @Transient
    private Integer receiveCount;
    @Transient
    private String changeRelation;
    @Transient
    private String goldActDesc;
    @Transient
    private String realNums;

    /**
     * 是否自动佩戴
     */
    @Transient
    private Boolean autoWear;

    public void multiNums(int multiNum) {
        this.nums *= multiNum;
    }


    public RewardInfoData() {
    }

    public RewardInfoData(Integer activityType, Integer nums, Integer type, Integer dataId, Integer unitCost) {
        this.activityType = activityType;
        this.nums = nums;
        this.realNums = String.valueOf(nums);
        this.type = type;
        this.dataId = dataId;
        this.unitCost = unitCost;
    }

    public RewardInfoData(Integer activityType, Integer nums, Integer type, Integer dataId) {
        this.activityType = activityType;
        this.nums = nums;
        this.realNums = String.valueOf(nums);
        this.type = type;
        this.dataId = dataId;
        this.unitCost = 0;
    }

    public RewardInfoData(Integer nums, Integer type, Integer dataId) {
        this.nums = nums;
        this.realNums = String.valueOf(nums);
        this.type = type;
        this.dataId = dataId;
    }

    public RewardInfoData(String name, String icon, Integer nums, Integer type, Integer dataId) {
        this.name = name;
        this.icon = icon;
        this.nums = nums;
        this.realNums = String.valueOf(nums);
        this.type = type;
        this.dataId = dataId;
    }

    public RewardInfoData(Integer eventCode, String name, String icon, Integer nums, Integer type, Integer dataId, Integer unitCost) {
        this.activityType = eventCode;
        this.name = name;
        this.icon = icon;
        this.nums = nums;
        this.realNums = String.valueOf(nums);
        this.type = type;
        this.dataId = dataId;
        this.unitCost = unitCost;
    }

    public RewardInfoData(Integer eventCode, String name, String icon, Integer nums, Integer type, Integer dataId, String bigIcon) {
        this.activityType = eventCode;
        this.name = name;
        this.icon = icon;
        this.nums = nums;
        this.realNums = String.valueOf(nums);
        this.type = type;
        this.dataId = dataId;
        this.bigIcon = bigIcon;
    }

    public RewardInfoData(Integer eventCode, Integer dataId, String name, String icon, Integer nums, Integer type, Integer unitCost) {
        this.activityType = eventCode;
        this.dataId = dataId;
        this.name = name;
        this.icon = icon;
        this.nums = nums;
        this.realNums = String.valueOf(nums);
        this.type = type;
        this.unitCost = unitCost;
    }

    public RewardInfoData(AppConfigAwardPoolData poolData, AppConfigAwardData awardInfo) {
        this.id = awardInfo.getId();
        this.name = awardInfo.getName();
        this.activityType = poolData.getEventCode();
        this.activityId = poolData.getId().intValue();
        this.icon = awardInfo.getIcon();
        this.bigIcon = awardInfo.getBigIcon();
        this.nums = poolData.getAwardNum();
        this.realNums = String.valueOf(nums);
        this.type = awardInfo.getAwardType();
        this.dataId = awardInfo.getDataId();
        this.unitCost = awardInfo.getUnitPrice();
        this.receiveCount = poolData.getReceivedCount();
    }

    public RewardInfoData(int eventCode, AwardInfo awardInfo) {

        this.setType(awardInfo.getAwardType())
                .setDataId(awardInfo.getDataId())
                .setNums(awardInfo.getNums())
                .setName(awardInfo.getAwardName())
                .setIcon(awardInfo.getAwardIcon())
                .setUnitCost(awardInfo.getUnitPrice())
                .setActivityType(eventCode);
        // 设置自动佩戴属性（仅房间物品奖励使用）
        if (awardInfo.getAutoWear() != null && awardInfo.getAutoWear()) {
            this.setAutoWear(true);
        }
    }

    public String fetchRealNums() {
        if (StringUtils.isEmpty(realNums)) {
            return String.valueOf(nums);
        }
        return realNums;
    }
}

