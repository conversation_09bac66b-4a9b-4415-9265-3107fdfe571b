package com.quhong.dao.datas.db;

import com.quhong.core.utils.DateHelper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 活动通用个人数据统计表（数据维度：uid+eventCode+count_group）(activity_count_actor_info)实体类
 *
 * <AUTHOR>
 * @since 2023-11-07 18:05:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "activity_count_actor_info")
public class ActivityCountActorInfoData {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    private String uid;
    /**
     * 活动码
     */
    private Integer eventCode;

    private Long rid;
    /**
     * 统计数据分组
     */
    private String countGroup;
    /**
     * 统计数据
     */
    private Long count;
    /**
     * 最后一次统计时间
     */
    private Long lastCountTime;
    /**
     * 子对象
     */
    private String sonObj;
    /**
     * 是否有效
     */
    private Integer valid;

    private Long ctime;

    private Long mtime;
    /**
     * 是否覆盖Count
     */
    @Transient
    private boolean coverCount = false;

    public ActivityCountActorInfoData(String uid, Integer eventCode, String countGroup) {
        this.uid = uid;
        this.eventCode = eventCode;
        this.countGroup = countGroup;
    }

    public void increaseCount(ActivityCountActorInfoData bo){
        this.count += bo.getCount();
        this.lastCountTime = bo.getLastCountTime();
        this.setMtime(DateHelper.getCurrTime());
    }

    public void computeDiffCount(ActivityCountActorInfoData bo){
        int initCount = Integer.parseInt(this.getSonObj());
        this.count = bo.getCount() - initCount;
        this.lastCountTime = bo.getLastCountTime();
        this.setMtime(DateHelper.getCurrTime());
    }

    public static class InitFactory{
        public static ActivityCountActorInfoData initQueryOneBO(String uid, Integer eventCode, String countGroup){
            return new ActivityCountActorInfoData(uid, eventCode, countGroup);
        }

        public static ActivityCountActorInfoData initUpdateCountData(ActivityCountActorInfoData data){
            ActivityCountActorInfoData updateData = new ActivityCountActorInfoData();
            updateData.setId(data.getId());
            updateData.setUid(data.getUid());
            updateData.setEventCode(data.getEventCode());
            updateData.setCountGroup(data.getCountGroup());
            updateData.setCount(data.getCount());
            updateData.setLastCountTime(data.getLastCountTime());
            updateData.setSonObj(data.getSonObj());
            updateData.setMtime(data.getMtime());
            return updateData;
        }
    }
}

