package com.quhong.dao.datas.log;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * @ClassName InviteCommissionLogData
 * <AUTHOR>
 * @date 2024/3/21 20:54
 */
@Data
@Table(name = "invite_commission_log")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InviteCommissionLogData {

    @Id
    private Long id;

    /**
     * 邀请方uid
     */
    private String uid;

    /**
     * 被邀请发uid
     */
    private String invitedUid;

    /**
     * 返佣类型 1充值 2直播 3收礼
     *
     * @see com.quhong.constant.InviteCommissionTypeConstant
     */
    private Integer commissionType;

    /**
     * 返佣奖励等级
     */
    private Integer level;

    /**
     * 返佣钻石数
     */
    private Long commission;

    private Long ctime;

    private Long mtime;

    /**
     * 状态 1有效 0无效
     */
    private Integer status;

    /**
     * 返佣具体档位配置
     */
    private String configVal;

    @Transient
    private String name;
    @Transient
    private String head;
    @Transient
    private long rid;
}
