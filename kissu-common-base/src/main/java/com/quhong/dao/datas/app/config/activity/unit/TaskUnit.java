package com.quhong.dao.datas.app.config.activity.unit;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 任务组件
 *
 * <AUTHOR>
 * @since 2025-05-30 15:48
 */
@Data
@Accessors(chain = true)
public class TaskUnit {
    /**
     * 任务类型
     * 常量接口com.quhong.constant.activity.model.event.mode.TaskTypeConstant
     *
     * @see com.quhong.constant.activity.model.event.mode.TaskTypeConstant 任务类型常量类
     */
    private Integer type;
    /**
     * 任务方式
     * 常量接口com.quhong.constant.activity.model.event.mode.TaskModeConstant
     *
     * @see com.quhong.constant.activity.model.event.mode.TaskModeConstant 任务方式常量类
     */
    private Integer mode;
    /**
     * 任务周期
     * 常量接口com.quhong.constant.activity.unit.EventUnitTaskDuration
     *
     * @see com.quhong.constant.activity.unit.EventUnitTaskDuration 任务周期
     */
    private Integer duration;
    /**
     * 任务排序权重 值越大排越前
     */
    private Integer weight;

    /**
     * 任务配置
     */
    private TaskUnitConfig config;
    /**
     * 多节点任务配置
     */
    private List<TaskUnitConfig> configs;
}
