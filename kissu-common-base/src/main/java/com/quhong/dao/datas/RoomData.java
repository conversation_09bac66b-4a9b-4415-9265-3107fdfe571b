package com.quhong.dao.datas;

import com.quhong.core.enums.RoomOwnerStatus;
import com.quhong.room.mic.MicConstant;
import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "room")
public class RoomData {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;
    /**
     * 房间id r;{onwerId}
     */
    private String roomId;
    /**
     * 流房间id
     */
    private String streamRoomId;
    /**
     * 房间类型
     *
     * @see com.quhong.enums.RoomType
     */
    private Integer roomType;
    /**
     * 流类型
     *
     * @see com.quhong.enums.StreamType
     */
    private Integer streamType;
    /**
     * 场次id
     */
    private String chapterId;
    /**
     * 房主状态 0 已关闭 1 房间内 2 已离开
     *
     * @see RoomOwnerStatus
     */
    private Integer ownerStatus;
    private Long ctime;
    private Long mtime;
    /**
     * 直播开始时间
     */
    private Long chapterStartTime;
    /**
     * 直播结束时间
     */
    private Long chapterEndTime;

    /**
     * 游戏相关id
     */
    private Integer gameRelatedId;
    /**
     * 热度值
     */
    private Integer heatValue;
    /**
     * 房主uid
     */
    private String ownerUid;
    /**
     * 房间标题
     */
    private String roomName;
    /**
     * 房间公告
     */
    private String roomNotice;
    /**
     * 子类型 0-默认，1-游戏房间
     *
     * @see com.quhong.enums.RoomSubType
     */
    private Integer subType;

    /**
     * 房间背景id
     */
    private Integer roomBackgroundId;

    /**
     * 连麦申请开关
     */
    private Integer micApplySwitch;

    /**
     * 房间封面
     */
    private String roomCover;

    private Integer officialRoom;

    private Integer top;

    /**
     * 麦位数量
     */
    private Integer roomMicCount;
    /**
     * 房间标签
     */
    private String roomTag;
    /**
     * 房间状态 1有效 0冻结
     */
    private Integer roomValid;

    /**
     * 直播麦位类型 0默认 1多麦位
     *
     * @see MicConstant#LIVE_MIC_TYPE_THIRD
     * @see MicConstant#LIVE_MIC_TYPE_MUL
     */
    private Integer liveMicType;

    private String gameShowIcon;
}
