package com.quhong.dao.datas.common;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 奖励信息类
 *
 * <AUTHOR>
 * @since 2024/4/15
 */
@Data
@Accessors(chain = true)
public class AwardInfo {

    /**
     * 奖励ID
     */
    private Integer awardId;

    /**
     * 奖励类型：
     *
     * @see com.quhong.enums.RewardItemType 奖励类型
     */
    private Integer awardType;

    /**
     * 奖励对应的数据ID
     */
    private Integer dataId;

    /**
     * 下发数量
     */
    private Integer nums;

    /**
     * 奖励名称
     */
    private String awardName;

    /**
     * 奖励图标URL
     */
    private String awardIcon;

    /**
     * 奖励预览图URL（支持MP4）
     */
    private String awardVideoUrl;

    /**
     * 单位价值
     */
    private Integer unitPrice = 0;

    /**
     * 显示价值
     */
    private String showPrice;

    /**
     * 显示数量
     */
    private String showNums;

    /**
     * 普通用户抽奖概率（千分率）
     */
    private Integer rate;

    /**
     * 三方充值>50美金用户抽奖概率（千分率）
     */
    private Integer rate1;

    /**
     * 预留概率2
     */
    private Integer rate2;

    /**
     * 预留概率3
     */
    private Integer rate3;

    /**
     * 全局限制数量
     * -1 不限制 >=0 限制  0直接不入奖池
     */
    private Integer limit;

    /**
     * 是否自动佩戴
     */
    private Boolean autoWear = false;

    /**
     * 是否房间飘屏
     */
    private Boolean roomFloating = false;

    /**
     * 是否全服横幅
     */
    private Boolean fullServiceNotice = false;

    /**
     * 是否个人弹窗
     */
    private Boolean personalPopup = false;

    /**
     * 是否全服弹窗
     */
    private Boolean fullServicePopup = false;

    public Boolean getAutoWear() {
        return autoWear != null ? autoWear : false;
    }

    public Boolean getRoomFloating() {
        return roomFloating != null ? roomFloating : false;
    }

    public Boolean getFullServiceNotice() {
        return fullServiceNotice != null ? fullServiceNotice : false;
    }

    public Boolean getPersonalPopup() {
        return personalPopup != null ? personalPopup : false;
    }

    public Boolean getFullServicePopup() {
        return fullServicePopup != null ? fullServicePopup : false;
    }

    /**
     * 获取普通用户概率
     */
    public Integer getRate() {
        return rate != null ? rate : 0;
    }

    /**
     * 获取充值用户概率
     */
    public Integer getRate1() {
        return rate1 != null ? rate1 : 0;
    }

    /**
     * 获取预留概率2
     */
    public Integer getRate2() {
        return rate2 != null ? rate2 : 0;
    }

    /**
     * 获取预留概率3
     */
    public Integer getRate3() {
        return rate3 != null ? rate3 : 0;
    }
}
