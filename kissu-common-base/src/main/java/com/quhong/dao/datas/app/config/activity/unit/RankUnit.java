package com.quhong.dao.datas.app.config.activity.unit;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Set;

/**
 * 榜单组件
 *
 * <AUTHOR>
 * @since 2025-06-03 11:35
 */
@Data
@Accessors(chain = true)
public class RankUnit {
    /**
     * 榜单类型
     * @see com.quhong.core.annotation.ActivityRankType 榜单类型
     */
    private Integer rankType;
    /**
     * 榜单开始时间 默认活动的开始时间
     */
    private Long startTime;
    /**
     * 榜单结束时间 默认活动的结束时间
     */
    private Long endTime;
    /**
     * 展示的榜单TOP（默认10）
     */
    private Integer showTop = 10;
    /**
     * 奖励的榜单TOP（默认10）
     */
    private Integer awardTop = 10;
    /**
     * 榜单积分显示倍率（默认1） eg, 40
     */
    private Integer scoreRate = 1;
    /**
     *  校验参数列表
     *  eg. 指定的礼物id列表
     *  指定的游戏id列表
     *  指定的金币流水actType列表
     */
    private Set<String> checkParamsSet;
    /**
     * 是否自动发放奖励
     * true 自动发放奖励 false 手动发放奖励
     */
    private Boolean autoGiveAward = false;
    /**
     * 是否自动发送全局官方消息
     * true 自动发送 false 不发送
     */
    private Boolean autoSendGlobalNotice = false;
    /**
     * 榜单奖励配置
     */
    private List<RankRewardConfig> rewardConfigs;

}
