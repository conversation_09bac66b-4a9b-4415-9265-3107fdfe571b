package com.quhong.dao;

import com.quhong.dao.datas.log.GearRewardLogData;
import com.quhong.dao.mapper.log.GearRewardLogMapper;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName GearRewardLogDao
 * <AUTHOR>
 * @date 2024/7/25 14:06
 */
@Component
@Lazy
public class GearRewardLogDao {

    @Resource
    private GearRewardLogMapper mapper;

    public void save(GearRewardLogData data) {
        mapper.insertSelective(data);
    }

    public List<GearRewardLogData> getGearRewardLog(String uid, int gearType, long startTime, long endTime) {
        return mapper.selectGearRewardLog(uid, gearType, startTime, endTime);
    }

    public GearRewardLogData getGearLevelRewardLog(String uid, int gearType, int level, long startTime, long endTime) {
        return mapper.selectGearLevelRewardLog(uid, gearType, level, startTime, endTime);
    }
}
