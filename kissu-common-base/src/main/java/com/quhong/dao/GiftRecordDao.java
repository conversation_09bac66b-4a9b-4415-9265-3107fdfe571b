package com.quhong.dao;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.BaseHttpData;
import com.quhong.constant.GiftRecordStatusConstant;
import com.quhong.core.datas.DayTimeData;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.datas.GiftRecordData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.db.ActivityGalaRoomApplyData;
import com.quhong.dao.mapper.db.GiftRecordMapper;
import com.quhong.dao.slave.mapper.db.GiftRecordSlaveMapper;
import com.quhong.data.bo.activity.QueryGiftRankBO;
import com.quhong.data.dto.RankingDTO;
import com.quhong.data.pk.PkContributionData;
import com.quhong.data.pk.PkUserData;
import com.quhong.data.stat.LogGiftRecordData;
import com.quhong.data.thData.vo.ActivityInfoParamThVO;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.HostIncomeVO;
import com.quhong.enums.EventCode;
import com.quhong.monitor.CmdCodeEnum;
import com.quhong.report.EventReport;
import com.quhong.utils.RoomUtils;
import com.quhong.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * Description:礼物发送记录表Dao
 *
 * <AUTHOR>
 * @date 2021/12/3 19:21
 */
@Component
public class GiftRecordDao extends MonthShardingDao<GiftRecordMapper> {
    private static final Logger logger = LoggerFactory.getLogger(GiftRecordDao.class);

    public static final int STATUS_6 = 6;

    /**
     * 主播结算礼物换算比例用户
     */
    public static final Set<Integer> GIFT_ID_SET = new LinkedHashSet<>();

    @Resource
    private GiftRecordMapper mapper;

    @Resource
    private GiftRecordSlaveMapper slaveMapper;

    @Resource
    private EventReport eventReport;

    @Resource
    private ActivityGalaRoomApplyDao galaRoomApplyDao;

    @Resource
    private AppConfigActivityDao appConfigActivityDao;

    @Resource
    private MonitorSender sender;

    public GiftRecordDao() {
        super("t_gift_record");
        GIFT_ID_SET.add(88);
        GIFT_ID_SET.add(98);
        GIFT_ID_SET.add(41);
    }

    public List<CountVO> queryGiftGainSum(Set<String> toUidSet, int fromType, long startTime, long endTime) {
        if (ObjectUtils.isEmpty(toUidSet)) {
            return new ArrayList<>(0);
        }
        List<CountVO> list = slaveMapper.queryGiftGainSum(toUidSet, fromType, startTime, endTime);
        if (ObjectUtils.isEmpty(list)) {
            return new ArrayList<>(0);
        }
        return list;
    }

    public List<RankingDTO> getTopGiftPriceRanking(QueryGiftRankBO bo) {
        return slaveMapper.getTopGiftPriceRanking(bo);
    }

    /**
     * 活动临时使用，后续将废除
     */
    @Cacheable(value = "str:gift:ranking#60", key = "#bo.uidType + ':' +#bo.uid + ':' + #bo.start + ':' + #bo.end + ':' + #bo.fromType + ':' + #bo.top")
    public List<RankingDTO> getActivityGiftPriceRanking(QueryGiftRankBO bo) {
        List<RankingDTO> list = slaveMapper.getActivityGiftPriceRanking(bo);
        if (ObjectUtils.isEmpty(list)) {
            return new ArrayList<>(0);
        }
        return list;
    }

    public List<RankingDTO> getTopGiftRanking(QueryGiftRankBO bo) {
        return slaveMapper.getTopGiftRanking(bo);
    }

    public List<RankingDTO> getTopSendGiftData(long start, long end, int fromType, int giftId, int top) {
        return slaveMapper.getTopSendGiftData(start, end, giftId, fromType, top);
    }

    public List<RankingDTO> getTopReceiveGiftData(long start, long end, int fromType, int giftId, int top) {
        return getTopReceiveGiftData(start, end, fromType, giftId, top, null);
    }

    public List<RankingDTO> getTopReceiveGiftData(long start, long end, int fromType, int giftId, int top, Set<String> countryCodeSet) {
        return slaveMapper.getTopReceiveGiftData(start, end, giftId, fromType, top, countryCodeSet);
    }

    public RankingDTO getOneReceiveGiftBy(long start, long end, int fromType, int giftId, String uid) {
        return slaveMapper.getOneReceiveGiftData(start, end, giftId, fromType, uid);
    }

    public RankingDTO getOneSendGiftBy(long start, long end, int fromType, int giftId, String uid) {
        return slaveMapper.getOneSendGiftData(start, end, giftId, fromType, uid);
    }

    /**
     * 用于通话，不做小数处理
     *
     * @param cid
     * @return
     */
    public List<GiftRecordData> getListByRoomId(String cid) {
        if (StringUtils.isEmpty(cid)) {
            sender.info(CmdCodeEnum.UNKNOWNS.getWarnName(), "get list by roomId error. cid is null",
                    "通过roomId获取礼物列表异常 cid为null requestId=" + MDC.get(BaseHttpData.REQUEST_ID));
            throw new IllegalArgumentException("cid is empty");
        }
        return mapper.selectGiftRecordByRoomId(cid);
    }

    public List<GiftRecordData> getListByDay(DayTimeData day, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        return slaveMapper.selectListByDay(day.getTime(), day.getEndTime(), offset, pageSize);
    }

    public GiftRecordData getGiftRecordById(Integer rid) {
        return mapper.selectGiftRecordById(rid);
    }

    public void insert(GiftRecordData data) {
        data.setSuffix("");
        mapper.insertSelective(data);
        insertShardingTable(data);
        if (data.getStatus() != GiftRecordStatusConstant.CODE_INIT) {
            saveToThirdDB(data);
        }
    }

    private void insertShardingTable(GiftRecordData data) {
        String suffix = getSuffix(data);
        data.setSuffix(suffix);
        createTable(suffix);
        mapper.insertSelective(data);
    }

    public void saveToThirdDB(GiftRecordData data) {
        LogGiftRecordData logData = new LogGiftRecordData();
        Map<String, Object> additionalProps = additionalActivityInfo(data);
        logData.copyFrom(data, additionalProps);
        eventReport.track(logData);
    }

    // 记录活动额外埋点字段
    private Map<String, Object> additionalActivityInfo(GiftRecordData data) {
        Map<String, Object> additionalProps = new HashMap<>();
        fillAdditionalGalaInfo(data, additionalProps);
        return additionalProps;
    }

    // 庆典活动数据
    private void fillAdditionalGalaInfo(GiftRecordData data, Map<String, Object> additionalProps) {
        try {
            AppConfigActivityData activityData = appConfigActivityDao.getOneByCodeAndValid(EventCode.EVENT_GALA_ROOM_2412, AppConfigActivityData.validType.SERVING);
            if ((!ObjectUtils.isEmpty(activityData)) && (!StringUtils.isEmpty(data.getRoomid()))) {
                ActivityGalaRoomApplyData applyData = galaRoomApplyDao.getCurrStandingGala(RoomUtils.getRoomOwnerId(data.getRoomid()));
                if (!ObjectUtils.isEmpty(applyData)) {
                    ActivityInfoParamThVO activityInfoParamThVO = new ActivityInfoParamThVO();
                    activityInfoParamThVO.setActivityRoomType(applyData.getRoomType().toString());
                    activityInfoParamThVO.setActivityName(activityData.getName());
                    activityInfoParamThVO.setEventCode(activityData.getActivityCode().toString());
                    additionalProps.put("activity_info_param", JSON.toJSONString(activityInfoParamThVO));
                }
            }
        } catch (Exception e) {
            logger.error("data.getRoomId={} {}", data.getRoomid(), e.getMessage(), e);
            sender.info(CmdCodeEnum.UNKNOWNS.getWarnName(), "fillAdditionalGalaInfo error. data.getRoomId=" + data.getRoomid(), "");
        }
    }

    public List<GiftRecordData> getNoDealGiftRecordList(int startLastTime, int endLastTime, int ctime) {
        return mapper.selectNoDealGiftRecordListBy(startLastTime, endLastTime, ctime);
    }

    public void updateGiftRecordStatus(GiftRecordData data, int status) {
        int mtime = DateHelper.getCurrentTime();
        mapper.updateGiftRecordStatus(data.getRid(), status, mtime);
        String suffix = getSuffix(data);
        mapper.updateGiftRecordStatusByRidAndSuffix(data.getRid(), status, mtime, suffix);
    }

    public GiftRecordData getLargeGiftByCid(String cid) {
        return slaveMapper.selectLargeGiftByCid(cid);
    }

    public List<GiftRecordData> getAllLargeGiftByCid(String cid) {
        return slaveMapper.selectAllLargeGiftByCid(cid);
    }

    /**
     * 批量修改大礼物状态
     *
     * @param cid
     * @param mtime
     * @param startTime
     * @param endTime
     */
    public void changeCheatLargeGiftStatusByCid(String cid, int mtime, long startTime, long endTime) {
        mapper.updateCheatLargeGiftStatusByCid(cid, mtime);
        if (endTime <= startTime) {
            // 结束时间必须大于开始时间
            endTime = startTime + 1;
        }
        List<String> suffixs = getSuffixes(startTime, endTime);
        for (String suffix : suffixs) {
            mapper.updateCheatLargeGiftStatusByCidAndSuffix(cid, mtime, suffix);
        }
    }

    public List<PkUserData> getPkContributionRank(String pid, String roomId) {
        return slaveMapper.selectPkContributionUserRank(pid, roomId);
    }

    /**
     * todo 加缓存
     */
    public List<PkContributionData> getPkContributionRoom(String pid) {
        return slaveMapper.selectPkContributionRoom(pid);
    }


    public CountVO queryRoomGiftGainSum(String uid, DayTimeData allDateTime, int fromType) {
        return mapper.queryRoomGiftGainSum(uid, allDateTime.getTime(), allDateTime.getEndTime(), fromType);
    }

    public List<CountVO> queryPersonGiftRecord(String uid, Integer startTime, Integer endTime) {
        return slaveMapper.selectPersonGiftRecord(uid, startTime, endTime);
    }


    public List<HostIncomeVO> queryPKGiftRankList(long start, long end, String channel, String excludeChannel) {
        return slaveMapper.getPKGiftRankList(start, end, channel, excludeChannel);
    }

    public List<HostIncomeVO> queryPKGiftRankListByRoomId(long start, long end, List<String> roomIdList) {
        return slaveMapper.getPKGiftRankListByRoomId(start, end, roomIdList);
    }

    public double getGiftGain(String uid, Long startTime, Long endTime) {
        Double gain = slaveMapper.selectGiftGain(uid, startTime, endTime);
        return gain != null ? gain : 0;
    }

    public Set<String> queryUidSetByToUidAndDurationAndFromType(String uid, DayTimeData duration, int fromType) {
        Set<String> uidSet = mapper.queryUidSetByToUidAndDurationAndFromType(uid, duration.getTime(), duration.getEndTime(), fromType);
        if (ObjectUtils.isEmpty(uidSet)) {
            uidSet = new HashSet<>(0);
        }
        return uidSet;
    }

    private String getSuffix(GiftRecordData data) {
        return DateHelper.UTC.getTableSuffix(new Date(data.getCtime() * 1000L));
    }
}
