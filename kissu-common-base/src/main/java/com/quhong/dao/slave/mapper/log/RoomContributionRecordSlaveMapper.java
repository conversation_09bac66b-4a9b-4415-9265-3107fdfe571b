package com.quhong.dao.slave.mapper.log;

import com.quhong.dao.datas.RoomContributionRecordData;
import com.quhong.dao.mapper.ShardingMapper;
import com.quhong.data.room.RoomContributionData;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Set;

/**
 * @ClassName RoomContributionRecordSlaveMapper
 * <AUTHOR>
 * @date 2022/12/17 15:42
 */
public interface RoomContributionRecordSlaveMapper extends Mapper<RoomContributionRecordData>, ShardingMapper {
    Double selectSelfCharmRank(@Param("suffixList") List<String> suffixList,
                                @Param("startTime") int startTime,
                                @Param("endTime") int endTime,
                                @Param("roomId") String roomId,
                                @Param("testUidSet") Set<String> testUidSet);

    Double selectChapterGain(@Param("roomId") String roomId,
                          @Param("chapterId") String chapterId,
                          @Param("timeStart") Long timeStart,
                          @Param("timeEnd") Long timeEnd,
                          @Param("suffixList") List<String> suffixList);

    List<RoomContributionRecordData> selectRoomContributionRankTop3ByTime(@Param("roomId") String roomId,
                                                                          @Param("startTime") long startTime,
                                                                          @Param("endTime") long endTime,
                                                                          @Param("suffixList") List<String> suffixList);

    Double selectRoomSumContributionByTime(@Param("roomId") String roomId,
                                         @Param("startTime") long startTime,
                                         @Param("endTime") long endTime,
                                         @Param("suffixList") List<String> suffixList);

    RoomContributionRecordData selectMvpContributionByTime(@Param("roomId") String roomId,
                                                           @Param("startTime") long startTime,
                                                           @Param("endTime") long endTime,
                                                           @Param("suffixList") List<String> suffixList);

    Double selectUserSumContributionByTime(@Param("uid") String uid,
                                         @Param("roomId") String roomId,
                                         @Param("startTime") long startTime,
                                         @Param("endTime") long endTime,
                                         @Param("suffixList") List<String> suffixList);

    RoomContributionData getRoomContributionByRoomIdAndType(@Param("roomId") String roomId,
                                                            @Param("timeStart") long timeStart,
                                                            @Param("timeEnd") long timeEnd,
                                                            @Param("contributionType") long contributionType,
                                                            @Param("tableSuffixList") List<String> tableSuffixList);

    RoomContributionData getPartyRoomContributionByRoomId(@Param("roomId") String roomId,
                                                          @Param("timeStart") long timeStart,
                                                          @Param("timeEnd") long timeEnd,
                                                          @Param("tableSuffixList") List<String> tableSuffixList);

    Double selectMicContributionValue(@Param("uid") String uid,
                                      @Param("roomId") String roomId,
                                      @Param("chapterId") String chapterId,
                                      @Param("startTime") long startTime,
                                      @Param("endTime") long endTime,
                                      @Param("suffixList") List<String> suffixList);
}
