package com.quhong.dao.slave.mapper.db;

import com.quhong.dao.datas.GiftRecordData;
import com.quhong.data.bo.activity.QueryGiftRankBO;
import com.quhong.data.dto.RankingDTO;
import com.quhong.data.pk.PkContributionData;
import com.quhong.data.pk.PkUserData;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.HostIncomeVO;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Set;

public interface GiftRecordSlaveMapper extends Mapper<GiftRecordData> {

    /**
     * Description: 获得指定top数量的指定相关条件的【发送礼物】排行
     *
     * @param start    开始时间
     * @param end      结束时间
     * @param giftId   礼物id 23玫瑰
     * @param fromType 3为live房间发送
     * @param top      20 排名数量
     * @return java.util.List<com.quhong.data.dto.LiveRoomRoseRankingDTO>
     * <AUTHOR>
     * @date 2021/12/3 18:43
     */
    List<RankingDTO> getTopSendGiftData(@Param("start") long start, @Param("end") long end, @Param("giftId") int giftId, @Param("fromType") int fromType, @Param("top") int top);

    /**
     * Description: 获得指定top数量的指定相关条件的【接收礼物】排行
     *
     * @param start          开始时间
     * @param end            结束时间
     * @param giftId         礼物id 23玫瑰
     * @param fromType       3为live房间发送
     * @param top            20 排名数量
     * @param countryCodeSet 国家码列表
     * @return java.util.List<com.quhong.data.dto.LiveRoomRoseRankingDTO> 活动排行榜
     * <AUTHOR>
     * @date 2021/12/3 18:43
     */
    List<RankingDTO> getTopReceiveGiftData(@Param("start") long start, @Param("end") long end, @Param("giftId") int giftId, @Param("fromType") int fromType, @Param("top") int top, @Param("countryCodeSet") Set<String> countryCodeSet);

    RankingDTO getOneReceiveGiftData(@Param("start") long start, @Param("end") long end, @Param("giftId") int giftId, @Param("fromType") int fromType, @Param("uid") String fromUid);

    RankingDTO getOneSendGiftData(@Param("start") long start, @Param("end") long end, @Param("giftId") int giftId, @Param("fromType") int fromType, @Param("uid") String toUid);

    /**
     * 按日期查询指定数据
     *
     * @param start
     * @param end
     * @param offset
     * @param pageSize
     * @return
     */
    List<GiftRecordData> selectListByDay(@Param("start") int start, @Param("end") int end, @Param("offset") int offset, @Param("pageSize") int pageSize);

    List<GiftRecordData> selectNoDealGiftRecordListBy(@Param("startLastTime") int startLastTime,
                                                      @Param("endLastTime") int endLastTime,
                                                      @Param("ctime") int ctime);


    GiftRecordData selectLargeGiftByCid(@Param("cid") String cid);

    List<GiftRecordData> selectAllLargeGiftByCid(@Param("cid") String cid);

    List<PkUserData> selectPkContributionUserRank(@Param("pid") String pid, @Param("roomId") String roomId);

    List<PkContributionData> selectPkContributionRoom(@Param("pid") String pid);

    CountVO queryRoomGiftGainSum(@Param("uid") String uid,
                                 @Param("time") int time,
                                 @Param("endTime") int endTime,
                                 @Param("fromType") int fromType);

    List<RankingDTO> getTopGiftPriceRanking(QueryGiftRankBO bo);

    /**
     * 活动临时使用，后续将废除
     * @param bo
     * @return
     */
    List<RankingDTO> getActivityGiftPriceRanking(QueryGiftRankBO bo);

    List<RankingDTO> getTopGiftRanking(QueryGiftRankBO bo);

    List<CountVO> selectPersonGiftRecord(@Param("uid") String uid,
                                         @Param("startTime") Integer startTime,
                                         @Param("endTime") Integer endTime);

    List<HostIncomeVO> getPKGiftRankList(@Param("start") long start, @Param("end") long end, @Param("channel") String channel, @Param("excludeChannel") String excludeChannel);

    List<HostIncomeVO> getPKGiftRankListByRoomId(@Param("start") long start, @Param("end") long end, @Param("roomIdList") List<String> roomIdList);

    List<CountVO> queryGiftGainSum(@Param("toUidSet") Set<String> toUidSet,
                                   @Param("fromType") int fromType,
                                   @Param("startTime") long startTime,
                                   @Param("endTime") long endTime);

    Double selectGiftGain(@Param("uid") String uid, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    Set<String> queryUidSetByToUidAndDurationAndFromType(@Param("uid") String uid,
                                                         @Param("time") int time,
                                                         @Param("endTime") int endTime,
                                                         @Param("fromType") int fromType);

    List<CountVO> missRoseTop3Logic();
}
