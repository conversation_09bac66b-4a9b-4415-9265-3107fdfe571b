package com.quhong.dao.slave.mapper.log;

import com.quhong.dao.datas.LogPayRecordData;
import com.quhong.data.bo.QueryPayCoinBO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

public interface LogPayRecordSlaveMapper {

    /**
     * Description: 获取最近1000条支付记录
     *
     * @param
     * @return java.util.List<com.quhong.dao.datas.LogPayRecordData>
     * <AUTHOR>
     * @date 2021/11/25 16:11
     */
    List<LogPayRecordData> getNewThousandData();

    String selectTotalRecharge(@Param("uid") String uid);

    LogPayRecordData selectUserLatelyRechargeData(@Param("uid") String uid);

    @Select("select sum(usd_price) from log_pay_record where uid=#{uid} and ctime > #{startTime} and status in (1, 10)")
    Double selectSumUsdPrice(@Param("uid") String uid, @Param("startTime") long startTime);

    @Select("select sum(usd_price) from log_pay_record where uid=#{uid} and channel in #{channelList} ctime > #{startTime} and status = #{status}")
    Double selectSumUsdPriceByChannel(@Param("uid") String uid, @Param("startTime") long startTime, @Param("status") int status, @Param("channelList") List<String> channelList);

    LogPayRecordData selectHadPayMoney5evaRecord(@Param("uid") String uid);

    /**
     * 勋章计算专用
     */
    @Select("SELECT IFNULL(SUM(`changed`), 0) from log_pay_record WHERE uid=#{uid} AND ctime > 1695081600 AND status in (1, 10)")
    long queryTotalRechargeGold(String uid);

    @Select("SELECT IFNULL(SUM(`changed`), 0) from log_pay_record WHERE uid=#{uid} and `type`=#{type} and `status`=1")
    long queryRechargeGold(@Param("uid") String uid, @Param("type") int type);

    Long queryPayCoinCountBy(QueryPayCoinBO bo);

    String queryUsdPriceSumBy(QueryPayCoinBO bo);

    @Select("SELECT IFNULL(SUM(`usd_price`), 0) from kissu_log.log_pay_record WHERE channel=#{channel} AND ctime >= #{startTime} AND ctime < #{endTime} " +
            "and status = 1 and valid = 1")
    String queryChannelPayUsdPriceByDuration(@Param("channel") String channel,
                                             @Param("startTime") long startTime,
                                             @Param("endTime") long endTime);

    @Select("SELECT COUNT(distinct uid) from kissu_log.log_pay_record WHERE channel=#{channel} AND ctime >= #{startTime} AND ctime < #{endTime} " +
            "and status = 1 and valid = 1")
    Integer queryUserCountByChannelAndDuration(@Param("channel") String channel,
                                               @Param("startTime") long startTime,
                                               @Param("endTime") long endTime);

    List<LogPayRecordData> selectTotalRechargeByChannelListAndTime(@Param("channelList") List<String> channelList,
                                                                   @Param("startTime") long startTime,
                                                                   @Param("endTime") long endTime);

    List<LogPayRecordData> selectCoinSellerGoldByCoinSellerIdSet(@Param("startTime") long startTime,
                                                                 @Param("endTime") long endTime,
                                                                 @Param("coinSellerIdSet") Set<Long> coinSellerIdSet);

    List<LogPayRecordData> selectCoinSellerRecentlyBugCountByUid(@Param("startTime") long startTime,
                                                                 @Param("endTime") long endTime,
                                                                 @Param("uid") String uid,
                                                                 @Param("coinSellerIdSet") Set<Long> coinSellerIdSet);

    LogPayRecordData selectRecentlyCoinSellerRecordData(@Param("uid") String uid);

    String queryUsdPriceSum(@Param("uid") String uid,
                            @Param("typeSet") Set<Integer> typeSet,
                            @Param("statusSet") Set<Integer> statusSet,
                            @Param("start") Long startTime,
                            @Param("end") Long endTime);

    Double selectUserSetExtendTimePaySum(@Param("uidSet") Set<String> uidSet,
                                         @Param("startTime") long startTime,
                                         @Param("endTime") long endTime);

    String selectTotalRechargeByTime(@Param("uid") String uid, @Param("startTime") long startTime, @Param("endTime") long endTime);

    @Select("SELECT IFNULL(SUM(`changed`), 0) from kissu_log.log_pay_record WHERE uid=#{uid} and ctime>=#{startTime} and ctime <=#{endTime} and `type`=#{coinDealersRecharge} and `status`=1")
    long queryRechargeGoldByTime(@Param("uid") String uid, @Param("coinDealersRecharge") byte coinDealersRecharge, @Param("startTime") long startTime, @Param("endTime") long endTime);

    //queryLastPayTime
    @Select("select * from kissu_log.log_pay_record where uid = #{uid} and status = 1 and valid = 1 order by mtime desc limit 1")
    LogPayRecordData queryLastPayTime(@Param("uid") String uid);
}
