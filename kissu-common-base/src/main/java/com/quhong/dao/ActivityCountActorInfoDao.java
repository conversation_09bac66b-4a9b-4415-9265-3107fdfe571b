package com.quhong.dao;

import com.quhong.dao.datas.db.ActivityCountActorInfoData;
import com.quhong.dao.mapper.db.ActivityCountActorInfoMapper;
import com.quhong.data.bo.activity.BaseQueryRankBO;
import com.quhong.redis.BaseHashSaveRedis;
import com.quhong.utils.TKUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.time.Duration;
import java.util.List;

/**
 * 活动通用个人数据统计表（数据维度：uid+eventCode+count_group）(activity_count_actor_info)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-07 18:05:50
 */
@Lazy
@Slf4j
@Component
@RequiredArgsConstructor
public class ActivityCountActorInfoDao {

    private static final String ONE_KEY = "activity_count_actor_info";

    private static final String LIST_KEY = "hash:activity_count_actor_info_list";

    private final ActivityCountActorInfoMapper mapper;

    private final BaseHashSaveRedis baseHashSaveRedis;

    @CachePut(value = ONE_KEY, key = "#data.eventCode + ':' + #data.countGroup + ':' + #data.uid", unless = "#result == null")
    public ActivityCountActorInfoData insertOneSelective(ActivityCountActorInfoData data) {
        int code = mapper.insertSelective(data);
        if (code <= 0) {
            return null;
        }
        baseHashSaveRedis.deleteByKey(getListKey(data.getEventCode()));
        return getOneByEventCodeAndCountGroupAndUid(data);
    }

    @CachePut(value = ONE_KEY, key = "#data.eventCode + ':' + #data.countGroup + ':' + #data.uid", unless = "#result == null")
    public ActivityCountActorInfoData updateOneSelective(ActivityCountActorInfoData data) {
        int code = mapper.updateByPrimaryKeySelective(data);
        if (code <= 0) {
            return null;
        }
        baseHashSaveRedis.deleteByKey(getListKey(data.getEventCode()));
        return getOneByEventCodeAndCountGroupAndUid(data);
    }

    @Cacheable(value = ONE_KEY, key = "#data.eventCode + ':' + #data.countGroup + ':' + #data.uid", unless = "#result == null")
    public ActivityCountActorInfoData getOneByEventCodeAndCountGroupAndUid(ActivityCountActorInfoData data) {
        Example example = TKUtils.creatExample(ActivityCountActorInfoData.class);
        example.createCriteria()
                .andEqualTo("uid", data.getUid())
                .andEqualTo("eventCode", data.getEventCode())
                .andEqualTo("countGroup", data.getCountGroup())
                .andEqualTo("valid", 1);
        return mapper.selectOneByExample(example);
    }

    public List<ActivityCountActorInfoData> getListByEventCodeAndCountGroupFromRedis(Integer eventCode, String countGroup, Integer top) {
        BaseQueryRankBO bo = new BaseQueryRankBO(eventCode, countGroup, top);
        return baseHashSaveRedis.getListByRedis(
                bo,
                getListKey(eventCode),
                ActivityCountActorInfoData.class,
                this::getListByEventCodeAndCountGroup,
                Duration.ofDays(7));
    }

    private static String getListKey(Integer eventCode) {
        return LIST_KEY + ":" + eventCode;
    }

    public List<ActivityCountActorInfoData> getListByEventCodeAndCountGroup(BaseQueryRankBO bo) {
        Example example = TKUtils.creatExample(ActivityCountActorInfoData.class);
        example.createCriteria()
                .andEqualTo("eventCode", bo.getEventCode())
                .andEqualTo("countGroup", bo.getCountGroup())
                .andEqualTo("valid", 1);
        example.orderBy("count").desc().orderBy("lastCountTime");
        return mapper.selectByExampleAndRowBounds(example, new RowBounds(0, bo.getTop()));
    }

    public List<ActivityCountActorInfoData> getListByEventCodeAndCountGroupAndRid(Integer eventCode, String countGroup, Long rid) {
        Example example = TKUtils.creatExample(ActivityCountActorInfoData.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("eventCode", eventCode)
                .andEqualTo("countGroup", countGroup);
        if (rid != null) {
            criteria.andEqualTo("rid", rid);
        }
        criteria.andEqualTo("valid", 1);
        return mapper.selectByExample(example);
    }
}

