package com.quhong.dao.mapper.log;

import com.quhong.dao.datas.log.AwardsKeyRecordData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 礼包奖励发放记录DAO接口
 *
 * <AUTHOR>
 * @since 2024/4/15
 */
public interface AwardsKeyRecordMapper extends Mapper<AwardsKeyRecordData> {

    /**
     * 根据礼包key标识和用户ID查询发放记录
     *
     * @param awardsKey 礼包key标识
     * @param uid 用户ID
     * @return 发放记录列表
     */
    @Select("SELECT * FROM kissu_log.awards_key_record WHERE awards_key = #{awardsKey} AND uid = #{uid} ORDER BY id DESC")
    List<AwardsKeyRecordData> findByAwardsKeyAndUid(
        @Param("awardsKey") String awardsKey,
        @Param("uid") String uid
    );

    /**
     * 根据用户ID查询发放记录
     *
     * @param uid 用户ID
     * @return 发放记录列表
     */
    @Select("SELECT * FROM kissu_log.awards_key_record WHERE uid = #{uid} ORDER BY id DESC")
    List<AwardsKeyRecordData> findByUid(@Param("uid") String uid);
}
