package com.quhong.dao.mapper.log;

import com.quhong.dao.datas.log.AgentPerformanceDetailLogData;
import com.quhong.dao.mapper.ShardingMapper;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.SumVO;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.common.special.InsertListMapper;

import java.util.List;
import java.util.Set;

/**
 * @ClassName AgentPerformanceDetailLogMapper
 * <AUTHOR>
 * @date 2024/2/19 15:02
 */
public interface AgentPerformanceDetailLogMapper extends Mapper<AgentPerformanceDetailLogData>, ShardingMapper, InsertListMapper<AgentPerformanceDetailLogData> {


    void updatePerformanceFromUid(@Param("uid") String uid,
                                  @Param("linkIdSet") Set<String> linkIdSet,
                                  @Param("operateDataValid") int operateDataValid,
                                  @Param("suffix") String suffix,
                                  @Param("operateTime") long operateTime);

    void updatePerformanceFromAgentIdSet(@Param("agentIdSet") Set<String> agentIdSet,
                                         @Param("linkIdSet") Set<String> linkIdSet,
                                         @Param("operateDataValid") int operateDataValid,
                                         @Param("suffix") String suffix,
                                         @Param("operateTime") long operateTime);

    List<CountVO> queryAgentPerformanceList(@Param("agentIdSet") Set<String> agentIdSet,
                                            @Param("startTime") long startTime,
                                            @Param("endTime") long endTime,
                                            @Param("suffixes") List<String> suffixes);

    List<SumVO> queryAgentCommissionList(@Param("agentIdSet") Set<String> agentIdSet,
                                         @Param("startTime") long startTime,
                                         @Param("endTime") long endTime,
                                         @Param("suffixes") List<String> suffixes);
    
    List<SumVO> querySubChangeCommissionList(@Param("sonAgentIdSet") Set<String> sonAgentIdSet,
                                             @Param("startTime") long startTime,
                                             @Param("endTime") long endTime,
                                             @Param("agentId") String agentId,
                                             @Param("suffixes") List<String> suffixes);

    Long sumAgentHostPerformance(@Param("agentId") String agentId,
                                 @Param("changeReason") int changeReason,
                                 @Param("startTime") long startTime,
                                 @Param("endTime") long endTime,
                                 @Param("suffixes") List<String> suffixes);

    List<CountVO> queryAgentHostPerformanceList(@Param("changeReason") int changeReason,
                                                 @Param("startTime") long startTime,
                                                 @Param("endTime") long endTime,
                                                 @Param("suffixes") List<String> suffixes);

    Set<String> selectActiveRelationUid(@Param("agentId") String agentId,
                                        @Param("startTime") long startTime,
                                        @Param("endTime") long endTime,
                                        @Param("suffixes") List<String> suffixes);

    Double sumAgentHostCommission(@Param("agentId") String agentId,
                                  @Param("changeReason") int changeReason,
                                  @Param("startTime") long startTime,
                                  @Param("endTime") long endTime,
                                  @Param("suffixes") List<String> suffixes);
}
