package com.quhong.dao.mapper.doris;

import com.quhong.dao.datas.doris.DorisMoneyDetailData;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.HostIncomeVO;
import com.quhong.data.vo.MoneyHistoryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @ClassName DorisMoneyDetailMapper
 * <AUTHOR>
 * @date 2024/11/28 15:10
 */
public interface DorisMoneyDetailMapper {
    void insertOne(@Param("data") DorisMoneyDetailData data, @Param("dbName") String dbName);

    void insertList(@Param("dataList") List<DorisMoneyDetailData> dataList, @Param("dbName") String dbName);

    List<MoneyHistoryVO> selectMoneyHistoryFromDoris(@Param("startTime") long startTime,
                                                     @Param("endTime") long endTime,
                                                     @Param("uid") String uid,
                                                     @Param("actType") Set<Integer> actType,
                                                     @Param("ignoreActType") Set<Integer> ignoreActType,
                                                     @Param("action") Integer action,
                                                     @Param("currencyCode") Integer currencyCode,
                                                     @Param("segmentCode") Integer segmentCode,
                                                     @Param("dbName") String dbName,
                                                     @Param("partitionIndexList") List<String> partitionIndexList,
                                                     @Param("start") int start,
                                                     @Param("size") int size);

    List<CountVO> getDiamondIncomeFromDoris(@Param("uidSet") Set<String> uidSet,
                                            @Param("actTypeSet") Set<Integer> actTypeSet,
                                            @Param("ignoreActTypeSet") Set<Integer> ignoreActTypeSet,
                                            @Param("sgeCodeSet") Set<Integer> sgeCodeSet,
                                            @Param("startTime") long startTime,
                                            @Param("endTime") long endTime,
                                            @Param("dbName") String dbName,
                                            @Param("partitionIndexList") List<String> partitionIndexList);

    List<CountVO> getDiamondCostFromDoris(@Param("uidSet") Set<String> uidSet,
                                          @Param("actTypeSet") Set<Integer> actTypeSet,
                                          @Param("ignoreActTypeSet") Set<Integer> ignoreActTypeSet,
                                          @Param("sgeCodeSet") Set<Integer> sgeCodeSet,
                                          @Param("startTime") long startTime,
                                          @Param("endTime") long endTime,
                                          @Param("dbName") String dbName,
                                          @Param("partitionIndexList") List<String> partitionIndexList);

    CountVO selectDiamondIncomeByUidAndActTypeSetAndIgnoreActTypeAndSegCode(@Param("dbName") String dbName,
                                                                            @Param("partitionIndexs") List<String> partitionIndex,
                                                                            @Param("uid") String uid,
                                                                            @Param("actTypeSet") Set<Integer> actTypeSet,
                                                                            @Param("ignoreActTypeSet") Set<Integer> ignoreActTypeSet,
                                                                            @Param("segCode") Integer segCode,
                                                                            @Param("startTime") long startTime,
                                                                            @Param("endTime") long endTime);

    CountVO selectStatValue(@Param("dbName") String dbName,
                            @Param("partitionIndexs") List<String> partitionIndexs,
                            @Param("startTime") Long startTime,
                            @Param("endTime") Long endTime,
                            @Param("action") Integer action,
                            @Param("actTypeSet") Set<Integer> ignoreActTypeSet,
                            @Param("testUidSet") Set<String> testUidSet);

    /**
     * 消耗统计
     *
     * @param uid       uid
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    CountVO selectConsumeValue(@Param("dbName") String dbName,
                               @Param("partitionIndexs") List<String> partitionIndexs,
                               @Param("uid") String uid,
                               @Param("startTime") Long startTime,
                               @Param("endTime") Long endTime);


    Double selectTotalDiamondIncomeByUidSetAndTime(@Param("dbName") String dbName,
                                                   @Param("partitionIndex") List<String> partitionIndex,
                                                   @Param("uidSet") Set<String> uidSet,
                                                   @Param("startTime") long startTime,
                                                   @Param("endTime") long endTime,
                                                   @Param("actTypeList") List<Integer> actTypeList);

    List<HostIncomeVO> selectHostTotalCallIncomeByTime(@Param("uidSet") Set<String> uidSet,
                                                       @Param("startTime") int startTime,
                                                       @Param("endTime") int endTime,
                                                       @Param("partitionIndexs") List<String> partitionIndex,
                                                       @Param("dbName") String dbName);

    List<HostIncomeVO> selectHostGiftIncomeByTime(@Param("uidSet") Set<String> uidSet,
                                                  @Param("startTime") int startTime,
                                                  @Param("endTime") int endTime,
                                                  @Param("partitionIndexs") List<String> partitionIndex,
                                                  @Param("dbName") String dbName);

    List<HostIncomeVO> selectTaskIncomeByTime(@Param("uidSet") Set<String> uidSet,
                                              @Param("startTime") int startTime,
                                              @Param("endTime") int endTime,
                                              @Param("partitionIndexs") List<String> partitionIndex,
                                              @Param("dbName") String dbName);

    List<HostIncomeVO> selectHostIncomeByTime(@Param("uidSet") Set<String> uidSet,
                                              @Param("startTime") int startTime,
                                              @Param("endTime") int endTime,
                                              @Param("partitionIndexs") List<String> partitionIndex,
                                              @Param("dbName") String dbName);

    List<HostIncomeVO> selectAfterChangeByTime(@Param("uidSet") Set<String> uidSet,
                                               @Param("startTime") int startTime,
                                               @Param("endTime") int endTime,
                                               @Param("partitionIndexs") List<String> partitionIndex,
                                               @Param("dbName") String dbName);

    HostIncomeVO selectHostTotalDiamondIncome(@Param("dbName") String dbName,
                                              @Param("partitionIndexs") List<String> partitionIndexs,
                                              @Param("uid") String uid,
                                              @Param("startTime") long startTime,
                                              @Param("endTime") long endTime);

    List<CountVO> selectDiamondSumIncomeByUidSetAndActTypeSetAndIgnoreActTypeSet(@Param("dbName") String dbName,
                                                                                 @Param("partitionIndexs") List<String> partitionIndex,
                                                                                 @Param("uidSet") Set<String> uidSet,
                                                                                 @Param("actTypeSet") Set<Integer> actTypeSet,
                                                                                 @Param("ignoreActTypeSet") Set<Integer> ignoreActTypeSet,
                                                                                 @Param("startTime") long startTime,
                                                                                 @Param("endTime") long endTime);

    HostIncomeVO selectHostTotalDiamondIncomeByTypeSeg(@Param("dbName") String dbName,
                                                       @Param("partitionIndexs") List<String> partitionIndexs,
                                                       @Param("uid") String uid,
                                                       @Param("startTime") int startTime,
                                                       @Param("endTime") int endTime,
                                                       @Param("actTypeList") List<Integer> actTypeList,
                                                       @Param("segmentCodeList") List<Integer> segmentCodeList);

    List<DorisMoneyDetailData> selectGameRecordByTime(@Param("dbName") String dbName,
                                                      @Param("partitionIndexs") List<String> partitionIndexs,
                                                      @Param("uid") String uid,
                                                      @Param("startTime") long startTime,
                                                      @Param("endTime") long endTime,
                                                      @Param("limit") int limit);

    List<DorisMoneyDetailData> selectGameCoinDetail(@Param("dbName") String dbName,
                                                    @Param("partitionIndex") List<String> partitionIndex,
                                                    @Param("uid") String uid,
                                                    @Param("startTime") long startTime,
                                                    @Param("endTime") long endTime,
                                                    @Param("action") Integer action,
                                                    @Param("offset") int offset,
                                                    @Param("pageSize") int pageSize);

    List<HostIncomeVO> selectGiftIncomeByTimeAndUidSet(@Param("dbName") String dbName,
                                                       @Param("partitionIndexs") List<String> partitionIndex,
                                                       @Param("uidSet") Set<String> uidSet,
                                                       @Param("startTime") long startTime,
                                                       @Param("endTime") long endTime);

    List<HostIncomeVO> selectHostTotalIncome(@Param("hostList") Set<String> hostList,
                                             @Param("startTime") int startTime,
                                             @Param("endTime") int endTime,
                                             @Param("partitionIndexs") List<String> partitionIndexs,
                                             @Param("dbName") String dbName);
}
