package com.quhong.dao.mapper.db;

import com.quhong.dao.datas.db.TaskHostOpenLiveLevelConfigData;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TaskHostOpenLiveLevelConfigMapper extends Mapper<TaskHostOpenLiveLevelConfigData> {
    List<TaskHostOpenLiveLevelConfigData> selectStatList(@Param("taskLevel") Integer taskLevel,
                                                         @Param("valid") Integer valid,
                                                         @Param("countryCode") String countryCode);

    TaskHostOpenLiveLevelConfigData selectOneBy(@Param("taskLevel") Integer taskLevel,
                                                @Param("valid") Integer valid,
                                                @Param("countryCode") String countryCode);

    void updateValidBy(@Param("taskLevel") Integer taskLevel,
                  @Param("valid") Integer valid,
                  @Param("mtime") Long mtime,
                  @Param("operator") String operator);

    List<TaskHostOpenLiveLevelConfigData> selectAllByCountryCode(@Param("taskLevel") Integer taskLevel,
                                                                 @Param("countryCode") String countryCode,
                                                                 @Param("valid") Integer valid);
}
