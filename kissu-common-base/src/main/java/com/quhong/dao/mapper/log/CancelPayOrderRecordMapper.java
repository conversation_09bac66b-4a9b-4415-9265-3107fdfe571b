package com.quhong.dao.mapper.log;


import com.quhong.dao.datas.log.CancelPayOrderRecordData;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

/**
 * <AUTHOR>
 * @date 2025/6/5 20:19
 * @description CancelPayOrderRecordMapper
 */
public interface CancelPayOrderRecordMapper extends Mapper<CancelPayOrderRecordData> {

    /**
     * 获取支付失败次数
     * @param uid
     * @return
     */
    Integer getPayFailCount(@Param("uid") String uid);

    /**
     * 更新为已处理
     * @param uid
     */
    void updateUnread(@Param("uid") String uid);

    /**
     * 查询已处理次数
     */
    Integer getReadCount(@Param("uid") String uid);

}
