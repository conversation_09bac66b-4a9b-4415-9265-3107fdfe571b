package com.quhong.dao;

import com.quhong.dao.datas.db.AwardsKeyConfigData;
import com.quhong.dao.mapper.db.AwardsKeyConfigMapper;
import com.quhong.utils.StringUtils;
import com.quhong.utils.TKUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/16 18:31
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AwardsKeyConfigDao {
    public static final String ONE_KEY = "str:awards_key";
    private final AwardsKeyConfigMapper awardsKeyConfigMapper;

    @CachePut(value = ONE_KEY, key = "#data.awardsKey", unless = "#result == null")
    public AwardsKeyConfigData insert(AwardsKeyConfigData data) {
        int code = awardsKeyConfigMapper.insertSelective(data);
        if (code > 0) {
            return queryByAwardsKey(data.getAwardsKey());
        }
        return null;
    }

    @CachePut(value = ONE_KEY, key = "#data.awardsKey", unless = "#result == null")
    public AwardsKeyConfigData update(AwardsKeyConfigData data) {
        int code = awardsKeyConfigMapper.updateByPrimaryKeySelective(data);
        if (code > 0) {
            return queryByAwardsKey(data.getAwardsKey());
        }
        return null;
    }

    @Cacheable(value = ONE_KEY, key = "#awardsKey")
    public AwardsKeyConfigData queryByAwardsKey(String awardsKey) {
        Example example = TKUtils.creatExample(AwardsKeyConfigData.class);
        example.createCriteria().andEqualTo("awardsKey", awardsKey);
        return awardsKeyConfigMapper.selectOneByExample(example);
    }

    /**
     * 根据条件查询
     * @param awardsKey 礼包key
     * @param awardsKeyType 礼包key类型
     * @param valid 是否有效 1 有效 0 无效
     * @return
     */
    public List<AwardsKeyConfigData> findByCondition(String awardsKey, Integer awardsKeyType, Integer valid){
        Example example = TKUtils.creatExample(AwardsKeyConfigData.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.hasLength(awardsKey)) {
            criteria.andLike("awardsKey", awardsKey);
        }
        if (awardsKeyType != null) {
            criteria.andEqualTo("awardsKeyType", awardsKeyType);
        }
        if (valid != null && valid >= 0) {
            criteria.andEqualTo("valid", valid);
        }
        return awardsKeyConfigMapper.selectByExample(example);
    }

}
