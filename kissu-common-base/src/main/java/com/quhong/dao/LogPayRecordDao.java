package com.quhong.dao;

import com.quhong.constant.ActType;
import com.quhong.constant.RedisKeyConstant;
import com.quhong.core.cache.CacheMap;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.LogPayRecordData;
import com.quhong.dao.mapper.log.LogPayRecordMapper;
import com.quhong.dao.slave.mapper.log.LogPayRecordSlaveMapper;
import com.quhong.data.bo.QueryPayCoinBO;
import com.quhong.players.ActorMgr;
import com.quhong.utils.TKUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Description:
 *
 * <AUTHOR>
 * @date 2021/11/25 16:14
 */
@Component
@Lazy
public class LogPayRecordDao {

    private static final CacheMap<String, Map<String, String>> CACHE_MAP = new CacheMap<>(5 * 60 * 1000);

    private static final int USD_TO_GOLD = 135;

    @Resource
    private LogPayRecordMapper mapper;

    @Resource
    private LogPayRecordSlaveMapper slaveMapper;

    @Resource
    private ActorMgr actorMgr;
    @Resource
    private LogPayRecordDao logPayRecordDao;

    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate stringRedisTemplate;

    public LogPayRecordData selectByOrderId(String orderId) {
        return mapper.selectLastRechargeDataByOrderId(orderId);
    }

    public void updateSelective(LogPayRecordData recordData) {
        if (StringUtils.isEmpty(recordData.getOrderId())) {
            throw new IllegalArgumentException("tk check param orderId empty");
        }
        Example example = TKUtils.creatExample(LogPayRecordData.class);
        example.createCriteria().andEqualTo("orderId", recordData.getOrderId());
        mapper.updateByExampleSelective(recordData, example);
        if (StringUtils.hasLength(recordData.getUid())) {
            logPayRecordDao.deleteTotalMedalRechargeGold(recordData.getUid());
        }
    }

    public void insert(LogPayRecordData payRecordData) {
        mapper.insertSelective(payRecordData);
        if (StringUtils.hasLength(payRecordData.getUid())) {
            logPayRecordDao.deleteTotalMedalRechargeGold(payRecordData.getUid());
        }
    }

    public LogPayRecordData getDataByOrderId(String orderId) {
        Example example = TKUtils.creatExample(LogPayRecordData.class);
        example.createCriteria().andEqualTo("orderId", orderId);
        List<LogPayRecordData> list = mapper.selectByExampleAndRowBounds(example, new RowBounds(0, 1));
        if (list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }

//    public LogPayRecordData selectFirstRechargeData(String uid){
//        return mapper.selectFirstRechargeDataByUid(uid);
//    }
//
//    public LogPayRecordData getFirstRechargeDataFromRedis(String uid){
//        String key = getFirstRechargeRedisKey(uid);
//        //缓存获取
//        String dataStr = mainRedis.opsForValue().get(key);
//        if (!StringUtils.isEmpty(dataStr)) {
//            return JSONObject.parseObject(dataStr,LogPayRecordData.class);
//        }
//        //数据库获取
//        LogPayRecordData data = selectFirstRechargeData(uid);
//        if (data != null) {
//            mainRedis.opsForValue().set(key,JSONObject.toJSONString(data),30, TimeUnit.DAYS);
//        }
//        return data;
//    }
//
//    private String getFirstRechargeRedisKey(String uid) {
//        return RedisKeyConstant.ACTOR_FIRST_RECHARGE_RECORD + uid;
//    }


    /**
     * 获取最近1000支付用户数据用于预约功能假消息使用
     * 缓存处理
     *
     * @return
     */
    public Map<String, String> getCacheNewThousandData() {
        //从缓存获取
        Map<String, String> map = CACHE_MAP.getData("NewThousandData");
        if (!CollectionUtils.isEmpty(map)) {
            return map;
        }
        map = getNewThousandData();
        CACHE_MAP.cacheData("NewThousandData", map);
        return map;
    }

    /**
     * 获取最近1000支付用户数据用于预约功能假消息使用
     *
     * @return
     */
    public Map<String, String> getNewThousandData() {
        List<LogPayRecordData> payDataList = slaveMapper.getNewThousandData();
        ActorData actorData;
        Map<String, String> map = new LinkedHashMap<>();
        for (LogPayRecordData data : payDataList) {
            actorData = actorMgr.getActorData(data.getUid());
            if (actorData == null) {
                continue;
            }
            String name = actorData.getName();
            String coin = "";
            if (data.getChanged() != 0) {
                coin = String.valueOf(data.getChanged());
            }
            map.put(name, coin);
        }
        return map;
    }

    /**
     * 获取用户总充值金额
     */
    public double getUserTotalRecharge(String uid) {
        String totalRecharge = slaveMapper.selectTotalRecharge(uid);
        return !StringUtils.isEmpty(totalRecharge) ? Double.parseDouble(totalRecharge) : 0;
    }


    /**
     * 获取用户总充值金额
     *
     * @param uid
     * @return
     */
    @Cacheable(value = "str:user_total_recharge:uid:", key = "#uid")
    public double getUserTotalRechargeFromCache(String uid) {
        String totalRecharge = slaveMapper.selectTotalRecharge(uid);
        return !StringUtils.isEmpty(totalRecharge) ? Double.parseDouble(totalRecharge) : 0;
    }

    /**
     * 币商充值换算成美金，与数数对齐
     *
     * @return
     */
    @Cacheable(value = "str:user_partner_recharge:uid:", key = "#uid")
    public double selectPartnerUsdPriceFromCache(String uid) {
        long gold = slaveMapper.queryRechargeGold(uid, ActType.COIN_DEALERS_RECHARGE);
        return (double) gold / 135;
    }

    /**
     * 获取用户总充值金额
     *
     * @param uid
     * @return
     */
    @Cacheable(value = "str:user_total_recharge:uid:", key = "#uid+':startTime:'+#startTime+':endTime:'+#endTime")
    public double getUserTotalRechargeByTimeFromCache(String uid, long startTime, long endTime) {
        String totalRecharge = slaveMapper.selectTotalRechargeByTime(uid, startTime, endTime);
        return !StringUtils.isEmpty(totalRecharge) ? Double.parseDouble(totalRecharge) : 0;
    }

    /**
     * 币商充值换算成美金，与数数对齐
     *
     * @return
     */
    @Cacheable(value = "str:user_partner_recharge:uid:", key = "#uid+':startTime:'+#startTime+':endTime:'+#endTime")
    public double selectPartnerUsdPriceByTimeFromCache(String uid, long startTime, long endTime) {
        long gold = slaveMapper.queryRechargeGoldByTime(uid, ActType.COIN_DEALERS_RECHARGE, startTime, endTime);
        return (double) gold / 135;
    }

    /**
     * 获取用户最近充值记录
     */
    public LogPayRecordData getUserLatelyRechargeData(String uid) {
        return slaveMapper.selectUserLatelyRechargeData(uid);
    }


    /**
     * 获取startTime到目前的订单list
     *
     * @param uid       用户id
     * @param startTime 开始时间，单位 秒
     * @param payStatus 支付状态
     * @return
     */
    public List<LogPayRecordData> selectSuccessOrders(String uid, long startTime, int payStatus) {
        Example example = TKUtils.creatExample(LogPayRecordData.class);
        example.createCriteria().andEqualTo("uid", uid).andGreaterThanOrEqualTo("ctime", startTime)
                .andEqualTo("status", payStatus);
        return mapper.selectByExample(example);
    }

    /**
     * 查询累积充值花费的美元，不包含币商
     *
     * @param uid
     * @param startTime
     * @param payStatus 支付状态，不需要填写
     * @return
     */
    public Double selectSumUsdPrice(String uid, long startTime, int payStatus) {
        return slaveMapper.selectSumUsdPrice(uid, startTime);
    }

    /**
     * 查询累积充值花费的美元，不包含币商 筛选渠道
     *
     * @param uid
     * @param startTime
     * @param payStatus 支付状态
     * @return
     */
    public Double selectSumUsdPriceByChannel(String uid, long startTime, int payStatus, List<String> channelList) {
        return slaveMapper.selectSumUsdPriceByChannel(uid, startTime, payStatus, channelList);
    }

    /**
     * 币商充值换算成美金，与数数对齐
     *
     * @return
     */
    public double selectPartnerUsdPrice(String uid) {
        long gold = slaveMapper.queryRechargeGold(uid, ActType.COIN_DEALERS_RECHARGE);
        return (double) gold / USD_TO_GOLD;
    }

    /**
     * 查看是否有购买某种商品的记录
     *
     * @param uid
     * @return
     */
    public LogPayRecordData getHadPayMoney5evaRecord(String uid) {
        return slaveMapper.selectHadPayMoney5evaRecord(uid);
    }

    /**
     * 获取所有的充值金币
     * 2023-09-19 08开始算
     */
    @Cacheable(value = "str:recharge:total_gold#3600", key = "#uid")
    public long getTotalMedalRechargeGold(String uid) {
        return slaveMapper.queryTotalRechargeGold(uid);
    }

    /**
     * 删除 medal totalGold的缓存
     *
     * @param uid
     */
    @CacheEvict(value = "str:recharge:total_gold#3600", key = "#uid")
    public void deleteTotalMedalRechargeGold(String uid) {
        // do nothing
    }

    @Cacheable(value = "str:recharge:coin_count", key = "#bo.fillRedisKey()")
    public String queryPayCoinCountBy(QueryPayCoinBO bo) {
        Long count = slaveMapper.queryPayCoinCountBy(bo);
        if (count == null) {
            return "0";
        }
        return count.toString();
    }

    @Cacheable(value = "str:recharge:usd_price_sum", key = "#bo.fillRedisKey()")
    public String queryUsdPriceSumBy(QueryPayCoinBO bo) {
        String sum = slaveMapper.queryUsdPriceSumBy(bo);
        if (StringUtils.isEmpty(sum)) {
            sum = "0";
        }
        return sum;
    }

    public String queryUsdPriceSum(String uid, Set<Integer> typeSet, Set<Integer> statusSet, Long startTime, Long endTime) {
        return slaveMapper.queryUsdPriceSum(uid, typeSet, statusSet, startTime, endTime);
    }

    public String queryChannelPayUsdPriceByDuration(String channel, long startTime, long endTime) {
        return slaveMapper.queryChannelPayUsdPriceByDuration(channel, startTime, endTime);
    }

    public int getUserCountByChannelAndDuration(String channel, long startTime, long endTime) {
        Integer count = slaveMapper.queryUserCountByChannelAndDuration(channel, startTime, endTime);
        return count == null ? 0 : count;
    }

    public List<LogPayRecordData> getTotalRechargeByChannelListAndTime(List<String> channelList, long startTime, long endTime) {
        return slaveMapper.selectTotalRechargeByChannelListAndTime(channelList, startTime, endTime);
    }

    //    @Cacheable(value = "str:coin_seller:seller_gold", key = "#startTime+'_'+#endTime+'_'+#coinSellerIdSet")
    public List<LogPayRecordData> getCoinSellerGoldByCoinSellerIdSet(long startTime, long endTime, Set<Long> coinSellerIdSet) {
        return slaveMapper.selectCoinSellerGoldByCoinSellerIdSet(startTime, endTime, coinSellerIdSet);
    }

    //    @Cacheable(value = "str:coin_seller:recently_bug", key = "#startTime+'_'+#endTime+'_'+#uid+'_'+#coinSellerIdSet")
    public List<LogPayRecordData> getCoinSellerRecentlyBugCountByUid(long startTime, long endTime, String uid, Set<Long> coinSellerIdSet) {
        return slaveMapper.selectCoinSellerRecentlyBugCountByUid(startTime, endTime, uid, coinSellerIdSet);
    }

    public LogPayRecordData getRecentlyCoinSellerRecordData(String uid) {
        return slaveMapper.selectRecentlyCoinSellerRecordData(uid);
    }

    public double getUserSetExtendTimePaySum(Set<String> uidSet, long startTime, long endTime) {
        Double v = slaveMapper.selectUserSetExtendTimePaySum(uidSet, startTime, endTime);
        return v != null ? v : 0;
    }

    /**
     * 获取用户最近N天的累计充值美金
     *
     * @param uid  用户ID
     * @param days 最近天数
     * @return 累计充值美金金额
     */
    @Cacheable(value = "str:user_recent_recharge:uid_days", key = "#uid")
    public double getUserRecentRechargeUsd(String uid, int days) {
        // 根据当前时间计算N天前的时间戳
        DateHelper dateHelper = DateHelper.UTC;
        long startTime = dateHelper.getDayOffset(DateHelper.getCurrentTime(), -days);
        // 调用现有方法查询累计充值金额
        Double usdPrice = slaveMapper.selectSumUsdPrice(uid, startTime); // 1表示支付成功状态
        return usdPrice != null ? usdPrice : 0.0;
    }

    /**
     * 清除用户最近充值的缓存
     * 直接使用Redis操作，避免使用@CacheEvict的通配符问题
     *
     * @param uid 用户ID
     */
    public void clearUserRecentRechargeCache(String uid) {
        String key = "str:user_recent_recharge:uid_days::" + uid;
        stringRedisTemplate.delete(key);
    }

    /**
     * 获取用户最后成功充值的时间
     */
    @Cacheable(value = "str:last_recharge_success_time#172800", key = "#uid")
    public long getLastRechargeSuccessTime(String uid) {
        LogPayRecordData logPayRecordData = slaveMapper.queryLastPayTime(uid);
        Integer lastPayTime = logPayRecordData.getMtime();
        return lastPayTime == null ? 0 : lastPayTime;
    }

    /**
     * 更新用户最后成功充值的时间
     * 应该在充值成功的地方进行调用，更新缓存
     */
    @CacheEvict(value = "str:last_recharge_success_time#172800", key = "#uid")
    public void updateLastRechargeSuccessTime(String uid) {
    }
}
