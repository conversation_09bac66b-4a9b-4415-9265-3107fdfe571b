package com.quhong.dao;

import com.quhong.core.cache.CacheMap;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.constant.RedisCacheManagerConstant;
import com.quhong.core.constant.WarnName;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.datas.RoomContributionRecordData;
import com.quhong.dao.datas.RoomData;
import com.quhong.dao.mapper.log.RoomContributionRecordMapper;
import com.quhong.dao.slave.mapper.log.RoomContributionRecordSlaveMapper;
import com.quhong.data.room.RoomContributionData;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;


@Component
@Lazy
public class RoomContributionRecordDao extends MonthShardingDao<RoomContributionRecordMapper> {

    public static final Logger logger = LoggerFactory.getLogger(RoomContributionRecordDao.class);
    public static final String TABLE_PRE = "room_contribution_record";

    private static final int ROOM_MAX_DURATION_SECONDS = 3 * 24 * 60 * 60;

    private static final int ROOM_REDIS_EXPIRE_SECONDS = 2 * 24 * 60 * 60;

    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate redisTemplate;
    @Resource
    private RoomContributionRecordSlaveMapper slaveMapper;
    @Resource
    private TestUserDao testUserDao;
    @Resource
    private RoomDao roomDao;

    public RoomContributionRecordDao() {
        super(TABLE_PRE);
    }

    @Resource
    private MonitorSender monitorSender;
    private CacheMap<String, Double> contributionCacheMap = new CacheMap<>(30 * 1000L);

    public void saveRoomContribution(RoomContributionRecordData data) {
        try {
            String suffix = DateHelper.UTC.getTableSuffix(DateHelper.formatDate(data.getCtime()));
            data.setSuffix(suffix);
            createTable(suffix);
            tableMapper.insertSelective(data);
        } catch (Exception e) {
            logger.info("save room contribution error. e={}{} data={}", e, e.getMessage(), data);
        }
    }

    /**
     * 获取时间段内房间总贡献值
     */
    public double getRoomContributionByChapterIdFromDB(String chapterId, long timeStart, long timeEnd, List<String> tableSuffixList) {
        if (StringUtils.isEmpty(chapterId)) {
            return 0;
        }
        for (String suffix : tableSuffixList) {
            createTable(suffix);
        }
        double value;
        RoomContributionData roomContributionData = tableMapper.getRoomContributionByChapterId(chapterId, timeStart, timeEnd, tableSuffixList);
        if (roomContributionData == null) {
            value = 0;
        } else {
            value = roomContributionData.getTotalContributionValue();
        }
        return value;
    }

    /**
     * 获取时间段内房间总贡献值-包括贡献类型
     */
    @Cacheable(value = "str:getRoomContributionByRoomIdAndTypeFromDB", key = "#roomId + '_' + #contributionType", cacheManager = RedisCacheManagerConstant.CAFFEINE_EXPIRE_1MIN_RANDOM_AFTER_WRITE_MANAGER)
    public double getRoomContributionByRoomIdAndTypeFromDB(String roomId, long timeStart, long timeEnd, int contributionType, List<String> tableSuffixList) {
        for (String suffix : tableSuffixList) {
            createTable(suffix);
        }
        double value;
        RoomContributionData roomContributionData = slaveMapper.getRoomContributionByRoomIdAndType(roomId, timeStart, timeEnd, contributionType, tableSuffixList);
        if (roomContributionData == null) {
            value = 0;
        } else {
            value = roomContributionData.getTotalContributionValue();
        }
        return value;
    }

    /**
     * 获取时间段内房间总贡献值
     */
    @Cacheable(value = "str:getPartyRoomContributionByRoomIdFromDB", key = "#roomId + '_' + #timeStart", cacheManager = RedisCacheManagerConstant.CAFFEINE_EXPIRE_1MIN_RANDOM_AFTER_WRITE_MANAGER)
    public double getPartyRoomContributionByRoomIdFromDB(String roomId, long timeStart, long timeEnd, List<String> tableSuffixList) {
        if (StringUtils.isEmpty(roomId)) {
            return 0;
        }
        for (String suffix : tableSuffixList) {
            createTable(suffix);
        }
        double value;
        RoomContributionData roomContributionData = slaveMapper.getPartyRoomContributionByRoomId(roomId, timeStart, timeEnd, tableSuffixList);
        if (roomContributionData == null) {
            value = 0;
        } else {
            value = roomContributionData.getTotalContributionValue();
        }
        return value;
    }

    /**
     * 获取房间总贡献值
     */
    public double getContributionByChapterIdFromDB(String chapterId) {
        long timeStart = new ObjectId().getTimestamp();
        long timeEnd = timeStart + ROOM_MAX_DURATION_SECONDS; // 获得当前时间戳/单位（秒）
        List<String> tableSuffixList = DateHelper.UTC.getTableSuffixByTime(timeStart * 1000L, timeEnd * 1000L);
        return getRoomContributionByChapterIdFromDB(chapterId, timeStart, timeEnd, tableSuffixList);
    }

    /**
     * 获取时间段内房间总收益
     */
    public double getRoomGainsByChapterIdFromDB(String chapterId, long timeStart, long timeEnd, List<String> tableSuffixList) {
        for (String suffix : tableSuffixList) {
            createTable(suffix);
        }
        double value;
//        logger.info("getRoomGainsByChapterIdFromDB chapterId={} timeStart={} timeEnd={} tableSuffixList={}", chapterId, timeStart, timeEnd, tableSuffixList);
        RoomContributionData roomContributionData = tableMapper.getRoomContributionByChapterId(chapterId, timeStart, timeEnd, tableSuffixList);
        if (roomContributionData == null) {
            value = 0;
        } else {
            value = roomContributionData.getTotalGainsValue();
        }
        return value;
    }

    /**
     * 获取房间总收益
     */
    public double getRoomGainsByChapterIdFromDB(String chapterId) {
        long timeStart = new ObjectId().getTimestamp();
        long timeEnd = timeStart + ROOM_MAX_DURATION_SECONDS; // 获得当前时间戳/单位（秒）
        List<String> tableSuffixList = DateHelper.UTC.getTableSuffixByTime(timeStart * 1000L, timeEnd * 1000L);
        return getRoomGainsByChapterIdFromDB(chapterId, timeStart, timeEnd, tableSuffixList);
    }

    /**
     * 获取时间段内用户对房间该场直播的贡献值
     */
    public double getUserContributeRoomByUidChapterId(String uid, String chapterId, long timeStart, long timeEnd, List<String> tableSuffixList) {
        for (String suffix : tableSuffixList) {
            createTable(suffix);
        }
        for (String suffix : tableSuffixList) {
            createTable(suffix);
        }
        Double value = tableMapper.getUserContributeRoomByUidChapterId(uid, chapterId, timeStart, timeEnd, tableSuffixList);
        ;
        if (value == null) {
            value = 0.0;
        }
        return value;
    }

    /**
     * 获取用户对房间该场直播的贡献值
     */
    public double getUserContributeRoomByUidChapterId(String uid, String chapterId, long timeStart) {
        long timeEnd = timeStart + ROOM_MAX_DURATION_SECONDS; // 获得当前时间戳/单位（秒）
        List<String> tableSuffixList = DateHelper.UTC.getTableSuffixByTime(timeStart * 1000L, timeEnd * 1000L);
        return getUserContributeRoomByUidChapterId(uid, chapterId, timeStart, timeEnd, tableSuffixList);
    }

    /**
     * 从redis获取房间的场次总收益情况
     */
    public double getGainsByChapterId(String roomId, String chapterId) {
        try {
            String key = getRoomGainsZsetKey(roomId, chapterId);
            Double valueDouble = redisTemplate.opsForZSet().score(key, chapterId);
            double value = 0;
            if (valueDouble == null) {
                value = getRoomGainsByChapterIdFromDB(chapterId);
                redisTemplate.opsForZSet().add(key, chapterId, value);
                redisTemplate.expire(key, ROOM_REDIS_EXPIRE_SECONDS, TimeUnit.SECONDS);
            } else {
                value = valueDouble;
            }
            return value;
        } catch (Exception e) {
            logger.error("getRoomTotalRoomContributionByChapterId. uid={} {}", chapterId, e.getMessage(), e);
            String detail = "roomId=" + roomId + ", chapterId=" + chapterId;
            monitorSender.info(0, "获取房间的场次总收益情况", detail);
        }
        return 0;
    }

    /**
     * 从redis获取房间的场次总贡献值情况
     */
    public double getContributionByChapterId(String roomId, String chapterId) {
        try {
            String key = getRoomContributionZsetKey(roomId, chapterId);
            Double valueDouble = redisTemplate.opsForZSet().score(key, chapterId);
            double value = 0;
            if (valueDouble == null) {
                value = getContributionByChapterIdFromDB(chapterId);
                redisTemplate.opsForZSet().add(key, chapterId, value);
                redisTemplate.expire(key, ROOM_REDIS_EXPIRE_SECONDS, TimeUnit.SECONDS);
            } else {
                value = valueDouble;
            }
            return value;
        } catch (Exception e) {
            logger.error("getRoomTotalRoomContributionByChapterId. uid={} {}", chapterId, e.getMessage(), e);
            String detail = "roomId=" + roomId + ", chapterId=" + chapterId;
            monitorSender.info(WarnName.COMMON, "获取房间的场次总贡献值情况", detail);
        }
        return 0;
    }

    public double getContributionFromCacheByUid(String uid, String roomId, String chapterId) {
        String key = roomId + "_" + uid + "_" + chapterId;
        Double value = contributionCacheMap.getData(key);
        if (value == null) {
            value = getContributionByUid(uid, roomId, chapterId);
            contributionCacheMap.cacheData(key, value);
        }
        return value;
    }

    private String getContributionCacheKey(String uid, String roomId, String chapterId) {
        return roomId + "_" + uid + "_" + chapterId;
    }

    /**
     * 从redis获取用户对房间的该场次贡献值情况
     */
    public double getContributionByUid(String uid, String roomId, String chapterId) {
        try {
            String key = getRoomContributionZsetKey(roomId, chapterId);
            Double valueDouble = redisTemplate.opsForZSet().score(key, uid);
            double value;
            if (valueDouble == null) {
                long timeStart;
                if (!StringUtils.isEmpty(chapterId)) {
                    timeStart = new ObjectId(chapterId).getTimestamp();
                } else {
                    RoomData roomData = roomDao.getRoomData(roomId);
                    timeStart = roomData.getChapterStartTime();
                }
                if (timeStart < 1704038400) {
                    logger.info("get contributionByUid. roomId={} chapterId={} uid={} timeStart={}", roomId, chapterId, uid, timeStart);
                }
                value = getUserContributeRoomByUidChapterId(uid, chapterId, timeStart);
                redisTemplate.opsForZSet().add(key, uid, value);
                redisTemplate.expire(key, ROOM_REDIS_EXPIRE_SECONDS, TimeUnit.SECONDS);
            } else {
                value = valueDouble;
            }
            return value;
        } catch (Exception e) {
            logger.error("getUserTotalRoomContributionByUid. uid={} {}", uid, e.getMessage(), e);
            String detail = "uid=" + uid + ", roomId=" + roomId + ", chapterId=" + chapterId;
            monitorSender.info(WarnName.COMMON, "获取用户对房间的该场次贡献值情况", detail);
        }
        return 0;
    }

    /**
     * get contribution ranks
     *
     * @param roomId
     * @param chapterId
     * @param start
     * @param pageSize
     * @return
     */
    public Set<String> getContributionRank(String roomId, String chapterId, int start, int pageSize) {
        String key = getRoomContributionZsetKey(roomId, chapterId);
        Set<String> set = redisTemplate.opsForZSet().reverseRangeByScore(key, 1, Integer.MAX_VALUE, start, pageSize);
        if (set == null) {
            return new HashSet<>();
        }
        set.remove(chapterId);
        return set;
    }

    /**
     * 房间场次贡献详情数据的缓存的key
     */
    private String getRoomContributionZsetKey(String roomId, String chapterId) {
        return "zset:room:contribution:" + roomId + ":" + chapterId;
    }

    /**
     * 获取房间内用户金币收入的缓存的key
     */
    private String getRoomGainsZsetKey(String roomId, String chapterId) {
        return "zset:room:gains:" + roomId + ":" + chapterId;
    }

    public Double getSelfCharmRankFromDB(int startTime, int endTime, String roomId, Set<String> testUidSet) {
        List<String> suffixList = DateHelper.UTC.getTableSuffixByTime(startTime * 1000L, endTime * 1000L);
        suffixList = checkSuffix(suffixList);
        if (CollectionUtils.isEmpty(suffixList)) {
            return 0.0;
        }
        return slaveMapper.selectSelfCharmRank(suffixList, startTime, endTime, roomId, testUidSet);
    }

    private List<String> checkSuffix(List<String> suffixList) {
        List<String> suffixes = new ArrayList<>();
        suffixList.forEach(suffix -> {
            if (checkExist(suffix)) {
                suffixes.add(suffix);
            } else {
                logger.error("ActorCheckInLog is not exist this table,suffix={}", suffix);
            }
        });
        return suffixes;
    }

    public double getChapterGain(String roomId, String chapterId, Long timeStart, Long timeEnd) {
        List<String> suffixList = DateHelper.UTC.getTableSuffixByTime(timeStart * 1000L, timeEnd * 1000L);
        suffixList = checkSuffix(suffixList);
        if (CollectionUtils.isEmpty(suffixList)) {
            return 0;
        }
        logger.info("get chapter gain roomId={} chapterId={} timeStart={} timeEnd={} suffixList={}", roomId, chapterId, timeStart, timeEnd, suffixList);
        Double gain = slaveMapper.selectChapterGain(roomId, chapterId, timeStart, timeEnd, suffixList);
        return gain != null ? gain : 0.0;
    }

    @Cacheable(value = "str:getRoomContributionRankTop3ByTime", key = "#roomId + '_' + #startTime + '_' + #endTime", cacheManager = RedisCacheManagerConstant.CAFFEINE_EXPIRE_5MIN_AFTER_WRITE_MANAGER)
    public List<RoomContributionRecordData> getRoomContributionRankTop3ByTime(String roomId, long startTime, long endTime) {
        List<String> suffixList = DateHelper.UTC.getTableSuffixByTime(startTime * 1000L, endTime * 1000L);
        suffixList = checkSuffix(suffixList);
        if (CollectionUtils.isEmpty(suffixList)) {
            return new ArrayList<>();
        }
        return slaveMapper.selectRoomContributionRankTop3ByTime(roomId, startTime, endTime, suffixList);
    }

    public double getRoomSumContributionByTime(String roomId, long startTime, long endTime) {
        List<String> suffixList = DateHelper.UTC.getTableSuffixByTime(startTime * 1000L, endTime * 1000L);
        suffixList = checkSuffix(suffixList);
        if (CollectionUtils.isEmpty(suffixList)) {
            return 0;
        }
        Double totalContribution = slaveMapper.selectRoomSumContributionByTime(roomId, startTime, endTime, suffixList);
        return totalContribution != null ? totalContribution : 0;
    }

    public Double getUserSumContributionByTime(String uid, String roomId, long startTime, long endTime) {
        List<String> suffixList = DateHelper.UTC.getTableSuffixByTime(startTime * 1000L, endTime * 1000L);
        suffixList = checkSuffix(suffixList);
        if (CollectionUtils.isEmpty(suffixList)) {
            return 0.0;
        }
        Double userSumContribution = slaveMapper.selectUserSumContributionByTime(uid, roomId, startTime, endTime, suffixList);
        return userSumContribution != null ? userSumContribution : 0;
    }

    public RoomContributionRecordData getMvpContributionByTime(String roomId, long startTime, long endTime) {
        List<String> suffixList = DateHelper.UTC.getTableSuffixByTime(startTime * 1000L, endTime * 1000L);
        suffixList = checkSuffix(suffixList);
        if (CollectionUtils.isEmpty(suffixList)) {
            return null;
        }
        return slaveMapper.selectMvpContributionByTime(roomId, startTime, endTime, suffixList);
    }

    public double getMicContributionValue(String uid, String roomId, String chapterId, long startTime, long endTime) {
        List<String> suffixList = DateHelper.UTC.getTableSuffixByTime(startTime * 1000L, endTime * 1000L);
        suffixList = checkSuffix(suffixList);
        if (CollectionUtils.isEmpty(suffixList)) {
            return 0;
        }
        Double value = slaveMapper.selectMicContributionValue(uid, roomId, chapterId, startTime, endTime, suffixList);
        return value  != null ? value : 0;
    }
}
