package com.quhong.data.dto;

import com.quhong.common.data.BaseHttpData;
import com.quhong.common.enums.HttpCode;
import com.quhong.exceptions.WebException;
import com.quhong.utils.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023/7/31 14:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class CommonDTO extends BaseHttpData {
    /**
     * 用户uid
     */
    private String uid;
    /**
     * 活动类型
     */
    private Integer eventType;

    public void checkParams() {
        if (StringUtils.isEmpty(this.uid)) {
            throw new WebException(this, HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "uid is empty"));
        }
        if (eventType == null) {
            throw new WebException(this, HttpCode.createHttpCode(HttpCode.PARAM_ERROR, false, "eventType is empty"));
        }
    }

}
