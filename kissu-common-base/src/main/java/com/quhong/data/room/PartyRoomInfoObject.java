package com.quhong.data.room;

import com.quhong.core.utils.SpringUtils;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RoomData;
import com.quhong.data.game.GameInfoObject;
import com.quhong.server.protobuf.RoomListProtobuf;
import com.quhong.socket.msg.IProto;
import lombok.Data;

import java.util.List;

/**
 * @ClassName PartyRoomInfoObject
 * <AUTHOR>
 * @date 2022/12/13 15:11
 */
@Data
public class PartyRoomInfoObject implements IProto<RoomListProtobuf.PartyRoomInfo> {
    private String name = "";
    private String head = "";
    private String country = "";
    private String countryCode = "";
    private int heatValue;
    private String roomId = "";
    private String ownerUid = "";
    private int roomType;
    private GameInfoObject gameInfo;
    private List<MicroActorInfoObject> microActorInfo;
    private int userType;
    private int actorCount;
    private RoomData roomData;
    private ActorData roomOwnInfo;
    private String roomTag;
    private boolean gameInviteWhite;
    private int top;
    private String ownerLanguage;
    private String ownerName;
    private String gameShowIcon;

    //中间生成
    private int micCount;
    // 庆典标签 url
    private String galaRoomTag;

    private int redEnvelopeValue; // 红包值 0-没有 1-有

    @Override
    public void doFromBody(RoomListProtobuf.PartyRoomInfo proto) {

    }

    @Override
    public RoomListProtobuf.PartyRoomInfo.Builder doToBody() {
        RoomListProtobuf.PartyRoomInfo.Builder builder = RoomListProtobuf.PartyRoomInfo.newBuilder();
        builder.setName(this.name != null ? this.name : "");
        builder.setHead(this.head != null ? this.head : "");
        builder.setCountry(this.country != null ? this.countryCode : "");
        builder.setCountryCode(this.countryCode != null ? this.countryCode : "");
        builder.setHeatValue(this.heatValue);
        builder.setRoomId(this.roomId != null ? this.roomId : "");
        builder.setOwnerUid(this.ownerUid != null ? this.ownerUid : "");
        builder.setRoomType(this.roomType);
        if (this.gameInfo != null) {
            builder.setGameInfo(this.gameInfo.doToBody());
        }
        if (this.microActorInfo != null) {
            for (MicroActorInfoObject microActorInfoObject : this.microActorInfo) {
                builder.addMicroActorInfo(microActorInfoObject.doToBody());
            }
        }
        builder.setActorCount(this.actorCount);
        builder.setRoomTag(this.roomTag != null ? this.roomTag : "");
        builder.setOwnName(this.ownerName != null ? this.ownerName : "");
        builder.setGalaRoomTag(this.galaRoomTag != null ? this.galaRoomTag : "");
        builder.setRedEnvelopeValue(this.redEnvelopeValue);
        builder.setGameShowIcon(this.gameShowIcon != null ? this.gameShowIcon : "");
        return builder;
    }

    public PartyRoomInfoObject copyTo() {
        PartyRoomInfoObject to = new PartyRoomInfoObject();
        SpringUtils.copyPropertiesIgnoreNull(this, to);
        to.setGameInfo(this.gameInfo);
        to.setMicroActorInfo(this.microActorInfo);
        to.setRoomData(this.roomData);
        to.setRoomOwnInfo(this.roomOwnInfo);
        to.setGalaRoomTag(this.galaRoomTag);
        to.setRedEnvelopeValue(this.redEnvelopeValue);
        to.setGameShowIcon(this.gameShowIcon);
        return to;
    }
}
