package com.quhong.data.room;

import com.quhong.core.utils.SpringUtils;
import com.quhong.data.game.GameInfoObject;
import com.quhong.server.protobuf.RoomListProtobuf;
import com.quhong.socket.msg.IProto;
import lombok.Data;

@Data
public class LiveRoomInfoObject implements IProto<RoomListProtobuf.LiveRoomInfo> {
    private String name;
    private String channel;
    private String head;
    private int age;
    private int gender;
    private String country;
    private String countryCode;
    private int heatValue;
    private String roomId;
    private String ownerUid;
    private int userType;   // 用户类型
    private int roomType;
    private GameInfoObject GameInfo;
    private int hostLevel;
    private int isInMic;//是否在连麦中 0没有 1有
    private int isInLiveMulMic;//是否多人连麦中 0-不是 1-是
    private int hostGrade;
    private int isInPK;//是否在pk中 0不是 1是
    private String streamRoomId;//流房间id
    private int streamType;//流类型 0-即构 1-声网 2-trtc
    private String streamToken;//第三方进房间token
    private int rid;//房主rid
    private int sLabel;//是否s主播标签 0不是 1是
    private int cLabel;//是否c主播标签 0不是 1是
    private int tLabel;//是否t主播标签 0不是 1是
    private String title;//房间标题
    private int isOfficialRoom;//是否为官方直播间
    private int hadSayHi;//是否有sayHi记录 0没有 1有

    //中间生成
    private int isGame;//是否游戏房
    private int top;//是否置顶
    private String ownerChannel;//房主渠道
    private long authTime;//认证时间

    //中间生成
    private long lastReceiveGiftValue;//是否游戏房
    private String realLastReceiveGiftValue;
    private String ownerLanguage;//房主语言
    private int sex;//
    private int redEnvelopeValue; // 红包值 0-没有 1-有

    @Override
    public void doFromBody(RoomListProtobuf.LiveRoomInfo proto) {

    }

    @Override
    public RoomListProtobuf.LiveRoomInfo.Builder doToBody() {
        RoomListProtobuf.LiveRoomInfo.Builder builder = RoomListProtobuf.LiveRoomInfo.newBuilder();
        builder.setName(name == null ? "" : name);
        builder.setHead(head == null ? "" : head);
        builder.setAge(this.age);
        builder.setCountry(countryCode == null ? "" : countryCode);
        builder.setCountryCode(countryCode == null ? "" : countryCode);
        builder.setHeatValue(this.heatValue);
        builder.setRoomId(roomId == null ? "" : roomId);
        builder.setOwnerUid(ownerUid == null ? "" : ownerUid);
        builder.setRoomType(roomType);
        builder.setHostLevel(hostLevel);
        builder.setHostGrade(hostGrade);
        if (this.GameInfo != null) {
            builder.setGameInfo(this.GameInfo.doToBody());
        }
        builder.setIsInMic(this.isInMic);
        builder.setIsInPK(this.isInPK);
        builder.setStreamRoomId(this.streamRoomId != null ? this.streamRoomId : "");
        builder.setStreamType(this.streamType);
        builder.setStreamToken(this.streamToken != null ? this.streamToken : "");
        builder.setRid(this.rid);
        builder.setSLabel(this.sLabel);
        builder.setCLabel(this.cLabel);
        builder.setTLabel(this.tLabel);
        builder.setTitle(this.title != null ? this.title : "");
        builder.setIsOfficialRoom(this.isOfficialRoom);
        builder.setHadSayHi(this.hadSayHi);
        builder.setRedEnvelopeValue(this.redEnvelopeValue);
        builder.setIsInLiveMulMic(this.isInLiveMulMic);
        return builder;
    }

    public LiveRoomInfoObject copyTo() {
        LiveRoomInfoObject to = new LiveRoomInfoObject();
        SpringUtils.copyPropertiesIgnoreNull(this, to);
        to.setGameInfo(this.getGameInfo());
        return to;
    }

}
