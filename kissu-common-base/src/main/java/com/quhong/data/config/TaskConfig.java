package com.quhong.data.config;

import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获得门票任务配置
 *
 * <AUTHOR>
 * @since 2023/7/28 17:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TaskConfig {
    /**
     * 任务类型 1送礼 2首充 3yxsk游戏金币消耗x金币 4直播间停留超x秒
     *
     * @see TaskTypeConstant 任务类型
     */
    protected Integer taskType;
    /**
     * 判定值 如礼物id, 消耗金币数, 停留时长秒数
     */
    protected Integer checkParams;
    /**
     * 可获得门票数量
     */
    protected Integer ticket;
    /**
     * 每日限制获取次数, -1无限制
     */
    protected Integer limit;


    //    /**
//     * 首充任务
//     */
//    public static final TaskConfig FIRST_RECHARGE_TASK = new TaskConfig(TaskTypeConstant.FIRST_RECHARGE, 0, 20, -1);
//    /**
//     * 雅讯时空系列游戏任务
//     */
//    public static final TaskConfig PLAY_YXSK_GAME_TASK = new TaskConfig(TaskTypeConstant.PLAY_YXSK_GAME, 200, 4, 10);
//    /**
//     * 房间停留任务
//     */
//    public static final TaskConfig ROOM_STAY_TIME_TASK = new TaskConfig(TaskTypeConstant.ROOM_STAY_TIME, 120, 2, 1);
//
//    /**
//     * 门票图标地址
//     */
//    public static final String TICKET_ICON_URL = "https://statics.kissu.mobi/icon/independence/new/ticket_v4.png";
//    /**
//     * 活动地址
//     */
//    public static final String EVENT_URL = "https://videochat.kissu.site/independence/";
//
//    public static final String TEST_EVENT_URL = "https://testvideochat.kissu.site/independence/";
//
//    /**
//     * 飘屏背景图
//     */
//    public static final String FLOATING_SCREEN_BACKGROUND_IMG = "https://statics.kissu.mobi/icon/independence/new/floating_v4.png";
//
//    /**
//     * 任务配置
//     */
//    public static final List<TaskConfig> TASK_CONFIGS = new ArrayList<TaskConfig>() {{
//        add(new TaskConfig(TaskTypeConstant.SEND_GIFT, 515, 5, -1));
//        add(new TaskConfig(TaskTypeConstant.SEND_GIFT, 514, 20, -1));
////        add(new TaskConfig(TaskTypeConstant.FIRST_RECHARGE, 0, 20, -1));
////        add(new TaskConfig(TaskTypeConstant.PLAY_YXSK_GAME, 200, 4, 10));
////        add(new TaskConfig(TaskTypeConstant.ROOM_STAY_TIME, 120, 2, 1));
//    }};
}
