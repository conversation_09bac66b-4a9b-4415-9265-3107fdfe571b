package com.quhong.data.config;

import com.quhong.dao.datas.RewardInfoData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/28 13:54
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class RewardTaskConfig {
    private String taskName;
    /**
     * 任务类型
     */
    private Integer taskType;
    /**
     * 判定值 礼物id, 消耗金币数, 停留时长秒数
     */
    private Integer checkParams;
    /**
     * 限制（可不用）
     */
    private Integer limit;
    /**
     * 奖励key
     */
    private String awardsKey;
    /**
     * 奖励
     */
    private List<RewardInfoData> rewards;

    private String notice;
    /**
     * 达到limit 时，使用的notice
     */
    private String notice1;

    public RewardTaskConfig(Integer taskType, Integer checkParams, Integer limit, List<RewardInfoData> rewards) {
        this.taskType = taskType;
        this.checkParams = checkParams;
        this.limit = limit;
        this.rewards = rewards;
    }
}
