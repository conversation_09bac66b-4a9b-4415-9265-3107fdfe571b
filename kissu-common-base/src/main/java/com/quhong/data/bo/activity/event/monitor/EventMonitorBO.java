package com.quhong.data.bo.activity.event.monitor;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 监控配置信息
 * <AUTHOR>
 * @since 2025/4/28 11:27
 */
@Data
@Accessors(chain = true)
public class EventMonitorBO {
    /**
     * 监控周期类型
     * @see com.quhong.constant.event.monitor.MonitorCycleType
     */
    private Integer cycleType;
    /**
     * 监控级别 1重要  2 一般
     * @see com.quhong.constant.event.monitor.MonitorLevelConstant
     */
    private Integer level;
    /**
     * 最大失败率
     * （万分率%%）： eg.  500代表5%，失败率>=5%时进行告警
     *  配置0含义：不监控失败率
     */
    private Integer maxFailRate;
    /**
     * 最大执行时长 单位（秒 s）
     * eg. 300 代表300s=5分钟，执行时间>=300s时告警
     * 配置0含义：不监控执行时长
     */
    private Integer maxExecDuration;
    /**
     * 监控描述
     */
    private String monitorDesc;
    /**
     * 监控周期 单位（秒 s）
     * eg. 每300s为一周期
     */
    private Long monitorDuration;
    /**
     * 监控开始时间
     */
    private Long monitorStartTime;
    /**
     * 监控结束时间
     */
    private Long monitorEndTime;

    /**
     * 是否需要检查排行榜空置率
     */
    private Boolean needCheckRankEmpty;
    /**
     * 最大排行榜空置率
     * （万分率%%）  10000 代表 空置率>=100%时告警
     */
    private Long maxRankEmptyRate;
    /**
     * 最小执行次数
     */
    private Long minExecTimes;


    public EventMonitorBO() {
        cycleType = 0;
        level = 2;
        maxFailRate = 0;
        maxExecDuration = 0;
        monitorDesc = "";
        monitorDuration = 0L;
        monitorStartTime = 0L;
        monitorEndTime = 0L;
        needCheckRankEmpty = false;
        maxRankEmptyRate = 0L;
        minExecTimes = 0L;
    }
}
