package com.quhong.data;

import com.quhong.server.protobuf.BaseProtobuf;
import com.quhong.server.protobuf.RoomListProtobuf;
import com.quhong.socket.msg.IProto;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @ClassName ZoneTabConfigData
 * <AUTHOR>
 * @date 2024/9/10 10:39
 */
@Data
public class ZoneTabConfigData implements IProto<BaseProtobuf.ZoneTabConfigData> {
    private List<TabConfigData> zoneList;
    private String zoneObjectId;

    private int relateZone;

    @Override
    public void doFromBody(BaseProtobuf.ZoneTabConfigData proto) {

    }

    @Override
    public BaseProtobuf.ZoneTabConfigData.Builder doToBody() {
        BaseProtobuf.ZoneTabConfigData.Builder builder = BaseProtobuf.ZoneTabConfigData.newBuilder();
        if (!CollectionUtils.isEmpty(this.zoneList)) {
            for (TabConfigData tabConfigData : this.zoneList) {
                builder.addZoneList(tabConfigData.doToBody());
            }
        }
        builder.setZoneObjectId(this.zoneObjectId != null ? this.zoneObjectId : "");
        return builder;
    }
}
