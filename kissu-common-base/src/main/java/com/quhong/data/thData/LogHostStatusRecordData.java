package com.quhong.data.thData;

import com.quhong.dao.datas.HostStatusRecordLogData;
import com.quhong.dto.EventDTO;
import lombok.Data;


/**
 * @ClassName LogHostStatusRecordData
 * @Description TODO
 * <AUTHOR>
 * @Date 10:17 AM 2022/6/2
 * @Version 1.0
 **/
@Data
public class LogHostStatusRecordData extends EventDTO {
    /**
     * 主播试用考核状态
     */

    public LogHostStatusRecordData() {
        this.setEventName("host_status_record_log");
    }

    public void copyFrom(HostStatusRecordLogData data) {
        this.setUid(data.getUid());
        this.addProperties("uid", data.getUid());
        this.addProperties("host_status_type", data.getStatusType());
        this.addProperties("alter_time", data.getAlterTime());
        this.addProperties("ctime", data.getCtime());
        this.addProperties("host_examine_score", data.getHostExamineScore());
    }
}
