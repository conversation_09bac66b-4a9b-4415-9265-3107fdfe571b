package com.quhong.data.thData;

import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.constant.activity.model.event.mode.TaskTypeConstant;
import com.quhong.dto.EventDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023/10/25 11:07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LogEventCurrencyAddData extends EventDTO {

    public LogEventCurrencyAddData() {
        this.setEventName("event_currency_add_log");
    }

    private String uid;
    /**
     * 活动id
     */
    @JSONField(name = "event_code")
    private Integer eventCode;
    /**
     * 活动名
     */
    @JSONField(name = "activity_name")
    private String activityName;
    /**
     * 任务类型
     * @see TaskTypeConstant 任务类型
     */
    @JSONField(name = "event_task_type")
    private Integer eventTaskType;
    /**
     * 任务判定条件
     */
    @JSONField(name = "event_task_params")
    private Integer eventTaskParams;
    /**
     * 活动货币获取数量
     */
    @JSONField(name = "event_currency_changed")
    private Integer eventCurrencyChanged;

    private Long ctime;
}
