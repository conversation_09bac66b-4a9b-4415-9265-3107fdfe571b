package com.quhong.data.appConfig;

import lombok.Data;

/**
 * 复购和AB面逻辑配置VO
 * 用于接收复购和AB面逻辑配置数据
 * 
 * 配置格式示例:
 * {
 *     "repurchase": false,
 *     "abSide": false
 * }
 * <AUTHOR>
 */
@Data
public class RepurchaseAndAbLogicConfigVO {
    
    /**
     * 复购开关
     * true: 开启复购逻辑
     * false: 关闭复购逻辑
     */
    private Boolean repurchase;
    
    /**
     * AB面类型
     * true: 使用新逻辑
     * false: 使用旧逻辑
     */
    private Boolean abSide;
    
    /**
     * 获取复购开关，提供默认值
     * @return 复购开关，默认为false
     */
    public boolean getRepurchaseWithDefault() {
        return repurchase != null ? repurchase : false;
    }
    
    /**
     * 获取AB面类型，提供默认值
     * @return AB面类型，默认为false（旧逻辑）
     */
    public boolean getAbSideWithDefault() {
        return abSide != null ? abSide : false;
    }
}
