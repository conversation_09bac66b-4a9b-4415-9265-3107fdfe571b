package com.quhong.data.vo.model.rank.row;

import com.quhong.dao.datas.ActorData;
import lombok.Data;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2023/7/12 13:51
 */
@Data
public class RankRowVO {
    /**
     * 排名
     */
    private String rankNum;
    /**
     * 用户信息
     */
    private ActorInfo actorInfo;
    /**
     * 得分(可忽略)
     */
    private Long score;
    /**
     * 分数
     */
    private String realScore;
    /**
     * 可获得金币数
     */
    private Long canGet;

    public RankRowVO() {
        this.rankNum = "99+";
        this.score = 0L;
        this.canGet = 0L;
        this.actorInfo = new ActorInfo();
    }

    public RankRowVO(String rankNum) {
        this.rankNum = rankNum;
        this.score = 0L;
        this.canGet = 0L;
        this.actorInfo = new ActorInfo();
    }

    public RankRowVO(RankRowVO row) {
        this.rankNum = row.getRankNum();
        this.score = row.getScore();
        this.canGet = row.getCanGet();
        this.actorInfo = row.getActorInfo();
        this.realScore = row.getRealScore();
    }

    public RankRowVO(ActorData actorData) {
        if (ObjectUtils.isEmpty(actorData)) {
            this.actorInfo = new ActorInfo();
        } else {
            this.actorInfo = new ActorInfo(actorData);
        }

    }
}
