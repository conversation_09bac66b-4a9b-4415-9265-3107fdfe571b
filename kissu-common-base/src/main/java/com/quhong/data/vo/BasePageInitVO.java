package com.quhong.data.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 活动页面初始化VO
 */
@Data
@Accessors(chain = true)
public class BasePageInitVO {
    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 活动开始时间（秒）
     */
    private Long startSec;

    /**
     * 活动结束时间（秒）
     */
    private Long endSec;

    /**
     * 最近30条历史记录
     */
    private List<?> last30History;

    /**
     * 充值金币数
     */
    private Long rechargeCoins;

    /**
     * 活动链接
     */
    private String eventUrl;

    /**
     * 用户累计送礼个数
     */
    private Long sentGiftCount;
} 