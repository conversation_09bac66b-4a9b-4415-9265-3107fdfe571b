package com.quhong.service.event.model;

import com.quhong.constant.activity.model.join.mode.ConditionConstant;
import com.quhong.constant.activity.model.join.mode.ConditionModeConstant;
import com.quhong.constant.activity.model.join.mode.JoinModeConstant;
import com.quhong.constant.activity.model.join.mode.RelationConstant;
import com.quhong.core.enums.GenderTypeEnum;
import com.quhong.dao.MemberDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.app.config.activity.model.JoinModeCondition;
import com.quhong.dao.datas.app.config.activity.model.JoinModeInfo;
import com.quhong.dao.datas.app.config.activity.model.RelationInfo;
import com.quhong.players.ActorMgr;
import com.quhong.service.LordService;
import com.quhong.service.user.auth.level.LevelApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户群组校验服务
 *
 * <AUTHOR>
 * @since 2024/1/19 17:53
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EventUserGroupService {

    private final ActorMgr actorMgr;

    public boolean checkUserGroup(String uid, AppConfigActivityData configData) {
        ActorData currActor = actorMgr.getActorData(uid);
        if (currActor == null) {
            return false;
        }
        return checkUserGroup(currActor, configData);
    }

    /**
     * 验证是否是指定用户群体
     *
     * @param currActor  当前用户
     * @param configData 活动配置
     * @return true:是指定用户群体 false:不是指定用户群体
     */
    public boolean checkUserGroup(ActorData currActor, AppConfigActivityData configData) {
        if (!ObjectUtils.isEmpty(configData.getChannelSet()) && !configData.getChannelSet().contains(currActor.getChannel())) {
            return false;
        }
        if (!ObjectUtils.isEmpty(configData.getCountryCodeSet()) && !configData.getCountryCodeSet().contains(currActor.getCountryCode())) {
            return false;
        }
        if (!ObjectUtils.isEmpty(configData.getNotJoinRidSet()) && configData.getNotJoinRidSet().contains(currActor.getRid().intValue())) {
            return false;
        }
        return checkUserGroup(configData.getJoinModeInfo(), currActor);
    }

    /**
     * 验证是否是指定用户群体
     *
     * @param joinModeInfo 参与群体条件信息
     * @param currActor    当前用户
     * @return true命中 false未命中
     */

    public boolean checkUserGroup(JoinModeInfo joinModeInfo, ActorData currActor) {
        if (joinModeInfo == null) {//为空不进行判定
            return true;
        }
        boolean hit = false;
        switch (joinModeInfo.getMode()) {
            case JoinModeConstant.ALL_USER:
                hit = true;
                break;
            case JoinModeConstant.CONDITION_USER:
                hit = checkConditions(joinModeInfo, currActor);
                break;
            case JoinModeConstant.SIGN_UP_USER:
            default:
                break;
        }
        return hit;
    }

    private boolean checkConditions(JoinModeInfo joinModeInfo, ActorData currActor) {
        List<JoinModeCondition> conditions = joinModeInfo.getConditions();
        if (ObjectUtils.isEmpty(conditions)) {//条件为空不进行判定
            return true;
        }
        List<RelationInfo> relations = conditions.stream().map(condition -> checkCondition(condition, currActor))
                .collect(Collectors.toList());
        boolean hit = relations.stream()
                .filter(relation -> RelationConstant.DEFAULT.equals(relation.getRelation()))
                .map(RelationInfo::getHit)
                .findFirst().orElse(true);
        for (RelationInfo relation : relations) {
            hit = relateHit(relation, hit);
        }
        return hit;
    }

    private static boolean relateHit(RelationInfo relation, boolean hit) {
        switch (relation.getRelation()) {
            case RelationConstant.AND:
                hit = hit && relation.getHit();
                break;
            case RelationConstant.OR:
                hit = hit || relation.getHit();
                break;
            default:
                break;
        }
        return hit;
    }

    private RelationInfo checkCondition(JoinModeCondition condition, ActorData currActor) {
        RelationInfo relation = new RelationInfo().setRelation(condition.getRelation()).setHit(true);
        try {
            switch (condition.getMode()) {
                case ConditionModeConstant.REGISTER_TIME:
                    checkRegisterTime(condition, currActor, relation);
                    break;
                case ConditionModeConstant.GENDER:
                    checkGender(condition, currActor, relation);
                    break;
                case ConditionModeConstant.SEX:
                    checkSex(condition, currActor, relation);
                    break;
                case ConditionModeConstant.LORD:
                    checkLord(condition, currActor, relation);
                    break;
                case ConditionModeConstant.USER_LEVEL:
                    checkLevel(condition, currActor, relation, GenderTypeEnum.USER.getType());
                    break;
                case ConditionModeConstant.HOST_LEVEL:
                    checkLevel(condition, currActor, relation, GenderTypeEnum.HOST.getType());
                    break;
                case ConditionModeConstant.RID:
                    checkRid(condition, currActor, relation);
                    break;
                case ConditionModeConstant.IS_VIP:
                    checkIsVip(condition, currActor, relation);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("error to event model, checkCondition,msg={}", e.getMessage(), e);
        }
        return relation;
    }

    private final MemberDao memberDao;

    private void checkIsVip(JoinModeCondition condition, ActorData currActor, RelationInfo relation) {
        boolean isVip = memberDao.isMember(currActor.getUid());
        boolean hit = condition.getValue().equalsIgnoreCase(Boolean.toString(isVip));
        relation.setHit(hit);
    }

    private void checkRid(JoinModeCondition condition, ActorData currActor, RelationInfo relation) {
        Long rid = currActor.getRid();
        checkNumber(condition, relation, rid);
    }

    private final LevelApi levelApi;

    private void checkLevel(JoinModeCondition condition, ActorData currActor, RelationInfo relation, int gender) {
        int level = levelApi.getLevelByUid(currActor.getUid(), gender);
        checkNumber(condition, relation, (long) level);
    }

    private final LordService lordService;

    private void checkLord(JoinModeCondition condition, ActorData currActor, RelationInfo relation) {
        int level = lordService.getUserLordLevel(currActor.getUid());
        checkNumber(condition, relation, (long) level);
    }

    private void checkSex(JoinModeCondition condition, ActorData currActor, RelationInfo relation) {
        Integer sex = currActor.getSex();
        if (sex == null) {
            relation.setHit(false);
            return;
        }
        relation.setHit(condition.getValue().equalsIgnoreCase(Integer.toString(sex)));
    }

    private void checkGender(JoinModeCondition condition, ActorData currActor, RelationInfo relation) {
        Integer gender = currActor.getGender();
        relation.setHit(condition.getValue().equalsIgnoreCase(Integer.toString(gender)));
    }

    private void checkRegisterTime(JoinModeCondition condition, ActorData currActor, RelationInfo relation) {
        Long registerTime = currActor.getRegisterTime();
        checkNumber(condition, relation, registerTime);
    }

    private void checkNumber(JoinModeCondition condition, RelationInfo relation, Long num) {
        switch (condition.getCondition()) {
            case ConditionConstant.EQUALS:
                relation.setHit(condition.getValue().equals(Long.toString(num)));
                break;
            case ConditionConstant.GREATER_THAN:
                relation.setHit(num > Long.parseLong(condition.getValue()));
                break;
            case ConditionConstant.LESS_THAN:
                relation.setHit(num < Long.parseLong(condition.getValue()));
                break;
            case ConditionConstant.GREATER_THAN_OR_EQUALS:
                relation.setHit(num >= Long.parseLong(condition.getValue()));
                break;
            case ConditionConstant.LESS_THAN_OR_EQUALS:
                relation.setHit(num <= Long.parseLong(condition.getValue()));
                break;
            case ConditionConstant.IN:
                Set<String> levelSet = StringUtils.commaDelimitedListToSet(condition.getValue());
                relation.setHit(levelSet.contains(Long.toString(num)));
                break;
            default:
                break;
        }
    }
}
