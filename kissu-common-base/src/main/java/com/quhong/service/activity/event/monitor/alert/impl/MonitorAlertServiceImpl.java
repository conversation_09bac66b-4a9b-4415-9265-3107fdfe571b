package com.quhong.service.activity.event.monitor.alert.impl;

import com.quhong.constant.event.monitor.MonitorCycleType;
import com.quhong.constant.event.monitor.MonitorLevelConstant;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.constant.WarnName;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.bo.activity.event.monitor.EventMonitorBO;
import com.quhong.service.activity.event.monitor.alert.MonitorAlertService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 监控告警服务实现
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
@Lazy
@Slf4j
@Service
@RequiredArgsConstructor
public class MonitorAlertServiceImpl implements MonitorAlertService {

    /**
     * 告警间隔时间（秒）
     */
    private static final int ALERT_INTERVAL_SECONDS = 300; // 5分钟
    /**
     * 告警键前缀
     */
    private static final String ALERT_KEY_PREFIX = "monitor:alert:";
    private final MonitorSender monitorSender;
    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedis;

    @Override
    public void sendAlert(String monitorKey, String eventCode, String name, Integer level,
                          String alertType, Integer currentValue, Integer thresholdValue, String alertMsg, EventMonitorBO monitorBO) {
        // 生成告警键
        String alertKey = ALERT_KEY_PREFIX + monitorKey + ":" + alertType;

        // 检查是否需要抑制告警
        if (level == 2 && shouldSuppressAlert(alertKey)) { // 只对普通级别的告警进行抑制
            log.info("Alert suppressed: {}", alertMsg);
            return;
        }

        // 记录告警发送时间
        mainRedis.opsForValue().set(alertKey, String.valueOf(System.currentTimeMillis()), ALERT_INTERVAL_SECONDS, TimeUnit.SECONDS);

        // 生成告警ID
        String alertId = generateAlertId(monitorKey, alertType);

        // 封装告警信息
        AlertMessage message = new AlertMessage();
        message.setAlertId(alertId);
        message.setMonitorKey(monitorKey);
        message.setEventCode(eventCode);
        message.setName(name);
        message.setLevel(level);
        message.setAlertType(alertType);
        message.setCurrentValue(currentValue);
        message.setThresholdValue(thresholdValue);
        message.setAlertMsg(alertMsg);
        message.setAlertTime(DateHelper.BEIJING.getDateByTime(System.currentTimeMillis()));

        // 发送告警
        try {
            doSendAlert(message);

            log.info("Alert sent successfully: {}", alertMsg);
        } catch (Exception e) {
            log.error("Send alert error", e);
        }
    }

    /**
     * 检查是否需要抑制告警
     */
    private boolean shouldSuppressAlert(String alertKey) {
        return mainRedis.hasKey(alertKey);
    }

    /**
     * 生成告警ID
     */
    private String generateAlertId(String monitorKey, String alertType) {
        return monitorKey + "_" + alertType + "_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }

    /**
     * 实际发送告警
     * 可以根据实际情况实现，如发送邮件、短信、钉钉消息等
     */
    private void doSendAlert(AlertMessage message) {
        // 根据实际情况实现告警发送
        log.info("Sending alert: {}", message.getAlertMsg());
        String color = message.getLevel() == MonitorLevelConstant.HIGH ? "red" : "warning";
        String notice = "**告警ID：**" + message.getAlertId() + "\n" +
                "**监控Key：**" + message.getMonitorKey() + "\n" +
                "**事件码：**" + message.getEventCode() + "\n" +
                "**监控名称：**" + message.getName() + "\n" +
                "<font color=\"" + color + "\">**级别：**" + MonitorLevelConstant.getDesc(message.getLevel()) + "</font>\n" +
                "**告警类型：**" + message.getAlertType() + "\n" +
                "**周期类型：**" + MonitorCycleType.getDesc(message.getCycleType()) + "\n" +
                "**当前值：**" + message.getCurrentValue() + "\n" +
                "**阈值：**" + message.getThresholdValue() + "\n" +
                "**告警信息：**" + message.getAlertMsg() + "\n" +
                "**告警时间：**" + message.getAlertTime();
        if(message.getLevel() == MonitorLevelConstant.HIGH){
            monitorSender.customMarkdownWithPhone(WarnName.METHOD_MONITOR_NOTICE, notice);
        }else{
            monitorSender.customMarkdown(WarnName.METHOD_MONITOR_NOTICE, notice);
        }
    }


    /**
     * 告警消息
     */
    @Data
    private static class AlertMessage {
        private String alertId;
        private String monitorKey;
        private String eventCode;
        private String name;
        private Integer level;
        private String alertType;
        private Integer cycleType;
        private Integer currentValue;
        private Integer thresholdValue;
        private String alertMsg;
        private String alertTime;
    }
}
