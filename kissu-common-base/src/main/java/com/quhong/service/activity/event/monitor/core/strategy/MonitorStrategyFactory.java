package com.quhong.service.activity.event.monitor.core.strategy;

import com.quhong.constant.event.monitor.MonitorCycleType;
import com.quhong.service.activity.event.monitor.core.strategy.impl.ActivityPeriodStrategy;
import com.quhong.service.activity.event.monitor.core.strategy.impl.DailyStrategy;
import com.quhong.service.activity.event.monitor.core.strategy.impl.FixedTimeStrategy;
import com.quhong.service.activity.event.monitor.core.strategy.impl.PeriodStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 监控策略工厂
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
@Slf4j
@Component
public class MonitorStrategyFactory {

    private static final Map<Integer, MonitorStrategy> strategyMap = new HashMap<>();

    @Resource
    private ActivityPeriodStrategy activityPeriodStrategy;

    @Resource
    private DailyStrategy dailyStrategy;

    @Resource
    private PeriodStrategy periodStrategy;

    @Resource
    private FixedTimeStrategy fixedTimeStrategy;

    /**
     * 根据监控周期类型获取对应的策略
     *
     * @param cycleType 监控周期类型
     * @return 监控策略
     */
    public static MonitorStrategy getStrategy(Integer cycleType) {
        return strategyMap.getOrDefault(cycleType, null);
    }

    @PostConstruct
    public void init() {
        strategyMap.put(MonitorCycleType.EVENT_DURATION, activityPeriodStrategy);  // 活动期间监控
        strategyMap.put(MonitorCycleType.DAILY_MONITOR, dailyStrategy);          // 每日监控
        strategyMap.put(MonitorCycleType.CYCLE_MONITOR, periodStrategy);         // 周期监控
        strategyMap.put(MonitorCycleType.POINT_CYCLE_MONITOR, fixedTimeStrategy);      // 定点周期监控
        log.info("Monitor strategy factory initialized with {} strategies", strategyMap.size());
    }
}
