package com.quhong.service.activity.event.monitor.alert;

import com.quhong.data.bo.activity.event.monitor.EventMonitorBO;

/**
 * 监控告警服务接口
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
public interface MonitorAlertService {

    /**
     * 发送告警
     *
     * @param monitorKey     监控Key
     * @param eventCode      活动码
     * @param name           监控名称
     * @param level          告警级别
     * @param alertType      告警类型
     * @param currentValue   当前值
     * @param thresholdValue 阈值
     * @param alertMsg       告警消息
     * @param monitorBO
     */
    void sendAlert(String monitorKey, String eventCode, String name, Integer level,
                   String alertType, Integer currentValue, Integer thresholdValue, String alertMsg, EventMonitorBO monitorBO);
}
