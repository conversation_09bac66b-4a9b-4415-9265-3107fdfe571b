package com.quhong.service.activity.event.monitor.core.strategy.impl;

import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.date.DateSupport;
import com.quhong.data.bo.activity.event.monitor.EventMonitorBO;
import com.quhong.service.activity.event.monitor.MonitorManager;
import com.quhong.service.activity.event.monitor.core.MonitorContext;
import com.quhong.service.activity.event.monitor.core.strategy.MonitorStrategy;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 每日监控策略
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
@Lazy
@Slf4j
@Component
@RequiredArgsConstructor
public class DailyStrategy implements MonitorStrategy {

    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedis;

    @NonNull
    private static String getRankEmptyKey(String redisKeyBase, String dateStr) {
        return redisKeyBase + ":empty:" + dateStr;
    }

    @NonNull
    private static String getFailKey(String redisKeyBase, String dateStr) {
        return redisKeyBase + ":fail:" + dateStr;
    }

    @NonNull
    private static String getExecTimeKey(String redisKeyBase, String dateStr) {
        return redisKeyBase + ":maxExecTime:" + dateStr;
    }

    @NonNull
    private static String getTotalTimesKey(String redisKeyBase, String dateStr) {
        return redisKeyBase + ":total:" + dateStr;
    }

    @Override
    public void recordExecution(MonitorContext context, EventMonitorBO monitorBO, boolean success, long executionTime) {
        String redisKeyBase = MonitorManager.getRedisKey(context.getMonitorKey(), monitorBO);
        String dateStr = DateSupport.BEIJING.offsetDateStr(0);
        // 记录失败次数
        if (!success) {
            mainRedis.opsForValue().increment(getFailKey(redisKeyBase, dateStr), 1);
        }

        // 更新最大执行时间
        updateMaxExecutionTime(redisKeyBase, executionTime);

        // 如果是榜单方法且配置了榜单空置率监控，记录空结果次数
        if (context.isRankMethod() && Boolean.TRUE.equals(monitorBO.getNeedCheckRankEmpty()) && context.isResultEmpty()) {
            mainRedis.opsForValue().increment(getRankEmptyKey(redisKeyBase, dateStr), 1);
        }

        // 设置过期时间，到第二天凌晨过期
        setExpireTimeToNextDay(redisKeyBase);
    }

    @Override
    public void resetStatistics(String monitorKey, EventMonitorBO monitorBO) {
        String redisKeyBase = MonitorManager.getRedisKey(monitorKey, monitorBO);
        String dateStr = DateSupport.BEIJING.offsetDateStr(0);
        // 删除原有统计数据
        mainRedis.delete(getTotalTimesKey(redisKeyBase, dateStr));
        mainRedis.delete(getFailKey(redisKeyBase, dateStr));
        mainRedis.delete(getExecTimeKey(redisKeyBase, dateStr));
        mainRedis.delete(getRankEmptyKey(redisKeyBase, dateStr));
    }

    @Override
    public void beforeExecution(MonitorContext context, EventMonitorBO monitorBO) {
        String redisKeyBase = MonitorManager.getRedisKey(context.getMonitorKey(), monitorBO);
        String dateStr = DateSupport.BEIJING.offsetDateStr(0);
        mainRedis.opsForValue().increment(getTotalTimesKey(redisKeyBase, dateStr), 1);
    }


    public @NonNull String getTotalCountKey(String redisKeyBase) {
        String dateStr = DateSupport.BEIJING.offsetDateStr(0);
        return getTotalTimesKey(redisKeyBase, dateStr);
    }


    public @NonNull String getMaxExecTimeKey(String redisKeyBase) {
        String dateStr = DateSupport.BEIJING.offsetDateStr(0);
        return getExecTimeKey(redisKeyBase, dateStr);
    }


    public @NonNull String getFailCountKey(String redisKeyBase) {
        String dateStr = DateSupport.BEIJING.offsetDateStr(0);
        return getFailKey(redisKeyBase, dateStr);
    }


    public @NonNull String getRankEmptyKey(String redisKeyBase) {
        String dateStr = DateSupport.BEIJING.offsetDateStr(0);
        return getRankEmptyKey(redisKeyBase, dateStr);
    }

    /**
     * 更新最大执行时间
     */
    private void updateMaxExecutionTime(String redisKeyBase, long executionTime) {
        String dateStr = DateSupport.BEIJING.offsetDateStr(0);
        String maxExecTimeKey = getExecTimeKey(redisKeyBase, dateStr);
        String currentMaxObj = mainRedis.opsForValue().get(maxExecTimeKey);
        long currentMax = currentMaxObj != null ? Long.parseLong(currentMaxObj) : 0;

        if (executionTime > currentMax) {
            mainRedis.opsForValue().set(maxExecTimeKey, String.valueOf(executionTime));
        }
    }

    /**
     * 设置Redis键的过期时间
     */
    private void setExpireTimeToNextDay(String redisKeyBase) {
        String dateStr = DateSupport.BEIJING.offsetDateStr(0);

        // 设置过期时间
        mainRedis.expire(getTotalTimesKey(redisKeyBase, dateStr), 2, TimeUnit.DAYS);
        mainRedis.expire(getFailKey(redisKeyBase, dateStr), 2, TimeUnit.DAYS);
        mainRedis.expire(getExecTimeKey(redisKeyBase, dateStr), 2, TimeUnit.DAYS);
        mainRedis.expire(getRankEmptyKey(redisKeyBase, dateStr), 2, TimeUnit.DAYS);
    }
}
