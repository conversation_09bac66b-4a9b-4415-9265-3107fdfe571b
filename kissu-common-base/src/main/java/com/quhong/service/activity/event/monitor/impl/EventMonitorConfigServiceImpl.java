package com.quhong.service.activity.event.monitor.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.quhong.constant.event.monitor.MonitorCycleType;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.EventMonitorConfigDao;
import com.quhong.dao.datas.db.EventMonitorConfigData;
import com.quhong.data.bo.activity.event.monitor.EventMonitorBO;
import com.quhong.data.vo.PageResultVo;
import com.quhong.service.activity.event.monitor.EventMonitorConfigService;
import com.quhong.service.activity.event.monitor.MonitorManager;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 事件监控配置服务实现
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
@Lazy
@Slf4j
@Service
@RequiredArgsConstructor
public class EventMonitorConfigServiceImpl implements EventMonitorConfigService {

    private final EventMonitorConfigDao eventMonitorConfigDao;
    private final MonitorManager monitorManager;

    /**
     * 错误配置校验
     * @param monitorBO
     * @return false 通过， true不通过
     */
    private static boolean errorToPointCycleConfig(EventMonitorBO monitorBO) {
        // 定点周期监控需要配置生效时间范围
        if (monitorBO.getCycleType() != MonitorCycleType.POINT_CYCLE_MONITOR) {
            return false;
        }
        if (monitorBO.getMonitorStartTime() == null || monitorBO.getMonitorEndTime() == null) {
            return true;
        }
        if (monitorBO.getMonitorStartTime() >= monitorBO.getMonitorEndTime()) {
            return true;
        }
        // 榜单空置率验证
        if (Boolean.TRUE.equals(monitorBO.getNeedCheckRankEmpty())) {
            if (monitorBO.getMaxRankEmptyRate() == null || monitorBO.getMaxRankEmptyRate() <= 0) {
                return true;
            }

            if (monitorBO.getMinExecTimes() == null || monitorBO.getMinExecTimes() <= 0) {
                return true;
            }
        }
        return false;
    }

    @Override
    public PageResultVo<List<EventMonitorConfigData>> queryPage(String monitorKey, String eventCode, String name,
                                                                Integer valid, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<EventMonitorConfigData> list = eventMonitorConfigDao.queryListByCondition(monitorKey, eventCode, name, valid);
        PageInfo<EventMonitorConfigData> page = new PageInfo<>(list);
        return new PageResultVo<>((int) page.getTotal(), list);
    }

    @Override
    public EventMonitorConfigData getByKey(String key) {
        if (key == null) {
            return null;
        }
        return eventMonitorConfigDao.getOneByKey(key);
    }

    @Override
    public EventMonitorConfigData getByMonitorKey(String monitorKey) {
        return eventMonitorConfigDao.getOneByKey(monitorKey);
    }

    @Override
    public boolean validateConfig(EventMonitorConfigData config) {
        // 基础字段验证
        if (config == null || StringUtils.isEmpty(config.getMonitorKey())
                || StringUtils.isEmpty(config.getEventCode())
                || StringUtils.isEmpty(config.getName())) {
            return false;
        }

        // 监控列表验证
        if (config.getMonitorList() == null || config.getMonitorList().isEmpty()) {
            return false;
        }

        // 逐项验证监控配置
        return config.getMonitorList().stream().noneMatch(EventMonitorConfigServiceImpl::errorToPointCycleConfig);
    }

    @Override
//    @Transactional(value = "dbTransactionManager", rollbackFor = Exception.class)
    public boolean add(EventMonitorConfigData config) {
        config.setId(null);
        long currTime = DateHelper.getCurrTime();
        config.setCtime(currTime);
        config.setMtime(currTime);

        boolean result = eventMonitorConfigDao.insertOneSelective(config) != null;

        // 更新缓存
        if (result) {
            monitorManager.updateConfigCache(config);
        }

        return result;
    }

    @Override
//    @Transactional(value = "dbTransactionManager", rollbackFor = Exception.class)
    public boolean update(EventMonitorConfigData config) {
        if (config.getId() == null) {
            return false;
        }
        config.setMtime(DateHelper.getCurrTime());

        boolean result = eventMonitorConfigDao.updateOneSelective(config) != null;

        // 更新缓存
        if (result) {
            monitorManager.updateConfigCache(config);
        }

        return result;
    }

    @Override
//    @Transactional(value = "dbTransactionManager", rollbackFor = Exception.class)
    public boolean copy(EventMonitorConfigData sourceConfig, String newMonitorKey, String username) {
        long currTime = DateHelper.getCurrTime();
        // 创建副本
        EventMonitorConfigData copyConfig = new EventMonitorConfigData();
        copyConfig.setMonitorKey(newMonitorKey);
        copyConfig.setEventCode(sourceConfig.getEventCode());
        copyConfig.setWarnName(sourceConfig.getWarnName());
        copyConfig.setName(sourceConfig.getName());
        copyConfig.setMonitorList(new ArrayList<>(sourceConfig.getMonitorList()));
        copyConfig.setValid(1);
        copyConfig.setCtime(currTime);
        copyConfig.setMtime(currTime);

        boolean result = eventMonitorConfigDao.insertOneSelective(copyConfig) != null;

        // 更新缓存
        if (result) {
            monitorManager.updateConfigCache(copyConfig);
        }

        return result;
    }

    @Override
//    @Transactional(value = "dbTransactionManager", rollbackFor = Exception.class)
    public boolean updateValid(EventMonitorConfigData config, Integer valid, String username) {
        config.setValid(valid);
        config.setMtime(DateHelper.getCurrTime());
        config.setOperator(username);
        boolean result = eventMonitorConfigDao.updateOneSelective(config) != null;

        // 更新缓存
        if (result) {
            if (valid == 1) {
                monitorManager.updateConfigCache(config);
            } else {
                monitorManager.clearConfigCache(config.getMonitorKey());
            }
        }

        return result;
    }


}
