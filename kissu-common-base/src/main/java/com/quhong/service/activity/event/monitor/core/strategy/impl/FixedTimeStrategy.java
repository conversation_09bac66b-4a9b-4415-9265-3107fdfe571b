package com.quhong.service.activity.event.monitor.core.strategy.impl;

import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.bo.activity.event.monitor.EventMonitorBO;
import com.quhong.service.activity.event.monitor.MonitorManager;
import com.quhong.service.activity.event.monitor.core.MonitorContext;
import com.quhong.service.activity.event.monitor.core.strategy.MonitorStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 定点周期监控策略
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
@Lazy
@Slf4j
@Component
@RequiredArgsConstructor
public class FixedTimeStrategy implements MonitorStrategy {

    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedis;

    @Override
    public void recordExecution(MonitorContext context, EventMonitorBO monitorBO, boolean success, long executionTime) {
        // 检查当前时间是否在生效时间范围内
        if (!isWithinTime(monitorBO.getMonitorStartTime(), monitorBO.getMonitorEndTime())) {
            return;
        }

        String redisKeyBase = MonitorManager.getRedisKey(context.getMonitorKey(), monitorBO);

        // 记录失败次数
        if (!success) {
            mainRedis.opsForValue().increment(getFailCountKey(redisKeyBase), 1);
        }

        // 更新最大执行时间
        updateMaxExecutionTime(redisKeyBase, executionTime);

        // 如果是榜单方法且配置了榜单空置率监控，记录空结果次数
        if (context.isRankMethod() && Boolean.TRUE.equals(monitorBO.getNeedCheckRankEmpty()) && context.isResultEmpty()) {
            mainRedis.opsForValue().increment(getRankEmptyKey(redisKeyBase), 1);
        }

        // 设置过期时间，基于配置的监控周期
        setExpireTime(redisKeyBase, 3, TimeUnit.HOURS);
    }

    private boolean isWithinTime(Long monitorStartTime, Long monitorEndTime) {
        if (monitorStartTime != null && monitorEndTime != null) {
            long currTime = DateHelper.getCurrTime();
            return currTime >= monitorStartTime && currTime <= monitorEndTime;
        }
        return false;
    }

    @Override
    public boolean canAlert(MonitorContext context, EventMonitorBO monitorBO) {
        return isWithinTime(monitorBO.getMonitorStartTime(), monitorBO.getMonitorEndTime());
    }


    @Override
    public void resetStatistics(String monitorKey, EventMonitorBO monitorBO) {
        String redisKeyBase = MonitorManager.getRedisKey(monitorKey, monitorBO);

        // 删除原有统计数据
        mainRedis.delete(getTotalCountKey(redisKeyBase));
        mainRedis.delete(getFailCountKey(redisKeyBase));
        mainRedis.delete(getMaxExecTimeKey(redisKeyBase));
        mainRedis.delete(getRankEmptyKey(redisKeyBase));
    }

    @Override
    public void beforeExecution(MonitorContext context, EventMonitorBO monitorBO) {
        String redisKeyBase = MonitorManager.getRedisKey(context.getMonitorKey(), monitorBO);
        mainRedis.opsForValue().increment(getTotalCountKey(redisKeyBase), 1);
    }

    /**
     * 更新最大执行时间
     */
    private void updateMaxExecutionTime(String redisKeyBase, long executionTime) {
        String maxExecTimeKey = getMaxExecTimeKey(redisKeyBase);
        String currentMaxObj = mainRedis.opsForValue().get(maxExecTimeKey);
        long currentMax = currentMaxObj != null ? Long.parseLong(currentMaxObj) : 0;

        if (executionTime > currentMax) {
            mainRedis.opsForValue().set(maxExecTimeKey, String.valueOf(executionTime));
        }
    }

    /**
     * 设置Redis键过期时间
     */
    private void setExpireTime(String redisKeyBase, long timeout, TimeUnit unit) {
        mainRedis.expire(getTotalCountKey(redisKeyBase), timeout, unit);
        mainRedis.expire(getFailCountKey(redisKeyBase), timeout, unit);
        mainRedis.expire(getMaxExecTimeKey(redisKeyBase), timeout, unit);
        mainRedis.expire(getRankEmptyKey(redisKeyBase), timeout, unit);
    }
}
