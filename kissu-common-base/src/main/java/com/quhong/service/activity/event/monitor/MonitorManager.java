package com.quhong.service.activity.event.monitor;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.event.monitor.MonitorCycleType;
import com.quhong.constant.event.monitor.MonitorLevelConstant;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.constant.WarnName;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.dao.EventMonitorConfigDao;
import com.quhong.dao.datas.db.EventMonitorConfigData;
import com.quhong.data.bo.activity.event.monitor.EventMonitorBO;
import com.quhong.service.activity.event.monitor.alert.MonitorAlertService;
import com.quhong.service.activity.event.monitor.core.MonitorContext;
import com.quhong.service.activity.event.monitor.core.strategy.MonitorStrategy;
import com.quhong.service.activity.event.monitor.core.strategy.MonitorStrategyFactory;
import com.quhong.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 监控管理器
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
@Lazy
@Slf4j
@Component
@RequiredArgsConstructor
public class MonitorManager {

    /**
     * 监控统计数据Redis Key前缀
     */
    private static final String REDIS_KEY_PREFIX = "str:event:method_monitor:stat:";
    /**
     * 监控配置Redis Key前缀
     */
    private static final String CONFIG_REDIS_KEY_PREFIX = "str:event:method_monitor:config:";
    private final EventMonitorConfigDao eventMonitorConfigDao;
    private final MonitorAlertService monitorAlertService;
    /**
     * 本地缓存，减少Redis访问
     */
    private final Map<String, EventMonitorConfigData> configCache = new ConcurrentHashMap<>();
    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedis;

    private final MonitorSender monitorSender;

    /**
     * 获取Redis Key
     */
    public static String getRedisKey(String monitorKey, EventMonitorBO monitorBO) {
        return REDIS_KEY_PREFIX + monitorKey + ":" + monitorBO.getCycleType();
    }

    @PostConstruct
    public void init() {
        // 初始化时加载所有有效的监控配置到本地缓存
        try {
            List<EventMonitorConfigData> configList = eventMonitorConfigDao.queryListByCondition(null, null, null, 1);
            for (EventMonitorConfigData config : configList) {
                configCache.put(config.getMonitorKey(), config);
                cacheConfigToRedis(config);
            }
            log.info("Method monitor manager initialized with {} configs", configList.size());
        } catch (Exception e) {
            log.error("Method monitor manager init error", e);
        }
    }

    /**
     * 缓存配置到Redis
     */
    private void cacheConfigToRedis(EventMonitorConfigData config) {
        String redisKey = CONFIG_REDIS_KEY_PREFIX + config.getMonitorKey();
        try {
            mainRedis.opsForValue().set(redisKey, JSON.toJSONString(config), 1, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("Cache config to Redis error, monitorKey: {}", config.getMonitorKey(), e);
        }
    }

    /**
     * 获取监控配置
     */
    public EventMonitorConfigData getMonitorConfig(String monitorKey) {
        // 先从本地缓存获取
        EventMonitorConfigData config = configCache.get(monitorKey);
        if (config != null) {
            return config;
        }

        // 本地缓存未命中，从Redis获取
        String redisKey = CONFIG_REDIS_KEY_PREFIX + monitorKey;
        String configString = mainRedis.opsForValue().get(redisKey);

        // Redis也未命中，从数据库获取
        if (StringUtils.isEmpty(configString)) {
            config = eventMonitorConfigDao.getOneByKey(monitorKey);
            if (config != null) {
                // 缓存到本地和Redis
                configCache.put(monitorKey, config);
                cacheConfigToRedis(config);
                return config;
            }
            return null;
        }

        return JSON.parseObject(configString, EventMonitorConfigData.class);
    }

    /**
     * 更新监控配置缓存
     */
    public void updateConfigCache(EventMonitorConfigData config) {
        if (config == null) {
            return;
        }
        configCache.compute(config.getMonitorKey(),
                (k, v) -> config);
        cacheConfigToRedis(config);
    }
    /**
     * 清除监控配置缓存
     */
    public void clearConfigCache(String monitorKey) {
        configCache.remove(monitorKey);
        String redisKey = CONFIG_REDIS_KEY_PREFIX + monitorKey;
        mainRedis.delete(redisKey);
    }

    /**
     * 方法执行前处理
     */
    public void beforeExecution(MonitorContext context) {
        // 获取监控配置
        EventMonitorConfigData config = getMonitorConfig(context.getMonitorKey());
        if (config == null || config.getValid() != 1 || config.getMonitorList() == null || config.getMonitorList().isEmpty()) {
            return;
        }
        context.setEventCode(config.getEventCode());
        // 遍历监控列表，根据不同监控周期类型进行处理
        for (EventMonitorBO monitorBO : config.getMonitorList()) {
            // 获取监控策略
            MonitorStrategy strategy = MonitorStrategyFactory.getStrategy(monitorBO.getCycleType());
            if (strategy != null) {
                // 增加总执行次数
                strategy.beforeExecution(context, monitorBO);
            }else {
                log.error("Monitor strategy not found for cycle type: {}", monitorBO.getCycleType());
            }
        }
    }

    /**
     * 方法执行后处理
     * @param context 上下文
     * @param success 方法是否执行成功
     * @param executionTime 执行时长 单位毫秒
     */
    public void afterExecution(MonitorContext context, boolean success, long executionTime) {
        // 获取监控配置
        EventMonitorConfigData config = getMonitorConfig(context.getMonitorKey());
        if (config == null || config.getValid() != 1 || config.getMonitorList() == null || config.getMonitorList().isEmpty()) {
            return;
        }

        // 遍历监控列表，根据不同监控周期类型进行处理
        for (EventMonitorBO monitorBO : config.getMonitorList()) {
            try {
                // 获取监控策略
                MonitorStrategy strategy = MonitorStrategyFactory.getStrategy(monitorBO.getCycleType());
                if (strategy != null) {
                    // 记录执行结果
                    strategy.recordExecution(context, monitorBO, success, executionTime);
                    if (strategy.canAlert(context, monitorBO)) {
                        checkExceptionMonitor(context, monitorBO);
                        // 检查是否需要告警
                        checkAlertCondition(context, config, monitorBO, strategy);
                    }
                }
            } catch (Exception e) {
                log.error("Method monitor process error, monitorKey: {}", context.getMonitorKey(), e);
                monitorSender.customMarkdown(WarnName.METHOD_MONITOR_NOTICE, "## 方法监控处理异常\n" +
                        "monitorKey=" + context.getMonitorKey() + "\n" +
                        "周期类型：" + MonitorCycleType.getDesc(monitorBO.getCycleType()) + "\n" +
                        e.getMessage());
            }
        }
    }

    private void checkExceptionMonitor(MonitorContext context, EventMonitorBO monitorBO) {
        if (context.getException() != null && monitorBO.getLevel() == MonitorLevelConstant.HIGH) {
            monitorSender.customMarkdown(WarnName.METHOD_MONITOR_NOTICE, "## 服务异常告警\n" +
                    "**监控Key：**" + context.getMonitorKey() + "\n" +
                    "**事件码：**" + context.getEventCode() + "\n" +
                    "**监控周期类型：**" + MonitorCycleType.getDesc(monitorBO.getCycleType()) + "\n" +
                    "**异常类型：**" + context.getException().getClass().getName() + "\n" +
                    "**异常信息：**" + context.getException().getMessage() + "\n");
        }
    }

    /**
     * 检查告警条件
     */
    private void checkAlertCondition(MonitorContext context, EventMonitorConfigData config, EventMonitorBO monitorBO, MonitorStrategy strategy) {
        String redisKey = getRedisKey(context.getMonitorKey(), monitorBO);

        // 获取统计数据
        String totalCountKey = strategy.getTotalCountKey(redisKey, monitorBO);
        String maxExecTimeKey = strategy.getMaxExecTimeKey(redisKey, monitorBO);
        String failCountKey = strategy.getFailCountKey(redisKey, monitorBO);
        String totalTimes = mainRedis.opsForValue().get(totalCountKey);
        long totalCount = totalTimes != null ? Long.parseLong(totalTimes) : 0L;
        String failTimes = mainRedis.opsForValue().get(failCountKey);
        long failCount = failTimes != null ? Long.parseLong(failTimes) : 0L;
        String maxExecTimeStr = mainRedis.opsForValue().get(maxExecTimeKey);
        long maxExecTime = maxExecTimeStr != null ? Long.parseLong(maxExecTimeStr) : 0L;

        // 检查失败率
        checkFailRate(context, config, monitorBO, totalCount, failCount);

        // 检查执行时间
        checkExecTime(context, config, monitorBO, maxExecTime);

        // 检查榜单空置率（仅限定点周期监控）
        checkRankEmpty(context, config, monitorBO, redisKey, totalCount, strategy);
    }

    private void checkRankEmpty(MonitorContext context, EventMonitorConfigData config, EventMonitorBO monitorBO, String redisKey, long totalCount, MonitorStrategy strategy) {
        if (!context.isRankMethod() || monitorBO.getCycleType() != MonitorCycleType.POINT_CYCLE_MONITOR || !Boolean.TRUE.equals(monitorBO.getNeedCheckRankEmpty())
                || monitorBO.getMaxRankEmptyRate() <= 0 || monitorBO.getMinExecTimes() <= 0) {
            return;
        }
        String emptyCountKey = strategy.getRankEmptyKey(redisKey, monitorBO);
        String emptyCountStr = mainRedis.opsForValue().get(emptyCountKey);
        long emptyCount = emptyCountStr != null ? Long.parseLong(emptyCountStr) : 0L;

        // 当执行次数达到最小要求时才检查空置率
        if (totalCount < monitorBO.getMinExecTimes()) {
            return;
        }
        int emptyRate = (int) (emptyCount * 10000 / totalCount);
        if (emptyRate >= monitorBO.getMaxRankEmptyRate()) {
            // 触发榜单空置率告警
            String alertMsg = String.format("方法监控告警[榜单空置率] - %s, 当前空置率: %s%%, 阈值: %s%%, 总执行次数: %s, 空结果次数: %s",
                    config.getName(), emptyRate, monitorBO.getMaxRankEmptyRate(), totalCount, emptyCount);
            monitorAlertService.sendAlert(context.getMonitorKey(), config.getEventCode(),
                    config.getName(), monitorBO.getLevel(), "RANK_EMPTY", emptyRate, monitorBO.getMaxRankEmptyRate().intValue(), alertMsg, monitorBO);
        }
    }

    private void checkExecTime(MonitorContext context, EventMonitorConfigData config, EventMonitorBO monitorBO, long maxExecTime) {
        if (monitorBO.getMaxExecDuration() <= 0 || maxExecTime <= 0) {
            return;
        }
        int maxExecTimeSeconds = (int) (maxExecTime / 1000);
        if (maxExecTimeSeconds < monitorBO.getMaxExecDuration()) {
            return;
        }
        // 触发执行时间告警
        String alertMsg = String.format("方法监控告警[执行时间] - %s, 最大执行时间: %ss, 阈值: %ss",
                config.getName(), maxExecTimeSeconds, monitorBO.getMaxExecDuration());
        monitorAlertService.sendAlert(context.getMonitorKey(), config.getEventCode(),
                config.getName(), monitorBO.getLevel(), "EXEC_TIME", maxExecTimeSeconds, monitorBO.getMaxExecDuration(), alertMsg, monitorBO);
    }

    private void checkFailRate(MonitorContext context, EventMonitorConfigData config, EventMonitorBO monitorBO, long totalCount, long failCount) {
        if (monitorBO.getMaxFailRate() <= 0 || totalCount <= 0) {
            return;
        }
        double failRate = (double) failCount * 10000 / totalCount;
        if (failRate < monitorBO.getMaxFailRate()) {
            return;
        }
        // 触发失败率告警
        String alertMsg = String.format("方法监控告警[失败率] - %s, 当前失败率: %.2f%%, 阈值: %s%%, 总执行次数: %s, 失败次数: %s",
                config.getName(), failRate / 100, monitorBO.getMaxFailRate() / 100.0, totalCount, failCount);
        monitorAlertService.sendAlert(context.getMonitorKey(), config.getEventCode(),
                config.getName(), monitorBO.getLevel(), "FAIL_RATE", (int) failRate, monitorBO.getMaxFailRate(), alertMsg, monitorBO);
    }
}
