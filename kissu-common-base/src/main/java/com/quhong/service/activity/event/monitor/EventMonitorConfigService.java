package com.quhong.service.activity.event.monitor;

import com.quhong.dao.datas.db.EventMonitorConfigData;
import com.quhong.data.vo.PageResultVo;

import java.util.List;

/**
 * 事件监控配置服务接口
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
public interface EventMonitorConfigService {

    /**
     * 分页查询监控配置
     *
     * @param monitorKey 监控Key
     * @param eventCode  活动码
     * @param name       监控名称
     * @param valid      有效状态
     * @param pageNum    页码
     * @param pageSize   每页大小
     * @return 监控配置列表
     */
    PageResultVo<List<EventMonitorConfigData>> queryPage(String monitorKey, String eventCode, String name,
                                                         Integer valid, Integer pageNum, Integer pageSize);

    /**
     * 根据ID查询监控配置
     *
     * @param key 配置ID
     * @return 监控配置
     */
    EventMonitorConfigData getByKey(String key);

    /**
     * 根据监控Key查询监控配置
     *
     * @param monitorKey 监控Key
     * @return 监控配置
     */
    EventMonitorConfigData getByMonitorKey(String monitorKey);

    /**
     * 验证监控配置的有效性
     *
     * @param config 监控配置
     * @return 是否有效
     */
    boolean validateConfig(EventMonitorConfigData config);

    /**
     * 新增监控配置
     *
     * @param config 监控配置
     * @return 是否成功
     */
    boolean add(EventMonitorConfigData config);

    /**
     * 更新监控配置
     *
     * @param config 监控配置
     * @return 是否成功
     */
    boolean update(EventMonitorConfigData config);

    /**
     * 复制监控配置
     *
     * @param existConfig   源配置ID
     * @param newMonitorKey 新监控Key
     * @param username      操作人
     * @return 是否成功
     */
    boolean copy(EventMonitorConfigData existConfig, String newMonitorKey, String username);

    /**
     * 更新监控配置状态
     *
     * @param config   配置信息
     * @param valid    有效状态
     * @param username 操作人
     * @return 是否成功
     */
    boolean updateValid(EventMonitorConfigData config, Integer valid, String username);


}
