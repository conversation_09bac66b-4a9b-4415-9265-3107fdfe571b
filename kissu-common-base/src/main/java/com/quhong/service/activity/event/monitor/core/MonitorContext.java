package com.quhong.service.activity.event.monitor.core;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 监控上下文
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
@Data
@NoArgsConstructor
public class MonitorContext {

    /**
     * 监控Key
     */
    private String monitorKey;
    /**
     * 事件Code
     */
    private String eventCode;

    /**
     * 是否榜单方法
     */
    private boolean rankMethod;

    /**
     * 执行结果是否为空，用于榜单空置率统计
     */
    private boolean resultEmpty;

    /**
     * 异常信息
     */
    private Throwable exception;

    /**
     * 构造函数
     *
     * @param monitorKey 监控Key
     * @param rankMethod 是否榜单方法
     */
    public MonitorContext(String monitorKey, boolean rankMethod) {
        this.monitorKey = monitorKey;
        this.rankMethod = rankMethod;
    }

    /**
     * 设置执行结果是否为空
     *
     * @param result 执行结果
     */
    public void setResultEmpty(Object result) {
        if (result == null) {
            this.resultEmpty = true;
            return;
        }

        // 判断集合类型是否为空
        if (result instanceof java.util.Collection) {
            this.resultEmpty = ((java.util.Collection<?>) result).isEmpty();
            return;
        }

        // 判断数组类型是否为空
        if (result.getClass().isArray()) {
            this.resultEmpty = java.lang.reflect.Array.getLength(result) == 0;
            return;
        }

        this.resultEmpty = false;
    }
}
