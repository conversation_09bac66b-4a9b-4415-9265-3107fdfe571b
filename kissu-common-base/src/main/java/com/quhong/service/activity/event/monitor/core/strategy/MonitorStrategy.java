package com.quhong.service.activity.event.monitor.core.strategy;

import com.quhong.data.bo.activity.event.monitor.EventMonitorBO;
import com.quhong.service.activity.event.monitor.core.MonitorContext;
import lombok.NonNull;

/**
 * 监控策略接口
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
public interface MonitorStrategy {

    /**
     * 记录方法执行情况
     *
     * @param context       监控上下文
     * @param monitorBO     监控配置
     * @param success       是否执行成功
     * @param executionTime 执行时间(毫秒)
     */
    void recordExecution(MonitorContext context, EventMonitorBO monitorBO, boolean success, long executionTime);

    /**
     * 重置统计数据
     *
     * @param monitorKey 监控Key
     * @param monitorBO  监控配置
     */
    void resetStatistics(String monitorKey, EventMonitorBO monitorBO);

    /**
     * 方法执行前操作
     */
    void beforeExecution(MonitorContext context, EventMonitorBO monitorBO);

    /**
     * 是否需要告警（时间判定）
     */
    default boolean canAlert(MonitorContext context, EventMonitorBO monitorBO){
        return true;
    }

    @NonNull
    default String getTotalCountKey(String redisKeyBase){
        return redisKeyBase + ":total";
    }

    @NonNull
    default String getMaxExecTimeKey(String redisKeyBase){
        return redisKeyBase + ":maxExecTime";
    }

    @NonNull
    default String getFailCountKey(String redisKeyBase){
        return redisKeyBase + ":fail";
    }

    @NonNull
    default String getRankEmptyKey(String redisKeyBase){
        return redisKeyBase + ":empty";
    }

    @NonNull
    default String getTotalCountKey(String redisKeyBase, EventMonitorBO monitorBO){
        return getTotalCountKey(redisKeyBase);
    }

    @NonNull
    default String getMaxExecTimeKey(String redisKeyBase, EventMonitorBO monitorBO){
        return getMaxExecTimeKey(redisKeyBase);
    }

    @NonNull
    default String getFailCountKey(String redisKeyBase, EventMonitorBO monitorBO){
        return getFailCountKey(redisKeyBase);
    }

    @NonNull
    default String getRankEmptyKey(String redisKeyBase, EventMonitorBO monitorBO){
        return getRankEmptyKey(redisKeyBase);
    }
}
