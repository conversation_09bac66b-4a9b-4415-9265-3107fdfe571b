package com.quhong.service.activity.event.monitor.core.strategy.impl;

import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.bo.activity.event.monitor.EventMonitorBO;
import com.quhong.service.activity.event.monitor.MonitorManager;
import com.quhong.service.activity.event.monitor.core.MonitorContext;
import com.quhong.service.activity.event.monitor.core.strategy.MonitorStrategy;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 周期监控策略
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
@Lazy
@Slf4j
@Component
@RequiredArgsConstructor
public class PeriodStrategy implements MonitorStrategy {
    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedis;

    @Override
    public @NonNull String getTotalCountKey(String redisKeyBase, EventMonitorBO monitorBO) {
        long expireSeconds = monitorBO.getMonitorDuration() != null ? monitorBO.getMonitorDuration() : 300; // 默认5分钟
        return getTotalTimesKey(redisKeyBase, expireSeconds);
    }

    @Override
    public @NonNull String getMaxExecTimeKey(String redisKeyBase, EventMonitorBO monitorBO) {
        long expireSeconds = monitorBO.getMonitorDuration() != null ? monitorBO.getMonitorDuration() : 300; // 默认5分钟
        return getExecTimeKey(redisKeyBase, expireSeconds);
    }

    @Override
    public @NonNull String getFailCountKey(String redisKeyBase, EventMonitorBO monitorBO) {
        long expireSeconds = monitorBO.getMonitorDuration() != null ? monitorBO.getMonitorDuration() : 300; // 默认5分钟
        return getFailKey(redisKeyBase, expireSeconds);
    }

    @Override
    public @NonNull String getRankEmptyKey(String redisKeyBase, EventMonitorBO monitorBO) {
        long expireSeconds = monitorBO.getMonitorDuration() != null ? monitorBO.getMonitorDuration() : 300; // 默认5分钟
        return getEmptyRankKey(redisKeyBase, expireSeconds);
    }

    @NonNull
    private static String getEmptyRankKey(String redisKeyBase, long expireSeconds) {
        long index = findDailyKeyIndex(expireSeconds);
        return redisKeyBase + ":empty:" + index;
    }

    @NonNull
    private static String getExecTimeKey(String redisKeyBase, long expireSeconds) {
        long index = findDailyKeyIndex(expireSeconds);
        return redisKeyBase + ":maxExecTime:" + index;
    }

    @NonNull
    private static String getFailKey(String redisKeyBase, long expireSeconds) {
        long index = findDailyKeyIndex(expireSeconds);
        return redisKeyBase + ":fail:" + index;
    }

    @NonNull
    private static String getTotalTimesKey(String redisKeyBase, long expireSeconds) {
        long index = findDailyKeyIndex(expireSeconds);
        return redisKeyBase + ":total:" + index;
    }

    private static long findDailyKeyIndex(long expireSeconds) {
        return (DateHelper.getCurrTime() - DateHelper.BEIJING.getTodayStartTime()) / expireSeconds;
    }

    @Override
    public void recordExecution(MonitorContext context, EventMonitorBO monitorBO, boolean success, long executionTime) {
        String redisKeyBase = MonitorManager.getRedisKey(context.getMonitorKey(), monitorBO);
        long expireSeconds = monitorBO.getMonitorDuration() != null ? monitorBO.getMonitorDuration() : 300; // 默认5分钟

        // 记录失败次数
        if (!success) {
            mainRedis.opsForValue().increment(getFailKey(redisKeyBase, expireSeconds), 1);
        }

        // 更新最大执行时间
        updateMaxExecutionTime(redisKeyBase, executionTime);

        // 如果是榜单方法且配置了榜单空置率监控，记录空结果次数
        if (context.isRankMethod() && Boolean.TRUE.equals(monitorBO.getNeedCheckRankEmpty()) && context.isResultEmpty()) {
            mainRedis.opsForValue().increment(getEmptyRankKey(redisKeyBase, expireSeconds), 1);
        }

        // 设置过期时间，基于配置的监控周期
        setExpireTime(redisKeyBase, expireSeconds, TimeUnit.SECONDS);
    }

    @Override
    public void resetStatistics(String monitorKey, EventMonitorBO monitorBO) {
        String redisKeyBase = MonitorManager.getRedisKey(monitorKey, monitorBO);
        long expireSeconds = monitorBO.getMonitorDuration() != null ? monitorBO.getMonitorDuration() : 300; // 默认5分钟
        // 删除原有统计数据
        mainRedis.delete(getTotalTimesKey(redisKeyBase, expireSeconds));
        mainRedis.delete(getFailKey(redisKeyBase, expireSeconds));
        mainRedis.delete(getExecTimeKey(redisKeyBase, expireSeconds));
        mainRedis.delete(getEmptyRankKey(redisKeyBase, expireSeconds));
    }

    /**
     * 方法执行前操作
     */
    @Override
    public void beforeExecution(MonitorContext context, EventMonitorBO monitorBO) {
        String redisKeyBase = MonitorManager.getRedisKey(context.getMonitorKey(), monitorBO);
        long expireSeconds = monitorBO.getMonitorDuration() != null ? monitorBO.getMonitorDuration() : 300; // 默认5分钟
        mainRedis.opsForValue().increment(getTotalTimesKey(redisKeyBase, expireSeconds), 1);
    }

    /**
     * 更新最大执行时间
     */
    private void updateMaxExecutionTime(String redisKeyBase, long executionTime) {
        String maxExecTimeKey = getExecTimeKey(redisKeyBase, executionTime);
        String currentMaxObj = mainRedis.opsForValue().get(maxExecTimeKey);
        long currentMax = currentMaxObj != null ? Long.parseLong(currentMaxObj) : 0;

        if (executionTime > currentMax) {
            mainRedis.opsForValue().set(maxExecTimeKey, String.valueOf(executionTime));
        }
    }

    /**
     * 设置Redis键过期时间
     */
    private void setExpireTime(String redisKeyBase, long timeout, TimeUnit unit) {
        mainRedis.expire(getTotalTimesKey(redisKeyBase, timeout), timeout * 2, unit);
        mainRedis.expire(getFailKey(redisKeyBase, timeout), timeout * 2, unit);
        mainRedis.expire(getExecTimeKey(redisKeyBase, timeout), timeout * 2, unit);
        mainRedis.expire(getEmptyRankKey(redisKeyBase, timeout), timeout * 2, unit);
    }
}
