package com.quhong.service.money.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.common.data.BaseHttpData;
import com.quhong.constant.ActType;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.constant.medal.MedalCategoryConstant;
import com.quhong.core.cache.CacheMap;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.constant.WarnName;
import com.quhong.core.constant.money.MoneyChangeActionConstant;
import com.quhong.core.enums.ActorType;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.enums.GenderTypeEnum;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.ActorExternalDao;
import com.quhong.dao.CurrencyDetailConfigDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.db.CurrencyDetailConfigData;
import com.quhong.data.MoneyExtendChangeData;
import com.quhong.data.appConfig.BalanceChangeMonitorConfig;
import com.quhong.data.bo.CurrencyChangeBO;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.CurrencyEnum;
import com.quhong.monitor.CmdCodeEnum;
import com.quhong.mq.data.MedalIssueData;
import com.quhong.mq.service.MedalIssueSender;
import com.quhong.players.ActorMgr;
import com.quhong.redis.MoneyDetailRedis;
import com.quhong.service.medal.BaseMedalService;
import com.quhong.service.ConfigApi;
import com.quhong.service.money.MoneyDetailService;
import com.quhong.service.money.TotalIncomeRecordService;
import com.quhong.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName CurrencyBalanceMonitorService
 * <AUTHOR>
 * @date 2022/11/14 10:16
 */
@Component
@Lazy
public class CurrencyBalanceMonitorService {
    private static final Logger logger = LoggerFactory.getLogger(CurrencyBalanceMonitorService.class);
    private static final CacheMap<String, BalanceChangeMonitorConfig> CACHE_MAP = new CacheMap<>(5 * 60 * 1000);
    private static final int EXPIRE_DAY = 2;
    private static final int SPACE_TIME = 2;
    private static final String CACHE_KEY_PRE = "currency_balance_monitor_";

    private static final int ACTIVITY_MONITOR_TYPE = 1;
    private static final List<Long> TIKKO_ACTIVITY_EXTEND_LIST = Arrays.asList(240000L, 312500L, 457500L, 675000L, 965000L);
    private static final List<Long> NOT_TIKKO_ACTIVITY_EXTEND_LIST = Arrays.asList(154000L, 204000L, 304000L, 454000L, 654000L);

    private static final int SIGN_IN_MONITOR_TYPE = 2;
    private static final List<Long> TIKKO_SIGN_IN_EXTEND_LIST = Arrays.asList(20000L, 92500L, 237500L, 455000L, 745000L);
    private static final List<Long> NOT_TIKKO_SIGN_IN_EXTEND_LIST = Arrays.asList(10000L, 60000L, 160000L, 310000L, 510000L);

    private static final int TASK_MONITOR_TYPE = 3;
    private static final List<Long> TIKKO_TASK_EXTEND_LIST = Arrays.asList(550000L, 675000L, 1300000L, 2050000L, 3050000L);
    private static final List<Long> NOT_TIKKO_TASK_EXTEND_LIST = Arrays.asList(200000L, 300000L, 500000L, 800000L, 1200000L);

    private static final int COMMISSION_MONITOR_TYPE = 4;
    private static final List<Long> TIKKO_COMMISSION_EXTEND_LIST = Arrays.asList(1550000L, 1675000L, 1925000L, 2300000L, 2800000L);

    private static final int INVITE_COMMISSION_MONITOR_TYPE = 5;
    private static final List<Long> TIKKO_INVITE_COMMISSION_EXTEND_LIST = Arrays.asList(170000L, 295000L, 545000L, 920000L, 1420000L);


    @Autowired
    private ConfigApi configApi;
    @Autowired
    private MonitorSender monitorSender;
    @Autowired
    private ActorMgr actorMgr;
    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedis;
    @Autowired
    private CurrencyDetailConfigDao currencyDetailConfigDao;
    @Autowired
    private TotalIncomeRecordService totalIncomeRecordService;
    @Autowired
    private MoneyDetailService moneyDetailService;
    @Autowired
    private MedalIssueSender medalIssueSender;
    @Autowired
    private BaseMedalService baseMedalService;
    @Resource
    private ActorExternalDao actorExternalDao;

    public void changeBalanceMonitor(CurrencyChangeBO changeBO) {
        try {
            //排除测试人员
            if (actorExternalDao.isTester(changeBO.getUid())) {
                logger.info("change balance monitor user is tester. uid={}", changeBO.getUid());
                return;
            }
            ActorData actorData = actorMgr.getActorData(changeBO.getUid());
            //获取配置
//            BalanceChangeMonitorConfig config = getConfig(changeBO.getUid());
//            if (config == null) {
//                return;
//            }
            //用户当日金币变动告警
//            long totalValue = userTotalChangeValue(changeBO, config, actorData);
            //单次变动告警
//            singleTimesChangeMonitor(changeBO, config, totalValue, actorData);
            //时间范围内金币变动告警
//            extendTimeChangeMonitor(changeBO, config, totalValue, actorData);
            //记录当日变动数据用于总量告警
//            totalChangeMonitor(changeBO, config, actorData);
            String today = DateHelper.BEIJING.getToday();
            //用户每日获得增加告警
            userAddMonitor(changeBO, actorData, today);
            //活动奖励获得增加告警
            activityAddMonitor(changeBO, actorData, today);
            //每日签到获得增加告警
            signInAddMonitor(changeBO, actorData, today);
            //任务奖励获得增加告警
            taskAddMonitor(changeBO, actorData, today);
            //佣金获得增加告警
            commissionAddMonitor(changeBO, actorData, today);
            //邀请返佣增加告警
            inviteCommissionAddMonitor(changeBO, actorData, today);
            // 主播总收入记录
            recordHostTotalIncome(changeBO);
            // 记录所有游戏的金币获得数量
            recordGameWinGold(changeBO);
        } catch (Exception e) {
            logger.error("change balance monitor error. uid={} e={} {} {}", changeBO.getUid(), e, e.getMessage(), e.getStackTrace());
        }
    }

    private void recordGameWinGold(CurrencyChangeBO changeBO) {
        String uid = changeBO.getUid();
        logger.info("changeBO={} MoneyDetailRedis.GAME_ACT_TYPE_SET={}", JSON.toJSONString(changeBO), JSON.toJSONString(MoneyDetailRedis.GAME_ACT_TYPE_SET));
        if (MoneyDetailRedis.GAME_ACT_TYPE_SET.contains(changeBO.getActType()) && changeBO.getAction() == MoneyChangeActionConstant.ACTION_ADD) {
            boolean validNotHaveMedal = baseMedalService.validNotHaveMedal(uid);
            logger.info("uid={} validNotHaveMedal={} MoneyDetailRedis.GAME_ACT_TYPE_SET={}", changeBO.getUid(), validNotHaveMedal, JSON.toJSONString(MoneyDetailRedis.GAME_ACT_TYPE_SET));
            if (validNotHaveMedal) {
                return;
            }
            double oldProcess = moneyDetailService.getUserPlayGamesWinSum(uid);
            double newProcess = moneyDetailService.incUserPlayGamesWinSum(uid, changeBO.getChanged());
            medalIssueSender.sendMedalIssueMq(new MedalIssueData(uid, (long) newProcess, (long) oldProcess, MedalCategoryConstant.MEDAL_CATEGORY_GAME_WIN));
        }
    }

    private void recordHostTotalIncome(CurrencyChangeBO changeBO) {
        if (GenderTypeEnum.HOST.getType().equals(changeBO.getGender())) {
            if (MoneyChangeActionConstant.ACTION_ADD == changeBO.getAction())
                if (changeBO.getActType() != ActType.VIDEO_CHAT)
                    totalIncomeRecordService.addTotalIncome(changeBO.getUid(), changeBO.getChanged());
        }

    }

    private void userAddMonitor(CurrencyChangeBO changeBO, ActorData actorData, String today) {
        if (MoneyChangeActionConstant.ACTION_ADD != changeBO.getAction() || actorData == null
                || ActType.GAME_ACT_LIST.contains(changeBO.getActType())
                || ActType.COIN_DEALERS_RECHARGE == changeBO.getActType() || ActType.WITHDRAW == changeBO.getActType()) {
            return;//只记录增加 游戏不做统计 币商充值不统计 提现类型不做统计
        }
        int currencyCode = changeBO.getCurrencyCode();
        String currencyCodeStr = CurrencyEnum.getCurrencyType(currencyCode);
        String key = getUserAddRecordKey(today, changeBO.getUid(), currencyCode);
        Long increment = mainRedis.opsForValue().increment(key, changeBO.getChanged());
        mainRedis.expire(key, 2, TimeUnit.DAYS);
        long changeValue = increment != null ? increment : changeBO.getChanged();
        String level = "";
        boolean sendMonitor = false;
        boolean phoneMonitor = false;
        if (changeValue >= 100000 && changeValue < 400000) {
            sendMonitor = decideMonitor(changeBO.getUid(), today, "3", currencyCode);
            level = "三级";
        } else if (changeValue >= 400000 && changeValue < 800000) {
            sendMonitor = decideMonitor(changeBO.getUid(), today, "2", currencyCode);
            level = "二级";
        } else if (changeValue >= 2000000) {
            sendMonitor = decideMonitor(changeBO.getUid(), today, "1", currencyCode);
            phoneMonitor = true;
            level = "一级";
        }
        if (sendMonitor) {
            String content = "## 用户货币增加" + level + "告警\n"
                    + "**用户rid: **" + actorData.getRid() + "\n"
                    + "**用户uid: **" + changeBO.getUid() + "\n"
                    + "**货币类型: **" + currencyCodeStr + "\n"
                    + "**累计额度: **" + changeValue + "\n";
            if (phoneMonitor) {//一级 电话告警
                monitorSender.customMarkdownWithPhone(CmdCodeEnum.ACTOR_BALANCE_NOTICE.getWarnName(), content);
            } else {
                monitorSender.customMarkdown(CmdCodeEnum.ACTOR_BALANCE_NOTICE.getWarnName(), content);
            }
        }
    }

    private void activityAddMonitor(CurrencyChangeBO changeBO, ActorData actorData, String today) {
        if (actorData == null) {
            return;
        }
        if (MoneyChangeActionConstant.ACTION_ADD != changeBO.getAction()) {
            return;
        }
        if (ActType.ACTIVITY_REWARD != changeBO.getActType()) {
            return;
        }
        if (changeBO.getSegmentCode() == ActivityTypeEnum.LUCKY_GIFT.getCode()) {//排除幸运礼物
            return;
        }
        int currencyCode = changeBO.getCurrencyCode();
        String currencyCodeStr = CurrencyEnum.getCurrencyType(currencyCode);
        int channelValue = ChannelEnum.isTikkoChannel(actorData.getChannel()) ? 1 : 2;
        String channelStrValue = ChannelEnum.isTikkoChannel(actorData.getChannel()) ? "tikko" : "no_tikko";
        String key = getAddRecordKey(today, ACTIVITY_MONITOR_TYPE, currencyCode, channelStrValue);
        Long increment = mainRedis.opsForValue().increment(key, changeBO.getChanged());
        mainRedis.expire(key, 2, TimeUnit.DAYS);
        long changeValue = increment != null ? increment : changeBO.getChanged();
        switch (channelValue) {
            case 1:
                checkSendMonitor(today, changeValue, currencyCodeStr, currencyCode, channelStrValue, ACTIVITY_MONITOR_TYPE, TIKKO_ACTIVITY_EXTEND_LIST, "活动奖励货币增加");
                break;
            case 2:
                checkSendMonitor(today, changeValue, currencyCodeStr, currencyCode, channelStrValue, ACTIVITY_MONITOR_TYPE, NOT_TIKKO_ACTIVITY_EXTEND_LIST, "活动奖励货币增加");
                break;
        }
    }

    private void signInAddMonitor(CurrencyChangeBO changeBO, ActorData actorData, String today) {
        if (actorData == null) {
            return;
        }
        if (MoneyChangeActionConstant.ACTION_ADD != changeBO.getAction()) {
            return;
        }
        if (ActType.SIGN_GOLD != changeBO.getActType()) {
            return;
        }
        int currencyCode = changeBO.getCurrencyCode();
        String currencyCodeStr = CurrencyEnum.getCurrencyType(currencyCode);
        int channelValue = ChannelEnum.isTikkoChannel(actorData.getChannel()) ? 1 : 2;
        String channelStrValue = ChannelEnum.isTikkoChannel(actorData.getChannel()) ? "tikko" : "no_tikko";
        String key = getAddRecordKey(today, SIGN_IN_MONITOR_TYPE, currencyCode, channelStrValue);
        Long increment = mainRedis.opsForValue().increment(key, changeBO.getChanged());
        mainRedis.expire(key, 2, TimeUnit.DAYS);
        long changeValue = increment != null ? increment : changeBO.getChanged();
        switch (channelValue) {
            case 1:
                checkSendMonitor(today, changeValue, currencyCodeStr, currencyCode, channelStrValue, SIGN_IN_MONITOR_TYPE, TIKKO_SIGN_IN_EXTEND_LIST, "签到奖励货币增加");
                break;
            case 2:
                checkSendMonitor(today, changeValue, currencyCodeStr, currencyCode, channelStrValue, SIGN_IN_MONITOR_TYPE, NOT_TIKKO_SIGN_IN_EXTEND_LIST, "签到奖励货币增加");
                break;
        }
    }

    private void taskAddMonitor(CurrencyChangeBO changeBO, ActorData actorData, String today) {
        if (actorData == null) {
            return;
        }
        if (MoneyChangeActionConstant.ACTION_ADD != changeBO.getAction()) {
            return;
        }
        if (ActType.TASK_REWARD != changeBO.getActType()) {
            return;
        }
        int currencyCode = changeBO.getCurrencyCode();
        String currencyCodeStr = CurrencyEnum.getCurrencyType(currencyCode);
        int channelValue = ChannelEnum.isTikkoChannel(actorData.getChannel()) ? 1 : 2;
        String channelStrValue = ChannelEnum.isTikkoChannel(actorData.getChannel()) ? "tikko" : "no_tikko";
        String key = getAddRecordKey(today, TASK_MONITOR_TYPE, currencyCode, channelStrValue);
        Long increment = mainRedis.opsForValue().increment(key, changeBO.getChanged());
        mainRedis.expire(key, 2, TimeUnit.DAYS);
        long changeValue = increment != null ? increment : changeBO.getChanged();
        switch (channelValue) {
            case 1:
                checkSendMonitor(today, changeValue, currencyCodeStr, currencyCode, channelStrValue, TASK_MONITOR_TYPE, TIKKO_TASK_EXTEND_LIST, "任务奖励货币增加");
                break;
            case 2:
                checkSendMonitor(today, changeValue, currencyCodeStr, currencyCode, channelStrValue, TASK_MONITOR_TYPE, NOT_TIKKO_TASK_EXTEND_LIST, "任务奖励货币增加");
                break;
        }
    }

    private void commissionAddMonitor(CurrencyChangeBO changeBO, ActorData actorData, String today) {
        if (actorData == null) {
            return;
        }
        if (MoneyChangeActionConstant.ACTION_ADD != changeBO.getAction()) {
            return;
        }
        if (!ActType.MONITOR_COMMISSION_ACT_SET.contains(changeBO.getActType())) {
            return;
        }
        int currencyCode = changeBO.getCurrencyCode();
        String currencyCodeStr = CurrencyEnum.getCurrencyType(currencyCode);
        int channelValue = ChannelEnum.isTikkoChannel(actorData.getChannel()) ? 1 : 2;
        String channelStrValue = ChannelEnum.isTikkoChannel(actorData.getChannel()) ? "tikko" : "no_tikko";
        String key = getAddRecordKey(today, COMMISSION_MONITOR_TYPE, currencyCode, channelStrValue);
        Long increment = mainRedis.opsForValue().increment(key, changeBO.getChanged());
        mainRedis.expire(key, 2, TimeUnit.DAYS);
        long changeValue = increment != null ? increment : changeBO.getChanged();
        if (channelValue == 1) {
            checkSendMonitor(today, changeValue, currencyCodeStr, currencyCode, channelStrValue, COMMISSION_MONITOR_TYPE, TIKKO_COMMISSION_EXTEND_LIST, "返佣奖励货币增加(游戏分成&代理下子代理/主播业绩变动)");
        }
    }

    private void inviteCommissionAddMonitor(CurrencyChangeBO changeBO, ActorData actorData, String today) {
        if (actorData == null) {
            return;
        }
        if (MoneyChangeActionConstant.ACTION_ADD != changeBO.getAction()) {
            return;
        }
        if (ActType.INVITE_COMMISSION != changeBO.getActType()) {
            return;
        }
        int currencyCode = changeBO.getCurrencyCode();
        String currencyCodeStr = CurrencyEnum.getCurrencyType(currencyCode);
        int channelValue = ChannelEnum.isTikkoChannel(actorData.getChannel()) ? 1 : 2;
        String channelStrValue = ChannelEnum.isTikkoChannel(actorData.getChannel()) ? "tikko" : "no_tikko";
        String key = getAddRecordKey(today, INVITE_COMMISSION_MONITOR_TYPE, currencyCode, channelStrValue);
        Long increment = mainRedis.opsForValue().increment(key, changeBO.getChanged());
        mainRedis.expire(key, 2, TimeUnit.DAYS);
        long changeValue = increment != null ? increment : changeBO.getChanged();
        if (channelValue == 1) {
            checkSendMonitor(today, changeValue, currencyCodeStr, currencyCode, channelStrValue, INVITE_COMMISSION_MONITOR_TYPE, TIKKO_INVITE_COMMISSION_EXTEND_LIST, "返佣奖励货币增加(邀请佣金)");
        }
    }

    private void checkSendMonitor(String today, long changeValue, String currencyCodeStr, int currencyCode, String channelStrValue, int type, List<Long> extenList, String title) {
        String level = "";
        boolean sendMonitor = false;
        boolean phoneMonitor = false;
        long warningCode = 0;
        if (changeValue >= extenList.get(4)) {
            sendMonitor = decideMonitor(type, today, "1", currencyCode, channelStrValue);
            level = "一级";
            phoneMonitor = true;
            warningCode = extenList.get(4);
        } else if (changeValue >= extenList.get(3)) {
            sendMonitor = decideMonitor(type, today, "2", currencyCode, channelStrValue);
            level = "二级";
            phoneMonitor = true;
            warningCode = extenList.get(3);
        } else if (changeValue >= extenList.get(2)) {
            sendMonitor = decideMonitor(type, today, "3", currencyCode, channelStrValue);
            level = "三级";
            phoneMonitor = true;
            warningCode = extenList.get(2);
        } else if (changeValue >= extenList.get(1)) {
            sendMonitor = decideMonitor(type, today, "4", currencyCode, channelStrValue);
            level = "四级";
            warningCode = extenList.get(1);
        } else if (changeValue >= extenList.get(0)) {
            sendMonitor = decideMonitor(type, today, "5", currencyCode, channelStrValue);
            level = "五级";
            warningCode = extenList.get(0);
        }
        if (sendMonitor) {
            String content = "##" + title + level + "告警\n"
                    + "**渠道: **" + channelStrValue + "\n"
                    + "**date: **" + today + "\n"
                    + "**预警值: **" + warningCode + "\n"
                    + "**货币类型: **" + currencyCodeStr + "\n"
                    + "**累计额度: **" + changeValue + "\n"
                    + "(五级正常幅度,四级需要排查,一二三级以上电话告警需要紧急排查)";

            if (phoneMonitor) {//一级 电话告警
                logger.info("phone monitor title={} level={}", title, level);
                monitorSender.customMarkdownWithPhone(WarnName.COIN_THRESHOLD_ALARM, content);
            } else {
                monitorSender.customMarkdown(WarnName.COIN_THRESHOLD_ALARM, content);
            }
        }
    }

    public boolean decideMonitor(String uid, String date, String level, int currencyCode) {
        String key = getMonitorRecordKey(uid, date, level, currencyCode);
        String str = mainRedis.opsForValue().get(key);
        if (!StringUtils.isEmpty(str)) {
            return false;
        }
        mainRedis.opsForValue().increment(key, 1);
        mainRedis.expire(key, 2, TimeUnit.DAYS);
        return true;
    }

    public boolean decideMonitor(int type, String date, String level, int currencyCode, String channelValue) {
        String key = getMonitorRecordKey(type, date, level, currencyCode, channelValue);
        Boolean b = mainRedis.hasKey(key);
        if (Boolean.TRUE.equals(b)) {
            return false;
        }
        mainRedis.opsForValue().set(key, "1", 2, TimeUnit.DAYS);
        return true;
    }

    public void totalChangeMonitor() {
        String lastDay = DateHelper.UTC.getDateByDeltaDay(-1);
        List<CurrencyDetailConfigData> currencyDetailConfigData = currencyDetailConfigDao.queryConfigList();
        List<String> detailList = new ArrayList<>();
        for (CurrencyDetailConfigData currencyDetailConfigDatum : currencyDetailConfigData) {
            int currencyCode = currencyDetailConfigDatum.getCurrencyCode();
            detailList.add(totalChangeMonitor(lastDay, currencyCode, ActorType.USER, MoneyChangeActionConstant.ACTION_DEDUCT));
            detailList.add(totalChangeMonitor(lastDay, currencyCode, ActorType.USER, MoneyChangeActionConstant.ACTION_ADD));
            detailList.add(totalChangeMonitor(lastDay, currencyCode, ActorType.HOST, MoneyChangeActionConstant.ACTION_DEDUCT));
            detailList.add(totalChangeMonitor(lastDay, currencyCode, ActorType.HOST, MoneyChangeActionConstant.ACTION_ADD));
        }
        StringBuilder detailBuilder = new StringBuilder();
        for (String detail : detailList) {
            if (StringUtils.isEmpty(detail)) {
                continue;
            }
            detailBuilder.append(detail).append(";\n");
        }
        String desc = lastDay + "金币流水总量变动告警";
        monitorSender.info(CmdCodeEnum.ACTOR_BALANCE_NOTICE.getWarnName(), desc, detailBuilder.toString());
    }

    private String totalChangeMonitor(String lastDay, int currencyCode, int gender, int action) {
        String totalChangeKey = getTotalChangeKey(lastDay, action, currencyCode, gender);
        String strVal = mainRedis.opsForValue().get(totalChangeKey);
        if (StringUtils.isEmpty(strVal)) {
            return "";
        }
        long totalValue = Long.parseLong(strVal);
        String actionStr = action == MoneyChangeActionConstant.ACTION_ADD ? "增加" : "扣减";
        String genderStr = gender == ActorType.USER ? "用户" : "主播";
        String currencyStr = CurrencyEnum.CURRENCY1.getCurrencyCode().equals(currencyCode) ? "金币" : "钻石";
        return actionStr + genderStr + currencyStr + "总量:" + totalValue;
    }

    private String getTotalChangeKey(String day, int action, int currencyCode, int gender) {
        return "str:money_total_change:" + day + ":" + action + ":" + currencyCode + ":" + gender;
    }

    private String getUserAddRecordKey(String day, String uid, int currencyCode) {
        return "str:money_user_add_record:" + day + ":uid:" + uid + ":currencyCode:" + currencyCode;
    }

    private String getMonitorRecordKey(String uid, String date, String level, int currencyCode) {
        return "str:money_monitor_record:date:" + date + ":uid:" + uid + ":level:" + level + ":currencyCode:" + currencyCode;
    }

    private String getMonitorRecordKey(int type, String date, String level, int currencyCode, String channelValue) {
        return "str:money_monitor_record:date:" + date + ":type:" + type + ":level:" + level + ":currencyCode:" + currencyCode + ":channel:" + channelValue;
    }

    private String getAddRecordKey(String day, int type, int currencyCode, String channel) {
        return "str:money_activity_add_record:" + day + ":channel:" + channel + ":currencyCode:" + currencyCode + ":type:" + type;
    }

//    private BalanceChangeMonitorConfig getConfig(String uid) {
//        //从缓存获取
//        String key = CACHE_KEY_PRE + uid;
//        BalanceChangeMonitorConfig data = CACHE_MAP.getData(key);
//        if (data != null) {
//            return data;
//        }
//        ConfigDTO dto = new ConfigDTO();
//        dto.setStatus(AppConfigKeyConstant.STATUS_SERVER);
//        dto.setKey(AppConfigKeyConstant.BALANCE_CHANGE_MONITOR_CONFIG);
//        dto.setUid(uid);
//        data = configApi.getJavaBeanVal(dto, BalanceChangeMonitorConfig.class);
//        CACHE_MAP.cacheData(key, data);
//        logger.info("change monitor uid={} config={}", uid, data);
//        return data;
//    }

//    private void singleTimesChangeMonitor(CurrencyChangeBO changeBO, BalanceChangeMonitorConfig config, long todayChangeValue, ActorData actorData) {
//        if (changeBO.getChanged() < config.getSingleTimesChange()) {
////            logger.info("change monitor single time change less than config. uid={} value={} configValue={} requestId={}",
////                    changeBO.getUid(), changeBO.getChanged(), config.getSingleTimesChange(), MDC.get(BaseHttpData.REQUEST_ID));
//            return;
//        }
//        if (actorData == null) {
//            return;
//        }
//        String desc = "单次金币变动大于" + config.getSingleTimesChange() + "告警";
//        String detail = "*rid=" + actorData.getRid() + "\n"
//                + "\t*变化的值=" + changeBO.getChanged() + "\n"
//                + "\t当日累计总值=" + todayChangeValue + "\n"
//                + "\t*变化前的值=" + changeBO.getBeforeBalance() + "\n"
//                + "\t*变化后的值=" + changeBO.getAfterBalance() + "\n"
//                + "\t*action=" + changeBO.getAction() + "\n"
//                + "\t*流水描述=" + changeBO.getActDesc() + "\n"
//                + "\t*bo=" + JSONObject.toJSONString(changeBO) + ";gender=" + actorData.getGender() + ";requestId=" + MDC.get(BaseHttpData.REQUEST_ID);
//        monitorSender.info(CmdCodeEnum.ACTOR_BALANCE_NOTICE.getWarnName(), desc, detail);
//    }
//
//    private void extendTimeChangeMonitor(CurrencyChangeBO bo, BalanceChangeMonitorConfig config, long todayChangeValue, ActorData actorData) {
//        if (actorData == null) {
//            return;
//        }
//        List<MoneyExtendChangeData> extendChangeList = config.getExtendChangeList();
//        for (MoneyExtendChangeData data : extendChangeList) {
//            if (data.getExtendTime() == 0 || data.getExtendTimeChange() == 0) {
//                continue;
//            }
//            String extendTimeChangeKey = getExtendTimeChangeKey(bo.getUid(), bo.getAction(), bo.getCurrencyCode(), data.getExtendTime());
//            mainRedis.opsForZSet().add(extendTimeChangeKey, String.valueOf(bo.getChanged()), DateHelper.getCurrentTime());
//            mainRedis.expire(extendTimeChangeKey, EXPIRE_DAY, TimeUnit.DAYS);
//            //计算
//            int currentTime = DateHelper.getCurrentTime();
//            int startTime = currentTime - data.getExtendTime();
//            Set<String> strValSet = mainRedis.opsForZSet().rangeByScore(extendTimeChangeKey, startTime, currentTime);
//            long totalValue = 0;
//            if (!CollectionUtils.isEmpty(strValSet)) {
//                for (String s : strValSet) {
//                    totalValue += Long.parseLong(s);
//                }
//                if (totalValue >= data.getExtendTimeChange()) {
//                    String extendTimeChangeSpaceKey = getExtendTimeChangeSpaceKey(bo.getUid(), bo.getAction(), bo.getCurrencyCode(), data.getExtendTime());
//                    Boolean hasKey = mainRedis.hasKey(extendTimeChangeSpaceKey);
//                    if (hasKey != null && hasKey) {
//                        return;
//                    } else {
//                        String desc = data.getExtendTime() + "秒内金币变动大于" + data.getExtendTimeChange() + "告警";
//                        String detail = "*rid=" + actorData.getRid() + "\n"
//                                + "\t*变化的值=" + bo.getChanged() + "\n"
//                                + "\t当日累计总值=" + todayChangeValue + "\n"
//                                + "\t*变化前的值=" + bo.getBeforeBalance() + "\n"
//                                + "\t*变化后的值=" + bo.getAfterBalance() + "\n"
//                                + "\t*action=" + bo.getAction() + "\n"
//                                + "\t*流水描述=" + bo.getActDesc() + "\n"
//                                + "\t*bo=" + JSONObject.toJSONString(bo) + ";gender=" + actorData.getGender() + ";totalValue=" + totalValue + ";requestId=" + MDC.get(BaseHttpData.REQUEST_ID) + ";key=" + extendTimeChangeKey;
//                        monitorSender.info(CmdCodeEnum.ACTOR_BALANCE_NOTICE.getWarnName(), desc, detail);
//                        mainRedis.opsForValue().set(extendTimeChangeSpaceKey, "1", 10, TimeUnit.MINUTES);
//                    }
//                }
//            }
//            //清除数据
//            mainRedis.opsForZSet().removeRangeByScore(extendTimeChangeKey, Double.MIN_VALUE, startTime);
//        }
//    }
//
//    private long userTotalChangeValue(CurrencyChangeBO bo, BalanceChangeMonitorConfig config, ActorData actorData) {
//        String today = DateHelper.UTC.getToday();
//        String userTotalChangeKey = getUserTotalChangeKey(today, bo.getUid(), bo.getAction(), bo.getCurrencyCode());
//        Long increment = mainRedis.opsForValue().increment(userTotalChangeKey, bo.getChanged());
//        mainRedis.expire(userTotalChangeKey, EXPIRE_DAY, TimeUnit.DAYS);
//        return increment != null ? increment : bo.getChanged();
//    }

//    private void totalChangeMonitor(CurrencyChangeBO bo, BalanceChangeMonitorConfig config, ActorData actorData) {
//        if (actorData == null) {
//            logger.info("change monitor save change fail actor is not exist. uid={}", bo.getUid());
//            return;
//        }
//        String today = DateHelper.UTC.getToday();
//        String totalChangeKey = getTotalChangeKey(today, bo.getAction(), bo.getCurrencyCode(), actorData.getGender());
//        Long increment = mainRedis.opsForValue().increment(totalChangeKey, bo.getChanged());
//        mainRedis.expire(totalChangeKey, EXPIRE_DAY, TimeUnit.DAYS);
//        if (config.getTotalChange() != 0) {
//            long totalValue = increment != null ? increment : bo.getChanged();
//            if (totalValue < config.getTotalChange()) {
//                return;
//            }
//            String totalChangeSpaceKey = getTotalChangeSpaceKey(today, bo.getAction(), bo.getCurrencyCode(), actorData.getGender());
//            Boolean hasKey = mainRedis.hasKey(totalChangeSpaceKey);
//            if (hasKey != null && hasKey) {
//                return;
//            } else {
//                String actionStr = bo.getAction() == MoneyChangeActionConstant.ACTION_ADD ? "增加" : "扣减";
//                String currencyStr = CurrencyEnum.CURRENCY1.getCurrencyCode().equals(bo.currencyCode) ? "金币" : "钻石";
//                String genderStr = actorData.getGender().equals(ActorType.USER) ? "用户" : "主播";
//                String desc = "当日金币变动大于" + config.getTotalChange() + "告警";
//                String detail = "当日" + genderStr + actionStr + currencyStr + "总值=" + totalValue + "\n"
//                        + "\t*rid=" + actorData.getRid() + "\n"
//                        + "\t*变化的值=" + bo.getChanged() + "\n"
//                        + "\t*变化前的值=" + bo.getBeforeBalance() + "\n"
//                        + "\t*变化后的值=" + bo.getAfterBalance() + "\n"
//                        + "\t*流水描述=" + bo.getActDesc() + "\n"
//                        + "\t*date=" + today + JSONObject.toJSONString(bo) + "; gender=" + actorData.getGender() + " ;totalValue=" + totalValue + ";requestId=" + MDC.get(BaseHttpData.REQUEST_ID) + ";key=" + totalChangeKey;
//                monitorSender.info(CmdCodeEnum.ACTOR_BALANCE_NOTICE.getWarnName(), desc, detail);
//                mainRedis.opsForValue().set(totalChangeSpaceKey, "1", 1, TimeUnit.DAYS);
//            }
//        }
//    }

//    private String getExtendTimeChangeKey(String uid, int action, int currencyCode, int extendTime) {
//        return "zset:money_extend_time_change:" + uid + ":" + action + ":" + currencyCode + ":" + extendTime;
//    }
//
//    private String getExtendTimeChangeSpaceKey(String uid, int action, int currencyCode, int extendTime) {
//        return "str:money_extend_time_change_space:" + uid + ":" + action + ":" + currencyCode + ":" + extendTime;
//    }
//
//    private String getTotalChangeSpaceKey(String day, int action, int currencyCode, int gender) {
//        return "str:money_total_change_space:" + day + ":" + action + ":" + currencyCode + ":" + gender;
//    }
//
//    private String getUserTotalChangeKey(String day, String uid, int action, int currencyCode) {
//        return "str:money_user_total_change:" + day + ":" + uid + ":" + action + ":" + currencyCode;
//    }
//
//    private String getUserTotalChangeSpaceKey(String day, String uid, int action, int currencyCode) {
//        return "str:money_user_total_change_space:" + day + ":" + uid + ":" + action + ":" + currencyCode;
//    }
}
