package com.quhong.service;

import com.quhong.dao.ActorConfigDao;
import com.quhong.dao.LogMemberSubscriptionDao;
import com.quhong.data.pay.RechargeStatData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 充值的数据统计
 */
@Component
@Lazy
public class RechargeStatService {
    private static final Logger logger = LoggerFactory.getLogger(RechargeStatService.class);

    @Resource
    private LogMemberSubscriptionDao logMemberSubscriptionDao;
    @Resource
    private ActorConfigDao actorConfigDao;

    @Cacheable(value = "recharge:stat#172800", key = "methodName + #uid")
    public RechargeStatData getRechargeStat(String uid){
        return doGetRechargeStat(uid);
    }

    @CachePut(value = "recharge:stat#172800", key = "methodName + #uid")
    public RechargeStatData updateStatData(String uid){
        return doGetRechargeStat(uid);
    }

    private RechargeStatData doGetRechargeStat(String uid){
        int bossPoint = actorConfigDao.getConfigIntValue(uid, ActorConfigDao.BOSS_POINTS);
        int subscriptionMonth = logMemberSubscriptionDao.getSubscriptionMonths(uid);
        RechargeStatData data = new RechargeStatData();
        data.setBuyGold(bossPoint);
        data.setSubscriptionMonths(subscriptionMonth);
        return data;
    }
}
