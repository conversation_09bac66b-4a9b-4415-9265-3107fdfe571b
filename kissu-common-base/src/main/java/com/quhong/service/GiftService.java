package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.common.data.HttpEnvData;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.constant.GiftFromTypeConstant;
import com.quhong.constant.LordConstant;
import com.quhong.constant.TemplateTaskConstant;
import com.quhong.core.constant.detect.pron.DetectSceneConstant;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.*;
import com.quhong.dao.datas.*;
import com.quhong.dao.datas.db.GiftNoticeRecordData;
import com.quhong.dao.datas.db.GiftProfileData;
import com.quhong.data.appConfig.HostGiftRateConfig;
import com.quhong.data.bo.RewardMessageBO;
import com.quhong.data.bo.TemplateTaskMqBO;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.data.room.RoomActorData;
import com.quhong.data.room.RoomActorDetailData;
import com.quhong.data.vo.HostIncomeVO;
import com.quhong.enums.TaskTriggerName;
import com.quhong.mq.data.activity.SendGiftSuccessMsgData;
import com.quhong.mq.enums.MQConstant;
import com.quhong.players.ActorMgr;
import com.quhong.redis.GiftRedis;
import com.quhong.redis.RankRedis;
import com.quhong.service.common.RewardService;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.utils.DirtySensitiveWordFilter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * @ClassName GiftService
 * <AUTHOR>
 * @date 2023/3/15 15:23
 */
@Component
@Lazy
@Slf4j
public class GiftService {
    private static final int NOTICE_CONFESSION_TYPE = 1;
    private static final int NOTICE_BULLETIN_TYPE = 2;

    /**
     * 融合礼物开关 0关 1开
     */
    public static final int GIFT_FUSION_SWITCH_CLOSE = 0;
    public static final int GIFT_FUSION_SWITCH_OPEN = 1;

    /**
     * 融合礼物 id展示开关
     */
    public static final int GIFT_FUSION_ID_SWITCH_CLOSE = 0;
    public static final int GIFT_FUSION_ID_SWITCH_OPEN = 1;

    /**
     * 融合礼物 昵称展示开关
     */
    public static final int GIFT_FUSION_NAME_SWITCH_CLOSE = 0;
    public static final int GIFT_FUSION_NAME_SWITCH_OPEN = 1;

    /**
     * 融合礼物类型 1单头像  2双头像
     */
    public static final int GIFT_FUSION_TYPE_SINGLE = 1;
    public static final int GIFT_FUSION_TYPE_DOUBLE = 2;

    /**
     * 融合礼物展示对象 1发送者 2接收者
     */
    public static final int GIFT_FUSION_SHOW_TYPE_SHOW_FROM_ACTOR = 1;
    public static final int GIFT_FUSION_SHOW_TYPE_SHOW_TO_ACTOR = 2;


    public static final int GIFT_CATEGORY_GIFT = 1;
    public static final int GIFT_CATEGORY_LUCKY = 2;
    public static final int GIFT_CATEGORY_PRIVILEGE = 3;
    public static final int GIFT_CATEGORY_CUSTOMIZED = 4;
    public static final int GIFT_CATEGORY_BACKPACK = 5;


    @Resource
    private GiftProfileDao giftProfileDao;
    @Resource
    private RewardService rewardService;
    @Resource
    private RankRedis rankRedis;
    @Resource
    private VestService vestService;
    @Resource
    private GiftListConfigDao giftListConfigDao;
    @Resource
    private ActorMgr actorMgr;
    @Resource
    private HostInfoDao hostInfoDao;
    @Resource
    private GiftNoticeRecordDao giftNoticeRecordDao;
    @Resource
    private GiftRedis giftRedis;
    @Resource
    private ConfigApi configApi;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private DirtyService dirtyService;
    @Resource
    private DirtySensitiveWordFilter dirtySensitiveWordFilter;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private RoomDao roomDao;
    @Resource
    private LordService lordService;

    /**
     * 贡献榜分页默认大小
     */
    private static final int DEFAULT_PAGE_SIZE = 20;

    public void saveGiftProfile(String uid, int giftId, int number) {
        try {
            GiftProfileData giftProfile = giftProfileDao.getGiftProfile(uid, giftId);
            if (giftProfile == null) {
                giftProfile = new GiftProfileData();
                giftProfile.setUid(uid);
                giftProfile.setNumber(number);
                giftProfile.setGiftId(giftId);
                giftProfile.setCtime((long) DateHelper.getCurrentTime());
                giftProfile.setMtime((long) DateHelper.getCurrentTime());
                giftProfileDao.save(giftProfile);
            } else {
                giftProfileDao.changeGiftNum(uid, giftProfile.getNumber() + number, giftId);
            }
        } catch (Exception e) {
            log.error("save gift profile error. uid={} giftId={} number={} e={}{}{}", uid, giftId, number, e, e.getMessage(), e.getStackTrace());
        }
    }

    public void sendFullImMsg(ActorData fromActor, ActorData toActor, GiftListConfigData giftConfig, String roomId, String iconUrl, Double wonNum, Integer num, boolean fromLuck) {
        RewardMessageBO bo = new RewardMessageBO();
        bo.setUid(fromActor.getUid());
        bo.setIcon(iconUrl);
        bo.setRoomId(roomId);
        bo.setItemsName(giftConfig.getShowName());
        if (fromLuck) {
            if (wonNum != null) {
                bo.setWonNums(wonNum.intValue());
                bo.setRealWonNum(String.valueOf(wonNum));
            }
            bo.setType(RewardService.LUCKY_GIFT);
            if (num != null) {
                bo.setNum(num);
            }
            bo.setMultiple(giftConfig.getMultiple());
        } else {
            bo.setType(RewardService.BIG_GIFT);
            if (giftConfig.getConfession() == 1) {
                bo.setType(RewardService.CONFESSION_GIFT);
                bo.setConfessionDesc(giftConfig.getConfessionDesc());
            }
            bo.setGiftId(giftConfig.getGiftId());
            if (toActor != null) {
                bo.setToUid(toActor.getUid());
            }
        }
        rewardService.sendFullServiceIM(bo);
    }

    public void dealReceiveGift(String uid, BigDecimal gain, GiftRecordData giftRecordData) {
        if (StringUtils.isEmpty(uid)) {
            return;
        }
        //缓存主播获得的礼物金币数
        if (gain.doubleValue() > 0) {
            Integer today = getDayOfMonth();
            Integer weekOfYear = DateHelper.UTC.getWeekOfYear(0);
            rankRedis.saveGiftDailyRank(today, uid, gain.doubleValue());
            rankRedis.saveGiftWeeklyRank(weekOfYear, uid, gain.doubleValue());
            sendTaskMQ(uid, gain, giftRecordData);
        }
    }

    private void sendTaskMQ(String uid, BigDecimal gainDiamonds, GiftRecordData giftRecordData) {
        TemplateTaskMqBO bo = TemplateTaskMqBO.newBuilder().taskName(TemplateTaskConstant.TASK_LIVE).nodeId(TemplateTaskConstant.Live.NODE_WEEKLY_GAIN).completeCount(gainDiamonds.intValue()).realCompleteCount(gainDiamonds.toString()).uid(uid).build();
        rabbitTemplate.convertAndSend(MQConstant.TEMPLATE_TASK_EXCHANGE, MQConstant.TEMPLATE_TASK_PRE + ".gift", JSON.toJSONString(bo));
        bo = TemplateTaskMqBO.newBuilder().taskName(TemplateTaskConstant.TASK_LIVE).nodeId(TemplateTaskConstant.Live.NODE_WEEKLY_GAIN).completeCount(gainDiamonds.intValue()).realCompleteCount(gainDiamonds.toString()).uid(uid).build();
        rabbitTemplate.convertAndSend(MQConstant.TEMPLATE_TASK_EXCHANGE, MQConstant.TEMPLATE_TASK_PRE + ".gift", JSON.toJSONString(bo));
        bo = TemplateTaskMqBO.newBuilder().triggerName(TaskTriggerName.DIAMONDS_INCOME_GIFT_CHAT).completeCount(gainDiamonds.intValue()).realCompleteCount(gainDiamonds.toString()).uid(uid).build();
        rabbitTemplate.convertAndSend(MQConstant.TEMPLATE_TASK_EXCHANGE, MQConstant.TEMPLATE_TASK_PRE + ".gift", JSON.toJSONString(bo));
        bo = TemplateTaskMqBO.newBuilder().triggerName(TaskTriggerName.RECEIVE_GIFT).completeCount(1).relateId(String.valueOf(giftRecordData.getGiftid())).relateParam1(giftRecordData.fetchRealPrice().toString()).uid(uid).build();
        rabbitTemplate.convertAndSend(MQConstant.TEMPLATE_TASK_EXCHANGE, MQConstant.TEMPLATE_TASK_PRE + ".gift", JSON.toJSONString(bo));
    }

    public GiftListConfigData getGiftListConfig(Integer giftId, Integer giftType, String uid) {
        ActorData actorData = actorMgr.getActorData(uid);
        if (actorData == null) {
            actorData = actorMgr.getActorUidContainDel(uid);
        }
        List<String> androidVestChannel = new ArrayList<>();
        try {
            androidVestChannel = vestService.getGiftVestConfigChannel(actorData.getUid());
        } catch (Exception e) {
            log.info("get gift vest config error e={}", e.getMessage(), e);
        }
        String channel = "";
        if (androidVestChannel.contains(actorData.getChannel())) {
            channel = actorData.getChannel();
        }
        return giftListConfigDao.getGiftListConfigByGiftId(giftId, giftType, channel);
    }

    public GiftListConfigData getGiftListConfigByRobot(Integer giftId, Integer giftType) {
        return giftListConfigDao.getGiftListConfigByGiftId(giftId, giftType, ChannelEnum.CDE.getName());
    }

    public SendGiftSuccessMsgData createMsgDataForMq(ActorData fromActor, ActorData toActor, int giftId, int giftNumber, String roomId, BigDecimal cost, BigDecimal gain, String chapterId, Integer fromType, Integer isLuck) {
        SendGiftSuccessMsgData msgData = new SendGiftSuccessMsgData();
        msgData.setUid(fromActor.getUid());
        msgData.setToUid(toActor.getUid());
        msgData.setTime(DateHelper.getCurrTime());
        msgData.setGiftId((long) giftId);
        msgData.setNum(giftNumber);
        msgData.setRequestId(MDC.get("request_id"));
        msgData.setRoomId(roomId);
        msgData.setCost(cost.longValue());
        msgData.setRealCost(cost.toString());
        msgData.setGain(gain.longValue());
        msgData.setRealGain(gain.toString());
        HostInfoData toHostInfo = hostInfoDao.getHostInfo(toActor.getUid());
        msgData.setAgentId(toHostInfo != null ? toHostInfo.getAgentId() : 0);
        msgData.setChannel(fromActor.getChannel());
        msgData.setToChannel(toActor.getChannel());
        msgData.setChapterId(chapterId);
        if (fromType == null) {
            fromType = GiftFromTypeConstant.GIFT_FROM_PARTY;
        }
        msgData.setFromType(fromType);
        msgData.setIsLuck(isLuck);
        return msgData;
    }

    public SendGiftSuccessMsgData createMsgDataForMq(ActorData fromActor, ActorData toActor, GiftRecordData giftRecordData, int giftId, int giftNumber, String roomId, BigDecimal cost, BigDecimal gain, String chapterId, Integer fromType) {
        SendGiftSuccessMsgData msgData = new SendGiftSuccessMsgData();
        msgData.setUid(fromActor.getUid());
        msgData.setToUid(toActor.getUid());
        msgData.setTime(DateHelper.getCurrTime());
        msgData.setGiftId((long) giftId);
        msgData.setNum(giftNumber);
        msgData.setRequestId(MDC.get("request_id"));
        msgData.setRoomId(roomId);
        msgData.setCost(cost.longValue());
        msgData.setRealCost(cost.toString());
        msgData.setGain(gain.longValue());
        msgData.setRealGain(gain.toString());
        HostInfoData toHostInfo = hostInfoDao.getHostInfo(toActor.getUid());
        msgData.setAgentId(toHostInfo != null ? toHostInfo.getAgentId() : 0);
        msgData.setChannel(fromActor.getChannel());
        msgData.setToChannel(toActor.getChannel());
        msgData.setChapterId(chapterId);
        if (fromType == null) {
            fromType = GiftFromTypeConstant.GIFT_FROM_PARTY;
        }
        msgData.setFromType(fromType);
        msgData.setIsLuck(giftRecordData.getIsLuck());
        msgData.setPid(giftRecordData.getPid());
        return msgData;
    }

    private Integer getDayOfMonth() {
        String lastDay = DateHelper.UTC.getDateByDeltaDay(0);
        String[] split = lastDay.split("-");
        String day = split[2];
        return Integer.parseInt(day);
    }

    public void dealConfession(ActorData fromActor, ActorData toActor, GiftListConfigData configData, String desc, int number) {
        if (configData.getConfession() == 0 && configData.getBulletin() == 0) {
            return;
        }
        GiftNoticeRecordData data = new GiftNoticeRecordData();
        data.setFromUid(fromActor.getUid());
        data.setToUid(toActor.getUid());
        data.setGiftId(configData.getGiftId());
        data.setCtime((long) DateHelper.getCurrentTime());
        data.setFromType(configData.getGiftType());
        data.setNumber(number);
        data.setValid(1);
        if (configData.getConfession() == 1) {
            data.setNoticeDetail(desc);
            data.setRecordType(NOTICE_CONFESSION_TYPE);
            giftNoticeRecordDao.save(data);
            giftRedis.delGiftNoticeList(data.getRecordType());
        }
        if (configData.getBulletin() == 1) {
            data.setRecordType(NOTICE_BULLETIN_TYPE);
            data.setNoticeDetail("");
            giftNoticeRecordDao.save(data);
            giftRedis.delGiftNoticeList(data.getRecordType());
        }
        giftRedis.delCarouselList();
    }

    public List<GiftNoticeRecordData> getGiftNoticeList(int type, int page, int size) {
        //第一页做缓存
        int start = (page - 1) * size;
        if (page > 1) {
            return giftNoticeRecordDao.getList(type, start, size);
        }
        String giftNoticeList = giftRedis.getGiftNoticeList(type);
        if (giftNoticeList != null) {
            return JSONObject.parseArray(giftNoticeList, GiftNoticeRecordData.class);
        } else {
            List<GiftNoticeRecordData> list = giftNoticeRecordDao.getList(type, start, size);
            giftRedis.saveGiftNoticeList(type, CollectionUtils.isEmpty(list) ? "" : JSONObject.toJSONString(list));
            return list;
        }
    }

    public List<GiftNoticeRecordData> getCarouselList(int size) {
        String carouselList = giftRedis.getCarouselList();
        if (carouselList != null) {
            return JSONObject.parseArray(carouselList, GiftNoticeRecordData.class);
        } else {
            List<GiftNoticeRecordData> list = giftNoticeRecordDao.getLatelyRecord(size);
            giftRedis.saveCarouselList(CollectionUtils.isEmpty(list) ? "" : JSONObject.toJSONString(list));
            return list;
        }
    }

    public List<String> getPropGiftListConfig(String uid) {
        ConfigDTO configDTO = new ConfigDTO();
        configDTO.setUid(uid);
        configDTO.setKey(AppConfigKeyConstant.GIFT_LIST_PROP_CONFIG);
        configDTO.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        String strVal = configApi.getStrVal(configDTO);
        if (StringUtils.isEmpty(strVal) || "0".equals(strVal)) {
            return new ArrayList<>();
        }
        return Arrays.asList(strVal.split(","));
    }

    public double getSceneRateConfig(String uid, int fromType) {
        HostGiftRateConfig config = configApi.getJavaBeanVal(new ConfigDTO(uid, AppConfigKeyConstant.HOST_GIFT_RAIN_CONFIG, AppConfigKeyConstant.STATUS_SERVER), HostGiftRateConfig.class);
        if (config == null) {
            return 0.3;
        }
        HostGiftRateConfig.GiftSceneRateConfig giftSceneRateConfig = config.getConfigList().stream().filter(data -> data.getScene() == fromType).findFirst().orElse(null);
        if (giftSceneRateConfig == null) {
            return 0.3;
        }
        return giftSceneRateConfig.getRate();
    }

    public boolean checkConfessionDesc(HttpEnvData dto, String desc, ActorData fromActor) {
        if (StringUtils.isEmpty(desc)) {
            return false;
        }
        if (ChannelEnum.isTikkoChannel(fromActor.getChannel())) {
            return dirtySensitiveWordFilter.dirtyWordToShuMei(fromActor.getUid(), desc, DetectSceneConstant.WORDS_GIFT_CONFESSION);
        }
        return dirtyService.containerDirty(dto.getUid(), desc);
    }

    /**
     * 添加麦位贡献
     *
     * @param roomId       房间ID
     * @param chapterId    房间场次ID
     * @param receiverUid  收礼方用户ID
     * @param senderUid    送礼方用户ID
     * @param contribution 贡献值(礼物价值)
     * @return 是否添加成功
     */
    public void addMicContribution(String roomId, String chapterId, String receiverUid,
                                   String senderUid, BigDecimal contribution) {
        if (StringUtils.isEmpty(roomId) || StringUtils.isEmpty(receiverUid) ||
                StringUtils.isEmpty(senderUid) || contribution == null) {
            return;
        }
        giftRedis.addOrUpdateMicContribution(roomId, chapterId, receiverUid, senderUid, contribution.doubleValue());
    }

    /**
     * 获取前三贡献者的头像列表
     *
     * @param roomId      房间ID
     * @param chapterId   房间场次ID
     * @param receiverUid 收礼方用户ID
     * @return 前三贡献者的头像列表
     */
    public List<String> getTopThreeContributorsHeadIcons(String roomId, String chapterId, String receiverUid) {
        if (StringUtils.isEmpty(roomId) || StringUtils.isEmpty(receiverUid) || StringUtils.isEmpty(chapterId)) {
            log.warn("Invalid parameters when getting top three contributors. roomId={}, chapterId={}, receiverUid={}",
                    roomId, chapterId, receiverUid);
            return new ArrayList<>();
        }
        Set<String> topThree = giftRedis.getTopContributors(roomId, chapterId, receiverUid, 3);
        if (CollectionUtils.isEmpty(topThree)) {
            return new ArrayList<>();
        }
        List<String> headIcons = new ArrayList<>(topThree.size());
        for (String uid : topThree) {
            ActorData actorData = actorMgr.getActorDataFromCache(uid);
            if (actorData != null && !StringUtils.isEmpty(actorData.getHeadIcon())) {
                if (lordService.decideHaveEnterRoomInvisible(actorData.getUid())) {
                    headIcons.add(LordConstant.INVISIBLE_HEAD);
                } else {
                    headIcons.add(actorData.getHeadIcon());
                }
            }
        }
        return headIcons;
    }

    /**
     * 分页获取麦位贡献榜
     *
     * @param roomData    房间信息
     * @param receiverUid 收礼方用户ID
     * @return 分页的贡献榜数据，HostIncomeVO
     */
    public List<HostIncomeVO> getMicContributionRankBy(RoomData roomData, String receiverUid) {
        if (roomData == null || StringUtils.isEmpty(roomData.getRoomId()) || StringUtils.isEmpty(receiverUid)) {
            return new ArrayList<>();
        }
        // 获取分页数据
        Set<ZSetOperations.TypedTuple<String>> contributorSet =
                giftRedis.getContributorsByPage(roomData.getRoomId(), roomData.getChapterId(), receiverUid);
        List<HostIncomeVO> contributorList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(contributorSet)) {
            for (ZSetOperations.TypedTuple<String> tuple : contributorSet) {
                String uid = tuple.getValue();
                Double score = tuple.getScore();
                if (score == null) {
                    continue;
                }
                HostIncomeVO incomeVO = new HostIncomeVO(uid, score.intValue(), score);
                contributorList.add(incomeVO);
            }
        }
        return contributorList;
    }
}
