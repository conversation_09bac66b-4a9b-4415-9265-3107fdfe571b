package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.common.service.HttpOperation;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.*;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.constant.WarnName;
import com.quhong.core.enums.AppMsgType;
import com.quhong.core.enums.GenderTypeEnum;
import com.quhong.core.exceptions.GoldException;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.timers.DelayTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.CustomerServiceUtils;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.*;
import com.quhong.dao.datas.*;
import com.quhong.dao.datas.db.GameConfigInfoData;
import com.quhong.dao.datas.log.RewardedRecordData;
import com.quhong.data.SendConfigData;
import com.quhong.data.appConfig.GameAllRoomImConfig;
import com.quhong.data.appConfig.RewardSendImConditionConfig;
import com.quhong.data.bo.AutoMessageTriggerBO;
import com.quhong.data.bo.RewardMessageBO;
import com.quhong.data.bo.game.GameImSendConfigBO;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.data.dto.InnerGiftSendDTO;
import com.quhong.data.dto.InnerItemsAddDTO;
import com.quhong.data.dto.InnerSendMsgDTO;
import com.quhong.data.dto.money.CurrencyAddDTO;
import com.quhong.data.dto.user.auth.level.AddScoreDTO;
import com.quhong.data.dto.user.auth.lord.GiveLordDTO;
import com.quhong.data.thData.LogBroadcastLogData;
import com.quhong.data.vo.RewardLoopBroadcastVO;
import com.quhong.data.vo.SmashEggMsgVo;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.CurrencyEnum;
import com.quhong.enums.RewardItemType;
import com.quhong.mq.enums.MQConstant;
import com.quhong.mq.service.BaseMqProducer;
import com.quhong.mq.service.MedalIssueSender;
import com.quhong.players.ActorMgr;
import com.quhong.redis.PlayerRedis;
import com.quhong.report.EventReport;
import com.quhong.service.common.RewardService;
import com.quhong.service.medal.BaseMedalService;
import com.quhong.service.medal.MedalIssueService;
import com.quhong.service.money.CurrencyService;
import com.quhong.service.user.auth.level.LevelApi;
import com.quhong.service.user.auth.lord.LordApi;
import com.quhong.utils.DistributeLockUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.utils.StringUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Description:奖励发放service
 *
 * <AUTHOR>
 * @date 2021/10/28 14:33
 */
@Slf4j
@Component
@Lazy
public class GiveOutRewardService {
    private static final String MATCH_CARD_TEXT = "#activityName to get #nums matching cards";

    private static final String AWARD_NUM = "#num";

    private static final String GOLD_DESC = "get coins #activityName";

    private static final String ACTIVITY_NAME_PLACEHOLDER = "#activityName";

    public static String TEXT_WIN = "Win";
    public static String TEXT_GET = "Get";

    public static String TEXT_SO_LUCKY = "So Luck and won";
    @Resource
    private ActorBackpackDao actorBackpackDao;
    @Resource
    private ActiveRewardRecordDao activeRewardRecordDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private ItemsApi itemsApi;
    @Resource
    private ActorMgr actorMgr;
    @Resource
    private ImApi imApi;
    @Resource
    private HttpOperation httpOperation;
    @Resource
    private ConfigApi configApi;
    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedis;
    @Resource
    private PlayerRedis playerRedis;
    @Resource
    private MemberDao memberDao;
    @Resource
    private BossLevelService bossLevelService;
    @Resource
    private EventReport eventReport;
    @Resource
    private CdnUtils cdnUtils;
    @Resource
    private CurrencyService currencyService;
    @Resource
    private RoomItemsService roomItemsService;
    @Resource
    private RewardService rewardService;
    @Resource
    private LevelApi levelApi;
    @Resource
    private LordApi lordApi;
    @Resource
    private BaseMedalService baseMedalService;
    @Resource
    private MedalIssueSender medalIssueSender;
    @Resource
    private GameConfigInfoDao gameConfigInfoDao;
    @Resource
    private MedalIssueService medalIssueService;
    @Resource
    private LordService lordService;
    @Resource
    private RewardedRecordDao rewardedRecordDao;
    @Resource
    private TicketService ticketService;
    @Resource
    private DistributeLockUtils distributeLockUtils;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private GiftApi giftApi;
    @Resource
    private HostConfigDao hostConfigDao;
    @Resource
    private ItemsChangeService itemsChangeService;
    @Resource
    private BaseMqProducer baseMqProducer;
    @Resource
    private BaseZoneService baseZoneService;
    public void giveEventReward(String uid, List<RewardInfoData> rewards, Integer eventCode, String changeDesc) {
        giveEventReward(uid, rewards, eventCode, changeDesc, "");
    }

    /**
     * 活动使用发奖方法
     *
     * @param uid        uid
     * @param rewards    奖励列表
     * @param eventCode  事件码
     * @param changeDesc 变动描述
     */
    public void giveEventReward(String uid, List<RewardInfoData> rewards, Integer eventCode, String changeDesc, String descDetail) {
        // 发奖品
        giveOutReward(uid, rewards);
        // 物品变动上报数数
        itemsChangeService.itemChangeReport(uid, rewards, eventCode, DateHelper.getCurrTime(), changeDesc, descDetail);
        // 活动成本预算
        rewards.forEach(reward -> baseMqProducer.sendMq(MQConstant.ACTIVITY_EXCHANGE, MQConstant.EVENT_COST_COUNT_ROUTE, reward));
    }


    public void giveOutReward(String uid, List<RewardInfoData> rewardList) {
        giveOutReward(uid, rewardList, "");
    }

    public void giveOutReward(String uid, List<RewardInfoData> rewardList, Integer logId) {
        giveOutReward(uid, rewardList, logId, "", ActType.ACTIVITY_REWARD);
    }


    public void giveOutReward(String uid, List<RewardInfoData> rewardList, String activityName) {
        giveOutReward(uid, rewardList, 0, activityName, ActType.ACTIVITY_REWARD);
    }

    /**
     * Description: 发放奖励
     *
     * @param uid          用户uid
     * @param rewardList   需要发放的奖励的信息
     * @param logId        对应活动日志表id
     * @param activityName 活动名
     * @param actType      金币奖励类型
     * <AUTHOR>
     * @date 2021/10/28 14:46
     */
    public void giveOutReward(String uid, List<RewardInfoData> rewardList, Integer logId, String activityName, int actType) {
        try {
            log.debug("start to giveOutReward method. uid={},rewardList={},logId={}", uid, rewardList, logId);
            if (CollectionUtils.isEmpty(rewardList)) {
                log.error("the reward list returned is empty. uid={},rewardList={},logId={}", uid, rewardList, logId);
                return;
            }
            if (logId == null) {
                logId = 0;
            }
            //奖励发放
            giveOut(uid, rewardList, logId, activityName, actType);
        } catch (Exception e) {
            log.error("giveOutReward is failed : uid={},rewardList={},logId={},message={}", uid, rewardList, logId, e.getMessage(), e);
            monitorSender.info("common", "reward distribution abnormal", e.getMessage() + Arrays.toString(e.getStackTrace()));
        }
    }

    public void giveOut(String uid, List<RewardInfoData> rewardList, Integer logId, String activityName, int actType) {
        giveOut(uid, rewardList, logId, activityName, actType, false);
    }

    public void giveOut(String uid, List<RewardInfoData> rewardList, Integer logId, String activityName, int actType, boolean onlyGiveOut) {
        for (RewardInfoData data : rewardList) {
            if (StringUtils.isEmpty(activityName)) {
                activityName = ActivityTypeEnum.getByCode(data.getActivityType()).getName();
            }

            //将数据插入active_reward_record 表
            ActiveRewardRecordData recordData = saveOneToActiveRewardRecord(uid, data, logId);
            boolean flag;
            switch (data.getType()) {
                case RewardItemType.GOLD:
                case RewardItemType.DIAMOND:
                case RewardItemType.GAME_COIN:
                    //金币
                    flag = giveOutGold(uid, data, actType, logId, activityName);
                    break;
                case RewardItemType.GIFT:
                    //礼物
                    flag = distributedLockGift(uid, data);
                    break;
                case RewardItemType.MATCH_CARD:
                    // 匹配卡
                    flag = giveOutMatchCard(uid, activityName, data, recordData);
                    break;
                case RewardItemType.VIP_DAYS:
                    // vip天数
                    flag = distributeLockVip(uid, data);
                    break;
                case RewardItemType.SEAT_FRAME:
                case RewardItemType.BUBBLE_FRAME:
                case RewardItemType.ENTER_EFFECT:
                case RewardItemType.ENTRY_EFFECT:
                case RewardItemType.DESIGNATION:
                case RewardItemType.PROFILE_CARD:
                    // 语聊房物品奖励
                    flag = giveRoomItem(uid, data);
                    break;
                case RewardItemType.LORD_DAYS:
                    // 贵族天数奖励下发
                    flag = giveLordDays(uid, data);
                    break;
                case RewardItemType.LEVEL_SCORE:
                    flag = giveLevelScore(uid, activityName, data);
                    break;
                case RewardItemType.MEDAL:
                    flag = giveMedal(uid, data);
                    break;
                case RewardItemType.STARRY_TICKET:
                    flag = giveTicket(uid, data);
                    break;
                case RewardItemType.GIFT_DIAMONDS:
                    flag = giveGiftDiamonds(uid, data);
                    break;
                case RewardItemType.HOST_LABEL:
                    flag = giveHostLabel(uid, data);
                    break;
                default:
                    //没有对应奖励发放
                    log.info("no corresponding prize information uid={},rewardList={},logId={}", uid, rewardList, logId);
                    flag = false;
                    break;
            }
            updateActiveRewardRecord(recordData, flag);
            if (onlyGiveOut) {
                continue;
            }
            if (ActivityTypeEnum.isSendFullServiceMessageActivityType(data.getActivityType())) {
                dealSendFullServiceMessage(uid, data);
            }
            // 奖励推送广播(奖励发放结束后推送)
            sendBigRewardIM(uid, data, logId);
            //(砸蛋才发送)判断是否大奖，发送全房间频道im消息
            if (data.getActivityType() == ActivityTypeEnum.SMASH_EGG.getCode()) {
                TimerService.getService().addDelay(new DelayTask(ActivityTypeEnum.SMASH_EGG.getPushDelayDuration()) {
                    @Override
                    protected void execute() {
                        sendMsg(uid, data);
                    }
                });
            }
        }
    }

    private boolean giveGiftDiamonds(String uid, RewardInfoData data) {
        InnerGiftSendDTO dto = new InnerGiftSendDTO();
        dto.setUid(CustomerServiceUtils.getSendGiftOfficialUid());
        dto.setAid(uid);
        dto.setGiftId(data.getDataId());
        dto.setFromType(GiftFromTypeConstant.GIFT_DIAMONDS);
        dto.setCid("");
        dto.setNumber(data.getNums());
        giftApi.cooperationGiftSend(dto);
        return true;
    }

    private boolean giveHostLabel(String uid, RewardInfoData data) {
        //先判断有没有
        int labelType = data.getDataId() > 0 ? data.getDataId() : HostConfigDao.RECOMMEND_LABEL;
        long expireTime = DateHelper.getCurrentTime() + TimeUnit.DAYS.toSeconds(data.getNums());
        HostConfigData hostConfigFromDB = hostConfigDao.getHostConfigFormDB(uid, HostConfigDao.LIST_LABEL_CONFIG);
        if (hostConfigFromDB == null) {
            HostConfigData hostConfigData = new HostConfigData();
            hostConfigData.setUid(uid);
            hostConfigData.setConfigKey(HostConfigDao.LIST_LABEL_CONFIG);
            hostConfigData.setValueInt(labelType);
            hostConfigData.setValueStr(expireTime + "");
            hostConfigData.setMtime(DateHelper.getCurrentTime());
            hostConfigDao.saveHostConfig(hostConfigData);
            log.info("insert label success. uid={}", uid);
        } else {
            HostConfigData configData = new HostConfigData();
            configData.setUid(uid);
            configData.setConfigKey(HostConfigDao.LIST_LABEL_CONFIG);
            configData.setValueInt(labelType);
            configData.setValueStr(expireTime + "");
            configData.setMtime(DateHelper.getCurrentTime());
            hostConfigDao.saveHostConfig(configData);
            log.info("update label success. uid={}", uid);
        }
        return true;
    }

    private boolean giveTicket(String uid, RewardInfoData data) {
        try {
            int ticketType = data.getType() % 2000;
            return ticketService.giveTicket(uid, ticketType, data.getDataId(), data.getNums());
        } catch (Exception e) {
            log.error("give out service, give ticket error,uid={},reward={}", uid, JSON.toJSONString(data), e);
            monitorSender.customMarkdown(WarnName.COMMON, "give out reward service, give ticket error, uid=" + uid);
        }
        return false;
    }

    private boolean giveOutMatchCard(String uid, String activityName, RewardInfoData data, ActiveRewardRecordData recordData) {
        boolean flag;
        String desc = MATCH_CARD_TEXT;
        desc = desc.replace("#activityName", activityName);
        desc = desc.replace("#nums", String.valueOf(data.getNums()));
        int sceneId = ActivityTypeEnum.getByCode(data.getActivityType()).getItemSceneId();
        flag = giveOutCallCard(uid, recordData, data, desc, sceneId, activityName);
        return flag;
    }

    private boolean giveMedal(String uid, RewardInfoData data) {
        try {
            if (data.getNums() == 0) {
                data.setNums(-1);
            }
            medalIssueService.sendMedal(uid, data.getDataId(), data.getNums());
            return true;
        } catch (Exception e) {
            log.error("give out service, give medal error,uid={},reward={}", uid, JSON.toJSONString(data), e);
            monitorSender.customMarkdown(WarnName.COMMON, "give out reward service, give medal error, uid=" + uid);
        }
        return false;
    }

    /**
     * 给予等级积分
     * dataId === LevelType
     */
    private boolean giveLevelScore(String uid, String activityName, RewardInfoData data) {
        // 等级积分下发
        try {
            AddScoreDTO addScoreDTO = new AddScoreDTO();
            addScoreDTO.setUid(uid);
            addScoreDTO.setScore((long) data.getNums());
            addScoreDTO.setFromType(activityName);
            addScoreDTO.setLevelType(data.getDataId() == 0 ? null : data.getDataId());
            levelApi.addLevelScore(addScoreDTO);
            return true;
        } catch (Exception e) {
            log.error("give out service, give level score error,uid={},reward={}", uid, JSON.toJSONString(data), e);
            monitorSender.customMarkdown(WarnName.COMMON, "give out reward service, give level score error, uid=" + uid);
        }
        return false;
    }

    /**
     * 下发贵族奖励
     *
     * @param uid  用户uid
     * @param data 奖品实体
     * @return true成功 false失败
     */
    private boolean giveLordDays(String uid, RewardInfoData data) {
        try {
            GiveLordDTO dto = GiveLordDTO.builder()
                    .uid(uid)
                    .lordLevel(data.getDataId())
                    .giveLordTime(Duration.ofDays(data.getNums().longValue()).getSeconds())
                    .build();
            return lordApi.giveLord(dto);
        } catch (Exception e) {
            log.error("give out service, give lord days error,uid={},reward={}", uid, JSON.toJSONString(data), e);
            monitorSender.customMarkdown(WarnName.COMMON, "give out reward service, give lord days error,uid=" + uid);
        }
        return false;
    }

    private void dealSendFullServiceMessage(String uid, RewardInfoData data) {
        RewardMessageBO bo = new RewardMessageBO();
        bo.setUid(uid);
        bo.setWonType(data.getType());
        bo.setType(RewardService.GAME);
        bo.setWonNums(data.getNums());
        bo.setRealWonNum(data.fetchRealNums());
        bo.setGiftId(data.getDataId());
        bo.setIcon(data.getIcon());
        bo.setRoomId(data.getRoomId());
        bo.setItemsName(data.getName());
        bo.setActivityType(data.getActivityType());
        rewardService.sendFullServiceIM(bo);
    }

    /**
     * 赠送语聊房物品  数量为0代表永久
     *
     * @param uid  用户uid
     * @param data 奖励信息
     * @return 是否成功
     */
    private boolean giveRoomItem(String uid, RewardInfoData data) {
        try {
            boolean isWear = ActivityTypeEnum.isRoomItemWear(data.getActivityType());
            ActorData actorData = actorMgr.getActorData(uid);
            int roomItemId = data.getDataId();
            roomItemsService.giveRoomItem(actorData, roomItemId, data.getType() % 1000, data.getNums(), RoomItemAccessConstant.ACTIVITY, isWear);
            return true;
        } catch (Exception e) {
            log.error("give out reward service, give room item error, uid={}, reward={}", uid, JSON.toJSONString(data), e);
            monitorSender.customMarkdown(WarnName.COMMON, "give out reward service, give room item error，uid=" + uid);
        }
        return false;
    }

    private void sendBigRewardIM(String uid, RewardInfoData data, Integer logId) {
        int showType = getShowType(uid, data);
        InnerSendMsgDTO dto = new InnerSendMsgDTO();
        dto.setUid(uid);
        SendConfigData sendConfigData = new SendConfigData();
        sendConfigData.setContainMe(true);
        if (decideAllRoom(uid, data, ActivityTypeEnum.getByCode(data.getActivityType()))) {
            dto.setToAllRoom(true);
        } else {
            if (fillImSendDTO(data, showType, dto, sendConfigData)) return;
        }
        dto.setConfigData(sendConfigData);
        dto.setCmd(AppMsgType.REWARD_SEND_MSG);
        ActivityTypeEnum enumByCode = ActivityTypeEnum.getByCode(data.getActivityType());
        RewardLoopBroadcastVO rewardMsg = createRewardMsg(uid, data, showType, logId, enumByCode);
        if (rewardMsg == null) {
            return;
        }
        if (dto.isToAllRoom()) {
            rewardMsg.setAllRoom(1);
        }
        log.info("createRewardMsg vo={}", JSONObject.toJSONString(rewardMsg));
        dto.setBody(rewardMsg.toBody());

        //获取推送延迟时间
        int delayTime = getDelayTime(data.getActivityType());
        TimerService.getService().addDelay(new DelayTask(delayTime) {
            @Override
            protected void execute() {
                doSendIm(dto, uid, data);
            }
        });
    }

    private boolean fillImSendDTO(RewardInfoData data, int showType, InnerSendMsgDTO dto, SendConfigData sendConfigData) {
        switch (showType) {
            case ActivityBigRewardSendShowTypeEnum.NO_SHOW:
                return true;
            case ActivityBigRewardSendShowTypeEnum.SHOW_TO_ROOM_ONLY:
                // 只推送全房间
                if (StringUtils.isEmpty(data.getRoomId())) {
                    return true;
                }
                dto.setRoomId(data.getRoomId());
                break;
            default:
                // 推送所有在线用户
                Set<String> userSet = playerRedis.getUserOnlineSet();
                sendConfigData.setToUidSet(userSet);
                // 清除历史记录缓存
                mainRedis.delete("reward_loop_broadcast_list");
                break;
        }
        return false;
    }

    /**
     * 获取im消息延迟推送时间
     *
     * @param activityType 活动类型
     * @return im消息延迟推送时间
     * @see ActivityTypeEnum 活动类型
     */
    private int getDelayTime(Integer activityType) {
        return ActivityTypeEnum.getByCode(activityType).getPushDelayDuration();
    }

    private void doSendIm(InnerSendMsgDTO dto, String uid, RewardInfoData data) {
        imApi.sendMsg(dto);
        LogBroadcastLogData logData = new LogBroadcastLogData(uid, data);
        eventReport.track(logData);
    }

    private RewardLoopBroadcastVO createRewardMsg(String uid, RewardInfoData data, int showType, Integer logId, ActivityTypeEnum enumByCode) {
        if (!enumByCode.isGame()) {//v5.9基础体验 通知改为游戏全服通知
            return null;
        }
        GameConfigInfoData gameConfig = gameConfigInfoDao.getGameConfigInfoByName(enumByCode.getName());
        if (gameConfig == null) {
            return null;
        }
        //用户做冷却
        String key = "str:game_reward_notice:" + uid;
        if (Boolean.TRUE.equals(mainRedis.hasKey(key))) {
            return null;
        }
        mainRedis.opsForValue().set(key, "1", 3, TimeUnit.SECONDS);
        ActorData currActor = actorMgr.getActorData(uid);
        RewardLoopBroadcastVO vo = new RewardLoopBroadcastVO();
        vo.setActivityType(data.getActivityType());
        vo.setSendChannel(currActor.getChannel());
        vo.setActivityName(enumByCode.getName());
        vo.setRewardIcon(getRewardIcon(data, currActor.getChannel()));
        vo.setUid(uid);
        vo.setHead(cdnUtils.replaceUrlDomain(currActor.getChannel(), currActor.getHeadIcon(), 0));
        vo.setUsername(currActor.getName());
        vo.setIsMember(memberDao.isMember(uid) ? 1 : 0);
        vo.setBossLevel(bossLevelService.getBossLevel(uid));
        vo.setRewardType(data.getType());
        vo.setRewardNums(data.getNums());
        vo.setRewardTime(data.getCtime());
        vo.setRoomId(data.getRoomId());
        if (!StringUtils.isEmpty(vo.getRoomId())) {
            vo.setAid(RoomUtils.getRoomOwnerId(vo.getRoomId()));
            List<String> imZoneShowCountryList = baseZoneService.getImZoneShowCountryList(vo.getRoomId());
            vo.setShowCountryList(imZoneShowCountryList);
        }
        vo.setScene(data.getScene());
        vo.setGameId(logId.toString());
        vo.setGender(currActor.getGender());
        vo.setHostGrade(levelApi.getLevelByUid(uid, GenderTypeEnum.HOST.getType()));
        vo.setUserLevel(levelApi.getLevelByUid(uid, GenderTypeEnum.USER.getType()));
        vo.setShowType(showType);
        String linkText = TEXT_WIN;
        if (vo.getRewardType() != RewardItemType.GOLD) {
            linkText = TEXT_GET;
        }
        vo.setRoomLinkText("Congratulations! #username won #rewardName in #activityName Click to view>");
        vo.setGameId(gameConfig.getGameId());
        vo.setGameType(gameConfig.getGameType());
        vo.setShowCountryList(new ArrayList<>());
        vo.setRewardName(data.getName());
        vo.setLinkText(linkText);
        if (!StringUtils.isEmpty(data.getRoomId()) && data.getRoomId().startsWith("r")) {
            if (lordService.decideHaveEnterRoomInvisible(uid)) {
                vo.invisible();
            }
        }
        return vo;
    }

    private boolean decideAllRoom(String uid, RewardInfoData data, ActivityTypeEnum enumByCode) {
        ConfigDTO configDTO = new ConfigDTO(uid, AppConfigKeyConstant.GAME_ALL_ROOM_IM_CONFIG, AppConfigKeyConstant.STATUS_SERVER);
        GameAllRoomImConfig config = configApi.getJavaBeanVal(configDTO, GameAllRoomImConfig.class);
        if (config == null || CollectionUtils.isEmpty(config.getConfigSet())) {
            return false;
        }
        GameImSendConfigBO configBO = config.getConfigSet().stream()
                .filter(configData -> configData.getEventCode() == enumByCode.getCode())
                .findFirst().orElse(null);
        if (configBO == null) {
            return false;
        }
        if (data.getType().equals(RewardItemType.GOLD)) {
            return data.getNums() >= configBO.getMinWinGold();
        } else {
            return configBO.getWinRewardIds().contains(data.getDataId().toString());
        }
    }


    private String getRewardIcon(RewardInfoData data, String channel) {
        String rewardIcon = null;
        switch (data.getType()) {
            case RewardItemType.GOLD:
                rewardIcon = RewardItemType.GOLD_ICON;
                break;
            case RewardItemType.GIFT:
                rewardIcon = RewardItemType.ROSE_GIFT_ICON;
                break;
            case RewardItemType.MATCH_CARD:
                rewardIcon = RewardItemType.CALL_CARD_ICON;
                break;
            case RewardItemType.VIP_DAYS:
                rewardIcon = RewardItemType.VIP_ICON;
                break;
            default:
                break;
        }
        rewardIcon = cdnUtils.replaceUrlDomain(channel, rewardIcon, 0);
        return rewardIcon;
    }


    /**
     * 获取推送类型
     *
     * @param uid  uid
     * @param data 奖励信息
     * @return showType
     */
    private int getShowType(String uid, RewardInfoData data) {
        int roomSend = 0;
        int homeIndexSend = 0;
        // 是否发送到房间
        if (ActivityTypeEnum.getByCode(data.getActivityType()).isNeedSendRoomIm() &&
                validSendByConfigKey(uid, data, AppConfigKeyConstant.ROOM_REWARD_SEND_IM_CONFIG)) {
            roomSend = 1;
        }
        //是否发送到首页
        if (ActivityTypeEnum.getByCode(data.getActivityType()).isNeedSendBroadcastMessageIm() &&
                validSendByConfigKey(uid, data, AppConfigKeyConstant.REWARD_LOOP_BROADCAST)) {
            homeIndexSend = 2;
        }
        return roomSend + homeIndexSend;
    }

    /**
     * 校验是否发送
     */
    private boolean validSendByConfigKey(String uid, RewardInfoData data, String configKey) {
        ConfigDTO configDTO = new ConfigDTO();
        configDTO.setUid(uid);
        configDTO.setKey(configKey);
        configDTO.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        RewardSendImConditionConfig configData = configApi.getJavaBeanVal(configDTO, RewardSendImConditionConfig.class);
        if (configData == null) {
            return false;
        }
        //判定奖励结果是否符合条件
        GameImSendConfigBO configBO = configData.getConfigSet().stream()
                .filter(config -> data.getActivityType().equals(config.getEventCode()))
                .findFirst().orElse(null);
        boolean needSendIm = false;
        if (configBO == null) {
            return needSendIm;
        }
        needSendIm = checkNeedSendIm(data, configBO, needSendIm);
        return needSendIm;
    }

    private static boolean checkNeedSendIm(RewardInfoData data, GameImSendConfigBO configBO, boolean needSendIm) {
        switch (ActivityTypeEnum.getByCode(data.getActivityType())) {
            case SMASH_EGG:
                Set<Integer> rewardIdSet = StringUtils.getIdSetToIdStr(configBO.getWinRewardIds(), ",");
                if ((configBO.getMinWinGold() <= data.getNums() && data.getType() == RewardItemType.GOLD) || rewardIdSet.contains(data.getId())) {
                    needSendIm = true;
                }
                break;
            case LUCKY_GIFT:
                if (configBO.getMinWinGold() <= data.getNums() || configBO.getMinWinRate() <= data.getRewardMultiplier()) {
                    needSendIm = true;
                }
                break;
            default:
                if (configBO.getMinWinGold() <= data.getNums()) {
                    needSendIm = true;
                }
                break;
        }
        return needSendIm;
    }

    private boolean distributeLockVip(String uid, RewardInfoData data) {
        String key = "uid_" + uid + "_give_vip";
        return distributeLockUtils.distributeMethod(new GiveDTO().setUid(uid).setData(data),
                key, this::giveOutVip);
//        DistributeLock lock = new DistributeLock(key);
//        try {
//            boolean ret = lock.tryLock(5, TimeUnit.SECONDS);
//            if (!ret) {
//                logger.error("give out vip distributed lock exception, no get this lock,uid={}", uid);
//                String code = "common";
//                String desc = "give out vip distributed lock exception";
//                String detail = "give out vip distributed lock exception uid=" + uid;
//                monitorSender.info(code, desc, detail);
//                return false;
//            }
//            flag = giveOutVip(uid, data);
//            return flag;
//        } catch (Exception e) {
//            logger.error("give out vip distributed lock exception,uid={},{}", uid, e.getMessage(), e);
//            String code = "common";
//            String desc = "give out vip distributed lock exception";
//            String detail = "give out vip distributed lock exception uid=" + uid + ",e=" + e.getMessage() + e;
//            monitorSender.info(code, desc, detail);
//        } finally {
//            lock.unlock();
//        }
//        return false;
    }

    private boolean distributedLockGift(String uid, RewardInfoData data) {
        boolean flag;
        String key = "uid_" + uid + "_give_gift_" + data.getDataId();
        return distributeLockUtils.distributeMethod(new GiveDTO().setUid(uid).setData(data),
                key, this::giveOutGift);
//        DistributeLock lock = new DistributeLock(key);
//        try {
//            boolean ret = lock.tryLock(5, TimeUnit.SECONDS);
//            if (!ret) {
//                logger.error("give out gift distributed lock exception, no get this lock,uid={}", uid);
//                String code = "common";
//                String desc = "give out gift distributed lock exception";
//                String detail = "give out gift distributed lock exception uid=" + uid;
//                monitorSender.info(code, desc, detail);
//                return false;
//            }
//            flag = giveOutGift(uid, data);
//            return flag;
//        } catch (Exception e) {
//            logger.error("give out gift distributed lock exception,uid={},{}", uid, e.getMessage(), e);
//            String code = "common";
//            String desc = "give out gift distributed lock exception";
//            String detail = "give out gift distributed lock exception uid=" + uid + ",e=" + e.getMessage() + e;
//            monitorSender.info(code, desc, detail);
//        } finally {
//            lock.unlock();
//        }
//        return false;
    }

    /**
     * Description: 大奖发送全频房间消息
     *
     * @param uid  uid
     * @param data 奖励信息
     * <AUTHOR>
     * @date 2021/11/9 11:05
     */
    private void sendMsg(String uid, RewardInfoData data) {
        log.info("grand prize send full frequency room message,uid={},reward={}", uid, data);
        if (data.getIsGrandPrize() == 0) {
            if (ServerConfiguration.isProduct()) {
                return;
            }
        }
        ActorData actorData = actorMgr.getActorData(uid);
        if (actorData == null) {
            log.error("grand prize send full frequency room message,actor is not exist,uid={}", uid);
            return;
        }
        try {
            InnerSendMsgDTO dto = msgAssembly(uid, data, actorData);
            imApi.sendMsg(dto);
        } catch (Exception e) {
            log.error("grand prize send full frequency room message,message sending exception: uid={},e.msg={}", uid, e.getMessage(), e);
            String code = "common";
            String desc = "message sending exception";
            String detail = "grand prize send full frequency room message,message sending exception: uid=" + uid + ",e=" + e;
            monitorSender.info(code, desc, detail);
        }

    }

    private InnerSendMsgDTO msgAssembly(String uid, RewardInfoData data, ActorData actorData) throws Exception {
        InnerSendMsgDTO dto = new InnerSendMsgDTO();
        dto.setToAllRoom(true);
        dto.setCmd(AppMsgType.SMASH_EGG_PUSH_MSG);
        SmashEggMsgVo vo = new SmashEggMsgVo();
        vo.setFromUid(uid);
        vo.setFromName(actorData.getName());
        vo.setAwardType(data.getType());
        vo.setNum(data.getNums());
        vo.setText(data.getName() + " X" + AWARD_NUM);
        if (data.getType() == 4) {
            vo.setRewardName(data.getName() + " +" + data.getNums() + " days");
            vo.setText(data.getName() + " +" + AWARD_NUM + " days");
        } else {
            vo.setRewardName(data.getName() + " X" + data.getNums());
        }
        if (!StringUtils.isEmpty(data.getRoomId()) && data.getRoomId().startsWith("r")) {
            if (lordService.decideHaveEnterRoomInvisible(uid)) {
                vo.invisible();
            }
        }
        //砸蛋运营位type
        vo.setType(5);
        vo.setContent(vo.getFromName() + " for participating in smashing eggs and getting " + vo.getRewardName() + "  Click to view>");
        dto.setBody(vo.doToBody());
        return dto;
    }

    private boolean giveOutCallCard(String uid, ActiveRewardRecordData recordData, RewardInfoData data, String desc, int sceneId, String activityName) {
        try {
            InnerItemsAddDTO dto = new InnerItemsAddDTO();
            int currentTime = DateHelper.getCurrentTime();
            String tableSuffix = DateHelper.UTC.getTableSuffix(new Date(currentTime * 1000L));
            //分表后缀,表内id
            dto.setRelatedId(tableSuffix + "," + recordData.getId());
            if (StringUtils.isEmpty(desc)) {
                desc = "activityType:" + activityName + " to get" + data.getNums() + " matching cards";
            }
            dto.setDesc(desc);
            dto.setConfigId(1);
            dto.setChangeValue(data.getNums());
            dto.setScene(sceneId);
            dto.setUid(uid);
            itemsApi.add(dto);
            return true;
        } catch (Exception e) {
            log.error("Error:giveOutCallCard method.activity_type={}, uid={},msg={}", data.getActivityType(), uid, e.getMessage(), e);
        }
        return false;
    }


    private boolean giveOutVip(GiveDTO dto) {
        try {
            if (StringUtils.isEmpty(dto.getUid()) || dto.getData().getNums() <= 0) {
                return false;
            }
            ActorData actorData = actorMgr.getActorData(dto.getUid());
            if (actorData == null) {
                return false;
            }

            return giveOutVip(actorData, dto.getData().getNums());
        } catch (Exception e) {
            log.error("give out vip reward error. {}", e.getMessage(), e);
        } finally {
            sendVipMQ(dto.getUid());
        }
        return false;
    }

    public boolean giveOutVip(ActorData actorData, int num) {
        //计算时间
        int addVipTime;
        if (ServerConfiguration.isTest()) {
            addVipTime = num * 60;
        } else {
            addVipTime = num * 86400;
        }
        int currentTime = DateHelper.getCurrentTime();
        RewardedRecordData rewardedRecord = rewardedRecordDao.getRewardedRecord(actorData.getUid(), ActType.REWARD_VIP);
        if (rewardedRecord == null) {
            giveVipInitData(actorData, currentTime, addVipTime);
            return true;
        }
        int valid = rewardedRecord.getValid();
        if (checkVipValidAndDealUpdateData(valid, rewardedRecord, currentTime, addVipTime)) return false;
        rewardedRecordDao.updateData(rewardedRecord);
        rewardedRecordDao.deleteRewardVipRedis(actorData.getUid());
        return true;
    }

    private static boolean checkVipValidAndDealUpdateData(int valid, RewardedRecordData rewardedRecord, int currentTime, int addVipTime) {
        switch (valid) {
            case RewardedRecordDao.INVALID://无效状态0，将状态改为有效 重置vip奖励时间为本次赠送时间，  更新mtime
                rewardedRecord.setMtime(currentTime);
                rewardedRecord.setValid(RewardedRecordDao.INACTIVE);
                rewardedRecord.setRewardGolds(addVipTime);
                break;
            case RewardedRecordDao.INACTIVE://未激活状态99，状态不改变 增加vip奖励时间，时长为本次赠送时长， 更新mtime
                rewardedRecord.setMtime(currentTime);
                rewardedRecord.setRewardGolds(rewardedRecord.getRewardGolds() + addVipTime);
                break;
            case RewardedRecordDao.VALID://已激活状态1
                int remainder = rewardedRecord.getMtime() + rewardedRecord.getRewardGolds() - currentTime;
                if (remainder > 0) {//未用完状态： 只需增加vip奖励时间，时长为本次赠送时长
                    rewardedRecord.setRewardGolds(rewardedRecord.getRewardGolds() + addVipTime);
                } else {//已用完状态： 将状态改为未激活， 重置vip奖励时间为本次赠送时间， 更新mtime
                    rewardedRecord.setMtime(currentTime);
                    rewardedRecord.setValid(RewardedRecordDao.INACTIVE);
                    rewardedRecord.setRewardGolds(addVipTime);
                }
                break;
            default:
                return true;
        }
        return false;
    }

    private void giveVipInitData(ActorData actorData, int currentTime, int addVipTime) {
        RewardedRecordData rewardedRecord;
        rewardedRecord = new RewardedRecordData();
        rewardedRecord.setKeyword(actorData.getUid());
        rewardedRecord.setCtime(currentTime);
        rewardedRecord.setMtime(currentTime);
        rewardedRecord.setRewardCode(ActType.REWARD_VIP);
        rewardedRecord.setValid(RewardedRecordDao.INACTIVE);
        rewardedRecord.setRewardGolds(addVipTime);
        rewardedRecordDao.insertData(rewardedRecord);
        rewardedRecordDao.deleteRewardVipRedis(actorData.getUid());
    }

    private void updateActiveRewardRecord(ActiveRewardRecordData recordData, Boolean flag) {
        recordData.setMtime((long) DateHelper.getCurrentTime());
        if (flag) {
            recordData.setStatus(2);
        } else {
            recordData.setStatus(3);
        }
        activeRewardRecordDao.update(recordData);
    }

    /**
     * Description: 插入数据到活动奖励记录表
     *
     * @param uid   用户uid
     * @param data  奖品信息
     * @param logId 对应活动日志表id
     * @return com.quhong.dao.datas.ActiveRewardRecordData
     * <AUTHOR>
     * @date 2021/10/29 17:58
     */
    private ActiveRewardRecordData saveOneToActiveRewardRecord(String uid, RewardInfoData data, Integer logId) {
        ActiveRewardRecordData dto = new ActiveRewardRecordData();
        dto.setUid(uid);
        dto.setRewardId(data.getId());
        dto.setActiveType(data.getActivityType());
        dto.setReleatedId(logId);
        dto.setRewardType(data.getType());
        dto.setDataId(data.getDataId());
        dto.setNums(data.getNums());
        dto.setRewardMultiplier(data.getRewardMultiplier());
        dto.setRoomId(data.getRoomId());
        dto.setScene(data.getScene());
        dto.setStatus(1);
        long currTime = DateHelper.getCurrTime();
        dto.setCtime(currTime);
        dto.setMtime(currTime);
        activeRewardRecordDao.insert(dto);
        return dto;
    }

    /**
     * Description: 发放礼物
     *
     * <AUTHOR>
     * @date 2021/10/29 10:33
     */
    private boolean giveOutGift(GiveDTO dto) {
        log.debug("start to giveOutGift method.uid={},rewardInfoData={}", dto.getUid(), JSON.toJSONString(dto.getData()));
        try {
            String dataId = String.valueOf(dto.getData().getDataId());
            Integer nums = dto.getData().getNums();
            ActorBackpackData backpackData = actorBackpackDao.getDataByDataIdAndUid(dto.getUid(), 1, dataId);
            if (ObjectUtils.isEmpty(backpackData)) {
                backpackData = new ActorBackpackData();
                backpackData.setCtime(DateHelper.getCurrentTime());
                backpackData.setMtime(DateHelper.getCurrentTime());
                backpackData.setUid(dto.getUid());
                backpackData.setDataId(dataId);
                backpackData.setDataType(1);
                backpackData.setExpiryDate(0);
                backpackData.setId(new ObjectId().toString());
                backpackData.setNum(nums);
                backpackData.setDel(0);
                backpackData.setStatus(0);
                backpackData.setDataName("");
                actorBackpackDao.saveData(backpackData);
            } else {
                backpackData.setNum(backpackData.getNum() + nums);
                backpackData.setMtime(DateHelper.getCurrentTime());
                actorBackpackDao.updateData(backpackData);
            }
            return true;
        } catch (Exception e) {
            log.error("give out gift reward failed. uid={} {}", dto.getUid(), e.getMessage(), e);
        }
        return false;
    }

    /**
     * Description: 金币发放
     *
     * @param uid          用户uid
     * @param data         奖励信息
     * @param actType      金币奖励类型
     * @param activityName 活动名称
     * <AUTHOR>
     * @date 2021/10/29 10:33
     * @see ActType
     */
    private boolean giveOutGold(String uid, RewardInfoData data, int actType, int logId, String activityName) {
        log.info("start to giveOutGold method.uid={},rewardInfoData={}", uid, data);
        String desc = GOLD_DESC.replace(ACTIVITY_NAME_PLACEHOLDER, "(" + activityName + ")");
        String goldActDesc = data.getGoldActDesc();
        if (!StringUtils.isEmpty(goldActDesc)) {
            desc = goldActDesc;
        }
        CurrencyAddDTO currencyAddDTO = new CurrencyAddDTO().generateRequestId();
        currencyAddDTO.setUid(uid);
        currencyAddDTO.setRealChangeNum(String.valueOf(Math.abs(Double.parseDouble(data.fetchRealNums()))));
        currencyAddDTO.setActDesc(desc);
        currencyAddDTO.setActType(actType);
        currencyAddDTO.setCid("");
        currencyAddDTO.setOperator("giveOutGold");
        currencyAddDTO.setOtherUid("");
        currencyAddDTO.setChangeRelation(data.getChangeRelation());
        if (StringUtils.isEmpty(data.getRoomId())) {
            currencyAddDTO.setRelatedId(data.getGameExposure());
        } else {
            currencyAddDTO.setRelatedId(data.getRoomId());
        }
        int currencyCode = CurrencyEnum.CURRENCY1.getCurrencyCode();
        if (data.getType() == RewardItemType.DIAMOND) {
            currencyCode = CurrencyEnum.CURRENCY2.getCurrencyCode();
        }
        if (data.getType() == RewardItemType.GAME_COIN) {
            currencyCode = CurrencyEnum.CURRENCY4.getCurrencyCode();
        }
        try {
            if (data.getActivityType() != ActivityTypeEnum.TASK_AWARD_DISTRIBUTE.getCode()) {
//                /**等待后续配套修改**/goldService.add(uid, data.getNums(), actType, logId, desc, activityType);
                currencyAddDTO.setActId(logId);
                currencyAddDTO.setSegmentCode(data.getActivityType());
//                currencyCode = CurrencyEnum.CURRENCY1.getCurrencyCode();
//                /**等待后续配套修改**/goldService.add(uid, data.getNums(), actType, logId, desc, activityType);
            } else {
                //任务单独处理
                currencyAddDTO.setActId(0);
                currencyAddDTO.setSegmentCode(logId);
//                currencyCode = GenderTypeEnum.HOST.getType().equals(actorData.getGender()) ? CurrencyEnum.CURRENCY2.getCurrencyCode() : CurrencyEnum.CURRENCY1.getCurrencyCode();

//                /**等待后续配套修改**/goldService.add(uid, data.getNums(), actType, 0, desc, logId);
            }
            currencyAddDTO.setCurrencyCode(currencyCode);

            currencyService.add(currencyAddDTO);
            return true;
        } catch (GoldException e) {
            log.error("close reward failed. uid={} {}", uid, e.getMessage(), e);
        }
        return false;
    }

    private void sendVipMQ(String uid) {
        AutoMessageTriggerBO bo = AutoMessageTriggerBO.newBuilder().strategyType(AutoMessageStrategyType.VIP).userId(uid).build();
        rabbitTemplate.convertAndSend(MQConstant.AUTO_MESSAGE_EXCHANGE, MQConstant.AUTO_MESSAGE_PRE + ".vipRewardVip", JSON.toJSONString(bo));
    }

    @Data
    @Accessors(chain = true)
    public static class GiveDTO {
        private String uid;
        private RewardInfoData data;
    }
}
