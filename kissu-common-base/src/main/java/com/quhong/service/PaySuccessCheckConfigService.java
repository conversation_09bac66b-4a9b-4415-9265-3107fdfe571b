package com.quhong.service;

import com.quhong.constant.ABSideTypeConstant;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.constant.RepurchaseTypeConstant;
import com.quhong.data.appConfig.RepurchaseAndAbLogicConfigVO;
import com.quhong.data.dto.ConfigDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/9 18:04
 * @description:
 */
@Component
public class PaySuccessCheckConfigService {

    @Resource
    private ConfigApi configApi;


    /**
     * 需要一个方法，下面的复购作为一个类型，AB面新旧逻辑也作为一个类型
     * 根据渠道获取配置配置内容为{复购:开/关， AB面:新/旧}，复购是否开关，
     */
    boolean getConfigValue(String channel, String key) {
        RepurchaseAndAbLogicConfigVO config = getLogicConfig(channel);
        if (RepurchaseTypeConstant.REPURCHASE_KEY.equals(key)) {
            return config.getRepurchaseWithDefault();
        } else if (ABSideTypeConstant.AB_SIDE_KEY.equals(key)) {
            return config.getAbSideWithDefault();
        }
        return false;
    }



    /**
     * 根据渠道获取配置配置内容为{复购:开/关， AB面:新/旧}，复购是否开关，
     * 这个实体对象专门设计
     * 默认返回结构
     * {
     *     "repurchase": false,
     *     "abSide": false
     * }
     */
    private RepurchaseAndAbLogicConfigVO getLogicConfig(String channel) {
        ConfigDTO configDTO = new ConfigDTO();
        configDTO.setChannel(channel);
        configDTO.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        configDTO.setKey(AppConfigKeyConstant.REPURCHASE_AND_AB_LOGIC_CONFIG);
        RepurchaseAndAbLogicConfigVO config = configApi.getJavaBeanVal(configDTO, RepurchaseAndAbLogicConfigVO.class);
        if (config == null) {
            // 返回默认配置
            config = new RepurchaseAndAbLogicConfigVO();
            config.setRepurchase(false);
            config.setAbSide(false);
        }
        return config;
    }
}
