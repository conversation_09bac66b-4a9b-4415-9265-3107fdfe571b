package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.ApiResult;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.enums.GenderTypeEnum;
import com.quhong.dao.ActiveRewardRecordDao;
import com.quhong.dao.MemberDao;
import com.quhong.dao.datas.ActiveRewardRecordData;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.slave.mapper.log.SlotMachineLogSlaveMapper;
import com.quhong.data.appConfig.RewardSendImConditionConfig;
import com.quhong.data.bo.game.GameImSendConfigBO;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.data.vo.RewardLoopBroadcastVO;
import com.quhong.data.vo.resp.HistoryRewardLoopResp;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.RewardItemType;
import com.quhong.players.ActorMgr;
import com.quhong.redis.PlayerRedis;
import com.quhong.service.common.EventBaseRecordService;
import com.quhong.service.user.auth.level.LevelApi;
import com.quhong.utils.RoomUtils;
import com.quhong.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;


/**
 * @ClassName RewardLoopBroadcastService
 * @Description TODO
 * <AUTHOR>
 * @Date 2:55 PM 2022/9/1
 * @Version 1.0
 **/
@Component
@Lazy
public class RewardLoopBroadcastService {
    public static final Logger logger = LoggerFactory.getLogger(RewardLoopBroadcastService.class);

    public static String TEXT_WIN = "Win";
    public static String TEXT_GET = "Get";

    @Resource
    private PlayerRedis playerRedis;
    @Resource
    private ConfigApi configApi;
    @Resource
    private ActiveRewardRecordDao activeRewardRecordDao;
    @Resource
    private ActorMgr actorMgr;
    @Resource
    private GrayService grayService;
    @Resource
    private SlotMachineLogSlaveMapper slotMachineLogSlaveMapper;
    @Resource
    private MemberDao memberDao;
    @Resource
    private BossLevelService bossLevelService;
    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedis;

    @Resource
    private CdnUtils cdnUtils;

    @Resource
    private LevelApi levelApi;
    @Resource
    private EventBaseRecordService eventBaseRecordService;
    private static final int LUCKY_GIFT_EVENT_CODE = ActivityTypeEnum.LUCKY_GIFT.getCode();

    public String bigLuckyGiftRewardRecordKey() {
        return eventBaseRecordService.listRecordKey(String.valueOf(LUCKY_GIFT_EVENT_CODE), "big_reward_loop_broadcast");
    }

    public ApiResult<HistoryRewardLoopResp> getHistoryList(HttpEnvData dto) throws Exception {
        HistoryRewardLoopResp resp = new HistoryRewardLoopResp();
        RewardSendImConditionConfig configData = getRewardLoopBroadcastConditionConfig(dto.getUid());
        logger.debug("configData={},requestId={}", JSON.toJSONString(configData), dto.getRequestId());
        boolean isAdmin = actorMgr.isAdmin(dto.getUid());
        if (isAdmin) {
            logger.info("actor is admin, not show reward loop, uid={},requestId={}", dto.getUid(), dto.getRequestId());
            resp.setGrayscale(0);
            return ApiResult.getOk(resp);
        }

        ActorData actorData = actorMgr.getActorData(dto.getUid());
        if (actorData == null) {
            logger.error("actor is not exists,uid={}", dto.getUid());
            return ApiResult.getError(HttpCode.ACTOR_NOT_EXIST);
        }

        boolean hit = grayService.isGray(actorData, configData);
        if (configData.getStart() == 0 && configData.getEnd() == 0) {//完全关闭
            hit = false;
        }
        if (hit) {
            logger.debug("gray is hit,rid={},requestId={}", actorData.getRid(), dto.getRequestId());
            List<RewardLoopBroadcastVO> list = getRedisLoopBroadcastVOList(configData, dto.getChannel());
            resp.setHistories(list);
            resp.setGrayscale(1);
        } else {
            resp.setGrayscale(0);
        }

        return ApiResult.getOk(resp);
    }

    private List<RewardLoopBroadcastVO> getRedisLoopBroadcastVOList(RewardSendImConditionConfig configData, String currChannel) throws Exception {
        String redisKey = "reward_loop_broadcast_list";
        String dataStr = mainRedis.opsForValue().get(redisKey);
        if (!StringUtils.isEmpty(dataStr)) {
            return JSON.parseArray(dataStr, RewardLoopBroadcastVO.class);
        }
        List<RewardLoopBroadcastVO> list = getLoopBroadcastVOList(configData, currChannel);
        if (!CollectionUtils.isEmpty(list)) {
            mainRedis.opsForValue().set(redisKey, JSON.toJSONString(list), 1, TimeUnit.DAYS);
        }
        return list;
    }

    private List<RewardLoopBroadcastVO> getLoopBroadcastVOList(RewardSendImConditionConfig configData, String currChannel) {
        List<RewardLoopBroadcastVO> list = new ArrayList<>();
        configData.getConfigSet().parallelStream()
                //  过滤掉不需要显示的
                .filter(config -> config.getHistoryShowNum() != null && config.getHistoryShowNum() > 0)
                .forEach(config -> fillVO(list, config, currChannel));

        list.sort(Comparator.comparing(RewardLoopBroadcastVO::getRewardTime));
        return list.subList(0, Math.min(list.size(), 30));
    }

    private void fillVO(List<RewardLoopBroadcastVO> list, GameImSendConfigBO config, String currChannel) {
        switch (ActivityTypeEnum.getByCode(config.getEventCode())) {
            case SMASH_EGG:
                Set<Integer> rewardIdSet = StringUtils.getIdSetToIdStr(config.getWinRewardIds(), ",");
                fillRewardRecord(config.getEventCode(), rewardIdSet, config.getMinWinGold(), null, config.getHistoryShowNum(), list, currChannel);
                break;
            case LUCKY_GIFT:
//                fillRewardRecord(config.getEventCode(), null, config.getMinWinGold(), config.getMinWinRate(), config.getHistoryShowNum(), list, currChannel);
                // 从redis获取
                dealLuckyGiftBigRewardRecord(list, config, currChannel);
                break;
            default:
                fillRewardRecord(config.getEventCode(), config.getMinWinGold(), config.getHistoryShowNum(), list, currChannel);

        }
    }

    private void dealLuckyGiftBigRewardRecord(List<RewardLoopBroadcastVO> list, GameImSendConfigBO config, String currChannel) {
        String key = bigLuckyGiftRewardRecordKey();
        List<ActiveRewardRecordData> redisList = eventBaseRecordService.lastHistory(key, ActiveRewardRecordData.class, config.getHistoryShowNum());
        if (CollectionUtils.isEmpty(redisList)) {
            return;
        }
        logger.debug("lucky gift big reward record redis data={},activityType={}", JSON.toJSONString(redisList), config.getEventCode());
        redisList.forEach(data -> fillBroadcastVO(list, currChannel, data));
    }

    private void fillRewardRecord(int activityType, Integer minWinGold, Integer showNums, List<RewardLoopBroadcastVO> list, String currChannel) {
        fillRewardRecord(activityType, null, minWinGold, null, showNums, list, currChannel);
    }

    private void fillRewardRecord(int activityType, Set<Integer> rewardIdSet, Integer minWinGold, Integer minWinRate, Integer showNums, List<RewardLoopBroadcastVO> list, String currChannel) {
        try {
            List<ActiveRewardRecordData> dataList = activeRewardRecordDao.queryListBy(activityType, rewardIdSet, minWinGold, minWinRate, showNums);
            if (!CollectionUtils.isEmpty(dataList)) {
                logger.debug("reward record db data={},activityType={}", JSON.toJSONString(dataList), activityType);
                dataList.forEach(data -> fillBroadcastVO(list, currChannel, data));
            }

        } catch (Exception e) {
            logger.error("error to query  RewardLoopBroadcast ,msg={}", e.getMessage(), e);
        }

    }

    private void fillBroadcastVO(List<RewardLoopBroadcastVO> list, String currChannel, ActiveRewardRecordData data) {
        ActorData awardActor = actorMgr.getActorData(data.getUid());
        if (awardActor == null) {
            return;
        }
        String rewardIcon = getRewardIcon(data);
        if (rewardIcon == null) {
            return;
        }
        RewardLoopBroadcastVO vo = new RewardLoopBroadcastVO();
        vo.setRewardIcon(rewardIcon);
        vo.setRewardTime(Math.toIntExact(data.getCtime()));
        vo.setActivityName(ActivityTypeEnum.getByCode(data.getActiveType()).getName());
        vo.setActivityType(data.getActiveType());
        vo.setUid(data.getUid());
        vo.setHead(cdnUtils.replacePrivateHeadAndUrlDomain(currChannel, awardActor.getHeadIcon(), 0, awardActor.getUid()));
        vo.setUsername(awardActor.getName());
        vo.setIsMember(memberDao.isMember(data.getUid()) ? 1 : 0);
        vo.setBossLevel(bossLevelService.getBossLevel(data.getUid()));
        vo.setRewardType(data.getRewardType());
        vo.setRewardNums(data.getNums());
        vo.setRoomId(data.getRoomId());
        vo.setScene(data.getScene());
        vo.setGameId(data.getReleatedId().toString());
        vo.setGender(awardActor.getGender());
        vo.setHostGrade(levelApi.getLevelByUid(awardActor.getUid(), GenderTypeEnum.HOST.getType()));
        vo.setUserLevel(levelApi.getLevelByUid(awardActor.getUid(), GenderTypeEnum.USER.getType()));
        if (!StringUtils.isEmpty(vo.getRoomId())) {
            vo.setAid(RoomUtils.getRoomOwnerId(vo.getRoomId()));
        }
        String linkText = TEXT_WIN;
        if (vo.getRewardType() != RewardItemType.GOLD) {
            linkText = TEXT_GET;
        }
        vo.setLinkText(linkText);
        list.add(vo);
    }


    private String getRewardIcon(ActiveRewardRecordData data) {
        String rewardIcon = null;
        switch (data.getRewardType()) {
            case RewardItemType.GOLD:
                rewardIcon = RewardItemType.GOLD_ICON;
                break;
            case RewardItemType.GIFT:
                rewardIcon = RewardItemType.ROSE_GIFT_ICON;
                break;
            case RewardItemType.MATCH_CARD:
                rewardIcon = RewardItemType.CALL_CARD_ICON;
                break;
            case RewardItemType.VIP_DAYS:
                rewardIcon = RewardItemType.VIP_ICON;
                break;
            default:
                break;
        }
        return rewardIcon;
    }

    public RewardSendImConditionConfig getRewardLoopBroadcastConditionConfig(String uid) {
        ConfigDTO configDTO = new ConfigDTO();
        configDTO.setUid(uid);
        configDTO.setKey(AppConfigKeyConstant.REWARD_LOOP_BROADCAST);
        configDTO.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        RewardSendImConditionConfig configData = configApi.getJavaBeanVal(configDTO, RewardSendImConditionConfig.class);
        if (configData == null) {
            configData = new RewardSendImConditionConfig();
        }
        return configData;
    }

}
