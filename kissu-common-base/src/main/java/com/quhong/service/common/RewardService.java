package com.quhong.service.common;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.constant.ItemsTypeReportConstant;
import com.quhong.constant.LordConstant;
import com.quhong.core.cache.CacheMap;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.enums.AppMsgType;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.AwardsKeyConfigDao;
import com.quhong.dao.GameConfigInfoDao;
import com.quhong.dao.RoomDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.RoomData;
import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.dao.datas.db.AwardsKeyConfigData;
import com.quhong.dao.datas.db.GameConfigInfoData;
import com.quhong.dao.datas.db.LordUserInfoData;
import com.quhong.dao.datas.log.AwardsKeyRecordData;
import com.quhong.dao.mapper.log.AwardsKeyRecordMapper;
import com.quhong.data.SendConfigData;
import com.quhong.data.appConfig.SendFullServiceIMConfig;
import com.quhong.data.bo.RewardMessageBO;
import com.quhong.data.bo.game.GameImSendConfigBO;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.data.dto.InnerSendMsgDTO;
import com.quhong.enums.ActivityTypeEnum;
import com.quhong.enums.RewardItemType;
import com.quhong.moderatoin.ModerationService;
import com.quhong.players.ActorMgr;
import com.quhong.redis.PlayerRedis;
import com.quhong.service.*;
import com.quhong.socket.msg.object.ItemDetailData;
import com.quhong.socket.msg.server.PersonalActivityPopMsg;
import com.quhong.socket.msg.server.RewardFullMsg;
import com.quhong.socket.msg.server.UniversalActivityPopMsg;
import com.quhong.socket.msg.server.UniversalFullServiceNoticeMsg;
import com.quhong.utils.RoomUtils;
import com.quhong.utils.StringUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName RewardService
 * <AUTHOR>
 * @date 2023/4/17 13:59
 */
@Component
@Lazy
@Slf4j
public class RewardService {
    private static final CacheMap<String, SendFullServiceIMConfig> CACHE_MAP = new CacheMap<>(TimeUnit.MINUTES.toMillis(5));
    private static final String CACHE_KEY = "send_full_service_im_config_";
    public static final int BIG_GIFT = 1;
    public static final int LUCKY_GIFT = 2;
    public static final int GAME = 3;
    public static final int CONFESSION_GIFT = 4;

    public static final Set<Integer> ZONE_TYPE_SET = new LinkedHashSet<Integer>() {{
        add(BIG_GIFT);
        add(GAME);
        add(LUCKY_GIFT);
    }};

    public static final String DEFAULT_TEXT = "#name get #rewardIcon #rewardName #num in #activityName";
    public static final String PERSONAL_ACTIVITY_DEFAULT_TEXT = "Congratulations in the #activityName Event you get";
    public static final String UNIVERSAL_ACTIVITY_DEFAULT_TEXT = "Congratulations #name #head in the #activityName Event get";

    @Resource
    private ActorMgr actorMgr;
    @Resource
    private ConfigApi configApi;
    @Resource
    private ImApi imApi;
    @Resource
    private PlayerRedis playerRedis;
    @Resource
    private GameConfigInfoDao gameConfigInfoDao;
    @Resource
    private RoomDao roomDao;
    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedis;
    @Resource
    private LordService lordService;
    @Resource
    private ModerationService moderationService;
    @Resource
    private ItemsChangeService itemsChangeService;
    @Resource
    private BaseZoneService baseZoneService;


    @Resource
    private AwardsKeyConfigDao awardsKeyConfigDao;
    @Resource
    private GiveOutRewardService giveOutRewardService;
    @Resource
    private AwardsKeyRecordMapper awardsKeyRecordMapper;
    @Resource
    private RoomFloatingImService roomFloatingImService;

    @Data
    @Accessors(chain = true)
    public static class GiveAwardsKeyDTO {
        private String uid;
        private String awardsKey;
        private int eventCode;
        // 数数使用
        private String changeDesc;
        private String descDetail;
        // 金币流水描述
        private String actDesc;
        /**
         * 操作人
         */
        private String operator;
        /**
         * 活动链接
         */
        private String eventUrl;
    }

    /**
     * 通过礼包key下发奖励
     *
     * @return 是否成功
     */
    public boolean giveAwardsKeyReward(GiveAwardsKeyDTO dto) {
        // 1. 通过礼包key获取礼包配置信息
        AwardsKeyConfigData awardsKeyData = awardsKeyConfigDao.queryByAwardsKey(dto.getAwardsKey());
        if (awardsKeyData == null || awardsKeyData.getValid() != 1) {
            log.error("礼包key配置不存在或已失效: {}", dto.getAwardsKey());
            return false;
        }

        // 普通类礼包key，直接获取配置的奖励列表
        List<AwardInfo> awardInfoList = awardsKeyData.getAwardInfoList();

        // 2. 转换奖励信息，将AwardInfo映射为RewardInfoData
        List<RewardInfoData> rewardInfoDataList = awardInfoList.stream()
                .map(awardInfo -> new RewardInfoData(dto.getEventCode(), awardInfo).setGoldActDesc(dto.getActDesc()))
                .collect(Collectors.toList());

        // 3. 使用通用奖励下发功能发放奖励
        giveOutRewardService.giveEventReward(dto.getUid(), rewardInfoDataList, dto.getEventCode(), dto.getChangeDesc(), dto.getDescDetail());

        // 成功发放奖励后，记录发放记录
        AwardsKeyRecordData recordData = initRecord(dto.getUid(), dto.getAwardsKey(), dto.getEventCode(), dto.getChangeDesc(), dto.getOperator());
        awardsKeyRecordMapper.insertSelective(recordData);

        // 4. 处理通知逻辑
        handleNotifications(dto.getUid(), dto.getEventCode(), awardInfoList, dto.getChangeDesc(), dto.getDescDetail(), dto.getEventUrl());
        return true;
    }

    private static AwardsKeyRecordData initRecord(String uid, String awardsKey, int eventCode, String changeDesc, String operator) {
        return new AwardsKeyRecordData()
                .setAwardsKey(awardsKey)
                .setUid(uid)
                .setChangeDesc(changeDesc)
                .setEventCode(eventCode)
                .setCtime(DateHelper.getCurrTime())
                .setOperator(operator);
    }


    /**
     * 处理各种通知逻辑
     */
    private void handleNotifications(String uid, int eventCode, List<AwardInfo> awardInfoList,
                                     String changeDesc, String descDetail, String eventUrl) {
        ActorData currActor = actorMgr.getActorData(uid);
        if (currActor == null) {
            return;
        }

        // 检查是否有任何奖励项目需要发送通知，如果都不需要则直接返回
        boolean allNoticeFalse = awardInfoList.stream()
                .noneMatch(awardInfo ->
                        awardInfo.getPersonalPopup() || awardInfo.getFullServicePopup()
                                || awardInfo.getRoomFloating() || awardInfo.getFullServiceNotice());
        if (allNoticeFalse) {
            return;
        }
        ActivityTypeEnum eventEnum = ActivityTypeEnum.getByCode(eventCode);
        // 个人弹窗通知
        List<RewardInfoData> needPersonalPopList = awardInfoList.stream().filter(AwardInfo::getPersonalPopup)
                .map(awardInfo -> new RewardInfoData(eventCode, awardInfo))
                .collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(needPersonalPopList)) {
            sendPersonalActivityPopMsg(uid, needPersonalPopList, eventUrl, eventEnum.getName(), "", "");
        }

        // 全服弹窗通知
        List<RewardInfoData> needFullServicePopList = awardInfoList.stream().filter(AwardInfo::getFullServicePopup)
                .map(awardInfo -> new RewardInfoData(eventCode, awardInfo))
                .collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(needFullServicePopList)) {
            sendUniversalActivityPopMsg(currActor, needFullServicePopList, eventUrl, eventEnum.getName(), "", "");
        }

        // 房间飘屏通知
        List<RewardInfoData> needRoomFloatingImList = awardInfoList.stream().filter(AwardInfo::getRoomFloating)
                .map(awardInfo -> new RewardInfoData(eventCode, awardInfo))
                .collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(needRoomFloatingImList)) {
            needRoomFloatingImList.forEach(rewardInfoData ->
                    roomFloatingImService.sendRoomFloatingIm(currActor, rewardInfoData.getNums(), rewardInfoData.getIcon(),
                            rewardInfoData.getName(), "to join event", eventCode, eventUrl));
        }
        // 全服横幅
        List<RewardInfoData> needFullServiceNoticeList = awardInfoList.stream().filter(AwardInfo::getFullServiceNotice)
                .map(awardInfo -> new RewardInfoData(eventCode, awardInfo))
                .collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(needFullServiceNoticeList)) {
            needFullServiceNoticeList.forEach(rewardInfoData -> sendUniversalFullServiceNoticeMsg(uid, rewardInfoData, eventUrl, eventEnum.getName(),
                    "#name get #rewardIcon #rewardName #num in " + eventEnum.getName(), true));
        }


    }


    /**
     * 发送通用全服通知
     * #head #name get #rewardIcon #rewardName #num in #activtyName
     * #rewardIcon #rewardName #num 可不传值
     *
     * @param uid            uid
     * @param rewardInfoData 奖励对象
     * @param webUrl         跳转链接
     * @param activityName   活动名
     */
    public void sendUniversalFullServiceNoticeMsg(String uid, RewardInfoData rewardInfoData, String webUrl, String activityName, String text, boolean useHead) {
        ActorData actorData = actorMgr.getActorData(uid);
        if (actorData == null) {
            return;
        }
        if (StringUtils.isEmpty(text)) {
            text = DEFAULT_TEXT;
        }
        UniversalFullServiceNoticeMsg noticeMsg = getUniversalFullServiceNoticeMsg(actorData, rewardInfoData, webUrl, activityName, text, useHead);
        InnerSendMsgDTO msg = new InnerSendMsgDTO();
        SendConfigData configData = new SendConfigData();
        configData.setContainMe(true);
        configData.setSendUid(actorData.getUid());
        configData.setToUidSet(playerRedis.getOnlineSet());
        msg.setConfigData(configData);
        msg.setUid(actorData.getUid());
        msg.setBody(noticeMsg.toBody());
        msg.setCmd(AppMsgType.UNIVERSAL_FULL_SERVICE_NOTICE_MSG);
        imApi.sendMsg(msg);
    }

    public void sendUniversalFullServiceNoticeMsg(String uid, RewardInfoData rewardInfoData, String webUrl, String activityName) {
        sendUniversalFullServiceNoticeMsg(uid, rewardInfoData, webUrl, activityName, DEFAULT_TEXT, false);
    }

    private UniversalFullServiceNoticeMsg getUniversalFullServiceNoticeMsg(ActorData actorData, RewardInfoData rewardInfoData,
                                                                           String webUrl, String activityName, String text, boolean useHead) {
        UniversalFullServiceNoticeMsg msg = new UniversalFullServiceNoticeMsg();
        msg.setUid(actorData.getUid());
        if (useHead) {
            msg.setHead(moderationService.dealRankHeadModeration(actorData));//头像鉴黄
        }
        msg.setName(actorData.getName());
        msg.setSendChannel(actorData.getChannel());
        msg.setWebUrl(webUrl);
        msg.setNoticeText(text);
        msg.setRewardIcon(rewardInfoData.getIcon());
        msg.setRewardName(rewardInfoData.getName());
        if (rewardInfoData.getNums() != null && rewardInfoData.getNums() > 0) {
            msg.setNum("*" + rewardInfoData.getNums());
        }
        msg.setActivityName(activityName);
        return msg;
    }

    /**
     * 个人展示活动弹窗 仅支持 用户名和活动名文案替换
     *
     * @param uid          用户uid
     * @param rewardList   奖励内容
     * @param webUrl       跳转链接
     * @param activityName 活动名
     * @param text         文案
     * @param tip          物料注解
     */
    public void sendPersonalActivityPopMsg(String uid, List<RewardInfoData> rewardList, String webUrl, String activityName, String text, String tip) {
        if (StringUtils.isEmpty(text)) {
            text = PERSONAL_ACTIVITY_DEFAULT_TEXT;
        }
        PersonalActivityPopMsg popMsg = new PersonalActivityPopMsg();
        popMsg.setActivityName(activityName);
        popMsg.setUrl(webUrl);
        popMsg.setTip(tip);
        popMsg.setText(text);
        popMsg.setItemList(getItemDetailList(rewardList));
        InnerSendMsgDTO msg = new InnerSendMsgDTO();
        SendConfigData configData = new SendConfigData();
        configData.setSendUid(uid);
        msg.setConfigData(configData);
        msg.setUid(uid);
        msg.setToUid(uid);
        msg.setBody(popMsg.toBody());
        msg.setCmd(AppMsgType.PERSONAL_ACTIVITY_POP_MSG);
        log.info("sendPersonalActivityPopMsg vo={} im={}", JSONObject.toJSONString(popMsg), JSONObject.toJSONString(msg));
        imApi.sendMsg(msg);
    }

    /**
     * 全服通知活动弹窗 仅支持用户名和活动名文案替换
     *
     * @param uid          用户uid
     * @param rewardList   奖励内容
     * @param webUrl       跳转链接
     * @param activityName 活动名
     * @param text         文案
     * @param tip          物料注解
     */
    public void sendUniversalActivityPopMsg(String uid, List<RewardInfoData> rewardList, String webUrl, String activityName, String text, String tip) {
        ActorData actorData = actorMgr.getActorData(uid);
        if (actorData == null) {
            return;
        }
        sendUniversalActivityPopMsg(actorData, rewardList, webUrl, activityName, text, tip);
    }

    /**
     * 全服通知活动弹窗  仅支持用户名和活动名文案替换
     */
    public void sendUniversalActivityPopMsg(ActorData actorData, List<RewardInfoData> rewardList, String webUrl, String activityName, String text, String tip) {
        if (StringUtils.isEmpty(text)) {
            text = UNIVERSAL_ACTIVITY_DEFAULT_TEXT;
        }
        UniversalActivityPopMsg popMsg = new UniversalActivityPopMsg();
        popMsg.setName(actorData.getName());
        popMsg.setHead(actorData.getHeadIcon());
        popMsg.setActivityName(activityName);
        popMsg.setText(text);
        popMsg.setTip(tip);
        popMsg.setUrl(webUrl);
        popMsg.setSendChannel(actorData.getChannel());
        popMsg.setItemList(getItemDetailList(rewardList));
        InnerSendMsgDTO msg = new InnerSendMsgDTO();
        SendConfigData configData = new SendConfigData();
        configData.setSendUid(actorData.getUid());
        configData.setContainMe(true);
        configData.setToUidSet(playerRedis.getOnlineSet());
        msg.setConfigData(configData);
        msg.setUid(actorData.getUid());
        msg.setBody(popMsg.toBody());
        msg.setCmd(AppMsgType.UNIVERSAL_ACTIVITY_POP_MSG);
        log.info("sendUniversalActivityPopMsg vo={} im={}", JSONObject.toJSONString(popMsg), JSONObject.toJSONString(msg));
        imApi.sendMsg(msg);
    }

    private List<ItemDetailData> getItemDetailList(List<RewardInfoData> rewardList) {
        List<ItemDetailData> itemList = new ArrayList<>();
        for (RewardInfoData rewardInfoDatum : rewardList) {
            ItemDetailData itemDetailData = new ItemDetailData();
            int itemType = itemsChangeService.getReportTypeFromRewardItemsType(rewardInfoDatum.getType());
            itemDetailData.setItemIcon(rewardInfoDatum.getIcon());
            itemDetailData.setItemName(rewardInfoDatum.getName());
            itemDetailData.setItemType(itemType);
            itemDetailData.setItemNum(rewardInfoDatum.getNums());
            itemDetailData.setItemUnit(ItemsTypeReportConstant.DAY_UNIT_SET.contains(itemType) ? 0 : 1);
            itemList.add(itemDetailData);
        }
        return itemList;
    }


    public void sendFullServiceIM(RewardMessageBO bo) {
        try {
            log.info("send full service im bo={}", JSONObject.toJSONString(bo));
            //获取配置
            if (bo == null) {
                log.info("send full service im bo is null");
                return;
            }
            if ("null".equals(bo.getRoomId()) || "default".equals(bo.getRoomId())) {
                bo.setRoomId(null);
            }
            SendFullServiceIMConfig config = getSendFullServiceIMConfig(bo.getUid());
            log.info("send full service im config={}", JSONObject.toJSONString(config));
            if (config == null) {
                log.info("send full service im config is null uid={}", bo.getUid());
                return;
            }
            ActorData actorData = actorMgr.getActorData(bo.getUid());
            if (actorData == null) {
                log.info("send full service im actor is null. uid={}", bo.getUid());
                return;
            }
            switch (bo.getType()) {
                case BIG_GIFT:
                    sendBigGiftIM(config.getBigGiftConfig(), bo, actorData);
                    break;
                case LUCKY_GIFT:
                    sendLuckGiftIM(config.getLuckGiftConfig(), bo, actorData);
                    break;
                case GAME:
                    sendGameIM(config.getGameConfig(), bo, actorData);
                    break;
                case CONFESSION_GIFT:
                    sendConfessionGiftIM(config.getConfessionGiftConfig(), bo, actorData);
                    break;
            }
        } catch (Exception e) {
            log.error("send full service im error. bo={} e={}{}{}", bo, e, e.getMessage(), e.getStackTrace());
        }
    }

    private void sendBigGiftIM(SendFullServiceIMConfig.BigGiftConfig config, RewardMessageBO bo, ActorData actorData) {
        log.info("send full service im send big gift im. config={} uid={}", JSONObject.toJSONString(config), actorData.getUid());
        if (config == null) {
            log.info("send full service im send big gift im config is null. uid={}", actorData.getUid());
            return;
        }
        if (StringUtils.isEmpty(config.getScene())) {
            log.info("send full service im send big gift im scene is null. uid={}", actorData.getUid());
            return;
        }
        ActorData toActor = actorMgr.getActorData(bo.getToUid());
        if (toActor == null) {
            log.info("send full service im send big gift im toActor is null uid={} toUid={}", actorData.getUid(), bo.getToUid());
            return;
        }
        String toActorName = toActor.getName();
        if (lordService.decideHaveEnterRoomInvisible(toActor.getUid())) {
            toActorName = LordConstant.INVISIBLE_NAME;
        }
        //头像鉴黄
        actorData.setHeadIcon(moderationService.dealRankHeadModeration(actorData));
        String content = actorData.getName() + " send a gift to " + toActorName;
        RewardFullMsg msg = new RewardFullMsg();
        msg.setUid(actorData.getUid());
        msg.setJumpRoomId(bo.getRoomId());
        msg.setScene(Arrays.asList(config.getScene().split(",")));
        msg.setContent(content);
        msg.setIconPath(bo.getIcon());
        msg.setUserHead(actorData.getHeadIcon());
        msg.setRewardType(BIG_GIFT);
        msg.setFromName(actorData.getName());
        msg.setToName(toActorName);
        sendMsg(msg, actorData, bo.getType());
    }

    private void sendConfessionGiftIM(SendFullServiceIMConfig.BigGiftConfig config, RewardMessageBO bo, ActorData actorData) {
        log.info("send full service im send confession gift im. config={} uid={}", JSONObject.toJSONString(config), actorData.getUid());
        if (config == null) {
            log.info("send full service im send confession gift im config is null. uid={}", actorData.getUid());
            return;
        }
        if (StringUtils.isEmpty(config.getScene())) {
            log.info("send full service im send confession gift im scene is null. uid={}", actorData.getUid());
            return;
        }
        ActorData toActor = actorMgr.getActorData(bo.getToUid());
        if (toActor == null) {
            log.info("send full service im send confession gift im toActor is null uid={} toUid={}", actorData.getUid(), bo.getToUid());
            return;
        }
        String toActorName = toActor.getName();
        if (lordService.decideHaveEnterRoomInvisible(toActor.getUid())) {
            toActorName = LordConstant.INVISIBLE_NAME;
        }
        //头像鉴黄  礼物图标+用户昵称：@赠送对象用户昵称 +对应文案 内超过最长宽距
        actorData.setHeadIcon(moderationService.dealRankHeadModeration(actorData));
        String content = actorData.getName() + "@" + toActorName + " " + bo.getConfessionDesc();
        RewardFullMsg msg = new RewardFullMsg();
        msg.setUid(actorData.getUid());
        msg.setJumpRoomId(bo.getRoomId());
        msg.setScene(Arrays.asList(config.getScene().split(",")));
        msg.setContent(content);
        msg.setIconPath(bo.getIcon());
        msg.setUserHead(actorData.getHeadIcon());
        msg.setRewardType(CONFESSION_GIFT);
        msg.setFromName(actorData.getName());
        msg.setToName(toActorName);
        sendMsg(msg, actorData, bo.getType());
    }

    private void sendLuckGiftIM(SendFullServiceIMConfig.LuckGiftConfig config, RewardMessageBO bo, ActorData actorData) {
        log.info("send full service im send luck gift im config={} uid={}", JSONObject.toJSONString(config), actorData.getUid());
        if (config == null) {
            log.info("send full service im send luck gift im config is null. uid={}", actorData.getUid());
            return;
        }
        if (StringUtils.isEmpty(config.getScene())) {
            log.info("send full service im send luck gift im scene is null. uid={}", actorData.getUid());
            return;
        }
        if (bo.getWonNums() < config.getWon()) {
            log.info("send full service im send luck gift im wonNum less than config value. uid={} wonNum={}", actorData.getUid(), bo.getWonNums());
            return;
        }
        //头像鉴黄
        actorData.setHeadIcon(moderationService.dealRankHeadModeration(actorData));
        String content = actorData.getName() + " won #num coins by sending " + bo.getItemsName();
        RewardFullMsg msg = new RewardFullMsg();
        msg.setUid(actorData.getUid());
        msg.setJumpRoomId(bo.getRoomId());
        msg.setScene(Arrays.asList(config.getScene().split(",")));
        msg.setContent(content);
        msg.setIconPath(bo.getIcon());
        msg.setUserHead(actorData.getHeadIcon());
        msg.setNum(bo.getNum());
        msg.setWonGold(bo.getWonNums());
        msg.setRealWonGold(bo.getRealWonNum());
        msg.setRewardType(LUCKY_GIFT);
        msg.setFromName(actorData.getName());
        msg.setMultiple(bo.getMultiple());
        sendMsg(msg, actorData, bo.getType());
        mainRedis.delete("reward_loop_broadcast_list");
    }

    private void sendGameIM(SendFullServiceIMConfig.GameConfig config, RewardMessageBO bo, ActorData actorData) {
        log.info("send full service im send game im config={} uid={}", JSONObject.toJSONString(config), actorData.getUid());
        if (config == null) {
            log.info("send full service im send game im config is null. uid={}", actorData.getUid());
            return;
        }
        if (StringUtils.isEmpty(config.getScene())) {
            log.info("send full service im send game im scene is null. uid={}", actorData.getUid());
            return;
        }
        RewardFullMsg msg = new RewardFullMsg();
        String content;
        if (bo.getWonType() == RewardItemType.GOLD) {
            if (decideGameWonGold(bo, config)) {
                log.info("send full service im send game im decide game won gold false. uid={} wonGold={}", actorData.getUid(), bo.getWonNums());
                return;
            }
            content = actorData.getName() + " get #num coins in the game";
            msg.setWonGold(bo.getWonNums());
            msg.setRealWonGold(bo.getRealWonNum());
        } else {
            if (!config.getSmashEggsItemsWon().contains(String.valueOf(bo.getWonType()))) {
                log.info("send full service im send game im wonType is not config uid={} wonType={}", actorData.getUid(), bo.getWonType());
                return;
            }
            content = actorData.getName() + " get " + bo.getItemsName() + "X" + bo.getWonNums() + " in the game";
        }
        if (actorData.getChannel().contains(ChannelEnum.PC_WEB_ALL.getName())) {
            GameConfigInfoData gameConfigInfoByName = gameConfigInfoDao.getGameConfigInfoByName(ActivityTypeEnum.getByCode(bo.getActivityType()).getName());
            if (gameConfigInfoByName != null) {
                msg.setWebGameId(Integer.parseInt(gameConfigInfoByName.getGameId()));
            }
        }
        //头像鉴黄
        actorData.setHeadIcon(moderationService.dealRankHeadModeration(actorData));
        GameConfigInfoData gameConfigInfoData = gameConfigInfoDao.getGameConfigInfoByName(ActivityTypeEnum.getByCode(bo.getActivityType()).getName());
        msg.setUid(actorData.getUid());
        msg.setJumpRoomId(bo.getRoomId());
        msg.setScene(Arrays.asList(config.getScene().split(",")));
        msg.setContent(content);
        if (gameConfigInfoData != null) {
            msg.setIconPath(gameConfigInfoData.getGameIcon());
        }
        msg.setUserHead(actorData.getHeadIcon());
        msg.setRewardType(GAME);
        msg.setFromName(actorData.getName());
        sendMsg(msg, actorData, bo.getType());
        mainRedis.delete("reward_loop_broadcast_list");
    }

    private void sendMsg(RewardFullMsg msg, ActorData actorData, int type) {
        String jumpRoomId = msg.getJumpRoomId();
        if (!StringUtils.isEmpty(jumpRoomId)) {
            if (ZONE_TYPE_SET.contains(type)) {
                List<String> imZoneShowCountryList = baseZoneService.getImZoneShowCountryList(jumpRoomId);
                msg.setShowCountryList(imZoneShowCountryList);
            }
            RoomData roomData = roomDao.getRoomData(jumpRoomId);
            if (roomData != null) {
                msg.setRoomType(roomData.getRoomType());
                String roomOwnerId = RoomUtils.getRoomOwnerId(jumpRoomId);
                msg.setOwnUid(roomOwnerId);
                ActorData roomOwnerActorData = actorMgr.getActorData(roomOwnerId);
                if (roomOwnerActorData != null) {
                    msg.setOwnHead(roomOwnerActorData.getHeadIcon());
                }
                if (roomData.getGameRelatedId() != null && roomData.getGameRelatedId() > 0) {
                    GameConfigInfoData gameConfig = gameConfigInfoDao.getGameConfigInfoByRelatedId(roomData.getGameRelatedId());
                    if (gameConfig != null) {
                        msg.setGameId(gameConfig.getGameId());
                        msg.setGameType(gameConfig.getGameType());
                        msg.setGameRelatedId(gameConfig.getRelatedId());
                    }
                }
            }
        }
        LordUserInfoData userLordLevelData = lordService.getUserLordLevelData(msg.getUid());
        if (userLordLevelData.getEnterStealthSwitch().equals(LordConstant.AUTH_SWITCH_OPEN)) {
            msg.setContent(msg.getContent().replace(msg.getFromName(), LordConstant.INVISIBLE_NAME));
            msg.setFromName(LordConstant.INVISIBLE_NAME);
            msg.setUserHead(LordConstant.INVISIBLE_HEAD);
            msg.setEnterRoomInvisible(1);
        }
        msg.setSendChannel(actorData.getChannel());
        log.info("send full service im msg={}", JSONObject.toJSONString(msg));
        SendConfigData sendConfigData = new SendConfigData();
        sendConfigData.setContainMe(true);
        //推送所有用户
        sendConfigData.setToUidSet(playerRedis.getOnlineSet());
        InnerSendMsgDTO dto = new InnerSendMsgDTO();
        dto.setUid(msg.getUid());
        dto.setConfigData(sendConfigData);
        dto.setCmd(msg.getCmd());
        dto.setBody(msg.toBody());
        imApi.sendMsg(dto);
    }

    private SendFullServiceIMConfig getSendFullServiceIMConfig(String uid) {
        String key = CACHE_KEY + uid;
        SendFullServiceIMConfig data = CACHE_MAP.getData(key);
        if (data == null) {
            ConfigDTO dto = new ConfigDTO();
            dto.setUid(uid);
            dto.setKey(AppConfigKeyConstant.SEND_FULL_SERVICE_IM_CONFIG);
            dto.setStatus(AppConfigKeyConstant.STATUS_SERVER);
            data = configApi.getJavaBeanVal(dto, SendFullServiceIMConfig.class);
            if (data != null) {
                CACHE_MAP.cacheData(key, data);
            }
        }
        return data;
    }

    private boolean decideGameWonGold(RewardMessageBO bo, SendFullServiceIMConfig.GameConfig config) {
        boolean result = true;
        //判断中奖金额
        GameImSendConfigBO configBO = config.getConfigSet().stream()
                .filter(data -> data.getEventCode() == bo.getActivityType())
                .findFirst().orElse(null);
        if (configBO != null) {
            if (bo.getWonNums() >= configBO.getMinWinGold()) {
                result = false;
            }
        }
        return result;
    }
}
