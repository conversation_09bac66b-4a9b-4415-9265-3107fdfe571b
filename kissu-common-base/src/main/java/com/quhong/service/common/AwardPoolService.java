package com.quhong.service.common;

import com.quhong.common.enums.HttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.dao.AwardsKeyConfigDao;
import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.dao.datas.db.AwardsKeyConfigData;
import com.quhong.data.vo.CountVO;
import com.quhong.exceptions.WebException;
import com.quhong.redis.BaseListRedis;
import com.quhong.redis.BaseZSetRedis;
import com.quhong.utils.DistributeLockUtils;
import com.quhong.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * 不放回，可定制随机奖池
 * <AUTHOR>
 * @since 2024/12/9 17:54
 */
@Lazy
@Slf4j
@Component
@RequiredArgsConstructor
public class AwardPoolService {
    public static final String INIT_POOL_LOCK_KEY_PRE = "lock:init_pool:";
    private static final int LIMIT_INIT_POOL = 50;
    private static final int ZERO_INIT_POOL = 0;

    private final BaseListRedis baseListRedis;

    private final DistributeLockUtils distributeLockUtils;

    private final BaseZSetRedis baseZSetRedis;

    private final AwardsKeyConfigDao awardsKeyConfigDao;

    private static String globalLimitKey(String poolKey) {
        return "zset:limit:award::" + poolKey;
    }

    private static String initPoolKey(Integer eventCode, String poolType) {
        return "list:event:draw_pool:" + eventCode + ":" + poolType;
    }

    /**
     * 不放回奖池抽奖接口(默认最多轮询10次)
     *
     * @param dto 抽奖参数
     */
    public List<AwardInfo> drawAwardFromRedisPool(DrawDTO dto) {
        return drawAwardFromRedisPool(dto, 10);
    }

    /**
     * 不放回奖池抽奖接口
     *
     * @param dto  抽奖参数
     * @param loop 最大循环限制抽奖次数(防止可能多次遇到被限制奖励的情况) <10
     */
    public List<AwardInfo> drawAwardFromRedisPool(DrawDTO dto, int loop) {
        int finalTimes = dto.getTimes();
        if (dto.getFillPoolMethod() == null) {
            dto.setFillPoolMethod(this::defaultFillPool);
        }
        String poolKey = initPoolKey(dto.getEventCode(), dto.getPoolType());
        String globalLimitKey = globalLimitKey(poolKey);

        List<AwardInfo> awardInfos = drawAwardFromRedis(dto);
        awardInfos = awardInfos.stream()
                .filter(awardInfo -> checkAndSaveLimitRedis(awardInfo, globalLimitKey))
                .collect(Collectors.toList());

        int needReDrawTimes = dto.getTimes() - awardInfos.size();
        if (loop > 0 && needReDrawTimes > 0) {//未达到最大循环次数 且 抽取的奖励不足
            dto.setTimes(needReDrawTimes);
            awardInfos.addAll(drawAwardFromRedisPool(dto, --loop));
        }
        if (awardInfos.size() > finalTimes) {
            awardInfos = awardInfos.stream()
                    .limit(finalTimes)
                    .collect(Collectors.toList());
        }
        return awardInfos;
    }

    private boolean checkAndSaveLimitRedis(AwardInfo awardInfo, String globalLimitKey) {
        if (awardInfo.getLimit() < 0) {
            return true;
        }
        if (awardInfo.getLimit() == 0) {
            return false;
        }
        CountVO count = baseZSetRedis.getOne(globalLimitKey, awardInfo.getAwardId().toString());
        if (count.getCount() >= awardInfo.getLimit()) {
            return false;
        }
        //TODO 考虑直接限制数量 还是限制nums
        baseZSetRedis.increaseToZSet(globalLimitKey, awardInfo.getAwardId().toString(), awardInfo.getNums());
        return true;
    }

    private List<AwardInfo> drawAwardFromRedis(DrawDTO dto) {
        List<String> awardKeys = drawFromRedisPool(dto);
        if (ObjectUtils.isEmpty(awardKeys)) {
            log.error("draw zone award failed,awardPool={}", dto.getPoolType());
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, true, "Award pool is empty"));
        }
        if (ObjectUtils.isEmpty(dto.getAwards())) {
            findAwardConfigs(dto);
        }
        return fillAwardInfos(awardKeys, dto.getAwards());
    }

    private List<String> drawFromRedisPool(DrawDTO dto) {
        String poolKey = initPoolKey(dto.getEventCode(), dto.getPoolType());
        return IntStream.range(0, dto.getTimes())
                .mapToObj(index -> drawOneFromRedisPool(dto, poolKey))
                .collect(Collectors.toList());
    }

    private String drawOneFromRedisPool(DrawDTO dto, String poolKey) {
        checkAndInitPool(dto, poolKey);
        String awardKey = baseListRedis.leftPop(poolKey);
        if (StringUtils.isEmpty(awardKey)) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.SERVER_ERROR, true, "drawFromRedisPool error"));
        }
        return awardKey;
    }

    private void checkAndInitPool(DrawDTO dto, String poolKey) {
        int poolSize = baseListRedis.size(poolKey);
        if (poolSize > LIMIT_INIT_POOL) {
            return;
        }
        if (poolSize > ZERO_INIT_POOL) {
            BaseTaskFactory.Util.slowTask(poolKey, (key) -> lockInitPool(dto, poolKey));
            return;
        }
        lockInitPool(dto, poolKey);
    }

    private void lockInitPool(DrawDTO dto, String poolKey) {
        String lockKey = INIT_POOL_LOCK_KEY_PRE + poolKey;
        distributeLockUtils.distributeConsumer(poolKey, lockKey, (key) -> initPool(dto, poolKey));
    }

    private void initPool(DrawDTO dto, String poolKey) {
        int poolSize = baseListRedis.size(poolKey);
        if (poolSize > LIMIT_INIT_POOL) {
            return;
        }
        findAwardConfigs(dto);
        List<String> poolList = dto.getFillPoolMethod().apply(new FillPoolNewDTO(poolKey, dto.getPoolType(), dto.getAwards()));
        baseListRedis.rightPushAll(poolKey, poolList);
    }

    private void findAwardConfigs(DrawDTO dto) {
        if (!ObjectUtils.isEmpty(dto.getAwards())) {
            return;
        }
        if (StringUtils.isEmpty(dto.getAwardsKey())) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, true, "AwardsKey is empty"));
        }
        AwardsKeyConfigData awardsKeyConfigData = awardsKeyConfigDao.queryByAwardsKey(dto.getAwardsKey());
        dto.setAwards(awardsKeyConfigData.getAwardInfoList());
    }

    private List<AwardInfo> fillAwardInfos(List<String> awardKeys, List<AwardInfo> awards) {
        Map<String, AwardInfo> awardInfoMap = fillAwardInfoMap(awards);
        return awardKeys.stream()
                .map(awardKey -> this.findAwardInfoByAwardKey(awardKey, awardInfoMap))
                .collect(Collectors.toList());
    }

    private static Map<String, AwardInfo> fillAwardInfoMap(List<AwardInfo> awards) {
        return awards.stream()
                .collect(Collectors.toMap(
                        award -> award.getAwardId().toString(),
                        Function.identity()));
    }

    private AwardInfo findAwardInfoByAwardKey(String awardKey, Map<String, AwardInfo> awardInfoMap) {
        AwardInfo awardInfo = awardInfoMap.getOrDefault(awardKey, null);
        if (awardInfo == null) {
            throw new WebException(HttpCode.createHttpCode(
                    HttpCode.ILLEGAL_OPERATION, false, "not found award,id=" + awardKey));
        }
        return awardInfo;
    }

    /**
     * 奖池逻辑有变时重写此方法
     */
    public List<String> defaultFillPool(FillPoolNewDTO dto) {
        List<String> poolList = initPoolList(dto);
        //指定位置替换物品逻辑(特殊逻辑)
//        String needAdd = poolList.set(321, "5");
//        poolList.add(needAdd);
        return poolList;
    }

    public List<String> initPoolList(FillPoolNewDTO dto) {
        List<String> poolList = dto.awards.stream()
                .flatMap(award -> fillPoolStream(dto, award))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Collections.shuffle(poolList);
        return poolList;
    }

    private Stream<String> fillPoolStream(FillPoolNewDTO dto, AwardInfo award) {
        int rate = findRate(dto.getPoolType(), award);
        if (rate <= 0) {
            return Stream.empty();
        }
        // 限制奖池拦截点
        rate = checkLimitAndGetRate(dto, award, rate);
        if (rate <= 0) {
            return Stream.empty();
        }
        return IntStream.range(0, rate).mapToObj(i -> award.getAwardId().toString());
    }

    private int checkLimitAndGetRate(FillPoolNewDTO dto, AwardInfo award, int rate) {
        //limit < 0不做限制
        if (award.getLimit() == null || award.getLimit() < 0) {
            return rate;
        }
        if (award.getLimit() == 0) {
            return 0;
        }
        String globalLimitKey = globalLimitKey(dto.getPoolKey());
        CountVO count = baseZSetRedis.getOne(globalLimitKey, award.getAwardId().toString());
        if (count.getCount() + rate <= award.getLimit()) {
            return rate;
        }
        return (int) (award.getLimit() - count.getCount());
    }


    private static int findRate(String poolType, AwardInfo award) {
        int awardRate;
        switch (poolType) {
            case "1":
                awardRate = award.getRate1();
                break;
            case "2":
                awardRate = award.getRate2();
                break;
            case "3":
                awardRate = award.getRate3();
                break;
            case "0":
            default:
                awardRate = award.getRate();
                break;
        }
        return awardRate;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class FillPoolNewDTO {
        /**
         * 奖池key
         */
        private String poolKey;

        /**
         * 奖池类型
         */
        private String poolType;
        /**
         * 奖品信息
         */
        private List<AwardInfo> awards;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class DrawDTO {
        /**
         * 活动码
         */
        private Integer eventCode;

        /**
         * 奖池类型
         */
        private String poolType;
        /**
         * 奖品配置列表
         */
        private List<AwardInfo> awards;
        /**
         * 概率类礼包key(礼包key兼容)
         */
        private String awardsKey;
        /**
         * 抽奖次数
         */
        private int times;
        /**
         * 奖池填充方法
         */
        private Function<FillPoolNewDTO, List<String>> fillPoolMethod;
    }

    @Deprecated
    public List<AwardInfo> drawAwardFromRedisPool(Integer eventCode, String poolType, List<AwardInfo> awards, int initPoolSize, int times) {
        return drawAwardFromRedisPool(eventCode, poolType, awards, initPoolSize, times, AwardPoolService::defaultFillPool);
    }

    @Deprecated
    private List<AwardInfo> drawAwardFromRedisPool(Integer eventCode, String poolType, List<AwardInfo> awards, int initPoolSize, int times, Consumer<FillPoolDTO> fillPoolMethod) {
        List<String> awardKeys = drawFromRedisPool(eventCode, poolType, awards, initPoolSize, times, fillPoolMethod);
        if (ObjectUtils.isEmpty(awardKeys)) {
            log.error("draw zone award failed,awardPool={}", poolType);
            throw new WebException(HttpCode.createHttpCode(HttpCode.ILLEGAL_OPERATION, true, "Award pool is empty"));
        }
        return fillAwardInfos(awardKeys, awards);
    }

    @Deprecated
    public List<String> drawFromRedisPool(Integer eventCode, String poolType, List<AwardInfo> awards, int initPoolSize, int times, Consumer<FillPoolDTO> fillPoolMethod) {
        String poolKey = initPoolKey(eventCode, poolType);
        return IntStream.range(0, times)
                .mapToObj(index -> drawOneFromRedisPool(awards, initPoolSize, poolType, poolKey, fillPoolMethod))
                .collect(Collectors.toList());
    }


    @Deprecated
    private String drawOneFromRedisPool(List<AwardInfo> awards, int initPoolSize, String poolType, String poolKey, Consumer<FillPoolDTO> fillPoolMethod) {
        checkAndInitPool(poolKey, awards, initPoolSize, poolType, fillPoolMethod);
        String awardKey = baseListRedis.leftPop(poolKey);
        if (StringUtils.isEmpty(awardKey)) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.SERVER_ERROR, true, "drawFromRedisPool error"));
        }
        return awardKey;
    }

    @Deprecated
    private void checkAndInitPool(String poolKey, List<AwardInfo> awards, int initPoolSize, String poolType, Consumer<FillPoolDTO> fillPoolMethod) {
        int poolSize = baseListRedis.size(poolKey);
        if (poolSize > ZERO_INIT_POOL && poolSize <= LIMIT_INIT_POOL) {
            BaseTaskFactory.Util.slowTask(poolKey, (key) -> initPool(key, awards, initPoolSize, poolType, fillPoolMethod));
        } else if (poolSize <= ZERO_INIT_POOL) {
            initPool(poolKey, awards, initPoolSize, poolType, fillPoolMethod);
        }
    }


    @Deprecated
    private void lockInitPool(String poolKey, List<AwardInfo> awards, int initPoolSize, String poolType, Consumer<FillPoolDTO> fillPoolMethod) {
        String lockKey = INIT_POOL_LOCK_KEY_PRE + poolKey;
        distributeLockUtils.distributeConsumer(poolKey, lockKey, (key) -> initPool(key, awards, initPoolSize, poolType, fillPoolMethod));
    }


    @Deprecated
    private void initPool(String poolKey, List<AwardInfo> awards, int initPoolSize, String poolType, Consumer<FillPoolDTO> fillPoolMethod) {
        List<String> poolList = new ArrayList<>(Collections.nCopies(initPoolSize, "-1"));
        List<Integer> indexList = IntStream.rangeClosed(0, initPoolSize - 1).boxed().collect(Collectors.toList());
        Collections.shuffle(indexList);
        awards.forEach(award -> fillPoolMethod.accept(new FillPoolDTO(poolType, award, poolList, indexList)));
        poolList.removeAll(Collections.singleton("-1"));
        baseListRedis.rightPushAll(poolKey, poolList);
    }

    @Deprecated
    private static void defaultFillPool(FillPoolDTO dto) {
        AwardInfo award = dto.getAward();
        List<Integer> indexList = dto.getIndexList();
        List<String> poolList = dto.getPoolList();
        String awardKey = award.getAwardId().toString();

        int awardRate = findRate(dto.getPoolType(), award);

        //指定位置替换物品逻辑
//        if ( "5".equals(awardKey)) {
//            poolList.set(321, awardKey);
//            indexList.remove(Integer.valueOf(321));
//            return;
//        }

        if (awardRate <= 0) {
            return;
        }
        IntStream.range(0, awardRate).forEach(i -> {
            if (!indexList.isEmpty()) {
                int index = indexList.remove(0);
                poolList.set(index, awardKey);
            } else {
                poolList.add(awardKey);
            }
        });
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    @Deprecated
    public static class FillPoolDTO {

        /**
         * 奖池类型
         */
        private String poolType;
        /**
         * 奖品信息
         */
        private AwardInfo award;
        /**
         * 奖池列表（待初始化）
         */
        private List<String> poolList;
        /**
         * 奖池索引列表
         */
        private List<Integer> indexList;
    }
}
