package com.quhong.service.common;

import com.alibaba.fastjson.JSON;
import com.quhong.common.utils.CdnUtils;
import com.quhong.core.enums.AppMsgType;
import com.quhong.dao.datas.ActorData;
import com.quhong.data.dto.InnerSendMsgDTO;
import com.quhong.service.ImApi;
import com.quhong.socket.msg.ServerMsg;
import com.quhong.socket.msg.room.RoomFloatingScreenMsg;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/5/22 16:18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RoomFloatingImService {


    public static final String SHOOT_CARD_SUFFIX_TEXT = "to shot prize";
    public static final String SHOOT_DRAW_SUFFIX_TEXT = "to shot draw prize";
    /**
     * im消息文案
     */
    private static final String IM_TEXT = "Received #ticket X #count #suffix >>>";
    private static final String TICKET_NAME = "#ticket";
    private static final String COUNT_TEXT = "#count";
    private static final String SUFFIX_TEXT = "#suffix";
    private static final String USER_NAME_TEXT = "#username";
    public static final String EVENT_SHOT_DRAW_TEXT = USER_NAME_TEXT + " get " + TICKET_NAME + ", " + SUFFIX_TEXT + " >>>";
    private static final String GIVE_OIL_LAMP_SUFFIX_TEXT = "to draw the prize";

    private static final String GIVE_LUCKY_LETTER_SUFFIX_TEXT = "to get lucky gift pack";

    /**
     * 飘屏背景图
     */
    public static final String FLOATING_SCREEN_BACKGROUND_IMG = "https://statics.kissu.mobi/icon/independence/new/floating_v4.png";

    private final CdnUtils cdnUtils;

    private final ImApi imApi;

    public void sendRoomFloatingIm(ActorData currActor, int count, String icon, String itemName, String suffixText, Integer eventCode, String url) {
        sendRoomFloatingIm(currActor, count, icon, itemName, suffixText, eventCode, url, false, IM_TEXT);
    }

    public void sendRoomFloatingIm(ActorData currActor, int count, String icon, String itemName, String suffixText, Integer eventCode, String url, boolean sendAllRoom, String textModel) {
        String text = textModel.replace(COUNT_TEXT, String.valueOf(count))
                .replace(TICKET_NAME, itemName)
                .replace(SUFFIX_TEXT, suffixText)
                .replace(USER_NAME_TEXT, currActor.getName());
        RoomFloatingScreenMsg msg = RoomFloatingScreenMsg.builder()
                .uid(currActor.getUid())
                .eventType(eventCode)
                .icon(cdnUtils.replaceUrlDomain(currActor.getChannel(), icon, 0))
                .text(text)
                .count((long) count)
                .itemName(itemName)
                .backgroundImg(cdnUtils.replaceUrlDomain(currActor.getChannel(), FLOATING_SCREEN_BACKGROUND_IMG, 0))
                .url(url)
                .build();
        log.debug("send deng im,dto={}", JSON.toJSONString(msg));
        if (sendAllRoom) {
            sendAllRoomIm(msg);
        } else {
            sendIm(currActor.getUid(), msg);
        }
    }

    private void sendAllRoomIm(ServerMsg msg) {
        InnerSendMsgDTO imDto = new InnerSendMsgDTO();
        imDto.setToAllRoom(true);
        imDto.setCmd(AppMsgType.ROOM_FLOATING_SCREEN_MSG);
        imDto.setBody(msg.toBody());
        imApi.sendMsg(imDto);
        log.debug("send all room im success, imMsg={}", JSON.toJSONString(msg));
    }

    /**
     * 获得门票im推送
     *
     * @param uid uid
     * @param msg 消息
     */
    private void sendIm(String uid, RoomFloatingScreenMsg msg) {
        InnerSendMsgDTO imMsg = new InnerSendMsgDTO();
        imMsg.setBody(msg.toBody());
        imMsg.setToUid(uid);
        imMsg.setCmd(AppMsgType.ROOM_FLOATING_SCREEN_MSG);
        imApi.sendMsg(imMsg);
        log.debug("send im success, imMsg={}", JSON.toJSONString(imMsg));
    }

}
