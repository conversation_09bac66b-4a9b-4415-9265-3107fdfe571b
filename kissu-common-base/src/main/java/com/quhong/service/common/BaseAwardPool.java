package com.quhong.service.common;

import com.alibaba.fastjson.JSON;
import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.AppConfigAwardDao;
import com.quhong.dao.AppConfigAwardPoolDao;
import com.quhong.dao.datas.RewardInfoData;
import com.quhong.dao.datas.db.AppConfigAwardData;
import com.quhong.dao.datas.db.AppConfigAwardPoolData;
import com.quhong.data.bo.common.award.pool.config.QueryBO;
import com.quhong.exceptions.WebException;
import com.quhong.utils.DistributeLockUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 奖池逻辑
 * 放回随机奖池（根据配置，限制最大领取数量)
 * <AUTHOR>
 * @since 2023/10/24 18:40
 */
@Slf4j
@Lazy
@Component
@RequiredArgsConstructor
public class BaseAwardPool {
    private final AppConfigAwardPoolDao appConfigAwardPoolDao;
    private final AppConfigAwardDao appConfigAwardDao;
    private final DistributeLockUtils distributeLockUtils;


    /**
     * 奖池锁
     */
    private static final String PLAY_AWARD_POOL_LOCK_KEY = "award_pool_lock";

    public RewardInfoData getOneAwardFromPool(QueryBO bo){
        String distributeLockKey = String.format("%s:%d:%d", PLAY_AWARD_POOL_LOCK_KEY, bo.getEventCode(), bo.getGroup());
        return distributeLockUtils.distributeMethod(
                bo,
                distributeLockKey,
                this::getOneAwardFromPoolAction,
                30, TimeUnit.SECONDS);
    }

    public RewardInfoData getOneAwardFromPoolAction(QueryBO bo){
        // 数据库拉取数据
        List<AppConfigAwardPoolData> configList = getAwardPoolConfigList(bo);
        // 排除达到限制的奖品配置
        configList = eliminateNotCanInPoolConfig(configList);
        return hitAward(bo, configList);
    }

    public RewardInfoData hitAward(QueryBO bo, List<AppConfigAwardPoolData> configList) {
        // 奖池总数
        AtomicInteger poolSize = new AtomicInteger(0);
        // 奖品配置界定
        configList.forEach(data-> data.computeMaxPoolLimit(poolSize));
        // 随机命中
        Integer hit = MathUtils.randomSplitInt(0, poolSize.get());
        // 获取命中的奖池配置
        AppConfigAwardPoolData awardPoolData = getHitAwardConfig(configList, hit);
        // 获取奖品基础信息
        AppConfigAwardData awardInfo = appConfigAwardDao.getOneById(awardPoolData.getAwardId());
        // 已发放奖品数递增
        saveReceivedCountFromDb(bo, awardPoolData);
        return new RewardInfoData(awardPoolData, awardInfo);
    }

    public void saveReceivedCountFromDb(QueryBO bo, AppConfigAwardPoolData awardPoolData) {
        awardPoolData.increaseReceivedCount();
        AppConfigAwardPoolData updateData = AppConfigAwardPoolData.InitFactory.initUpdateReceivedCount(awardPoolData);
        appConfigAwardPoolDao.updateOneSelective(updateData);
        appConfigAwardPoolDao.getListFromRedisBy(bo);
    }

    public AppConfigAwardPoolData getHitAwardConfig(List<AppConfigAwardPoolData> configList, Integer hit) {
        return configList.stream().filter(data -> data.getMaxPoolLimit() > hit)
                .min(Comparator.comparingInt(AppConfigAwardPoolData::getMaxPoolLimit))
                .orElseThrow(() -> new WebException(new HttpEnvData(),
                        new HttpCode(HttpCode.OTHER_NOT_EXISTS).setMsg("not match pool hit,hit=" + hit)));
    }

    private static List<AppConfigAwardPoolData> eliminateNotCanInPoolConfig(List<AppConfigAwardPoolData> configList) {
        return configList.stream()
                .filter(AppConfigAwardPoolData::canInPool)
                .sorted(Comparator.comparingLong(AppConfigAwardPoolData::getId))
                .collect(Collectors.toList());
    }

    public List<AppConfigAwardPoolData> getAwardPoolConfigList(QueryBO bo) {
        List<AppConfigAwardPoolData> configList = appConfigAwardPoolDao.getListFromRedisBy(bo);
        if (ObjectUtils.isEmpty(configList)) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.OTHER_NOT_EXISTS)
                    .setMsg("app_config_award_pool not found config,params=" + JSON.toJSONString(bo)));
        }
        return configList;
    }


}
