package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.datas.DayTimeData;
import com.quhong.core.enums.RoomOwnerStatus;
import com.quhong.core.room.redis.RoomActorRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.*;
import com.quhong.dao.datas.RoomData;
import com.quhong.dao.datas.db.ActivityGalaRoomApplyData;
import com.quhong.dao.datas.db.RoomIncreaseHeatConfigData;
import com.quhong.enums.RoomType;
import com.quhong.enums.activity.gala.GalaRoomTypeEnum;
import com.quhong.room.mic.RoomMic;
import com.quhong.room.mic.RoomMicRedis;
import com.quhong.utils.EnumUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
public class BaseRoomService {

    private static final Logger logger = LoggerFactory.getLogger(BaseRoomService.class);

    private static final int LIVE_HEAT_VALUE_RECENTLY = 30 * 60; //N分钟内热度值
    private static final int PARTY_HEAT_VALUE_RECENTLY = 30 * 60; //N分钟内热度值

    @Autowired
    private RoomActorRedis roomActorRedis;
    @Autowired
    private RoomContributionRecordDao roomContributionRecordDao;

    @Autowired
    private HostConfigDao hostConfigDao;

    @Autowired
    private RoomDao roomDao;

    @Autowired
    private RoomMicRedis roomMicRedis;

    @Resource(name = BaseRedisBeanConstant.OTHER_BEAN)
    private StringRedisTemplate otherRedisTemplate;

    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedis;

    @Autowired
    private TestUserDao testUserDao;
    @Resource
    private RoomIncreaseHeatConfigDao roomIncreaseHeatConfigDao;
    @Resource
    private ActivityGalaRoomApplyDao galaRoomApplyDao;
    @Resource
    private EnterRoomLogDao enterRoomLogDao;

    /**
     * 获取增量热度值
     *
     * @param curTime
     * @param roomId
     * @return
     */
    private int genIncreaseHeatValue(Integer curTime, String roomId) {
        int heat = 0;
        heat += galaIncreaseHeatValue(roomId);
        RoomIncreaseHeatConfigData configData = roomIncreaseHeatConfigDao.getOneByRoomId(roomId);
        if (configData == null || configData.getValid() != 1) {
            return heat;
        }
        if (configData.getEndTime() > 0 && (configData.getStartTime() > curTime || configData.getEndTime() < curTime)) {
            return heat;
        }
        heat = configData.getIncreaseHeat();
        return heat;
    }

    private int galaIncreaseHeatValue(String roomId) {
        int heat = 0;
        if (!StringUtils.isEmpty(roomId) && RoomType.CHAT == RoomUtils.getRoomType(roomId)) {
            ActivityGalaRoomApplyData currGala = galaRoomApplyDao.getCurrStandingGala(RoomUtils.getRoomOwnerId(roomId));
            if (!ObjectUtils.isEmpty(currGala)) {
                GalaRoomTypeEnum galaRoomTypeEnum = EnumUtils.fromCode(currGala.getRoomType(), GalaRoomTypeEnum.class);
                heat = ObjectUtils.isEmpty(galaRoomTypeEnum) ? 0 : galaRoomTypeEnum.getHeat();
                logger.info("Extra heatValue={}, roomId={}, standingGala={}", heat, roomId, JSON.toJSONString(currGala));
            }
        }
        return heat;
    }


    public int getRoomHeatValue(String roomId, String chapterId, Integer curTime, int roomBaseHeatValue) {
        int baseHeatValue = 1000;
        int roomActorCount = roomActorRedis.getRoomActorsCount(roomId);
        double contributionValue = getContributionValueRecently(roomId, chapterId, curTime, LIVE_HEAT_VALUE_RECENTLY);
        // 运营后台增量热度值
        int increaseHeatValue = genIncreaseHeatValue(curTime, roomId);
        return (int) (baseHeatValue + (roomActorCount * 10) + contributionValue + roomBaseHeatValue + increaseHeatValue);
    }

    public Integer getPartyRoomHeatValue(String roomId, Integer curTime, int roomBaseHeatValue) {
        int baseHeatValue = 1000;
        int roomActorCount = roomActorRedis.getRoomActorsCount(roomId);
        int upMicroCount = 0;
        RoomMic roomMic = roomMicRedis.getRoomMic(roomId);
        if (roomMic != null) {
            List<RoomMic.MicInfo> micList = roomMic.getMicList();
            for (RoomMic.MicInfo micInfo : micList) {
                if (micInfo.getStatus() == 1) {
                    upMicroCount++;
                }
            }
        }
        double contributionValue = getContributionPartyValueRecently(roomId, curTime, PARTY_HEAT_VALUE_RECENTLY);
        // 运营平台增量热度值
        int increaseHeatValue = genIncreaseHeatValue(curTime, roomId);
        return (int) (baseHeatValue + (roomActorCount * 1000) + (contributionValue / 50) + roomBaseHeatValue + (upMicroCount * 1000) + increaseHeatValue);
    }

    public Integer getPartyRoomHeatValue(String roomId, Integer curTime, int roomBaseHeatValue, int upMicroCount) {
        int baseHeatValue = 1000;
        int roomActorCount = roomActorRedis.getRoomActorsCount(roomId);
        double contributionValue = getContributionPartyValueRecently(roomId, curTime, PARTY_HEAT_VALUE_RECENTLY);
        // 运营平台增量热度值
        int increaseHeatValue = genIncreaseHeatValue(curTime, roomId);
        return (int) (baseHeatValue + (roomActorCount * 1000) + (contributionValue / 50) + roomBaseHeatValue + (upMicroCount * 1000) + increaseHeatValue);
    }

    /**
     * 获取该直播场次最近一段时间的贡献值
     */
    public double getContributionValueRecently(String roomId, String chapterId, Integer curTime, int extendTime) {
        long timeEnd = curTime; // 获得当前时间戳/单位（秒）
        long timeStart = timeEnd - extendTime;
//        logger.info("roomId={} chapterId={} timeStart={} timeEnd={}", roomId, chapterId, timeStart, timeEnd);
        List<String> tableSuffixList = DateHelper.UTC.getTableSuffixByTime(timeStart * 1000L, timeEnd * 1000L);
//        logger.info("roomId={} chapterId={} timeStart={} timeEnd={} tableSuffixList={}", roomId, chapterId, timeStart, timeEnd, tableSuffixList);
        return roomContributionRecordDao.getRoomContributionByChapterIdFromDB(chapterId, timeStart, timeEnd, tableSuffixList);
    }

    public double getContributionPartyValueRecently(String roomId, Integer curTime, int extendTime) {
        long timeEnd = curTime; // 获得当前时间戳/单位（秒）
        long timeStart = timeEnd - extendTime;
//        logger.info("roomId={}  timeStart={} timeEnd={}", roomId, timeStart, timeEnd);
        List<String> tableSuffixList = DateHelper.UTC.getTableSuffixByTime(timeStart * 1000L, timeEnd * 1000L);
//       logger.info("roomId={}  timeStart={} timeEnd={} tableSuffixList={}", roomId, timeStart, timeEnd, tableSuffixList);
        return roomContributionRecordDao.getPartyRoomContributionByRoomIdFromDB(roomId, timeStart, timeEnd, tableSuffixList);
    }


    /**
     * 获取该直播场次所有的贡献值
     */
    public double getContributionValueAll(String roomId, String chapterId) {
        return roomContributionRecordDao.getContributionByChapterIdFromDB(chapterId);
    }

    /**
     * 房间有效性
     *
     * @param roomId
     * @return
     */
    public boolean getRoomValidity(String roomId) {
        String ownerUid = RoomUtils.getRoomOwnerId(roomId);
        if (hostConfigDao.getConfigIntValue(ownerUid, HostConfigDao.WHITE_CONFIG) != 1) {
            // 房间没在白名单 不加入房间列表
            return false;
        }
        RoomData roomData = roomDao.getRoomData(roomId);
        int roomCount = roomActorRedis.getRoomActorsCount(roomId);
        if (roomCount <= 0 && (roomData == null || roomData.getOfficialRoom() == 0)) {
            // 房间没人 不加入房间列表
            return false;
        }

        // 房间关闭状态 不加入房间列表
        return getRoomOwnerStatus(roomId) != RoomOwnerStatus.CLOSE;
    }

    /**
     * party房间有效性
     *
     * @param roomId
     * @return
     */
    public boolean getPartyRoomValidity(String roomId) {
        int roomCount = roomActorRedis.getRoomActorsCount(roomId);
//        logger.info("getPartyRoomValidity roomId={} roomCount={}", roomId, roomCount);
        // 房间没人 不加入房间列表
        return roomCount > 0;
        // 房间关闭状态 不加入房间列表
    }

    public int getRoomOwnerStatus(String roomId) {
        RoomData roomData = roomDao.getRoomData(roomId);
        return roomData.getOwnerStatus();
    }

    /**
     * 主播是否在直播
     *
     * @param hostUid
     * @return
     */
    public boolean validHostInOwnerRoom(String hostUid) {
        return roomActorRedis.validRoomOwnerInOwnerRoomZSet(hostUid);
    }

    /**
     * 主播在自己房间的集合
     *
     * @return
     */
    public Set<String> getOwnerInOwnerRoomList() {
        return roomActorRedis.getRoomOwnerInOwnerRoomZSet();
    }

    /**
     * 获取房间周榜贡献值
     *
     * @param ownerUid 房主uid
     * @param roomId   房间id
     * @return
     */
    public double getWeekLiveRankScore(String ownerUid, String roomId) {
        String mondayStr = DateHelper.UTC.getDayOfWeekStr(Calendar.MONDAY, 0);
        String sundayStr = DateHelper.UTC.getDayOfWeekStr(Calendar.SUNDAY, 0);
        String redisKey = getRedisKey(mondayStr, sundayStr);
        Double score = otherRedisTemplate.opsForZSet().score(redisKey, ownerUid);
        if (score != null) {
            return score;
        }
        List<DayTimeData> continuesDays = DateHelper.UTC.getContinuesDays(mondayStr, sundayStr);
        if (CollectionUtils.isEmpty(continuesDays)) {
            otherRedisTemplate.opsForZSet().add(redisKey, ownerUid, 0);
            return 0;
        }
        Set<String> testUidSet = testUserDao.getTestUidSet();
        Double scoreFromDB = roomContributionRecordDao.getSelfCharmRankFromDB(continuesDays.get(0)
                .getTime(), continuesDays.get(continuesDays.size() - 1).getEndTime(), roomId, testUidSet);
        double rankScore = scoreFromDB != null ? scoreFromDB : 0;
        otherRedisTemplate.opsForZSet().add(redisKey, ownerUid, rankScore);
        return rankScore;
    }

    private String getRedisKey(String mondayStr, String sundayStr) {
        String redisKeySuffix = mondayStr + "-" + sundayStr;
        return "room_rank_" + redisKeySuffix;
    }

    public boolean decideInMic(String roomId) {
        String roomOwnerId = RoomUtils.getRoomOwnerId(roomId);
        RoomMic roomMic = roomMicRedis.getRoomMic(roomId);
        if (roomMic != null && !CollectionUtils.isEmpty(roomMic.getMicList())) {
            for (RoomMic.MicInfo micInfo : roomMic.getMicList()) {
                if (!StringUtils.isEmpty(micInfo.getAid()) && !micInfo.getAid().equals(roomOwnerId)) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean decideUserInMic(String uid) {
        try {
            String roomId = roomActorRedis.getActorRoomStatus(uid);
            if (StringUtils.isEmpty(roomId)) {
                return false;
            }
            if (uid.equals(RoomUtils.getRoomOwnerId(roomId))) {
                return false;
            }
            RoomData roomData = roomDao.getRoomData(roomId);
            if (roomData == null || roomData.getRoomType().equals(RoomType.CHAT)) {
                return false;
            }
            RoomMic roomMic = roomMicRedis.getRoomMic(roomId);
            if (roomMic != null && !CollectionUtils.isEmpty(roomMic.getMicList())) {
                for (RoomMic.MicInfo micInfo : roomMic.getMicList()) {
                    if (uid.equals(micInfo.getAid())) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("decide user in mic error. uid={} e={}", uid, e.getMessage(), e);
        }
        return false;
    }

    private String getChatRoomDurationKey(String roomId, String uid, long startTime, long endTime) {
        return "str:chat_room_duration:room_id::" + roomId + ":uid:" + uid + ":startTime:" + startTime + ":endTime:" + endTime;
    }

    public long getChatRoomDuration(String roomId, String uid, long startTime, long endTime, int expireTime) {
        String key = getChatRoomDurationKey(roomId, uid, startTime, endTime);
        String strVal = mainRedis.opsForValue().get(key);
        if (!StringUtils.isEmpty(strVal)) {
            return Long.parseLong(strVal);
        }
        long chatRoomDuration = enterRoomLogDao.getChatRoomDuration(uid, roomId, startTime, endTime);
        mainRedis.opsForValue().set(key, String.valueOf(chatRoomDuration), expireTime, TimeUnit.MINUTES);
        return chatRoomDuration;
    }


}
