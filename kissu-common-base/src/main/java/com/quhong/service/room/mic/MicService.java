package com.quhong.service.room.mic;

import com.quhong.core.enums.ActorType;
import com.quhong.core.room.redis.RoomActorRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.RoomDao;
import com.quhong.dao.datas.RoomData;
import com.quhong.data.room.RoomActorDetailData;
import com.quhong.redis.GiftRedis;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.mic.MicConstant;
import com.quhong.room.mic.RoomMic;
import com.quhong.room.mic.RoomMicRedis;
import com.quhong.service.BaseRoomService;
import com.quhong.service.MicShowIncomeService;
import com.quhong.utils.RoomUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@AllArgsConstructor
public class MicService {
    public static final int MIC_SIZE = 6;

    private final RoomMicRedis roomMicRedis;
    private final RoomActorCache roomActorCache;
    private final RoomActorRedis roomActorRedis;

    private final MicShowIncomeService micShowIncomeService;
    private final RoomDao roomDao;
    private final BaseRoomService baseRoomService;

    public RoomMic getChatRoomMic(String roomId) {
        RoomMic roomMic = roomMicRedis.getRoomMic(roomId);
        if (null != roomMic && roomMic.getMicList().size() == MIC_SIZE) {//麦位数量变化 重新创建麦位
            return roomMic;
        }
        roomMic = new RoomMic();
        roomMic.setRoomId(roomId);
        RoomData roomData = roomDao.getRoomData(roomId);
        if (roomData != null) {
            roomMic.setSubType(roomData.getSubType());
        }
        List<RoomMic.MicInfo> micInfoList = new ArrayList<>(MIC_SIZE);
        for (int i = 0; i < MIC_SIZE; i++) {
            RoomMic.MicInfo micInfo = new RoomMic.MicInfo();
            micInfo.setPosition(i);
            if (i == 0) {
                // 房间1号位固定为房主麦，房主离开时麦位显示离开状态，房主进入房间则显示正常上麦状态（房主不存在下麦时的状态）
                fillActorData(roomId, micInfo, RoomUtils.getRoomOwnerId(roomId), roomData);
                micInfo.setUpMicTime(DateHelper.getCurrentTime());
                micInfo.setMute(MicConstant.MIC_MUTE_OFF);
                micInfo.setCamera(MicConstant.MIC_CAMERA_OFF);//语聊房默认关闭摄像头
                micInfo.setStatus(roomActorRedis.hasInRoom(roomId, micInfo.getAid()) ? MicConstant.MIC_STATUS_OCCUPY : MicConstant.MIC_STATUS_LEAVE);
            }
            micInfoList.add(micInfo);
        }
        roomMic.setMicList(micInfoList);
        micChange(roomMic);
        return roomMic;
    }

    /**
     * 填充用户信息
     *
     * @param roomId  房间id
     * @param micInfo 麦位对象
     * @param aid     用户uid
     */
    private void fillActorData(String roomId, RoomMic.MicInfo micInfo, String aid, RoomData roomData) {
        RoomActorDetailData actorDetailData = roomActorCache.getData(roomId, aid, true);
        micInfo.setAid(aid);
        micInfo.setRid(actorDetailData.getActorInfo().getRid());
        micInfo.setName(actorDetailData.getActorInfo().getName());
        micInfo.setHead(actorDetailData.getActorInfo().getHead());
        micInfo.setCountry(actorDetailData.getActorInfo().getCountryCode());
        micInfo.setRole(actorDetailData.getActorInfo().getRole());
        micInfo.setGender(actorDetailData.getActorInfo().getGender() == ActorType.HOST ? ActorType.HOST : ActorType.USER);
        micInfo.setMicFrame(actorDetailData.getActorInfo().getMicFrame());
        double micIncome = baseRoomService.getMicContributionValue(roomData, aid);
        micInfo.setMicIncome((int) micIncome);
        micInfo.setRealMicIncome(String.valueOf(micIncome));
        BigDecimal showIncome = micShowIncomeService.getMicShowIncome(aid, roomId, DateHelper.genDateHelper(actorDetailData.getActorInfo().getChannel()).getToday());
        micInfo.setShowIncome(showIncome.longValue());
        micInfo.setRealShowIncome(showIncome.toString());
        micInfo.setSex(actorDetailData.getActorInfo().getSex());
        micInfo.setEnterRoomInvisible(actorDetailData.getRoomActorData().getEnterRoomInvisible());
    }

    /**
     * 麦位变化时进行版本递增，以及保存麦位信息至Redis
     *
     * @param roomMic
     */
    public void micChange(RoomMic roomMic) {
        // 更新麦位信息
        roomMic.incrVersion();
        roomMicRedis.saveRoomMic(roomMic);
    }
}
