package com.quhong.service;

import com.quhong.common.data.ApiResult;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.constant.MatchGuaranteeTypeConstant;
import com.quhong.constant.RedisKeyConstant;
import com.quhong.constant.items.ItemConfigIdConstant;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.datas.ChatCmdData;
import com.quhong.core.enums.CallFromType;
import com.quhong.core.enums.ClientSystemType;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.*;
import com.quhong.dao.datas.*;
import com.quhong.dao.datas.db.ItemsConfigData;
import com.quhong.dao.datas.log.DetectPornLogData;
import com.quhong.data.SendConfigData;
import com.quhong.data.appConfig.NotServeConfig;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.data.dto.InnerSendMsgDTO;
import com.quhong.data.dto.RefreshExamineDTO;
import com.quhong.data.vo.ItemQueryVO;
import com.quhong.exceptions.WebException;
import com.quhong.players.ActorMgr;
import com.quhong.redis.ChatCmdRedis;
import com.quhong.redis.ExamineRedis;
import com.quhong.redis.HostAppraiseRedis;
import com.quhong.redis.HostGradeRedis;
import com.quhong.service.money.CurrencyService;
import com.quhong.socket.msg.server.ProbationResultMsg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName ExamineService
 * <AUTHOR>
 * @date 2022/5/28 11:26
 */
@Component
@Lazy
public class ExamineService {
    private static final Logger logger = LoggerFactory.getLogger(ExamineService.class);

    private static final int ANDRIOD_SEND_MSG_VER = 402;
    private static final int IOS_SEND_MSG_VER = 296;


    @Autowired
    private ActorMgr actorMgr;
    @Autowired
    private HostInfoDao hostInfoDao;
    @Autowired
    private ConfigApi configApi;
    @Autowired
    private ExamineRedis examineRedis;
    @Autowired
    private ExamineCallLogDao examineCallLogDao;
    @Autowired
    private GiftRecordDao giftRecordDao;
    @Autowired
    private LogModerationPictureDao logModerationPictureDao;
    @Autowired
    private UserInfoApi userInfoApi;
    @Autowired
    private HostExamineScoreLogDao hostExamineScoreLogDao;
    @Autowired
    private HostAppraiseRedis hostAppraiseRedis;
    @Autowired
    private HostAppraiseScoreLogDao hostAppraiseScoreLogDao;
    @Autowired
    private ItemsApi itemsApi;
    @Autowired
    private HostStatusRecordLogDao hostStatusRecordLogDao;
    @Autowired
    private HostGradeRedis hostGradeRedis;
    @Autowired
    private ChatCmdRedis chatCmdRedis;
    @Autowired
    private GuaranteeWhiteListConfigDao guaranteeWhiteListConfigDao;
    @Autowired
    private ImApi imApi;
    @Autowired
    private HostSortScoreLogDao hostSortScoreLogDao;
    @Autowired
    private CurrencyService currencyService;
    @Autowired
    private HostAppraiseLevelDao hostAppraiseLevelDao;
    @Autowired
    private DetectPornLogDao detectPornLogDao;
    @Autowired
    private ItemsConfigDao itemsConfigDao;
    @Resource(name = BaseRedisBeanConstant.GRADE_BEAN)
    private StringRedisTemplate gradeRedisTemplate;

    /**
     * 通话结束 保存考核电话
     *
     * @param cmdData
     */
    public void saveExamineCallDetail(ChatCmdData cmdData) {
        String hostUid = cmdData.getToUid();
        logger.info("start save examine call detail. fromUid={} hostUid={} cid={} guaranteeType={}", cmdData.getFromUid(), hostUid, cmdData.getCid(), cmdData.getGuaranteeType());
        //判断通话类型
        if (!MatchGuaranteeTypeConstant.EXAMINE_CALL_LIST.contains(cmdData.getGuaranteeType())) {
            logger.info("save examine call detail call is not examine call type. type={}", cmdData.getGuaranteeType());
            return;
        }
        //判断主播状态
        HostInfoData hostInfo = hostInfoDao.getHostInfo(hostUid);
        if (hostInfo == null || hostInfo.getHostStatus() == null || hostInfo.getHostStatus() != HostInfoDao.EXAMINING) {
            logger.info("save examine call detail host is not examining host. hostUid={}", hostUid);
            return;
        }
//        //读取配置
//        int examineNeedCountConfig = getExamineNeedCountConfig(hostUid);
//        //获取已经考核记录缓存
//        Set<String> examineUsers = getExamineUsers(hostUid);
//        int examineUserCount = CollectionUtils.isEmpty(examineUsers) ? 0 : examineUsers.size();
//        //判断配置
//        if (examineUserCount >= examineNeedCountConfig) {
//            logger.info("save examine call detail host exceed config count. hostUid={} configCount={} actualCount={}", hostUid, examineNeedCountConfig, examineUserCount);
//            return;
//        }
        //记录入库
        saveExamineCallDetailToDB(cmdData);
        //记录考核名单
        examineRedis.saveExamineUser(hostUid, cmdData.getFromUid());
        //触发刷新评分
        RefreshExamineDTO dto = new RefreshExamineDTO();
        dto.setHostUid(hostUid);
        logger.info("save examine call detail start refresh examine score. uid={}", hostUid);
        userInfoApi.refreshExamine(dto);
    }


    /**
     * 校正是否为考核通话
     */
    public void fixGuaranteeType(ChatCmdData cmdData) {
        if (cmdData.getFromType() == CallFromType.FROM_FILTER_HOST_MATCH || cmdData.getFromType() == CallFromType.FROM_USER_MATCH_HOST) {
            logger.info("fix guarantee type. from_type is match from_type={}", cmdData.getFromType());
            return;
        }
        if (validExamineHost(cmdData.getGainUid()) && validExamineUser(cmdData.getDeductUid())) {
            cmdData.setGuaranteeType(MatchGuaranteeTypeConstant.VIDEO_CALL_EXAMINE_USUAL_USER_DIAL);
        }
    }

    /**
     * 获取主播已经考核过的用户
     *
     * @param hostUid 主播uid
     * @return Set<String> 用户uid列表
     */
    public Set<String> getExamineUsers(String hostUid) {
        return examineCallLogDao.getExamineUsers(hostUid);
    }

    /**
     * 获取考核需要X人配置
     *
     * @param hostUid 主播uid
     * @return int 配置数量
     */
    public int getExamineNeedCountConfig(String hostUid) {
        ConfigDTO dto = new ConfigDTO();
        dto.setUid(hostUid);
        dto.setKey(AppConfigKeyConstant.EXAMINE_NEED_COUNT);
        dto.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        Integer config = configApi.getIntegerVal(dto);
        return config != null ? config : 0;
    }

    /**
     * 保存考核通话详情到数据库
     *
     * @param cmdData 通话详情
     */
    private void saveExamineCallDetailToDB(ChatCmdData cmdData) {
        ExamineCallLogData data = new ExamineCallLogData();
        data.setCid(cmdData.getCid());
        data.setDuration((int) (cmdData.getEndTime() / 1000 - cmdData.getStartTime() / 1000));
        data.setFromUid(cmdData.getFromUid());
        data.setToUid(cmdData.getToUid());
        data.setCtime(DateHelper.getCurrentTime());
        data.setMtime(DateHelper.getCurrentTime());
        List<GiftRecordData> giftRecordFromCid = giftRecordDao.getListByRoomId(cmdData.getCid());
        if (CollectionUtils.isEmpty(giftRecordFromCid)) {
            data.setHaveBigGift(0);
            data.setHaveServe(0);
        } else {
            //获取不服务率配置
            NotServeConfig notServeConfig = getNotServeConfig(cmdData.getToUid());
            data.setHaveBigGift(decideHaveBigGift(giftRecordFromCid, notServeConfig) ? 1 : 0);
            data.setHaveServe(decideHaveServer(giftRecordFromCid, cmdData.getCid(), cmdData.getToUid(), notServeConfig) ? 1 : 0);
        }
        int giftRecord = 0;
        for (GiftRecordData giftRecordData : giftRecordFromCid) {
            int giftValue = getGiftValue(giftRecordData);
            giftRecord += giftValue;
        }
        data.setTotalGiftValue(giftRecord);
        data.setCallIncome(cmdData.getGainValue() + giftRecord);
        examineCallLogDao.insert(data);
    }

    private boolean decideHaveBigGift(List<GiftRecordData> giftRecordFromCid, NotServeConfig notServeConfig) {
        for (GiftRecordData giftRecordData : giftRecordFromCid) {
            if (giftRecordData.getPrice() >= notServeConfig.getGiftValue()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据礼物价格换算礼物
     * 通话中礼物不做小数处理
     *
     * @param giftRecordData 礼物信息
     * @return int 主播获得的价格
     * @classmethod def gain_by_gift_id(cls, gift_id, cost):
     * if gift_id not in (88, 98, 41):
     * gain = int(cost * 3 // 10)
     * else:
     * gain = int(cost * 5 // 10)
     * return gain
     */
    private int getGiftValue(GiftRecordData giftRecordData) {
        if (giftRecordData == null) {
            return 0;
        }
        if (giftRecordData.getGiftid() == 88 || giftRecordData.getGiftid() == 98 || giftRecordData.getGiftid() == 41) {
            //88 98 41 gift_id的礼物主播获取50%  其余的主播获取30%
            return giftRecordData.getPrice() / 5;
        } else {
            return giftRecordData.getPrice() / 3;
        }
    }

    /**
     * 判断通话送礼后是否有服务
     *
     * @param giftRecordFromCid 礼物列表
     * @param cid               通话cid
     * @param hostUid           主播uid
     * @return
     */
    private boolean decideHaveServer(List<GiftRecordData> giftRecordFromCid, String cid, String hostUid, NotServeConfig notServeConfig) {
        logger.info("save examine call detail start decide have server");
        ActorData actorData = actorMgr.getActorData(hostUid);
        if (actorData == null) {
            return false;
        }
        for (GiftRecordData giftRecordData : giftRecordFromCid) {
            if (giftRecordData.getPrice() >= notServeConfig.getGiftValue()) {
                int giftTime = giftRecordData.getCtime().intValue();
                int startTime = giftTime - notServeConfig.getBeforeTime();
                int endTime = giftTime + notServeConfig.getAfterTime();
                //鉴黄记录表更换
//                List<LogModerationPictureData> recordByCidAndTime = logModerationPictureDao.getRecordByCidAndTime(startTime, endTime, cid);
                List<DetectPornLogData> pornRecordByCidAndTime = detectPornLogDao.getPornRecordByCidAndTime(startTime, endTime, cid);
                if (!CollectionUtils.isEmpty(pornRecordByCidAndTime)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取不服务运营平台配置
     *
     * @param hostUid 主播uid
     * @return NotServeConfig
     */
    public NotServeConfig getNotServeConfig(String hostUid) {
        ConfigDTO dto = new ConfigDTO();
        dto.setUid(hostUid);
        dto.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        dto.setKey(AppConfigKeyConstant.NOT_SERVE_CONFIG);
        NotServeConfig notServeConfig = configApi.getJavaBeanVal(dto, NotServeConfig.class);
        return notServeConfig != null ? notServeConfig : new NotServeConfig();
    }


    /**
     * 触发刷新考核等级
     *
     * @param dto 参数
     */
    public ApiResult<String> refreshExamine(RefreshExamineDTO dto) {
        String hostUid = dto.getHostUid();
        String requestId = dto.getRequestId();
        logger.info("start refresh examine score. hostUid={} requestId={}", hostUid, requestId);
        if (StringUtils.isEmpty(hostUid)) {
            logger.info("refresh examine score fail. hostUid is null. requestId={}", requestId);
            return ApiResult.getOk();
        }
        HostInfoData hostInfo = hostInfoDao.getHostInfo(hostUid);
        if (hostInfo == null || hostInfo.getHostStatus() == null || hostInfo.getHostStatus() != HostInfoDao.EXAMINING) {
            logger.info("refresh examine score fail. host info is error. hostUid={} requestId={}", hostUid, requestId);
            return ApiResult.getOk();
        }
        //获取所有的考核通话
        List<ExamineCallLogData> hostExamineCall = examineCallLogDao.getHostExamineCall(hostUid);
        if (CollectionUtils.isEmpty(hostExamineCall)) {
            logger.info("refresh examine score fail. examine calls is null. hostUid={} requestId={}", hostUid, requestId);
            return ApiResult.getOk();
        }
        //前X通不触发刷新
        if (hostExamineCall.size() <= 3) {
            logger.info("refresh examine score before 3 calls not refresh. hostUid={} callSize={} requestId={}", hostUid, hostExamineCall.size(), requestId);
            return ApiResult.getOk();
        }
        int examineScore = calculateExamineScore(hostExamineCall, hostUid);
        //分数入库
        saveExamineScore(hostUid, examineScore);
        //判断是否可以转正为优质主播
        //获取运营平台配置
        int examineNeedCountConfig = getExamineNeedCountConfig(hostUid);
        saveAppraiseScore(hostUid, examineScore);
        //获取考核用户数
        Set<String> examineUsers = getExamineUsers(hostUid);
        if (CollectionUtils.isEmpty(examineUsers) || examineUsers.size() < examineNeedCountConfig) {
            //未达到配置
            logger.info("refresh examine score host not arrive config continue examine. hostUid={} requestId={} nowCount={} configCount={}", hostUid, requestId, examineUsers.size(), examineNeedCountConfig);
            return ApiResult.getOk();
        }
        //修改主播状态
        HostInfoData newHostInfoData = new HostInfoData();
        newHostInfoData.setId(hostInfo.getId());
        newHostInfoData.setUid(hostInfo.getUid());
        newHostInfoData.setHostStatus(HostInfoDao.PROBATION_SUCCESS);
        newHostInfoData.setPositiveTime(DateHelper.getCurrentTime());
        hostInfoDao.updateHosInfo(newHostInfoData);
        //从考核主播池移除主播
        examineRedis.delExamineTimeHostList(hostUid);
        examineRedis.delTotalExamineHostList(hostUid);
        //加入优质主播池
        gradeRedisTemplate.opsForSet().add(RedisKeyConstant.HIGH_QUALITY_HOST, hostUid);
        //将主播加入保收池中
        hostGradeRedis.saveGuaranteeHostScoreRedis(hostUid, 50);
        //如果为A级加排序分
        if (examineScore >= 80) {
            int addScore = 100000;
            String today = DateHelper.UTC.getToday();
            String redisKey = RedisKeyConstant.GRADED_HIGH_QUALITY_HOST + today;
            gradeRedisTemplate.opsForZSet().incrementScore(redisKey, hostUid, addScore);
            hostSortScoreLogDao.addScore(hostUid, today, addScore);
        }
        //将优质主播最后通话时长存入redis
        chatCmdRedis.saveLatestPositiveHostCallTimeToRedis(hostUid, 2);
        //主播状态记录入库
        saveHostStatus(hostUid, HostInfoDao.PROBATION_SUCCESS, examineScore);
        //加入保收白名单
        if (examineScore >= 60) {
            saveGuaranteeWhiteList(hostUid);
        }
        //推送考核通过im
        sendResultToHost(hostUid, getAppraiseScore(examineScore));
        return ApiResult.getOk();
    }

    /**
     * 添加保收白名单
     *
     * @param hostUid 主播uid
     */
    private void saveGuaranteeWhiteList(String hostUid) {
        logger.info("save guarantee white list. uid={}", hostUid);
        ActorData actorData = actorMgr.getActorData(hostUid);
        if (actorData == null) {
            logger.info("refresh examine score save guarantee white list fail actor data is null. hostUid={}", hostUid);
            return;
        }
        GuaranteeWhiteListConfigData data = new GuaranteeWhiteListConfigData();
        data.setGuaranteeTarget(actorData.getRid().toString());
        data.setUid(hostUid);
        data.setVail(1);
        data.setCtime(DateHelper.getCurrentTime());
        data.setMtime(DateHelper.getCurrentTime());
        data.setOperator("System");
        data.setSource(GuaranteeWhiteListConfigDao.EXAMINE_SUCCESS_SOURCE);
        guaranteeWhiteListConfigDao.insertConfig(data);
    }


    /**
     * 主播状态变动记录入库
     *
     * @param hostUid    主播uid
     * @param hostStatus 主播状态
     */
    public void saveHostStatus(String hostUid, int hostStatus) {
        saveHostStatus(hostUid, hostStatus, 0);
    }

    public void saveHostStatus(String hostUid, int hostStatus, int examineScore) {
        HostStatusRecordLogData data = new HostStatusRecordLogData();
        data.setUid(hostUid);
        data.setStatusType(hostStatus);
        data.setAlterTime(DateHelper.getCurrentTime());
        data.setCtime(DateHelper.getCurrentTime());
        data.setHostExamineScore(examineScore);
        hostStatusRecordLogDao.insert(data);
    }

    /**
     * 计算考核总分
     *
     * @param hostExamineCall 考核通话列表
     * @param hostUid         主播uid
     * @return int 考核分数
     */
    public int calculateExamineScore(List<ExamineCallLogData> hostExamineCall, String hostUid) {
        logger.info("refresh examine score start calculate examine score. hostUid={}", hostUid);
        int duration = 0;
        int haveGiftCount = 0;
        int notServeCount = 0;
        int callIncome = 0;
        int giftValue = 0;
        int exceedCallTimeCount = 0;
        int exceedCallTime = getExceedCallTime(hostUid);
        for (ExamineCallLogData data : hostExamineCall) {
            duration += data.getDuration() != null ? data.getDuration() : 0;
            if (data.getHaveBigGift() != null && data.getHaveBigGift() == 1) {
                haveGiftCount++;
                if (data.getHaveServe() != null && data.getHaveServe() == 0) {
                    notServeCount++;
                }
            }
            exceedCallTimeCount += data.getDuration() >= exceedCallTime ? 1 : 0;
            callIncome += data.getCallIncome() != null ? data.getCallIncome() : 0;
            giftValue += data.getTotalGiftValue() != null ? data.getTotalGiftValue() : 0;
        }
        int durationScore = calculateDurationScore(duration, hostExamineCall.size(), hostUid);
        int notServeScore = calculateNotServeScore(hostExamineCall.size(), notServeCount, hostUid);
        int giftRateScore = calculateGiftRateScore(callIncome, giftValue, hostUid);
        int redialRateScore = calculateRedialRateScore(hostExamineCall, hostUid);
        int exceedCallTimeScore = calculateExceedCallTimeScore(exceedCallTimeCount, hostExamineCall.size());
        logger.info("refresh examine score calculate examine score detail. hostUid={} durationScore={} notServeScore={} giftRateScore={} redialRateScore={} exceedCallTimeScore={}",
                hostUid, durationScore, notServeScore, giftRateScore, redialRateScore, exceedCallTimeScore);
        int totalScore = calculateTotalScore(durationScore, notServeScore, giftRateScore, redialRateScore, exceedCallTimeScore);
        logger.info("refresh examine score calculate examine score detail. hostUid={} totalScore={}", hostUid, totalScore);
        return totalScore;
    }

    /**
     * 计算不服务分数
     *
     * @param callSize      通话成功数量
     * @param notServeCount 有礼物没有服务通话数量
     * @param hostUid       主播uid
     * @return int 不服务分数
     */
    public int calculateNotServeScore(int callSize, int notServeCount, String hostUid) {
        logger.info("refresh examine score calculate notServeScore. callSize={} notServeCount={} hostUid={}", callSize, notServeCount, hostUid);
        double notServeRate = notServeCount * 1.0 / callSize;
        if (notServeRate >= 0.5) {
            return 100;
        } else if (notServeRate >= 0.3) {
            return 70;
        } else if (notServeRate >= 0.2) {
            return 40;
        }
        return 0;
    }

    /**
     * 计算平均通话时长分数
     *
     * @param duration 总考核通话时长
     * @param size     总考核通话通数
     * @param hostUid  主播uid
     * @return int 平均通话时长分数
     */
    public int calculateDurationScore(int duration, int size, String hostUid) {
        logger.info("refresh examine score. calculate durationScore. duration={} size={} hostUid={}", duration, size, hostUid);
        int avgDuration = duration / size;
        if (avgDuration >= 120) {
            return 100;
        } else if (avgDuration >= 60) {
            return 70;
        } else {
            return 40;
        }
    }

    /**
     * 计算礼物率分数
     *
     * @param callIncome 通话总收入(通话收入+礼物收入)
     * @param giftValue  礼物收入
     * @param hostUid    主播uid
     * @return int 礼物率分数
     */
    public int calculateGiftRateScore(int callIncome, int giftValue, String hostUid) {
        logger.info("refresh examine score. calculate giftRateScore. callIncome={} giftValue={} hostUid={}", callIncome, giftValue, hostUid);
        if (callIncome == 0) {
            return 0;
        }
        double giftRate = giftValue * 1.0 / callIncome;
        if (giftRate >= 0.1) {
            return 100;
        } else if (giftRate >= 0.05) {
            return 70;
        } else {
            return 40;
        }
    }

    /**
     * 计算复拨率分数
     *
     * @param hostExamineCall 考核所有通话
     * @param hostUid         主播uid
     * @return int 复拨率分数
     */
    public int calculateRedialRateScore(List<ExamineCallLogData> hostExamineCall, String hostUid) {
        double redialRate = getRedialRate(hostExamineCall, hostUid);
        if (redialRate >= 0.1) {
            return 100;
        } else {
            return 70;
        }
    }

    public double getRedialRate(List<ExamineCallLogData> hostExamineCall, String hostUid) {
        Map<String, String> userMap = new HashMap<>();
        Set<String> redialUserSet = new HashSet<>();
        Set<String> totalUserSet = new HashSet<>();
        for (ExamineCallLogData examineCallLogData : hostExamineCall) {
            if (!StringUtils.isEmpty(userMap.get(examineCallLogData.getFromUid()))) {
                redialUserSet.add(examineCallLogData.getFromUid());
            }
            userMap.put(examineCallLogData.getFromUid(), examineCallLogData.getFromUid());
            totalUserSet.add(examineCallLogData.getFromUid());
        }
        if (CollectionUtils.isEmpty(redialUserSet)) {
            return 0;
        }
        logger.info("refresh examine score calculate redialRateScore. redialUserSet={} totalCallSize={} hostUid={}", redialUserSet.size(), totalUserSet.size(), hostUid);
        return redialUserSet.size() * 1.0 / totalUserSet.size();
    }

    /**
     * 计算通话时长超过X秒分数
     *
     * @param exceedCallTimeCount 超过X秒的通话次数
     * @param totalCallCount      所有通话次数
     * @return int 分数
     */
    public int calculateExceedCallTimeScore(int exceedCallTimeCount, int totalCallCount) {
        double rate = exceedCallTimeCount * 1.0 / totalCallCount;
        if (rate >= 0.6) {
            return 100;
        } else if (rate >= 0.4) {
            return 70;
        } else if (rate >= 0.2) {
            return 40;
        } else {
            return 0;
        }
    }

    private int calculateTotalScore(int durationScore, int notServeScore, int giftRateScore, int redialRateScore, int exceedCallTimeScore) {
        double durationScoreMulti = getScoreMultiplication(durationScore, 0.3);
        double giftRateScoreMulti = getScoreMultiplication(giftRateScore, 0.1);
        double redialRateScoreMulti = getScoreMultiplication(redialRateScore, 0.2);
        double exceedCallTimeMulti = getScoreMultiplication(exceedCallTimeScore, 0.4);
        double notServeScoreMulti = getScoreMultiplication(notServeScore, 0.5);
        return (int) (durationScoreMulti + giftRateScoreMulti + redialRateScoreMulti + exceedCallTimeMulti - notServeScoreMulti);
    }

    public double getScoreMultiplication(double v1, double v2) {
        if (v1 == 0 || v2 == 0) {
            return 0;
        }
        BigDecimal resultDecimal = new BigDecimal(String.valueOf(v1)).multiply(new BigDecimal(v2));
        return resultDecimal.doubleValue();
    }

    /**
     * 获取考核电话超过X秒配置
     *
     * @param hostUid 主播uid
     * @return int
     */
    public int getExceedCallTime(String hostUid) {
        ConfigDTO dto = new ConfigDTO();
        dto.setUid(hostUid);
        dto.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        dto.setKey(AppConfigKeyConstant.EXAMINE_CALL_EXCEED_CALL_TIME);
        Integer exceedCallTime = configApi.getIntegerVal(dto);
        logger.info("refresh examine score get exceed call time. uid={} exceedCallTime={}", hostUid, exceedCallTime);
        return exceedCallTime != null ? exceedCallTime : 0;
    }

    /**
     * 保存主播考核分数到数据库
     *
     * @param hostUid      主播uid
     * @param examineScore 考核分数
     */
    public void saveExamineScore(String hostUid, int examineScore) {
        HostExamineScoreLogData data = new HostExamineScoreLogData();
        data.setUid(hostUid);
        data.setExamineScore(examineScore);
        data.setCtime(DateHelper.getCurrentTime());
        data.setMtime(DateHelper.getCurrentTime());
        hostExamineScoreLogDao.insert(data);
        //分数加入缓存
        examineRedis.saveExamineScore(hostUid, examineScore);
    }

    /**
     * 根据考核分数更新主播评分等级分数
     *
     * @param hostUid      主播uid
     * @param examineScore 考核分数
     */
    public void saveAppraiseScore(String hostUid, int examineScore) {
        int appraiseScore = getAppraiseScore(examineScore);
        String today = DateHelper.UTC.getToday();
        HostAppraiseScoreLogData data = new HostAppraiseScoreLogData();
        data.setUid(hostUid);
        data.setHostType(4);
        data.setCtime(DateHelper.getCurrentTime());
        data.setDate(today);
        data.setScore(appraiseScore);
        hostAppraiseScoreLogDao.insertOne(data);
        hostAppraiseRedis.addAppraiseHostScore(hostUid, today, appraiseScore);
    }

    /**
     * 根据考核分数 获取对应的评分等级分数
     *
     * @param examineScore 考核分数
     * @return appraiseScore 评分等级分数
     */
    public int getAppraiseScore(int examineScore) {
        if (examineScore >= 80) {
            return 85;
        } else if (examineScore >= 60) {
            return 70;
        } else {
            return 50;
        }
    }

    /**
     * 获取总考核池主播列表
     *
     * @return
     */
    public List<String> getTotalExamineHostList() {
        List<String> totalExamineHostList = examineRedis.getTotalExamineHostList();
        if (!CollectionUtils.isEmpty(totalExamineHostList)) {
            return totalExamineHostList;
        }
        totalExamineHostList = hostInfoDao.getHostListByHostStatus(HostInfoDao.EXAMINING);
        examineRedis.saveTotalExamineHostList(totalExamineHostList);
        return totalExamineHostList;
    }

    /**
     * 通过试用期加入总考核池
     */
    public void saveExaminePool(String hostUid, int lastCallTime) {
        HostExamineScoreLogData data = new HostExamineScoreLogData();
        data.setUid(hostUid);
        data.setExamineScore(50);
        data.setCtime(DateHelper.getCurrentTime());
        data.setMtime(DateHelper.getCurrentTime());
        hostExamineScoreLogDao.insert(data);
        examineRedis.saveTotalExamineHostList(hostUid);
        examineRedis.saveExamineTimeHostList(hostUid, lastCallTime);
        examineRedis.saveExamineScore(hostUid, 50);
    }

    /**
     * 判断主播是否为考核主播
     *
     * @param hostUid 主播uid
     * @return boolean false:不是 true:是
     */
    public boolean validExamineHost(String hostUid) {
        HostInfoData hostInfo = hostInfoDao.getHostInfo(hostUid);
        if (hostInfo == null || hostInfo.getHostStatus() == null || hostInfo.getHostStatus() != HostInfoDao.EXAMINING) {
            logger.info("decide examine host. host info is error. hostUid={}", hostUid);
            return false;
        }
        return true;
    }

    /**
     * 判断主播是否为考核主播
     *
     * @param hostUid 主播uid
     * @return boolean false:不是 true:是
     */
    public boolean validNewCanCallHost(String hostUid) {
        HostInfoData hostInfo = hostInfoDao.getHostInfo(hostUid);
//        List<Integer> list = Arrays.asList(HostInfoDao.FIRST_PROBATION, HostInfoDao.SECOND_PROBATION, HostInfoDao.EXAMINING);
        // 之前是一筛-二晒-淘汰 现在是 一筛过程不中断（一直到10通电话），根据电话质量评分给出等级
        List<Integer> list = Arrays.asList(HostInfoDao.FIRST_PROBATION);
        if (hostInfo == null || hostInfo.getHostStatus() == null) {
            logger.info("decide examine host. host info is error. hostUid={}", hostUid);
            return false;
        }
        return list.contains(hostInfo.getHostStatus());
    }


    /**
     * 通话结束 写入时长考核池池方法更新
     */
    public void saveExamineTimeHostList(String hostUid) {
        int joinTime = DateHelper.getCurrentTime();
        saveExamineTimeHostList(hostUid, joinTime);
    }

    public void saveExamineTimeHostList(String hostUid, int joinTime) {
        examineRedis.delExamineTimeHostList(hostUid);
        if (validExamineHost(hostUid)) {
            if (joinTime <= 0) {
                joinTime = DateHelper.getCurrentTime();
            }
            examineRedis.saveExamineTimeHostList(hostUid, joinTime);
        }
        //清理长时间未通话的主播
        examineRedis.delExpireExamineTimeHostList();
    }

    /**
     * 获取主播最近一条考核分数
     */
    public Integer getLatelyExamineScore(String hostUid) {
        String examineScoreStr = examineRedis.getExamineScore(hostUid);
        Integer examineScoreInt = 0;
        if (!StringUtils.isEmpty(examineScoreStr)) {
            examineScoreInt = Integer.parseInt(examineScoreStr);
        } else {
            HostExamineScoreLogData latelyData = hostExamineScoreLogDao.getLatelyData(hostUid);
            examineScoreInt = latelyData != null ? latelyData.getExamineScore() : null;
            if (examineScoreInt != null) {
                examineRedis.saveExamineScore(hostUid, examineScoreInt);
            }
        }
        return examineScoreInt;
    }


    /**
     * 获取用户当前最少可通话分钟
     *
     * @return
     */
    public int getUserCanCallMinutes(ActorData actorData) {
        String userUid = actorData.getUid();
        try {
            ItemQueryVO itemQuery60VO = itemsApi.queryCount(userUid, null, null, ItemConfigIdConstant.MATCH_CARD_60S);
            int matchCardTime60 = 0;
            int matchCount60 = 0;
            if (itemQuery60VO != null) {
                int durationConfig = getDurationConfig(actorData, ItemConfigIdConstant.MATCH_CARD_60S);
                matchCount60 = itemQuery60VO.getCount();
                matchCardTime60 = durationConfig * matchCount60;
            }

            ItemQueryVO itemQueryFreeVO = itemsApi.queryCount(userUid, null, null, ItemConfigIdConstant.MATCH_CHAT_FREE);
            int matchCountFree = 0;
            int matchCardTimeFree = 0;
            if (itemQueryFreeVO != null) {
                int durationConfig = getDurationConfig(actorData, ItemConfigIdConstant.MATCH_CHAT_FREE);
                matchCountFree = itemQueryFreeVO.getCount();
                matchCardTimeFree = durationConfig * matchCountFree;
            }

            int matchCardTime = matchCardTime60 + matchCardTimeFree;
            BigDecimal balance = currencyService.getRealCurrency1Balance(userUid);
            long totalSeconds = matchCardTime + balance.longValue();
            long totalMinutes = TimeUnit.SECONDS.toMinutes(totalSeconds);
            return (int) totalMinutes;
        } catch (WebException e) {
            logger.error("error. code={} msg={} uid={}", e.getHttpCode().getCode(), e.getHttpCode().getMsg(), userUid);
            return 0;
        }
    }

    private int getDurationConfig(ActorData actorData, int configId) {
        ItemsConfigData itemsConfigData = itemsConfigDao.queryByConfigId(configId);
        ConfigDTO configDTO = new ConfigDTO();
        configDTO.setKey(itemsConfigData.getDurationConfigKey());
        configDTO.setUid(actorData.getUid());
        configDTO.setStatus(AppConfigKeyConstant.STATUS_ALL);
        Integer callCardDuration = configApi.getIntegerVal(configDTO);
        if (callCardDuration == null) {
            callCardDuration = 20;
        }
        return callCardDuration;
    }

    /**
     * 该用户是否可以定义为考核用户
     * return true-可以定义为考核用户  false-不可以定义为考核用户
     */
    public boolean validExamineUser(String userUid) {
        ActorData actorData = actorMgr.getActorData(userUid);
        if (actorData == null) {
            return false;
        }
        Integer isPayUser = actorData.getIsPayUser();
        if (!(isPayUser != null && isPayUser == 1)) {
            return false;
        }
        ConfigDTO configDTO = new ConfigDTO();
        configDTO.setKey(AppConfigKeyConstant.EXAMINE_USER_MIN_CALL_DURATION);
        configDTO.setUid(userUid);
        configDTO.setStatus(AppConfigKeyConstant.STATUS_SERVER);
        Integer needExamineMinMinutes = configApi.getIntegerVal(configDTO);
        if (needExamineMinMinutes == null) {
            needExamineMinMinutes = 2;
        }
        int userExamineMinMinutes = getUserCanCallMinutes(actorData);
        return userExamineMinMinutes >= needExamineMinMinutes;
    }

    /**
     * 发送im提醒主播
     *
     * @param hostUid       主播id
     * @param appraiseScore 主播评分等级
     */
    private void sendResultToHost(String hostUid, int appraiseScore) {
        ActorData actorData = actorMgr.getActorData(hostUid);
        if (actorData == null) {
            return;
        }
        if ((actorData.getPlatform() == ClientSystemType.ANDRIOD && actorData.getVer() >= ANDRIOD_SEND_MSG_VER)
                || (actorData.getPlatform() == ClientSystemType.IOS && actorData.getVer() >= IOS_SEND_MSG_VER)) {
            ProbationResultMsg msg = new ProbationResultMsg();
            msg.setUid(hostUid);
            msg.setTitle("Verification success");
            msg.setMsgBody("Congratulations! You completed all verifications. More calls to come! Keep it up!");
            msg.setResult(1);
            msg.setHostAppraiseLevel(hostAppraiseLevelDao.getHostAppraiseLevelBase(hostUid, 0, appraiseScore, 1));
            SendConfigData sendConfigData = new SendConfigData();
            sendConfigData.setSendType(ImApi.SEND_MSG_REPEAT);
            InnerSendMsgDTO dto = new InnerSendMsgDTO();
            dto.setCmd(msg.getCmd());
            dto.setToUid(hostUid);
            dto.setConfigData(sendConfigData);
            dto.setBody(msg.toBody());
            imApi.sendMsg(dto);
            logger.info("send examine success msg result.uid={} msg={} dto={}", hostUid, msg, dto);
        }
    }

}
