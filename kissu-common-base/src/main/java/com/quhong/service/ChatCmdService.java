package com.quhong.service;


import com.quhong.constant.PlayerStatusConstant;
import com.quhong.core.constant.RedisCacheManagerConstant;
import com.quhong.core.datas.ChatCmdData;
import com.quhong.core.datas.ChatCmdUser;
import com.quhong.core.enums.ActorType;
import com.quhong.core.enums.CallFromType;
import com.quhong.core.enums.ClientSystemType;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.ChatRecordLogDao;
import com.quhong.dao.HostInfoDao;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.ChatRecordLogData;
import com.quhong.dao.datas.HostInfoData;
import com.quhong.data.dto.autoMesage.UserSignDTO;
import com.quhong.data.vo.autoMessage.UserSignVO;
import com.quhong.enums.UserSignType;
import com.quhong.players.ActorMgr;
import com.quhong.redis.ChatCmdRedis;
import com.quhong.redis.HostGradeRedis;
import com.quhong.redis.MatchListRedis;
import com.quhong.redis.PlayerRedis;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Component
@Lazy
public class ChatCmdService {
    private static final Logger logger = LoggerFactory.getLogger(ChatCmdRedis.class);

//    public static final int IOS_GIFT_PAY_VERSION = 75;
//    public static final int IOS_GIFT_PAY_VERSION_76 = 76;

    @Autowired
    protected ChatCmdRedis chatCmdRedis;
    @Autowired
    protected ActorMgr actorMgr;
    @Autowired
    protected ChatRecordLogDao chatRecordDao;
    @Autowired
    protected HostInfoDao hostInfoDao;
    @Autowired
    protected PlayerRedis playerRedis;
    @Autowired
    protected HostGradeService hostGradeService;
    @Autowired
    protected HostGradeRedis hostGradeRedis;
    @Autowired
    protected GuaranteeService guaranteeService;
    @Autowired
    protected MatchListRedis matchListRedis;
    @Autowired
    protected AutoMessageApi autoMessageApi;

    public ChatCmdData getChatCmdData(String cid) {
        ChatCmdData data = chatCmdRedis.getChatData(cid);
        if (data != null) {
            return data;
        }
        ChatRecordLogData recordData = chatRecordDao.findOne(cid);
        if (recordData == null) {
            logger.error("record data can not find. cid={}", cid);
            return null;
        }
        if (recordData.getEndAt() != null && recordData.getEndAt() > 0) {
            logger.error("record data has end. cid={}", cid);
            return null;
        }
        data = createChatCmdData(recordData, cid);
        chatCmdRedis.saveDataToRedis(data);
        return data;
    }

    public void saveChatCmdData(ChatCmdData cmdData) {
        chatCmdRedis.saveDataToRedis(cmdData);
    }


    public ChatCmdData createChatCmdData(ChatRecordLogData data, String cid) {
        ChatCmdData cmdData = new ChatCmdData();
        cmdData.setCid(cid);
        cmdData.setFromUid(data.getFromUid());
        cmdData.setToUid(data.getToUid());
        cmdData.setCallType(data.getCallType());
        cmdData.setCallChannel(data.getCallChannel());
        cmdData.setFromType(data.getFromType());
        if (data.getFromType() == CallFromType.FROM_HOST_MATCH || data.getFromType() == CallFromType.FROM_HOST_CALL) {
            cmdData.getUserList().add(createUser(cmdData.getFromUid(), false));
            cmdData.getUserList().add(createUser(cmdData.getToUid(), true));
        } else {
            cmdData.getUserList().add(createUser(cmdData.getFromUid(), true));
            cmdData.getUserList().add(createUser(cmdData.getToUid(), false));
        }
        cmdData.setRecordId(data.getRecordId());
        cmdData.setOpenTime(data.getCallAt() * 1000L);
        cmdData.setPrice(data.getPrice());
        return cmdData;
    }

    public ChatCmdUser createUser(String uid, boolean isDial) {
        ActorData actorData = actorMgr.getActorData(uid, "createUser");
        ChatCmdUser user = new ChatCmdUser();
        user.setUid(uid);
        user.setRid(actorData.getRid());
        user.setDial(isDial);
        user.setHost(actorData.getGender() == ActorType.HOST);
        return user;
    }

    public ChatRecordLogDao getChatRecordDao() {
        return chatRecordDao;
    }

    public boolean useGiftPay(ChatCmdData cmdData, boolean hasHost) {
        String uid = null;
        if (hasHost) {
            for (ChatCmdUser user : cmdData.getUserList()) {
                if (!user.isHost()) {
                    uid = user.getUid();
                    break;
                }
            }
            if (uid == null) {
                logger.error("getChatGift. do not find user. cid={}", cmdData.getCid());
                uid = cmdData.getFromUid();
            }
        } else {
            uid = cmdData.getFromUid();
        }
        ActorData actorData = actorMgr.getActorData(uid, "useGiftPay");
        return useGiftPay(cmdData, actorData);
    }

    public boolean useGiftPay(ChatCmdData cmdData, ActorData actorData) {
        if (actorData.getPlatform() != ClientSystemType.IOS) {
            return false;
        }
        if (cmdData.isSubscription()) {
            // ios 订阅 按正常扣费走
            return false;
        }

        return false;
    }

    /**
     * 通话结束 写入时长保收池方法更新
     *
     * @param hostUid
     */
    public void savePositiveHostLatestCallTime(String hostUid) {
        chatCmdRedis.removeLatestPositiveHostNoCallSet(hostUid);
        ActorData actorData = actorMgr.getActorData(hostUid);
        if (guaranteeService.decideGuaranteePoolConfigType(actorData, GuaranteeService.TIME_GUARANTEE_POOL)) {
            saveLatestPositiveHostNoCallList(hostUid, 1);
        }
        int curTimeStamp = DateHelper.getCurrentTime();
        matchListRedis.saveLatestHighQualityHostCallTimeToRedis(hostUid, 2, curTimeStamp);
    }

    public void saveProbationHostLatestCallTime(String hostUid) {
        chatCmdRedis.removeLatestProbationHostNoCallSet(hostUid, 1);
        HostInfoData hostInfoData = hostInfoDao.getHostInfo(hostUid);
        if (hostInfoData == null) {
            return;
        }
        if (hostInfoData.getHostStatus() == null || !hostInfoDao.getProbationTypeSet().contains(hostInfoData.getHostStatus())) {
            return;
        }
//        int curTime = DateUtils.getNowSeconds();
//        if (hostInfoData.getActivateTime() == null || curTime - hostInfoData.getActivateTime() > 4 * 24 * 60 * 60){
//            return;
//        }
        saveLatestProbationHostNoCallList(hostUid, 1);
    }

    /**
     * 获取长时间没有通话的优质主播列表
     *
     * @return
     */
    public List<String> getLatestPositiveHostNoCallList() {
        List<String> retList = new ArrayList<>();
        try {
            Set<String> guaranteedCallHostSet = chatCmdRedis.getLatestPositiveHostNoCallSet();
            if (guaranteedCallHostSet.isEmpty()) {
                return retList;
            }
            for (String hostUid : guaranteedCallHostSet) {
                int status = playerRedis.getStatus(hostUid);
                if (status != PlayerStatusConstant.ONLINE) {
                    continue;
                }
                retList.add(hostUid);
            }
            return retList;
        } catch (Exception e) {
            logger.error("get latest positive host no call list error. {}", e.getMessage(), e);
        }
        return retList;
    }

    /**
     * 获取长时间没有通话的优质主播列表 兜底
     *
     * @return
     */
    public List<String> getLatestPositiveMinimumGuaranteeTimeList() {
        List<String> retList = new ArrayList<>();
        try {
            Set<String> minimumGuaranteedCallHostSet = chatCmdRedis.getLatestPositiveMinimumGuaranteeTimeSet();
            if (minimumGuaranteedCallHostSet.isEmpty()) {
                return retList;
            }
            for (String hostUid : minimumGuaranteedCallHostSet) {
                int status = playerRedis.getStatus(hostUid);
                if (status != PlayerStatusConstant.ONLINE) {
                    continue;
                }
                retList.add(hostUid);
            }
            return retList;
        } catch (Exception e) {
            logger.error("get latest positive host no call list error. {}", e.getMessage(), e);
        }
        return retList;
    }

    /**
     * 获取长时间没有通话的试用期主播列表
     *
     * @return
     */
    public List<String> getLatestProbationHostNoCallList() {
        List<String> retList = new ArrayList<>();
        try {
            Set<String> probationCallHostSet = chatCmdRedis.getLatestProbationHostNoCallSet();
            if (probationCallHostSet.isEmpty()) {
                return retList;
            }
            for (String hostUid : probationCallHostSet) {
                int status = playerRedis.getStatus(hostUid);
                if (status != PlayerStatusConstant.ONLINE) {
                    continue;
                }
                retList.add(hostUid);
            }
            return retList;
        } catch (Exception e) {
            logger.error("get latest positive host no call list error. {}", e.getMessage(), e);
        }
        return retList;
    }


    /**
     * 更新长时间没有通话的优质主播(保收)列表
     *
     * @return
     */
    public void saveLatestPositiveHostNoCallList(String uid, int positiveFromType) {
        List<String> guaranteeHostList = hostGradeService.getGuaranteeHostList(uid);
        if (guaranteeHostList.contains(uid)) {
            chatCmdRedis.saveLatestPositiveHostCallTimeToRedis(uid, positiveFromType);
        }
    }

    /**
     * 更新长时间没有通话的试用期（筛选）主播列表
     *
     * @return
     */
    public void saveLatestProbationHostNoCallList(String uid, int probationFromType) {
        if (hostGradeService.validProbation(uid)) {
            chatCmdRedis.saveLatestProbationHostCallTimeToRedis(uid, probationFromType);
        }
    }



    /**
     * 判断用户和主播都需要有考核分类的标签 返回 1 否则 0
     * @see UserSignType#HOST_FIRST_SCREENING
     */
    public int getNeedProbationReport(String uid, String hostDataUid) {
        // 获取HOST_FIRST_SCREENING分类的标签，看看有符合什么标签
        // 获取用户
        List<String> userSignList = this.getFirstScreeningUserSignList(uid);
        // 获取主播的
        List<String> hostSignList = this.getFirstScreeningUserSignList(hostDataUid);
        if (CollectionUtils.isEmpty(userSignList) || CollectionUtils.isEmpty(hostSignList)) {
            return 0;
        }
        return 1;
    }

    /**
     * 根据用户唯一标识符获取用户签名列表
     *
     * @param uid 用户唯一标识符
     * @return 用户签名列表
     */
    @Cacheable(value = "getHostFirstScreeningUserSignList#300", key = "#uid",cacheManager = RedisCacheManagerConstant.CAFFEINE_EXPIRE_DYNAMIC_AFTER_WRITE_MANAGER)
    public List<String> getFirstScreeningUserSignList(String uid) {
        // 创建一个UserSignDTO对象并设置相关信息
        UserSignDTO userSignDTO = new UserSignDTO();
        userSignDTO.setUid(uid);
        userSignDTO.setSignType(UserSignType.HOST_FIRST_SCREENING);
        // 通过API获取用户签名信息
        UserSignVO userSignVO = autoMessageApi.getUserSign(userSignDTO);
        // 如果API返回的结果为空，则返回一个空列表
        if (userSignVO == null) {
            return new ArrayList<>();
        }
        // 返回用户签名列表
        return userSignVO.getSignList();
    }

}
