package com.quhong.redis;

import com.quhong.core.constant.BaseRedisBeanConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class UserGroupRedis {
    private static final Logger logger = LoggerFactory.getLogger(UserGroupRedis.class);

    /**
     * 用户组的缓存 (pre+uid)
     */
    public static final String USER_GROUP = "str:user_group:uid:";

    @Resource(name = BaseRedisBeanConstant.GRADE_BEAN)
    private StringRedisTemplate userGroupTemplate;

    private String getUserGroupKey(String uid) {
        return USER_GROUP + uid;
    }

    public void saveUserGroupToRedis(String uid, int value) {
        userGroupTemplate.opsForValue().set(getUserGroupKey(uid), Integer.toString(value), 2, TimeUnit.MINUTES);
    }

    public Integer getUserGroup(String uid) {
        try {
            String value = userGroupTemplate.opsForValue().get(getUserGroupKey(uid));
            if (StringUtils.isEmpty(value)) {
                return null;
            }
            return Integer.parseInt(value);
        } catch (Exception e) {
            logger.error("get user group from redis. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }
}
