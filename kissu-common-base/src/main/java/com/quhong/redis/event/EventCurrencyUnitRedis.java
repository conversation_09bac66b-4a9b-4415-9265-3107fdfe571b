package com.quhong.redis.event;

import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.redis.BaseHashSaveRedis;
import com.quhong.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025-06-04 12:45
 */
@Lazy
@Slf4j
@Component
public class EventCurrencyUnitRedis {
    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedis;
    @Resource
    private BaseHashSaveRedis baseHashSaveRedis;

    private String hashCurrencyUnitKey(Integer eventCode, Integer unitId) {
        return "hash:unit_event:currency_unit:" + eventCode + ":" + unitId;
    }

    public long increase(Integer eventCode, Integer unitId, String uid, long increase) {
        String key = hashCurrencyUnitKey(eventCode, unitId);
        return baseHashSaveRedis.increaseCount(key, uid, increase);
    }

    public long decrease(Integer eventCode, Integer unitId, String uid, long decrease) {
        String key = hashCurrencyUnitKey(eventCode, unitId);
        return baseHashSaveRedis.decreaseCount(key, uid, decrease);
    }

    public long getBalance(Integer eventCode, Integer unitId, String uid) {
        String key = hashCurrencyUnitKey(eventCode, unitId);
        String str = baseHashSaveRedis.getStrByRedis(key, uid);
        if (StringUtils.isEmpty(str)) {
            return 0L;
        }
        return new Double(str).longValue();
    }


}
