package com.quhong.redis.event;

import com.quhong.constant.LangIdConstant;
import com.quhong.constant.OfficialUserGroup;
import com.quhong.core.constant.WarnName;
import com.quhong.core.monitor.MonitorSender;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.MathUtils;
import com.quhong.dao.datas.ActorData;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.app.config.activity.unit.EventUnit;
import com.quhong.dao.datas.app.config.activity.unit.RankRewardConfig;
import com.quhong.data.vo.CountVO;
import com.quhong.data.vo.model.rank.ModelRankVO;
import com.quhong.data.vo.model.rank.row.ActorInfo;
import com.quhong.data.vo.model.rank.row.RankRowVO;
import com.quhong.moderatoin.ModerationService;
import com.quhong.players.ActorMgr;
import com.quhong.redis.BaseZSetRedis;
import com.quhong.service.OfficialNoticeService;
import com.quhong.service.common.RewardService;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2025-06-04 19:11
 */
@Lazy
@Slf4j
@Component
@RequiredArgsConstructor
public class EventRankUnitService {
    public static final String TEXT_FORMAT = "\uD83D\uDE01 Congratulations to the follows get Top#top in \"#eventName\" Event #rankName\n" +
            "#content" +
            "\uD83D\uDE01 The reward has been issued>>>>";

    public static final String KEY_PRE = "zset:unit_event:rank_unit:";

    private final BaseZSetRedis baseZSetRedis;
    private final ActorMgr actorMgr;
    private final ModerationService moderationService;
    private final OfficialNoticeService officialNoticeService;
    private final MonitorSender monitorSender;
    private final RewardService rewardService;


    /**
     * 榜单key
     */
    public String zsetRankKey(Integer eventCode, Integer unitId) {
        return KEY_PRE + eventCode + ":" + unitId;
    }


    public void rankValueIncrease(Integer eventCode, Integer unitId, String uid, double incr) {
        String rankKey = zsetRankKey(eventCode, unitId);
        baseZSetRedis.increaseToZSet(rankKey, uid, incr);
    }

    public void coverRankValue(Integer eventCode, Integer unitId, String uid, long count) {
        String rankKey = zsetRankKey(eventCode, unitId);
        baseZSetRedis.addToZSet(rankKey, uid, count);
    }

    /**
     * 获取榜单列表(只有简单的键值)
     */
    public List<CountVO> getRankList(Integer eventCode, Integer unitId, int page, int pageSize) {
        String key = zsetRankKey(eventCode, unitId);
        return baseZSetRedis.getRange(key, page, pageSize);
    }

    public CountVO getOne(Integer eventCode, Integer unitId, String uid) {
        String rankKey = zsetRankKey(eventCode, unitId);
        return baseZSetRedis.getOne(rankKey, uid);
    }

    public Long getRankNum(Integer eventCode, Integer unitId, String uid) {
        String rankKey = zsetRankKey(eventCode, unitId);
        return baseZSetRedis.getRank(rankKey, uid);
    }

    public RankRowVO findTop1(Integer eventCode, Integer unitId) {
        String key = zsetRankKey(eventCode, unitId);
        List<CountVO> top1s = baseZSetRedis.getRange(key, 1, 1);
        if (!ObjectUtils.isEmpty(top1s)) {
            List<RankRowVO> top1Rows = fillRankRowList(top1s);
            return top1Rows.get(0);
        }
        return new RankRowVO();
    }

    public ModelRankVO<RankRowVO> rank(ActorData currActor, Integer eventCode, Integer unitId) {
        return rank(currActor, eventCode, unitId, "99+");
    }

    /**
     * 榜单接口
     *
     * @param eventCode      活动id
     * @param unitId         榜单组件id
     * @param defaultRankNum 榜单排名默认值
     */
    public ModelRankVO<RankRowVO> rank(ActorData currActor, Integer eventCode, Integer unitId, String defaultRankNum) {
        String rankKey = zsetRankKey(eventCode, unitId);
        List<RankRowVO> rankList = fillRankList(rankKey);
        RankRowVO self = fillSelfData(currActor, rankKey, defaultRankNum);
        return new ModelRankVO<>(self, rankList);
    }

    private RankRowVO fillSelfData(ActorData currActor, String rankKey, String defaultRankNum) {
        RankRowVO self = new RankRowVO();
        self.setRankNum(defaultRankNum);
        ActorInfo actorInfo = new ActorInfo(currActor);
        String head = moderationService.dealRankHeadModeration(currActor);
        actorInfo.setHead(head);
        self.setActorInfo(actorInfo);
        Long rank = baseZSetRedis.getRank(rankKey, currActor.getUid());
        if (rank != 0) {
            if (rank < 100) {
                self.setRankNum(rank.toString());
            }
            CountVO selfData = baseZSetRedis.getOne(rankKey, currActor.getUid());
            self.setScore(selfData.getCount());
            self.setRealScore(String.valueOf(selfData.fetchRealCount()));
        }
        return self;
    }

    private List<RankRowVO> fillRankList(String rankKey) {
        List<CountVO> dataList = baseZSetRedis.getRange(rankKey, 1, 10);
        return fillRankRowList(dataList);
    }

    public List<RankRowVO> fillRankRowList(List<CountVO> dataList) {
        if (dataList.isEmpty()) {
            return new ArrayList<>(0);
        }
        return IntStream.range(0, dataList.size())
                .mapToObj(index -> this.fillRankRowVO(index + 1, dataList.get(index)))
                .collect(Collectors.toList());
    }

    private RankRowVO fillRankRowVO(Integer rankNum, CountVO data) {
        RankRowVO vo = new RankRowVO();
        vo.setRankNum(rankNum.toString());
        vo.setScore(data.getCount());
        vo.setRealScore(String.valueOf(data.fetchRealCount()));
        ActorData rankActor = actorMgr.getActorDataFromCache(data.getUid());
        ActorInfo actorInfo = fillActorInfo(rankActor);
        actorInfo.setUid(data.getUid());
        vo.setActorInfo(actorInfo);
        return vo;
    }

    private ActorInfo fillActorInfo(ActorData rankActor) {
        ActorInfo actorInfo = new ActorInfo();
        if (rankActor == null) {
            return actorInfo;
        }
        actorInfo = new ActorInfo(rankActor);
        String head = moderationService.dealRankHeadModeration(rankActor);
        actorInfo.setHead(head);
        return actorInfo;
    }


    private static void fillGlobalNoticeFormatter(RankRewardsDTO dto) {
        if (!dto.unit.getRankUnit().getAutoSendGlobalNotice()) {
            dto.setGlobalNoticeFormatter(null);
            return;
        }
        if (StringUtils.isEmpty(dto.getGlobalNoticeFormatter())) {
            dto.setGlobalNoticeFormatter(TEXT_FORMAT);
        }
        String formatter = dto.getGlobalNoticeFormatter().replace("#eventName", dto.getConfigData().getName())
                .replace("#top", dto.getUnit().getRankUnit().getAwardTop() + "")
                .replace("#rankName", dto.getUnit().getUnitName());
        dto.setGlobalNoticeFormatter(formatter);
    }

    /**
     * （推荐）榜单奖励发放方法
     */
    public List<CountVO> rankRewards(RankRewardsDTO dto) {
        fillGlobalNoticeFormatter(dto);
        EventUnit rankUnit = dto.getUnit();
        AppConfigActivityData configData = dto.getConfigData();
        fillChangeDesc(rankUnit, dto);
        String rankKey = zsetRankKey(configData.getActivityCode(), rankUnit.getUnitId());
        List<CountVO> dataList = baseZSetRedis.getRange(rankKey, 1, rankUnit.getRankUnit().getAwardTop());
        if (ObjectUtils.isEmpty(dataList)) {
            return new ArrayList<>(0);
        }
        StringBuilder content = new StringBuilder();
        content.append("## ").append(rankUnit.getUnitName()).append("（").append(configData.getActivityDesc()).append(")\n")
                .append("排名\t分数\t\t用户id\n");
        StringBuilder globalContent = new StringBuilder();
        IntStream.range(0, dataList.size())
                .forEach(index -> this.giveRewardAndFillNotice(index + 1, dataList.get(index), content, rankUnit.getRankUnit().getRewardConfigs(), globalContent, dto.getChangeDesc(), configData, rankUnit));
        if (rankUnit.getRankUnit().getAutoSendGlobalNotice()) {
            sendGlobalNotice(configData, dto.getGlobalNoticeFormatter(), configData.getNoticeImg(), configData.getUrl(), globalContent);
        }
        monitorSender.customMarkdown(WarnName.ACTIVITY_RANK_NOTICE, content.toString());
        return dataList;
    }

    private void fillChangeDesc(EventUnit rankUnit, RankRewardsDTO dto) {
        if (StringUtils.isEmpty(dto.getChangeDesc())) {
            dto.setChangeDesc(rankUnit.getUnitName());
        }
    }

    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class RankRewardsDTO {
        /**
         * 活动配置
         */
        @NonNull
        private AppConfigActivityData configData;
        /**
         * 组件信息
         */
        @NonNull
        private EventUnit unit;

        /**
         * 数数物品变动信息描述
         * 没填时会默认填充unit.unitName
         */
        private String changeDesc = "";

        /**
         * 全局通知消息结构
         * 结构：pre\n #content end
         */
        private String globalNoticeFormatter;
    }


    /**
     * @param configData            活动配置
     * @param globalNoticeFormatter 结构：pre\n #content end
     * @param noticeImg             消息图片地址
     * @param eventUrl              活动地址
     * @param globalContent         全局内容
     */
    private void sendGlobalNotice(AppConfigActivityData configData, String globalNoticeFormatter, String noticeImg, String eventUrl, StringBuilder globalContent) {
        if (!StringUtils.hasLength(globalNoticeFormatter)) {
            return;
        }
        String globalNotice = globalNoticeFormatter.replace("#content", globalContent.toString());
        configData.getChannelSet().forEach(channel -> sendGlobalNotice(configData, noticeImg, eventUrl, channel, globalNotice));
    }

    private void sendGlobalNotice(AppConfigActivityData configData, String noticeImg, String eventUrl, String channel, String globalNotice) {
        int fixTime = DateHelper.getCurrentTime() + MathUtils.randomSplitInt(0, 50);
        officialNoticeService.sendGlobalNotice(configData.getName(), globalNotice, noticeImg, eventUrl, channel,
                fixTime, configData.getActivityCode(),
                LangIdConstant.ENGLISH, null, OfficialUserGroup.CHANNEL);
    }

    private void giveRewardAndFillNotice(int rankNum, CountVO data, StringBuilder content, List<RankRewardConfig> rewardConfigs, StringBuilder globalContent, String desc, AppConfigActivityData configData, EventUnit rankUnit) {
        try {
            content.append(rankNum).append("\t").append(data.getCount()).append("\t").append(data.getUid());
            ActorData rankActor = actorMgr.getActorDataFromCache(data.getUid());
            if (rankActor == null) {
                return;
            }
            content.append("\t").append(rankActor.getRid());
            rewardConfigs.stream()
                    .filter(reward -> rankNum >= reward.getMinTop())
                    .max(Comparator.comparingLong(RankRewardConfig::getMinTop))
                    .ifPresent(rewardConfig ->
                            giveReward(rankNum, data, rewardConfig, globalContent, desc, rankActor, configData, rankUnit));
        } finally {
            content.append("\n");
        }
    }

    private void giveReward(int rankNum, CountVO data, RankRewardConfig rewardConfig, StringBuilder globalContent, String desc, ActorData rankActor, AppConfigActivityData configData, EventUnit rankUnit) {
        if (rankNum <= 3) {
            globalContent.append("Top").append(rankNum).append(" ").append(rankActor.getRid()).append("\n");
        }
        if (!rankUnit.getRankUnit().getAutoGiveAward()) {
            return;
        }
//        List<RewardInfoData> rewards = checkRewards(data, rewardConfig);
//        giveOutRewardService.giveEventReward(data.getUid(), rewards, configData.getActivityCode(), desc);
        giveRewardByAwardsKey(data, rewardConfig, desc, configData);
    }

    private void giveRewardByAwardsKey(CountVO data, RankRewardConfig rewardConfig, String desc, AppConfigActivityData configData) {
        RewardService.GiveAwardsKeyDTO dto = new RewardService.GiveAwardsKeyDTO()
                .setUid(data.getUid())
                .setAwardsKey(rewardConfig.getAwardsKey())
                .setEventCode(configData.getActivityCode())
                .setChangeDesc(desc)
                .setDescDetail(desc)
                .setOperator("auto settlement rank")
                .setEventUrl(configData.getUrl());
        rewardService.giveAwardsKeyReward(dto);
    }
}
