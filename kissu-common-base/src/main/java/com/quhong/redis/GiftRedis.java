package com.quhong.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName GiftRedis
 * <AUTHOR>
 * @date 2022/7/13 16:43
 */
@Component
@Lazy
public class GiftRedis {
    private static final Logger logger = LoggerFactory.getLogger(GiftRedis.class);

    private static final int COMBO_EXPIRE = 7; //礼物连击缓存过期时间 单位s
    private static final int CHANGE_RELATION_EXPIRE = 2;

    /**
     * 麦位贡献榜过期时间（1天）
     */
    private static final int MIC_CONTRIBUTION_RANK_EXPIRE = 24 * 60 * 60;

    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedisTemplate;
    @Resource(name = BaseRedisBeanConstant.PUBLISH_BEAN)
    private StringRedisTemplate publishRedisTemplate;


    public String getComboKey(String uid, String roomId, int giftId) {
        return "str:live_room:gift:from:" + uid + ":room_id:" + roomId + ":gift_id:" + giftId;
    }

    public String getLastGiftKey(String uid, String roomId) {
        return "str:live_room:gift:from:" + uid + ":room_id:" + roomId;
    }

    public String getLastSendRidListKey(String uid, String roomId) {
        return "str:room_gift:gift:last_send_rid_list:uid:" + uid + ":room_id:" + roomId;
    }

    public void setLastSendRidList(String uid, String roomId, String ridListStr) {
        String key = getLastSendRidListKey(uid, roomId);
        mainRedisTemplate.opsForValue().set(key, ridListStr, 1, TimeUnit.HOURS);
    }

    public String getLastSendRidList(String uid, String roomId) {
        String key = getLastSendRidListKey(uid, roomId);
        return mainRedisTemplate.opsForValue().get(key);
    }

    public String getMicIncomeRecordKey(String uid, String roomId, String chapterId) {
        return "str:live_room:mic_income:" + roomId + ":" + chapterId + ":" + uid;
    }

    public String getChangeRelationKey(int giftRecordId) {
        return "str:gift_change_relation:" + giftRecordId;
    }

    public String getGiftDailyKey(int dayOfMonth) {
        return "gift_daily_" + dayOfMonth;
    }

    public String getGiftWeekKey(int weekOfYear) {
        return "gift_weekly_" + weekOfYear;
    }

    public long incrCombo(String uid, String roomId, int giftId, int number) {
        String comboKey = getComboKey(uid, roomId, giftId);
        Long increment = mainRedisTemplate.opsForValue().increment(comboKey, number);
        mainRedisTemplate.expire(comboKey, COMBO_EXPIRE, TimeUnit.SECONDS);
        return increment != null ? increment : 0;
    }

    public void delCombo(String uid, String roomId, int giftId) {
        String comboKey = getComboKey(uid, roomId, giftId);
        mainRedisTemplate.delete(comboKey);
    }

    public String getLastGift(String uid, String roomId) {
        String lastGiftKey = getLastGiftKey(uid, roomId);
        return mainRedisTemplate.opsForValue().get(lastGiftKey);
    }

    public void saveLastGift(String uid, String roomId, String giftId) {
        String lastGiftKey = getLastGiftKey(uid, roomId);
        mainRedisTemplate.opsForValue().set(lastGiftKey, giftId, COMBO_EXPIRE, TimeUnit.SECONDS);
    }

    public void sendGiftPublish(JSONObject jsonObject, String topic) {
        try {
            JSONObject msgObject = new JSONObject();
            String msgId = UUID.randomUUID().toString().replace("-", "");
            msgObject.put("msg_id", msgId);
            msgObject.put("msg_body", jsonObject);
            String jsonValue = JSONObject.toJSONString(msgObject);
            publishRedisTemplate.convertAndSend(topic, jsonValue);
        } catch (Exception e) {
            logger.error("send gift publish error. e={}{} topic={} data={}", e, e.getMessage(), topic, JSONObject.toJSONString(jsonObject));
        }
    }

    public void recordExposurePublish(JSONObject jsonObject, String topic) {
        try {
            JSONObject msgObject = new JSONObject();
            msgObject.put("msg_body", jsonObject);
            publishRedisTemplate.convertAndSend(topic, JSONObject.toJSONString(msgObject));
        } catch (Exception e) {
            logger.error("record exposure publish error. e={}{} topic={} data={}", e, e.getMessage(), topic, JSONObject.toJSONString(jsonObject));
        }
    }

    public void saveChangeRelation(int giftRecordId, String changeRelation) {
        String changeRelationKey = getChangeRelationKey(giftRecordId);
        mainRedisTemplate.opsForValue().set(changeRelationKey, changeRelation, CHANGE_RELATION_EXPIRE, TimeUnit.DAYS);
    }

    public String getChangeRelation(int giftRecordId) {
        String changeRelationKey = getChangeRelationKey(giftRecordId);
        return mainRedisTemplate.opsForValue().get(changeRelationKey);
    }

    public double getMicIncome(String uid, String roomId, String chapterId) {
        String strVal = getMicIncomeStrVal(uid, roomId, chapterId);
        if (StringUtils.isEmpty(strVal)) {
            return 0;
        }
        return Double.parseDouble(strVal);
    }

    public String getMicIncomeStrVal(String uid, String roomId, String chapterId) {
        String key = getMicIncomeRecordKey(uid, roomId, chapterId);
        return mainRedisTemplate.opsForValue().get(key);
    }

    public double incMicIncome(String uid, String roomId, double gain, String chapterId) {
        String key = getMicIncomeRecordKey(uid, roomId, chapterId);
        Double increment = mainRedisTemplate.opsForValue().increment(key, gain);
        mainRedisTemplate.expire(key, 5, TimeUnit.HOURS);
        return increment == null ? 0 : increment;
    }

    public void saveMicIncome(String uid, String roomId, double gain, String chapterId) {
        String key = getMicIncomeRecordKey(uid, roomId, chapterId);
        mainRedisTemplate.opsForValue().set(key, String.valueOf(gain), 5, TimeUnit.HOURS);
    }

//    public void detMicIncome(String uid, String roomId, String chapterId) {
//        String key = getMicIncomeRecordKey(uid, roomId, chapterId);
//        mainRedisTemplate.delete(key);
//    }

    private String getGiftNoticeListKey(int recordType) {
        return "str:gift:notice:type:" + recordType;
    }

    public void saveGiftNoticeList(int recordType, String value) {
        String key = getGiftNoticeListKey(recordType);
        mainRedisTemplate.opsForValue().set(key, value, 10, TimeUnit.MINUTES);
    }

    public String getGiftNoticeList(int recordType) {
        String key = getGiftNoticeListKey(recordType);
        return mainRedisTemplate.opsForValue().get(key);
    }

    public void delGiftNoticeList(int recordType) {
        String key = getGiftNoticeListKey(recordType);
        mainRedisTemplate.delete(key);
    }

    private String getCarouselListKey() {
        return "str:gift:notice:carouse_list";
    }

    public void saveCarouselList(String value) {
        String key = getCarouselListKey();
        mainRedisTemplate.opsForValue().set(key, value, 10, TimeUnit.MINUTES);
    }

    public String getCarouselList() {
        String key = getCarouselListKey();
        return mainRedisTemplate.opsForValue().get(key);
    }

    public void delCarouselList() {
        String key = getCarouselListKey();
        mainRedisTemplate.delete(key);
    }

    public void recordRoomGift(String roomId, String sendUid) {
        if (StringUtils.isEmpty(roomId)) {
            return;
        }
        String key = getRecordKey(roomId, sendUid);
        mainRedisTemplate.opsForValue().set(key, DateHelper.getCurrentTime() + "", 3, TimeUnit.DAYS);
    }

    /**
     * 是否有发送礼物记录
     *
     * @param roomId
     * @param sendUid
     * @param channel
     * @return
     */
    public boolean hasSendGiftInRoom(String roomId, String sendUid, String channel) {
        String key = getRecordKey(roomId, sendUid);
        String value = mainRedisTemplate.opsForValue().get(key);
        if (StringUtils.hasLength(value)) {
            long sendTime = Long.parseLong(value);
            long limitTime = DateHelper.genDateHelper(channel).getDayOffset(-2) / 1000;
            return sendTime >= limitTime;
        }
        return false;
    }

    private String getRecordKey(String roomId, String sendUid) {
        return "str:gift:send:" + roomId + ":" + sendUid;
    }

    /**
     * 获取麦位贡献榜的key
     *
     * @param roomId      房间ID
     * @param chapterId   房间场次ID
     * @param receiverUid 收礼方用户ID
     * @return 完整的Redis key
     */
    public String getMicContributionRankKey(String roomId, String chapterId, String receiverUid) {
        return "zset:mic:contribution:rank:" + roomId + ":" + chapterId + ":" + receiverUid;
    }

    /**
     * 添加或更新麦位贡献值
     *
     * @param roomId      房间ID
     * @param chapterId   房间场次ID
     * @param receiverUid 收礼方用户ID
     * @param senderUid   送礼方用户ID
     * @param score       贡献值（礼物价值）
     */
    public void addOrUpdateMicContribution(String roomId, String chapterId, String receiverUid,
                                           String senderUid, double score) {
        String key = getMicContributionRankKey(roomId, chapterId, receiverUid);
        mainRedisTemplate.opsForZSet().incrementScore(key, senderUid, score);
        mainRedisTemplate.expire(key, MIC_CONTRIBUTION_RANK_EXPIRE, TimeUnit.SECONDS);
    }

    /**
     * 获取麦位贡献榜前N名用户ID
     *
     * @param roomId      房间ID
     * @param chapterId   房间场次ID
     * @param receiverUid 收礼方用户ID
     * @param count       获取的数量
     * @return 前N名用户ID列表，按贡献值从高到低排序
     */
    public Set<String> getTopContributors(String roomId, String chapterId, String receiverUid, int count) {
        String key = getMicContributionRankKey(roomId, chapterId, receiverUid);
        Set<String> topUsers = mainRedisTemplate.opsForZSet().reverseRange(key, 0, count - 1);
        return topUsers != null ? topUsers : new HashSet<>();
    }

    /**
     * 获取分页的麦位贡献榜用户
     *
     * @param roomId      房间ID
     * @param chapterId   房间场次ID
     * @param receiverUid 收礼方用户ID
     * @return 分页的用户ID和贡献值列表
     */
    public Set<ZSetOperations.TypedTuple<String>> getContributorsByPage(String roomId, String chapterId, String receiverUid) {
        String key = getMicContributionRankKey(roomId, chapterId, receiverUid);
        Set<ZSetOperations.TypedTuple<String>> result = mainRedisTemplate.opsForZSet()
                .reverseRangeWithScores(key, 0, -1);
        return result != null ? result : new HashSet<>();
    }

    /**
     * 获取贡献榜中的总人数
     *
     * @param roomId      房间ID
     * @param chapterId   房间场次ID
     * @param receiverUid 收礼方用户ID
     * @return 总人数
     */
    public Long getContributorsCount(String roomId, String chapterId, String receiverUid) {
        String key = getMicContributionRankKey(roomId, chapterId, receiverUid);
        Long size = mainRedisTemplate.opsForZSet().size(key);
        return size != null ? size : 0L;
    }
}