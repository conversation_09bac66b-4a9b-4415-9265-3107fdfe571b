package com.quhong.redis.balance;

import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.constant.BaseRedisBeanConstant;
import com.quhong.core.datas.DayTimeData;
import com.quhong.core.enums.ChannelEnum;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class GamePointsBalanceRedis {

    private static final Logger log = LoggerFactory.getLogger(GamePointsBalanceRedis.class);
    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate mainRedisTemplate;

    /**
     * 正式环境每个自然月过期，测试环境每到整10分钟数就过期
     *
     * @param channel
     * @return
     */
    public Integer getGamePointExpireTimeByChannel(String channel) {
        boolean product = ServerConfiguration.isProduct();
        int expireTime;
        if (product) {
            DateHelper dateHelper = DateHelper.genDateHelper(channel);
            DayTimeData monthStartAndEndTime = dateHelper.getMonthStartAndEndTime(0);
            // 获取当前月份的最后一刻的时间戳
            expireTime = monthStartAndEndTime.getEndTime() - 1;
        } else {
            TimeZone timeZone = TimeZone.getTimeZone("UTC");
            Calendar calendar = Calendar.getInstance(timeZone);
            int currentMinute = calendar.get(Calendar.MINUTE);
            // 计算到下一个整十分钟的分钟数差
            int minutesToNextTen = ((currentMinute + 10) / 10) * 10 - currentMinute;
            // 添加分钟数到当前时间，得到下一个整十的分钟数
            calendar.add(Calendar.MINUTE, minutesToNextTen);
            // 设置秒和毫秒为0
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            // 获取下一个整十的分钟数的时间戳
            expireTime = (int) (calendar.getTimeInMillis() / 1000);
        }
        log.info("channel={} product={} expireTime={}", channel, product, expireTime);
        return expireTime;

    }


    private String getActorGamePointsBalanceKey(String uid, Integer expireTimeMillis) {
        return "str:balance:game_points:uid" + uid + ":expireTime:" + expireTimeMillis;
    }

    public Long getGamePointsBalanceRds(String uid, Integer expireTimeMillis) {
        String gamePointsBalanceKey = getActorGamePointsBalanceKey(uid, expireTimeMillis);
        Object result = mainRedisTemplate.opsForValue().get(gamePointsBalanceKey);
        return result == null ? null : Long.parseLong(String.valueOf(result));
    }

    public void saveGamePointsBalanceRds(String uid, long gamePoints, Integer gamePointExpireTime) {
        String gamePointsBalanceKey = getActorGamePointsBalanceKey(uid, gamePointExpireTime);
        mainRedisTemplate.opsForValue().set(gamePointsBalanceKey, String.valueOf(gamePoints), 1, TimeUnit.HOURS);
    }


}
