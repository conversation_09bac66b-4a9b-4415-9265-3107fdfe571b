package com.quhong.aop;

import com.quhong.annotation.MethodMonitor;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.exceptions.WebException;
import com.quhong.service.activity.event.monitor.MonitorManager;
import com.quhong.service.activity.event.monitor.core.MonitorContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Method;

/**
 * 方法监控AOP切面
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
@Slf4j
@Aspect
@RequiredArgsConstructor
public class MethodMonitorAop {

    private final MonitorManager monitorManager;

    /**
     * 环绕通知，监控被@MethodMonitor注解标记的方法
     */
    @Around("@annotation(com.quhong.annotation.MethodMonitor)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取注解
        MethodMonitor annotation = method.getAnnotation(MethodMonitor.class);
        if (annotation == null) {
            return joinPoint.proceed();
        }

        // 获取监控Key
        String monitorKey = annotation.value();
        boolean isRankMethod = annotation.isRankMethod();

        // 创建监控上下文
        MonitorContext context = new MonitorContext(monitorKey, isRankMethod);
        // 记录执行开始
        monitorManager.beforeExecution(context);

        // 执行方法
        Object result;
        boolean success = false;
        long startTime = System.currentTimeMillis();

        try {
            // 执行目标方法
            result = joinPoint.proceed();
            if (isRankMethod) {
                context.setResultEmpty(result);
            }
            success = true;
            return result;
        }catch (WebException e){
            success = true;
            throw e;
        } catch (Throwable e) {
            // 记录异常信息
            context.setException(e);
            throw e;
        } finally {
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;

            // 异步处理监控数据，避免影响业务方法性能
            boolean finalSuccess = success;
            BaseTaskFactory.Util.slowTask(context,
                    c -> monitorManager.afterExecution(c, finalSuccess, executionTime));
        }
    }
}
