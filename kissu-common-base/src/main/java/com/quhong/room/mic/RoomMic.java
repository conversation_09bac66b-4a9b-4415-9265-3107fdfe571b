package com.quhong.room.mic;

import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.common.data.ProtoVO;
import com.quhong.core.utils.SpringUtils;
import com.quhong.data.room.ResourceInfoObject;
import com.quhong.enums.RoomType;
import com.quhong.server.protobuf.RoomMessageProtobuf;
import com.quhong.socket.msg.IProto;
import com.quhong.utils.StringUtils;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

@Getter
@Setter
public class RoomMic extends ProtoVO {

    private String roomId; // 房间id
    /**
     * @see com.quhong.enums.RoomSubType
     */
    private int subType;//房间subType 0 普通语聊房间  2语音聊天室
    private int liveMicType;//直播连麦类型 0默认 1六麦位
    private int version; // 麦位版本号
    private List<MicInfo> micList;

    /**
     * ==================
     * 内部字段
     * ==================
     */
    @JSONField(serialize = false)
    private long expireTime; // 对象过期时间戳，用于jvm内存释放
    @JSONField(serialize = false)
    private long lastUpdate; // 用户基础数据的最后刷新时间
    @JSONField(serialize = false)
    private ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    public void incrVersion() {
        this.version = version + 1;
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {

    }

    @Override
    protected byte[] doToBody() throws Exception {
        return doToBody2().build().toByteArray();
    }

    public RoomMessageProtobuf.RoomMicResp.Builder doToBody2() {
        RoomMessageProtobuf.RoomMicResp.Builder builder = RoomMessageProtobuf.RoomMicResp.newBuilder();
        builder.setRoomId(this.roomId == null ? "" : this.roomId);
        builder.setSubType(this.subType);
        builder.setVersion(this.version);
        builder.setLiveMicType(this.liveMicType);
        if (!CollectionUtils.isEmpty(micList)) {
            for (MicInfo micInfo : micList) {
                if (micInfo != null) {
                    builder.addMicList(micInfo.doToBody());
                }
            }
        }
        return builder;
    }

    @Getter
    @Setter
    public static class MicInfo implements IProto<RoomMessageProtobuf.MicInfo> {
        private int position; // 麦位位置，从0开始
        private int status; // 麦位状态 0空闲 1有人 2离开 3上锁
        private int mute; // 麦位禁音 0未静音 1静音
        // 用户信息
        private String aid; // 用户id
        private int rid; // 用户rid
        private String head; // 头像
        private String name; // 名字
        private String country; // 国家
        private ResourceInfoObject micFrame; // 麦位框资源
        private int role; // 权限 0-普通用户 1-房主 2-管理员 3-房间会员
        private int gender; // 性别 1-用户 2-主播
        // 内部字段
        private int upMicTime; // 上麦时间
        private int upType; // 1主动上麦 2房主邀请上麦 3管理员邀请上麦
        private int camera;//摄像头 1打开 0关闭
        private int micIncome;//麦位收益
        private int sex;//性别 1男 2女
        private int enterRoomInvisible;//是否进房隐身 0不是 1是
        private int forbiddenCamera; // 是否禁止麦位
        private long showIncome;//麦位展示收益
        private String realShowIncome;
        private String realMicIncome;
        private int goldNickname;//金色昵称
        private List<String> contributionTop3Head;

        public void resetMic(int roomType) {
            this.status = MicConstant.MIC_STATUS_FREE;
            this.mute = MicConstant.MIC_MUTE_OFF;
            this.upMicTime = 0;
            this.upType = 1;
            this.aid = null;
            this.rid = 0;
            this.head = null;
            this.name = null;
            this.country = null;
            this.micFrame = null;
            this.role = 0;
            this.gender = 0;
            if (roomType == RoomType.CHAT) {
                this.camera = MicConstant.MIC_CAMERA_OFF;
            } else {
                this.camera = MicConstant.MIC_CAMERA_ON;
            }
            this.micIncome = 0;
            this.sex = 1;
            this.enterRoomInvisible = 0;
            this.forbiddenCamera = 0;
            this.showIncome = 0;
            this.realShowIncome = "0";
            this.realMicIncome = "0";
            this.goldNickname = 0;
            this.contributionTop3Head = null;
        }

        @Override
        public void doFromBody(RoomMessageProtobuf.MicInfo proto) {

        }

        @Override
        public RoomMessageProtobuf.MicInfo.Builder doToBody() {
            RoomMessageProtobuf.MicInfo.Builder builder = RoomMessageProtobuf.MicInfo.newBuilder();
            SpringUtils.copyPropertiesIgnoreNull(this, builder);
            if (null != this.getMicFrame()) {
                builder.setMicFrame(this.micFrame.doToBody());
            }
            if (!CollectionUtils.isEmpty(this.contributionTop3Head)) {
                builder.addAllContributionTop3Head(this.contributionTop3Head);
            }
            return builder;
        }

        public BigDecimal fetchRealShowIncome() {
            if (StringUtils.hasLength(realShowIncome)) {
                return new BigDecimal(realShowIncome);
            } else {
                return BigDecimal.valueOf(showIncome);
            }
        }

        public MicInfo copyTo() {
            MicInfo to = new MicInfo();
            SpringUtils.copyPropertiesIgnoreNull(this, to);
            if (this.micFrame != null) {
                to.setMicFrame(this.micFrame.copyTo());
            }
            if (!CollectionUtils.isEmpty(this.contributionTop3Head)) {
                to.setContributionTop3Head(new ArrayList<>(this.contributionTop3Head));
            }
            return to;
        }
    }
}
