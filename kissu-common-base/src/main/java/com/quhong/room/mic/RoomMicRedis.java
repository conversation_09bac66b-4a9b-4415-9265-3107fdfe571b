package com.quhong.room.mic;

import com.alibaba.fastjson.JSON;
import com.quhong.core.constant.BaseRedisBeanConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Component
public class RoomMicRedis {
    private static final Logger logger = LoggerFactory.getLogger(RoomMicRedis.class);

    private static final long EXPIRE_DAY = 2;

    @Resource(name = BaseRedisBeanConstant.MAIN_BEAN)
    private StringRedisTemplate redisTemplate;


    /**
     * 上麦成功后记录上麦的房间
     *
     * @param roomId 上麦的房间
     * @param uid    用户uid
     */
    public void addRoomMicUser(String roomId, String uid) {
        try {
            redisTemplate.opsForValue().set(getRoomMicUserKey(uid), roomId, 12, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.error("add room mic user error. roomId={} uid={}", roomId, uid, e);
        }
    }

    /**
     * 获取用户上麦的roomId
     *
     * @param uid 用户uid
     * @return 用户上麦的roomId
     */
    public String getRoomMicUser(String uid) {
        try {
            return redisTemplate.opsForValue().get(getRoomMicUserKey(uid));
        } catch (Exception e) {
            logger.error("get room mic user error. uid={}", uid, e);
            return null;
        }
    }

    /**
     * 下麦后删除上麦的标记
     *
     * @param uid 用户uid
     */
    public void removeRoomMicUser(String uid) {
        try {
            redisTemplate.delete(getRoomMicUserKey(uid));
        } catch (Exception e) {
            logger.error("remove room mic user error. uid={}", uid, e);
        }
    }

    private String getRoomMicUserKey(String uid) {
        return "str:roomMicUser:" + uid;
    }

    public RoomMic getRoomMic(String roomId) {
        try {
            return JSON.parseObject(redisTemplate.opsForValue().get(getRoomMicKey(roomId)), RoomMic.class);
        } catch (Exception e) {
            logger.info("get room mic error, roomId={} {}", roomId, e.getMessage(), e);
            return null;
        }
    }

    public void saveRoomMic(RoomMic roomMic) {
        try {
            redisTemplate.opsForValue().set(getRoomMicKey(roomMic.getRoomId()), JSON.toJSONString(roomMic), EXPIRE_DAY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("save room mic error, roomId={} {}", roomMic.getRoomId(), e.getMessage(), e);
        }
    }

    public void deleteRoomMic(String roomId) {
        try {
            redisTemplate.delete(getRoomMicKey(roomId));
        } catch (Exception e) {
            logger.info("delete room mic error, roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    private String getRoomMicKey(String roomId) {
        return "str:roomMic:" + roomId;
    }

    private String getRoomMicApplyListKey(String roomId, String chapterId) {
        return "str:room_mic_apply_list:" + roomId + ":" + chapterId;
    }

    public RoomMicApplyVO getRoomMicApplyList(String roomId, String chapterId) {
        try {
            String key = getRoomMicApplyListKey(roomId, chapterId);
            return JSON.parseObject(redisTemplate.opsForValue().get(key), RoomMicApplyVO.class);
        } catch (Exception e) {
            logger.info("get room mic apply list error, roomId={} chapterId={} {}", roomId, chapterId, e.getMessage(), e);
            return null;
        }
    }

    public void saveRoomMicApplyList(String roomId, String chapterId, RoomMicApplyVO vo) {
        try {
            String key = getRoomMicApplyListKey(roomId, chapterId);
            redisTemplate.opsForValue().set(key, JSON.toJSONString(vo), EXPIRE_DAY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("save room mic apply list error, roomId={} chapterId={} {}", roomId, chapterId, e.getMessage(), e);
        }
    }

    public void deleteRoomMicApplyList(String roomId, String chapterId) {
        try {
            String key = getRoomMicApplyListKey(roomId, chapterId);
            redisTemplate.delete(key);
        } catch (Exception e) {
            logger.info("del room mic apply list error, roomId={} chapterId={} {}", roomId, chapterId, e.getMessage(), e);
        }
    }

    private String getRoomMicApplyRecordKey(String roomId, String chapterId, String uid) {
        return "str:room_mic_apply_record:" + roomId + ":" + chapterId + ":" + uid;
    }

    public boolean hadApplyRecord(String roomId, String chapterId, String uid) {
        try {
            String key = getRoomMicApplyRecordKey(roomId, chapterId, uid);
            Boolean hasKey = redisTemplate.hasKey(key);
            return hasKey != null ? hasKey : false;
        } catch (Exception e) {
            logger.info("get room mic apply record error, roomId={} chapterId={} uid={} {}", roomId, chapterId, uid, e.getMessage(), e);
        }
        return false;
    }

    public void saveApplyRecord(String roomId, String chapterId, String uid) {
        try {
            String key = getRoomMicApplyRecordKey(roomId, chapterId, uid);
            redisTemplate.opsForValue().set(key, "1", 1, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.info("save room mic apply record error, roomId={} chapterId={} uid={} {}", roomId, chapterId, uid, e.getMessage(), e);
        }
    }

    //连麦申请限制
    private String getApplyLimitKey(String uid) {
        return "str:mic_apply_limit:" + uid;
    }

    public void saveApplyLimitKey(String uid, int expireTime) {
        String key = getApplyLimitKey(uid);
        redisTemplate.opsForValue().set(key, "1", expireTime, TimeUnit.SECONDS);
    }

    public boolean hadApplyLimitKey(String uid) {
        String key = getApplyLimitKey(uid);
        Boolean hasKey = redisTemplate.hasKey(key);
        return hasKey != null ? hasKey : false;
    }

    public void saveMicUserEffectiveTime(String roomId, String uid, long effectiveTime, String date) {
        String key = getEffectiveMicUserZSetKey(roomId, date);
        redisTemplate.opsForZSet().incrementScore(key, uid, effectiveTime);
        redisTemplate.expire(key, 30, TimeUnit.DAYS);
    }

    public void saveUserInMicTime(String uid, long effectiveTime, String date) {
        String key = getEffectiveMicUserStrKey(uid, date);
        redisTemplate.opsForValue().increment(key, effectiveTime);
        redisTemplate.expire(key, 30, TimeUnit.DAYS);
    }

    public Integer getUserInMicTime(String uid, String date) {
        String key = getEffectiveMicUserStrKey(uid, date);
        String value = redisTemplate.opsForValue().get(key);
        if (!StringUtils.isEmpty(value)) {
            //缓存不为null返回
            return Integer.parseInt(value);
        }
        return 0;
    }

    private String getEffectiveMicUserStrKey(String uid, String date) {
        return "str:user_in_mic_time:uid:" + uid + ":date:" + date;
    }

    private String getEffectiveMicUserZSetKey(String roomId, String date) {
        return "zset:effective_mic_user:room_id:" + roomId + ":date:" + date;
    }

    private String getOwnerMicCameraBlockRecordKey(String roomId, String uid) {
        return "str:room_mic:owner_camera_block_record:" + roomId + ":uid:" + uid;
    }

    public void saveOwnerMicCameraBlockRecord(String roomId, String uid) {
        String key = getOwnerMicCameraBlockRecordKey(roomId, uid);
        redisTemplate.opsForValue().set(key, "1", 12, TimeUnit.HOURS);
    }

    public boolean haveOwnerMicCameraBlockRecord(String roomId, String uid) {
        String key = getOwnerMicCameraBlockRecordKey(roomId, uid);
        Boolean hasKey = redisTemplate.hasKey(key);
        return hasKey != null ? hasKey : false;
    }

    private String getAudioUpMicRecordKey(String roomId, String chapterId, String uid) {
        return "str:mic:live_audio_up_mic_record:roomId:" + roomId + ":chapterId:" + chapterId + ":uid:" + uid;
    }

    public void saveAudioUpMicRecord(String roomId, String chapterId, String uid) {
        String key = getAudioUpMicRecordKey(roomId, chapterId, uid);
        redisTemplate.opsForValue().set(key, "1", 1, TimeUnit.DAYS);
    }

    public boolean hadAudioUpMicRecord(String roomId, String chapterId, String uid) {
        String key = getAudioUpMicRecordKey(roomId, chapterId, uid);
        Boolean hasKey = redisTemplate.hasKey(key);
        if (Boolean.TRUE.equals(hasKey)) {
            redisTemplate.delete(key);
            return true;
        }
        return false;
    }


}
