package com.quhong.room.mic;

import com.quhong.data.room.RoomActorData;
import com.quhong.server.protobuf.RoomMessageProtobuf;
import com.quhong.socket.msg.IProto;
import lombok.Data;

/**
 * @ClassName MicApplyInfoObject
 * <AUTHOR>
 * @date 2023/5/9 20:49
 */
@Data
public class RoomMicApplyInfoObject implements IProto<RoomMessageProtobuf.RoomMicApplyInfo> {
    private String uid;
    private String head;
    private String name;
    private String countryCode;
    private int vip;
    private int bossLevel;
    private int userLevel;//用户等级
    private int gender;
    private int age;
    private int hostGrade;
    private RoomActorData actorInfo;
    private int micType;

    @Override
    public void doFromBody(RoomMessageProtobuf.RoomMicApplyInfo proto) {
        this.uid = proto.getUid();
        this.head = proto.getHead();
        this.name = proto.getName();
        this.countryCode = proto.getCountryCode();
        this.vip = proto.getVip();
        this.bossLevel = proto.getBossLevel();
        this.userLevel = proto.getUserLevel();
        this.gender = proto.getGender();
        this.age = proto.getAge();
        this.hostGrade = proto.getHostGrade();
        if (proto.getActorInfo() != null) {
            this.actorInfo = new RoomActorData();
            this.actorInfo.doFromBody(proto.getActorInfo());
        }
    }

    @Override
    public RoomMessageProtobuf.RoomMicApplyInfo.Builder doToBody() {
        RoomMessageProtobuf.RoomMicApplyInfo.Builder builder = RoomMessageProtobuf.RoomMicApplyInfo.newBuilder();
        builder.setUid(this.uid != null ? this.uid : "");
        builder.setHead(this.head != null ? this.head : "");
        builder.setName(this.name != null ? this.name : "");
        builder.setCountryCode(this.countryCode != null ? this.countryCode : "");
        builder.setVip(this.vip);
        builder.setBossLevel(this.bossLevel);
        builder.setUserLevel(this.userLevel);
        builder.setGender(this.gender);
        builder.setAge(this.age);
        builder.setHostGrade(this.hostGrade);
        if (this.actorInfo != null) {
            builder.setActorInfo(this.actorInfo.doToBody());
        }
        builder.setMicType(this.micType);
        return builder;
    }
}
