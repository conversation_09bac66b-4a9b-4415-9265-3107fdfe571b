package com.quhong.room.mic;


import com.quhong.dao.datas.RoomData;

public class MicConstant {

    /**
     * 麦位状态 0空闲 1有人 2离开 3上锁
     *
     * @see RoomMic.MicInfo#status
     */
    public static final int MIC_STATUS_FREE = 0;
    public static final int MIC_STATUS_OCCUPY = 1;
    public static final int MIC_STATUS_LEAVE = 2;
    public static final int MIC_STATUS_LOCKED = 3;

    /**
     * 麦位禁音 0未静音 1静音
     *
     * @see RoomMic.MicInfo#mute
     */
    public static final int MIC_MUTE_OFF = 0;
    public static final int MIC_MUTE_ON = 1;

    /**
     * 麦位摄像头 0关闭 1打开
     */
    public static final int MIC_CAMERA_OFF = 0;
    public static final int MIC_CAMERA_ON = 1;

    /**
     * 操作类型：0获取麦位列表 1上麦 2下麦 3静音 4取消静音 5邀请上麦 6踢人下麦 7上锁 8取消上锁 9接受邀请上麦 10打开摄像头 11关闭摄像头
     * 12主播接受用户上麦 13禁止用户打开摄像头 14解禁用户打开摄像头 15切换麦位类型 三麦位变更为多麦位 16切换麦位类型 多麦位变更为三麦位
     *
     * @see MicDTO#opType
     */
    public static final int MIC_OPT_LIST = 0;
    public static final int MIC_OPT_UP = 1;
    public static final int MIC_OPT_DOWN = 2;
    public static final int MIC_OPT_MUTE_ON = 3;
    public static final int MIC_OPT_MUTE_OFF = 4;
    public static final int MIC_OPT_INVITE = 5;
    public static final int MIC_OPT_KICK = 6;
    public static final int MIC_OPT_LOCK = 7;
    public static final int MIC_OPT_UNLOCK = 8;
    public static final int MIC_OPT_INVITED_UP = 9;
    public static final int MIC_OPT_CAMERA_ON = 10;
    public static final int MIC_OPT_CAMERA_OFF = 11;
    public static final int MIC_OPT_ACCEPT_UP = 12;

    public static final int MIC_OPT_CAMERA_FORBIDDEN = 13;
    public static final int MIC_OPT_CAMERA_UNBAN = 14;

    public static final int MIC_OPT_MIC_TYPE_CHANGE_MUL = 15;
    public static final int MIC_OPT_MIC_TYPE_CHANGE_THIRD = 16;

    /**
     * 下麦类型：1主动下麦 2离开房间下麦 3踢下麦 4在其他房间上麦 5上麦检查
     *
     * @see RoomMicService#downMic(String, String, int)
     */
    public static final int MIC_DOWN_NORMAL = 1;
    public static final int MIC_DOWN_LEAVE_ROOM = 2;
    public static final int MIC_DOWN_KICK = 3;
    public static final int MIC_DOWN_OTHER_ROOM_UP_MIC = 4;
    public static final int MIC_DOWN_UP_MIC_CHECK = 5;
    public static final int MIC_DOWN_REPEATED = 6;

    /**
     * 连麦申请列表开关 0关闭 1打开
     */
    public static final int MIC_SWITCH_CLOSE = 0;
    public static final int MIC_SWITCH_OPEN = 1;
    /**
     * 连麦申请列表操作  1加入 2退出
     */
    public static final int MIC_APPLY_JOIN = 1;
    public static final int MIC_APPLY_QUIT = 2;
    /**
     * 退出连麦列表原因
     */
    public static final int APPLY_OPT_DEFAULT = 0;//默认或连麦成功
    public static final int APPLY_OPT_QUIT = 1;//退出列表
    public static final int APPLY_OPT_ACCEPT_REMOVE = 2;//房主接受移除列表
    public static final int APPLY_OPT_REFUSE_REMOVE = 3;//房主拒接移除列表
    public static final int APPLY_OPT_LEAVE_ROOM = 4;//离开房间
    public static final int APPLY_OPT_OWNER_LEAVE = 5;//房主离开房间


    /**
     * 直播麦位类型  0:三麦位  1:多麦位
     *
     * @see RoomData#liveMicType
     */
    public static final int LIVE_MIC_TYPE_THIRD = 0;
    public static final int LIVE_MIC_TYPE_MUL = 1;

}
