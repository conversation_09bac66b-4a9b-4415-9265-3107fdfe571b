<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.dao.mapper.doris.DorisMoneyDetailMapper">
    <insert id="insertOne">
        INSERT INTO ${dbName}.`money_detail` (`userid`, `partition_index`, `ctime`, `action`, `changed`,
                                              `bechange`, `aftchange`, `act_type`, `seg_code`, `act_id`,
                                              `act_desc`, `channel`, `change_relation`, `related_id`,
                                              `currency_code`, `other_uid`, `deleted`, `operator`, `mtime`,
                                              `agent_id`, `before_real_change`, `after_real_change`,
                                              `real_change`)
        VALUES (#{data.userid}, #{data.partitionIndex}, #{data.ctime}, #{data.action}, #{data.changed},
                #{data.bechange}, #{data.aftchange}, #{data.actType}, #{data.segCode}, #{data.actId},
                #{data.actDesc}, #{data.channel}, #{data.changeRelation}, #{data.relatedId},
                #{data.currencyCode}, #{data.otherUid}, #{data.deleted}, #{data.operator}, #{data.mtime},
                #{data.agentId}, #{data.beforeRealChange}, #{data.afterRealChange},
                #{data.realChange})
    </insert>

    <insert id="insertList">
        INSERT INTO ${dbName}.`money_detail` (`userid`, `partition_index`, `ctime`, `action`, `changed`,
                                              `bechange`, `aftchange`, `act_type`, `seg_code`, `act_id`,
                                              `act_desc`, `channel`, `change_relation`, `related_id`,
                                              `currency_code`, `other_uid`, `deleted`, `operator`, `mtime`,
                                              `agent_id`, `before_real_change`, `after_real_change`,
                                              `real_change`)
        values
        <foreach collection="dataList" item="data" separator=",">
            (#{data.userid}, #{data.partitionIndex}, #{data.ctime}, #{data.action}, #{data.changed}, #{data.bechange}, #{data.aftchange}, #{data.actType}, #{data.segCode}, #{data.actId}, #{data.actDesc}, #{data.channel}, #{data.changeRelation}, #{data.relatedId}, #{data.currencyCode}, #{data.otherUid}, #{data.deleted}, #{data.operator}, #{data.mtime}, #{data.agentId}, #{data.beforeRealChange}, #{data.afterRealChange}, #{data.realChange})
        </foreach>
    </insert>

    <select id="selectMoneyHistoryFromDoris" resultType="com.quhong.data.vo.MoneyHistoryVO">
        select md.userid                              as uid,
               md.act_type                            as actType,
               md.seg_code                            as segmentCode,
               md.currency_code                       as currencyCode,
               md.act_desc                            as actDesc,
               md.bechange                            as beforeChange,
               md.changed                             as changed,
               md.aftchange                           as afterChange,
               md.ctime                               as ctime,
               md.action                              as action,
               FROM_UNIXTIME(md.mtime, '%Y-%m-%d %T') as createDate,
               md.operator                            as operator,
               md.other_uid                           as otherUid,
               md.change_relation                     as changeRelation,
               md.related_id                          as relatedId,
               md.act_id                              as actId,
               md.mtime                               as mtime,
               md.real_change                         as realChange,
               md.before_real_change                  as beforeRealChange,
               md.after_real_change                   as afterRealChange
        from ${dbName}.money_detail as md
        <where>
            md.partition_index in
            <foreach collection="partitionIndexList" open="(" close=")" separator="," item="partitionIndex">
                #{partitionIndex}
            </foreach>
            and md.userid=#{uid}
            and md.ctime >=#{startTime}
            and md.ctime &lt;=#{endTime}
            <if test="actType != null and actType.size() != 0">
                and md.act_type in
                <foreach collection="actType" item="act" separator="," open="(" close=")">
                    #{act}
                </foreach>
            </if>
            <if test="segmentCode != null and segmentCode != 0">
                and md.seg_code = #{segmentCode}
            </if>
            <if test="currencyCode != null">
                and md.currency_code = #{currencyCode}
            </if>
            <if test="ignoreActType != null and ignoreActType.size() != 0">
                and md.act_type not in
                <foreach collection="ignoreActType" item="act" separator="," open="(" close=")">
                    #{act}
                </foreach>
            </if>
        </where>
        order by md.mtime desc
            limit #{start},#{size}
    </select>

    <select id="getDiamondIncomeFromDoris" resultType="com.quhong.data.vo.CountVO">
        select md.userid           as uid,
               sum(md.changed)     as `count`,
               sum(md.real_change) as realCount
        from ${dbName}.money_detail as md
        <where>
            md.partition_index in
            <foreach collection="partitionIndexList" open="(" close=")" separator="," item="partitionIndex">
                #{partitionIndex}
            </foreach>
            and md.userid in
            <foreach collection="uidSet" open="(" close=")" separator="," item="uid">
                #{uid}
            </foreach>
            and md.ctime >= #{startTime}
            and md.ctime
           &lt; #{endTime}
            <if test="actTypeSet != null and actTypeSet.size() > 0">
                and md.act_type in
                <foreach collection="actTypeSet" separator="," open="(" close=")" item="actType">
                    #{actType}
                </foreach>
            </if>
            <if test="sgeCodeSet != null and sgeCodeSet.size() > 0">
                and md.seg_code in
                <foreach collection="sgeCodeSet" separator="," open="(" close=")" item="sgeCode">
                    #{sgeCode}
                </foreach>
            </if>
            and md.currency_code = 2
            and action = 1
            <if test="ignoreActTypeSet != null and ignoreActTypeSet.size() > 0">
                and md.act_type not in
                <foreach collection="ignoreActTypeSet" separator="," open="(" close=")" item="actType">
                    #{actType}
                </foreach>
            </if>
        </where>
        group by md.userid
    </select>


    <select id="getDiamondCostFromDoris" resultType="com.quhong.data.vo.CountVO">
        select md.userid       as uid,
               sum(md.changed) as `count`,
               sum(md.changed) as realCount
        from ${dbName}.money_detail as md
        <where>
            md.partition_index in
            <foreach collection="partitionIndexList" open="(" close=")" separator="," item="partitionIndex">
                #{partitionIndex}
            </foreach>
            and md.userid in
            <foreach collection="uidSet" open="(" close=")" separator="," item="uid">
                #{uid}
            </foreach>
            and md.ctime >= #{startTime}
            and md.ctime
           &lt; #{endTime}
            <if test="actTypeSet != null and actTypeSet.size() > 0">
                and md.act_type in
                <foreach collection="actTypeSet" separator="," open="(" close=")" item="actType">
                    #{actType}
                </foreach>
            </if>
            <if test="sgeCodeSet != null and sgeCodeSet.size() > 0">
                and md.seg_code in
                <foreach collection="sgeCodeSet" separator="," open="(" close=")" item="sgeCode">
                    #{sgeCode}
                </foreach>
            </if>
            and md.currency_code = 2
            and action = 2
            <if test="ignoreActTypeSet != null and ignoreActTypeSet.size() > 0">
                and md.act_type not in
                <foreach collection="ignoreActTypeSet" separator="," open="(" close=")" item="actType">
                    #{actType}
                </foreach>
            </if>
        </where>
        group by md.userid
    </select>

    <select id="selectDiamondIncomeByUidAndActTypeSetAndIgnoreActTypeAndSegCode"
            resultType="com.quhong.data.vo.CountVO">
        select userid           as uid,
               sum(real_change) as `count`,
               sum(real_change) as realCount
        from ${dbName}.money_detail
        <where>
            partition_index in
            <foreach collection="partitionIndexs" open="(" close=")" separator="," item="partitionIndex">
                #{partitionIndex}
            </foreach>
            and userid = #{uid}
            and ctime>=#{startTime}
            and ctime &lt;=#{endTime}
            <if test="actTypeSet != null and actTypeSet.size() > 0">
                and act_type in
                <foreach collection="actTypeSet" separator="," open="(" close=")" item="actType">
                    #{actType}
                </foreach>
            </if>
            <if test="segCode != null">
                and seg_code = #{segCode}
            </if>
            and currency_code = 2
            and action = 1
            <if test="ignoreActTypeSet != null and ignoreActTypeSet.size() > 0">
                and act_type not in
                <foreach collection="ignoreActTypeSet" separator="," open="(" close=")" item="actType">
                    #{actType}
                </foreach>
            </if>
        </where>
        group by uid
    </select>

    <select id="selectStatValue"
            resultType="com.quhong.data.vo.CountVO">
        select sum(real_change) as realCount
        from ${dbName}.money_detail
        <where>
            partition_index in
            <foreach collection="partitionIndexs" open="(" close=")" separator="," item="partitionIndex">
                #{partitionIndex}
            </foreach>
            and ctime >= #{startTime}
            and ctime &lt;= #{endTime}
            <if test="actTypeSet != null and actTypeSet.size() > 0">
                and act_type in
                <foreach collection="actTypeSet" separator="," open="(" close=")" item="actType">
                    #{actType}
                </foreach>
            </if>
            and action = #{action}
            and currency_code = 1
            <if test="testUidSet != null and testUidSet.size() > 0">
                and userid not in
                <foreach collection="testUidSet" separator="," open="(" close=")" item="uid">
                    #{uid}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectConsumeValue"
            resultType="com.quhong.data.vo.CountVO">
        select sum(real_change) as realCount
        from ${dbName}.money_detail
        <where>
            partition_index in
            <foreach collection="partitionIndexs" open="(" close=")" separator="," item="partitionIndex">
                #{partitionIndex}
            </foreach>
            and uid = #{uid}
            and ctime >= #{startTime}
            and ctime &lt;= #{endTime}
            and action = 2;
        </where>
    </select>

    <select id="selectTotalDiamondIncomeByUidSetAndTime" resultType="java.lang.Double">
        select sum(real_change)
        from ${dbName}.money_detail
        where
        partition_index in
        <foreach collection="partitionIndex" open="(" close=")" separator="," item="partition">
            #{partition}
        </foreach>
        and userid in
        <foreach collection="uidSet" open="(" close=")" separator="," item="uid">
            #{uid}
        </foreach>
        and ctime >= #{startTime}
        and ctime
       &lt; #{endTime}
        and action = 1
        and currency_code = 2
        <if test="actTypeList != null and actTypeList.size() > 0">
            and act_type in
            <foreach collection="actTypeList" open="(" close=")" separator="," item="actType">
                #{actType}
            </foreach>
        </if>
    </select>


    <select id="selectHostTotalCallIncomeByTime" resultType="com.quhong.data.vo.HostIncomeVO">
        select userid           as uid,
               sum(real_change) as income,
               sum(real_change) as realIncome
        from ${dbName}.money_detail
        where partition_index in
        <foreach collection="partitionIndexs" open="(" close=")" separator="," item="partitionIndex">
            #{partitionIndex}
        </foreach>
        and ctime >= #{startTime}
        and ctime &lt;= #{endTime}
        and userid in
        <foreach collection="uidSet" open="(" close=")" separator="," item="uid">
            #{uid}
        </foreach>
        and action = 1
        and act_type = 10
        group by userid
    </select>

    <select id="selectHostGiftIncomeByTime" resultType="com.quhong.data.vo.HostIncomeVO">
        select userid           as uid,
               sum(real_change) as income,
               sum(real_change) as realIncome
        from ${dbName}.money_detail
        where partition_index in
        <foreach collection="partitionIndexs" open="(" close=")" separator="," item="partitionIndex">
            #{partitionIndex}
        </foreach>
        AND action = 1
        AND act_type = 21
        and ctime >= #{startTime}
        and ctime &lt;= #{endTime}
        and userid in
        <foreach collection="uidSet" open="(" close=")" separator="," item="uid">
            #{uid}
        </foreach>
        group by userid
    </select>

    <select id="selectTaskIncomeByTime" resultType="com.quhong.data.vo.HostIncomeVO">
        select userid           as uid,
               sum(real_change) as income,
               sum(real_change) as realIncome
        from ${dbName}.money_detail
        where partition_index in
        <foreach collection="partitionIndexs" open="(" close=")" separator="," item="partitionIndex">
            #{partitionIndex}
        </foreach>
        AND action = 1
        AND act_type = 18
        and ctime >= #{startTime}
        and ctime &lt;= #{endTime}
        and userid in
        <foreach collection="uidSet" open="(" close=")" separator="," item="uid">
            #{uid}
        </foreach>
        group by userid
    </select>

    <select id="selectHostIncomeByTime" resultType="com.quhong.data.vo.HostIncomeVO">
        select userid           as uid,
               sum(real_change) as income,
               sum(real_change) as realIncome
        from ${dbName}.money_detail
        where partition_index in
        <foreach collection="partitionIndexs" open="(" close=")" separator="," item="partitionIndex">
            #{partitionIndex}
        </foreach>
        AND action = 1
        and ctime >= #{startTime}
        and ctime &lt;= #{endTime}
        and userid in
        <foreach collection="uidSet" open="(" close=")" separator="," item="uid">
            #{uid}
        </foreach>
        group by userid
    </select>

    <select id="selectAfterChangeByTime" resultType="com.quhong.data.vo.HostIncomeVO">
        select md.userid            as uid,
               md.aftchange         as income,
               md.after_real_change as realIncome
        from ${dbName}.money_detail md
            join
        (select MAX(mtime) as mtime, userid as uid
         from ${dbName}.money_detail
        where partition_index in
        <foreach collection="partitionIndexs" open="(" close=")" separator="," item="partitionIndex">
            #{partitionIndex}
        </foreach>
        and ctime >=#{startTime}
            and ctime &lt;=#{endTime}
            and userid in
        <foreach collection="uidSet" separator="," open="(" close=")" item="uid">
            #{uid}
        </foreach>
        group by userid
            ) as ids
        where md.userid = ids.uid and md.mtime = ids.mtime
    </select>

    <select id="selectHostTotalDiamondIncome" resultType="com.quhong.data.vo.HostIncomeVO">
        select md.userid       as uid,
               sum(md.changed) as income,
               sum(md.changed) as realIncome
        from ${dbName}.money_detail as md
        where
        partition_index in
        <foreach collection="partitionIndexs" open="(" close=")" separator="," item="partitionIndex">
            #{partitionIndex}
        </foreach>
        and md.userid = #{uid}
        and md.ctime >= #{startTime}
        and md.ctime &lt;= #{endTime}
        and md.currency_code = 2
        and md.action = 1
        group by uid
    </select>

    <select id="selectDiamondSumIncomeByUidSetAndActTypeSetAndIgnoreActTypeSet" resultType="com.quhong.data.vo.CountVO">
        select md.userid       as uid,
               sum(md.changed) as count,
               sum(md.changed) as realCount
        from ${dbName}.money_detail as md
        where  partition_index in
        <foreach collection="partitionIndexs" open="(" close=")" separator="," item="partitionIndex">
            #{partitionIndex}
        </foreach>
        and md.userid in
        <foreach collection="uidSet" open="(" close=")" separator="," item="uid">
            #{uid}
        </foreach>
        <if test="actTypeSet != null and actTypeSet.size() > 0">
            and md.act_type in
            <foreach collection="actTypeSet" separator="," open="(" close=")" item="actType">
                #{actType}
            </foreach>
        </if>
        and md.ctime >= #{startTime}
        and md.ctime
       &lt; #{endTime}
        and md.currency_code = 2
        and action = 1
        <if test="ignoreActTypeSet != null and ignoreActTypeSet.size() > 0">
            and md.act_type not in
            <foreach collection="ignoreActTypeSet" separator="," open="(" close=")" item="actType">
                #{actType}
            </foreach>
        </if>
        group by uid
    </select>

    <select id="selectHostTotalDiamondIncomeByTypeSeg" resultType="com.quhong.data.vo.HostIncomeVO">
        select sum(changed) as income, sum(real_change) as realIncome
        from ${dbName}.money_detail
        where  partition_index in
        <foreach collection="partitionIndexs" open="(" close=")" separator="," item="partitionIndex">
            #{partitionIndex}
        </foreach>
        and ctime >= #{startTime}
        and ctime &lt;= #{endTime}
        and userid = #{uid}
        <if test="actTypeList != null and actTypeList.size() != 0">
            and act_type in
            <foreach collection="actTypeList" separator="," open="(" close=")" item="actType">
                #{actType}
            </foreach>
        </if>
        <if test="segmentCodeList != null and segmentCodeList.size() != 0">
            and seg_code in
            <foreach collection="segmentCodeList" separator="," open="(" close=")" item="segmentCode">
                #{segmentCode}
            </foreach>
        </if>
        and currency_code = 2
        and action = 1
        group by userid
    </select>

    <select id="selectGameRecordByTime" resultType="com.quhong.dao.datas.doris.DorisMoneyDetailData">
        select *
        from ${dbName}.money_detail
        where  partition_index in
        <foreach collection="partitionIndexs" open="(" close=")" separator="," item="partitionIndex">
            #{partitionIndex}
        </foreach>
        and userid = #{uid}
        and ctime >= #{startTime}
        and ctime &lt;= #{endTime}
        and act_type = 99
        and `action` = 2
        order by mtime desc
            limit #{limit}
    </select>

    <select id="selectGameCoinDetail" resultType="com.quhong.dao.datas.doris.DorisMoneyDetailData">
        SELECT userid            as userid,
               ctime             as ctime,
               action            as action,
               act_type          as actType,
               act_desc          as actDesc,
               real_change       as realChange,
               after_real_change as afterRealChange
        FROM ${dbName}.money_detail
        WHERE partition_index IN
        <foreach collection="partitionIndex" item="index" open="(" separator="," close=")">
            #{index}
        </foreach>
        AND userid = #{uid}
        AND ctime BETWEEN #{startTime}
        AND #{endTime}
        AND currency_code = 4
        <if test="action != null">
            AND `action` = #{action}
        </if>
        ORDER BY ctime DESC
            LIMIT #{offset}, #{pageSize}
    </select>

    <select id="selectGiftIncomeByTimeAndUidSet" resultType="com.quhong.data.vo.HostIncomeVO">
        select md.userid       as uid,
               sum(md.changed) as income,
               sum(md.changed) as realIncome
        from ${dbName}.money_detail as md
        where
        partition_index in
        <foreach collection="partitionIndexs" open="(" close=")" separator="," item="partitionIndex">
            #{partitionIndex}
        </foreach>
        and md.userid in
        <foreach collection="uidSet" open="(" close=")" separator="," item="uid">
            #{uid}
        </foreach>
        and md.ctime >= #{startTime}
        and md.ctime &lt;= #{endTime}
        and act_type = 21
        group by uid
    </select>

    <select id="selectHostTotalIncome" resultType="com.quhong.data.vo.HostIncomeVO">
        select md.userid           as uid,
               sum(md.changed)      as income,
               sum(md.real_change) as realIncome
        from ${dbName}.money_detail as md
        where
        partition_index in
        <foreach collection="partitionIndexs" open="(" close=")" separator="," item="partitionIndex">
            #{partitionIndex}
        </foreach>
        and md.ctime >= #{startTime}
        and md.ctime &lt;= #{endTime}
        and md.action = 1
        <if test="hostList != null and hostList.size() != 0">
            and md.userid in
            <foreach collection="hostList" open="(" close=")" separator="," item="uid">
                #{uid}
            </foreach>
        </if>
        group by uid
    </select>
</mapper>

