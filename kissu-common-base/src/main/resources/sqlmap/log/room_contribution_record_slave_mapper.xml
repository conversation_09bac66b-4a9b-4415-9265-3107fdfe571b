<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.dao.slave.mapper.log.RoomContributionRecordSlaveMapper">
    <select id="selectSelfCharmRank" resultType="java.lang.Double">
        select sum(rc.score) from (
        <foreach collection="suffixList" open="(" item="suffix" close=")" separator=") union all (">
            select sum(real_contribution_value) as score
            from kissu_log.room_contribution_record_${suffix}
            <where>
                ctime >= #{startTime}
                and ctime &lt;= #{endTime}
                and room_id = #{roomId}
                <if test="testUidSet != null and testUidSet.size() != 0">
                    and uid not in
                    <foreach collection="testUidSet" separator="," item="testUid" open="(" close=")">
                        #{testUid}
                    </foreach>
                </if>
            </where>
        </foreach>
        ) as rc
    </select>

    <select id="selectChapterGain" resultType="java.lang.Double">
        select sum(rc.score) from (
        <foreach collection="suffixList" open="(" item="suffix" close=")" separator=") union all (">
            select sum(real_gain) as score
            from kissu_log.room_contribution_record_${suffix}
            <where>
                ctime >= #{timeStart}
                and ctime &lt;= #{timeEnd}
                and room_id = #{roomId}
                and chapter_id = #{chapterId}
            </where>
        </foreach>
        ) as rc
    </select>

    <select id="selectRoomContributionRankTop3ByTime" resultType="com.quhong.dao.datas.RoomContributionRecordData">
        select rc.u as uid, sum(rc.c) as contributionValue, sum(rc.c) as realContributionValue
        from (
        <foreach collection="suffixList" open="(" item="suffix" close=")" separator=") union all (">
            select uid as u,real_contribution_value as c
            from kissu_log.room_contribution_record_${suffix}
            <where>
                ctime >= #{startTime}
                and ctime &lt;= #{endTime}
                and room_id = #{roomId}
            </where>
        </foreach>
        ) as rc
        group by uid
        order by contributionValue desc
        limit 3
    </select>

    <select id="selectRoomSumContributionByTime" resultType="java.lang.Double">
        select sum(rc.c) as contributionValue from (
        <foreach collection="suffixList" open="(" item="suffix" close=")" separator=") union all (">
            select real_contribution_value as c
            from kissu_log.room_contribution_record_${suffix}
            <where>
                ctime >= #{startTime}
                and ctime &lt;= #{endTime}
                and room_id = #{roomId}
            </where>
        </foreach>
        ) as rc
    </select>

    <select id="selectMvpContributionByTime" resultType="com.quhong.dao.datas.RoomContributionRecordData">
        select rc.u as uid, sum(rc.c) as contributionValue,sum(rc.c) as realContributionValue
        from (
        <foreach collection="suffixList" open="(" item="suffix" close=")" separator=") union all (">
            select uid as u,real_contribution_value as c
            from kissu_log.room_contribution_record_${suffix}
            <where>
                ctime >= #{startTime}
                and ctime &lt;= #{endTime}
                and room_id = #{roomId}
            </where>
        </foreach>
        ) as rc
        group by uid
        order by contributionValue desc
        limit 1
    </select>

    <select id="selectUserSumContributionByTime" resultType="java.lang.Double">
        select sum(rc.c) as contributionValue from (
        <foreach collection="suffixList" open="(" item="suffix" close=")" separator=") union all (">
            select real_contribution_value as c
            from kissu_log.room_contribution_record_${suffix}
            <where>
                ctime >= #{startTime}
                and ctime &lt;= #{endTime}
                and room_id = #{roomId}
                and uid=#{uid}
            </where>
        </foreach>
        ) as rc
    </select>

    <select id="getRoomContributionByRoomIdAndType" resultType="com.quhong.data.room.RoomContributionData">
        SELECT IFNULL(SUM(totalContributionValue), 0) AS totalContributionValue,
        IFNULL(SUM(totalGainsValue), 0)        AS totalGainsValue
        FROM
        (
        <foreach collection="tableSuffixList" open="(" item="item" separator=") union all (" close=")">
            SELECT SUM(real_contribution_value) AS totalContributionValue,
            SUM(real_gain)              AS totalGainsValue
            FROM room_contribution_record_${item}
            <where>
                <if test="timeStart != null and timeStart != 0">
                    AND ctime >= #{timeStart}
                </if>
                <if test="timeEnd != null and timeEnd != 0">
                    AND ctime &lt; #{timeEnd}
                </if>
                <if test="roomId != null and roomId != ''">
                    AND room_id = #{roomId}
                </if>
                <if test="contributionType != null and contributionType != 0">
                    AND contribution_type = #{contributionType}
                </if>
            </where>
        </foreach>
        ) AS total_table
    </select>

    <select id="getPartyRoomContributionByRoomId" resultType="com.quhong.data.room.RoomContributionData">
        SELECT IFNULL(SUM(totalContributionValue), 0) AS totalContributionValue,
        IFNULL(SUM(totalGainsValue), 0)        AS totalGainsValue
        FROM
        (
        <foreach collection="tableSuffixList" open="(" item="item" separator=") union all (" close=")">
            SELECT SUM(real_contribution_value) AS totalContributionValue,
            SUM(real_gain)              AS totalGainsValue
            FROM room_contribution_record_${item}
            <where>
                <if test="roomId != null and roomId != ''">
                    AND room_id = #{roomId}
                </if>
                <if test="timeStart != null and timeStart != 0">
                    AND ctime >= #{timeStart}
                </if>
                <if test="timeEnd != null and timeEnd != 0">
                    AND ctime &lt; #{timeEnd}
                </if>
            </where>
        </foreach>
        ) AS total_table
    </select>

    <select id="selectMicContributionValue" resultType="java.lang.Double">
        select sum(rc.c) as contributionValue from (
        <foreach collection="suffixList" open="(" item="suffix" close=")" separator=") union all (">
            select real_contribution_value as c
            from kissu_log.room_contribution_record_${suffix}
            <where>
                ctime >= #{startTime}
                and ctime &lt;= #{endTime}
                and room_id = #{roomId}
                and chapter_id = #{chapterId}
                and uid=#{uid}
            </where>
        </foreach>
        ) as rc
    </select>
</mapper>
