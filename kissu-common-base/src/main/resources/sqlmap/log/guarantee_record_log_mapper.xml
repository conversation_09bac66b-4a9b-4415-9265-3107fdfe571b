<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.dao.mapper.log.GuaranteeRecordLogMapper">

    <update id="updateRecordLogData">
        update kissu_log.guarantee_record_log
        set guarantee_income = #{guaranteeIncome},
            mtime=#{mtime}
        where uid = #{uid}
          and `date` = #{date}
    </update>

    <select id="selectRecordLogDataByDate" resultType="com.quhong.dao.datas.GuaranteeRecordLogData">
        select uid, guarantee_income
        from kissu_log.guarantee_record_log
        where `date` = #{date}
    </select>

    <select id="selectRecordLogDataByUid" resultType="com.quhong.dao.datas.GuaranteeRecordLogData">
        select uid, sum(guarantee_income) as guaranteeIncome
        from kissu_log.guarantee_record_log
        where uid = #{uid} limit 1
    </select>
    <select id="selectRecordLogDataListByUid" resultType="com.quhong.dao.datas.GuaranteeRecordLogData">
        select uid,sum(guarantee_income) as guaranteeIncome from kissu_log.guarantee_record_log
        <where>
            <if test="uidSet != null and uidSet.size() != 0">
                uid in
                <foreach collection="uidSet" open="(" item="item" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        group by uid
    </select>

</mapper>
