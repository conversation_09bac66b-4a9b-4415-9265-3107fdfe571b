<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.dao.mapper.log.CancelPayOrderRecordMapper">
    <update id="updateUnread">
        update kissu_log.cancel_pay_order_record set unread = 1 where uid=#{uid}
    </update>


    <select id="getPayFailCount" resultType="java.lang.Integer">
        select count(*) from kissu_log.cancel_pay_order_record where uid=#{uid} and cancel_code != 2
    </select>

    <select id="getReadCount" resultType="java.lang.Integer">
        select count(*) from kissu_log.cancel_pay_order_record where uid=#{uid} and unread = 1
    </select>
</mapper>