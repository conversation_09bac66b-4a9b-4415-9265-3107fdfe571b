<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.dao.slave.mapper.log.HostOnlineTimeRoundLogSlaveMapper">
    <select id="selectSumOnlineTime" resultType="com.quhong.dao.datas.HostOnlineTimeRoundLogData">
        select uid as uid, sum(online_time) as onlineTime
        from kissu_log.host_online_time_round_log_${tableSuffix}
        where `date` = #{date}
        group by uid
    </select>

    <select id="selectOnlineTimeListByRound" resultType="com.quhong.dao.datas.HostOnlineTimeRoundLogData">
        select uid as uid, rid as rid, sum(online_time) as onlineTime
        from kissu_log.host_online_time_round_log_${tableSuffix}
        where `date` = #{date}
          and `round` >= #{startRound}
          and `round` &lt;= #{endRound}
          and `online_time` > 0
        group by uid
    </select>
</mapper>
