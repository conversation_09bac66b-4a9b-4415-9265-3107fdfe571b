<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.dao.mapper.db.ActivityIndependenceActorInfoMapper">

    <select id="selectOrderLimitListBy" resultType="com.quhong.dao.datas.db.ActivityIndependenceActorInfoData">
        select *
        from kissu.activity_independence_actor_info aiai
        left join kissu.actor a on aiai.uid = a.uid
        where aiai.event_type = #{eventType}
          and aiai.${columnName} > 0
          and a.del = 0
        order by aiai.${columnName} desc
        limit #{limit}
    </select>
</mapper>

