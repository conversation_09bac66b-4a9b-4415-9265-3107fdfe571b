<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--suppress ALL -->
<mapper namespace="com.quhong.dao.slave.mapper.db.GiftRecordSlaveMapper">
    <sql id="t_gift_record_columns">
        rid
                , from_uid
                , to_uid
                , roomId
                , chapter_id
                , giftid
                , gtype
                , gnumber
                , gname
                , price
                , from_lang_id
                , from_country_code
                , from_channel
                , status
                , ctime
                , mtime
                , last_time
                , from_type
                ,is_luck
                ,gain
                ,real_price
                ,real_gain
    </sql>

    <select id="getTopSendGiftData" resultType="com.quhong.data.dto.RankingDTO">
        select tgr.from_uid uid, a.name name, a.head_icon head, sum(tgr.gnumber) count, a.rid rid
        from kissu.t_gift_record tgr
                     left join kissu.actor a on tgr.from_uid = a.uid
                where tgr.ctime >= #{start}
                  and tgr.ctime &lt; #{end}
        <if test="fromType != -1">
            and tgr.from_type = #{fromType}
        </if>
        and tgr.giftid = #{giftId}
                group by tgr.from_uid
                order by count desc
                limit #{top}
    </select>

    <select id="getTopReceiveGiftData" resultType="com.quhong.data.dto.RankingDTO">
        select tgr.to_uid uid, a.name name, a.head_icon head, sum(tgr.gnumber) count, a.rid rid
        from kissu.t_gift_record tgr
                     left join kissu.actor a on tgr.to_uid = a.uid
                where tgr.ctime >= #{start}
                  and tgr.ctime &lt; #{end}
        <if test="fromType != -1">
            and tgr.from_type = #{fromType}
        </if>
        and tgr.giftid = #{giftId}
        <if test="countryCodeSet != null and countryCodeSet.size > 0">
            and a.country_code in
            <foreach collection="countryCodeSet" open='(' item='countryCode' separator=',' close=')'>
                #{countryCode}
            </foreach>
        </if>
        group by tgr.to_uid
        order by count desc
        limit #{top}
    </select>
    <select id="getOneReceiveGiftData" resultType="com.quhong.data.dto.RankingDTO">
        select tgr.to_uid uid, a.name name, a.head_icon head, sum(gnumber) count, a.rid rid
        from kissu.t_gift_record tgr
                     left join kissu.actor a on tgr.to_uid = a.uid
                where tgr.ctime >= #{start}
                  and tgr.ctime &lt; #{end}
                  and tgr.to_uid = #{uid}
        <if test="fromType != -1">
            and tgr.from_type = #{fromType}
        </if>
        and tgr.giftid = #{giftId}
                limit 1
    </select>

    <select id="getOneSendGiftData" resultType="com.quhong.data.dto.RankingDTO">
        select from_uid uid, a.name name, a.head_icon head, sum(gnumber) count, a.rid rid
        from kissu.t_gift_record tgr
                     left join kissu.actor a on tgr.from_uid = a.uid
                where tgr.ctime >= #{start}
                  and tgr.ctime &lt; #{end}
                  and tgr.from_uid = #{uid}
        <if test="fromType != -1">
            and tgr.from_type = #{fromType}
        </if>
        and tgr.giftid = #{giftId}
                limit 1
    </select>

    <select id="selectListByDay" resultType="com.quhong.dao.datas.GiftRecordData">
        select
        <include refid="t_gift_record_columns"/>
        from kissu.t_gift_record
        where ctime >= #{start}
          and ctime &lt; #{end}
        limit #{offset},#{pageSize}
    </select>

    <select id="selectNoDealGiftRecordListBy" resultType="com.quhong.dao.datas.GiftRecordData">
        select
        <include refid="t_gift_record_columns"/>
        from kissu.t_gift_record
        where last_time > #{startLastTime}
          and last_time &lt;= #{endLastTime}
          and ctime >= #{ctime}
          and status = 0
          and gtype = 1
          and from_type = 1
    </select>
    <select id="selectLargeGiftByCid" resultType="com.quhong.dao.datas.GiftRecordData">
        select *
        from kissu.t_gift_record
        where roomId = #{cid}
          and gtype = 1
        limit 1
    </select>
    <select id="selectAllLargeGiftByCid" resultType="com.quhong.dao.datas.GiftRecordData">
        select *
        from kissu.t_gift_record
        where roomId = #{cid}
          and gtype = 1
          and status = 0
        order by ctime
    </select>
    <select id="selectPkContributionUserRank" resultType="com.quhong.data.pk.PkUserData">
        SELECT from_uid             AS uid,
               SUM(pk_rate * real_price) AS contributionValue,
               SUM(pk_rate * real_price) AS realContributionValue
        FROM t_gift_record
        WHERE (pid = #{pid} AND roomId = #{roomId} AND real_price > 0)
        GROUP BY from_uid
        ORDER BY contributionValue DESC
        LIMIT 3;
    </select>

    <select id="selectPkContributionRoom" resultType="com.quhong.data.pk.PkContributionData">
        SELECT g.roomId                 AS roomId,
               SUM(g.pk_rate * g.real_price) AS contributionValue
        FROM t_gift_record g
        WHERE g.pid = #{pid}
          AND g.real_price > 0
        GROUP BY roomId
    </select>

    <select id="queryRoomGiftGainSum" resultType="com.quhong.data.vo.CountVO">
        select to_uid    as uid,
               sum(real_gain) as count
        from kissu.t_gift_record
        where mtime >= #{time}
          and mtime &lt; #{endTime}
          and from_type = #{fromType}
          and to_uid = #{uid}
          and gain > 0
    </select>

    <select id="getTopGiftPriceRanking" resultType="com.quhong.data.dto.RankingDTO">
        select tgr.${uidType}               as uid,
               a.name                       as name,
               a.head_icon                  as head,
               sum(tgr.gnumber * tgr.real_price) as `count`,
               sum(tgr.gnumber * tgr.real_price) as realCount,
               a.rid                        as rid
        from kissu.t_gift_record tgr
                     left join kissu.actor a on tgr.${uidType} = a.uid
                     left join kissu.actor_external ae on tgr.${uidType} = ae.uid
        <where>
            tgr.ctime >= #{start}
                    and tgr.ctime &lt; #{end}
            <if test="giftIdSet != null and giftIdSet.size > 0">
                and tgr.giftid in
                <foreach collection="giftIdSet" open='(' item='giftId' separator=',' close=')'>
                    #{giftId}
                </foreach>
            </if>
            <if test="uid != null">
                and tgr.${uidType} = #{uid}
            </if>
            <if test="fromType != null and fromType.size > 0">
                and tgr.from_type in
                <foreach collection="fromType" open='(' item='item' separator=',' close=')'>
                    #{item}
                </foreach>
            </if>
            <if test="channelSet != null and channelSet.size > 0">
                and a.channel in
                <foreach collection="channelSet" open='(' item='channel' separator=',' close=')'>
                    #{channel}
                </foreach>
            </if>
            <if test="countryCodeSet != null and countryCodeSet.size > 0">
                and a.country_code in
                <foreach collection="countryCodeSet" open='(' item='countryCode' separator=',' close=')'>
                    #{countryCode}
                </foreach>
            </if>
            <if test="excludeChannel != null and excludeChannel.length > 0">
                and a.channel &lt;> #{excludeChannel}
            </if>
            <if test="excludeChannelSet != null and excludeChannelSet.size > 0">
                and a.channel not in
                <foreach collection="excludeChannelSet" open='(' item='ignoreChannel' separator=',' close=')'>
                    #{ignoreChannel}
                </foreach>
            </if>
            and ae.is_tester &lt;> 1
        </where>
        group by tgr.${uidType}
        order by count desc
        <if test="top != null and top != 0">
            limit #{top}
        </if>
    </select>

    <select id="getActivityGiftPriceRanking" resultType="com.quhong.data.dto.RankingDTO">
        select tgr.${uidType}               as uid,
        a.name                       as name,
        a.head_icon                  as head,
        sum(tgr.gnumber * tgr.real_price) as `count`,
        sum(tgr.gnumber * tgr.real_price) as realCount,
        a.rid                        as rid
        from kissu.t_gift_record_2025_06 tgr
        left join kissu.actor a on tgr.${uidType} = a.uid
        left join kissu.actor_external ae on tgr.${uidType} = ae.uid
        <where>
            tgr.ctime >= #{start}
            and tgr.ctime &lt; #{end}
            <if test="giftIdSet != null and giftIdSet.size > 0">
                and tgr.giftid in
                <foreach collection="giftIdSet" open='(' item='giftId' separator=',' close=')'>
                    #{giftId}
                </foreach>
            </if>
            <if test="uid != null">
                and tgr.${uidType} = #{uid}
            </if>
            <if test="fromType != null and fromType.size > 0">
                and tgr.from_type in
                <foreach collection="fromType" open='(' item='item' separator=',' close=')'>
                    #{item}
                </foreach>
            </if>
            <if test="channelSet != null and channelSet.size > 0">
                and a.channel in
                <foreach collection="channelSet" open='(' item='channel' separator=',' close=')'>
                    #{channel}
                </foreach>
            </if>
            <if test="countryCodeSet != null and countryCodeSet.size > 0">
                and a.country_code in
                <foreach collection="countryCodeSet" open='(' item='countryCode' separator=',' close=')'>
                    #{countryCode}
                </foreach>
            </if>
            <if test="excludeChannel != null and excludeChannel.length > 0">
                and a.channel &lt;> #{excludeChannel}
            </if>
            <if test="excludeChannelSet != null and excludeChannelSet.size > 0">
                and a.channel not in
                <foreach collection="excludeChannelSet" open='(' item='ignoreChannel' separator=',' close=')'>
                    #{ignoreChannel}
                </foreach>
            </if>
            and ae.is_tester &lt;> 1
        </where>
        group by tgr.${uidType}
        order by count desc
        <if test="top != null and top != 0">
            limit #{top}
        </if>
    </select>

    <select id="getTopGiftRanking" resultType="com.quhong.data.dto.RankingDTO">
        select tgr.${uidType}   as uid,
               a.name           as name,
               a.head_icon      as head,
               sum(tgr.gnumber) as count,
               a.rid            as rid
        from kissu.t_gift_record tgr
                     left join kissu.actor a on tgr.${uidType} = a.uid
        <where>
            tgr.ctime >= #{start}
                    and tgr.ctime &lt; #{end}
            <if test="uid != null">
                and tgr.${uidType} = #{uid}
            </if>
            <if test="fromType != null and fromType.size > 0">
                and tgr.from_type in
                <foreach collection="fromType" open='(' item='item' separator=',' close=')'>
                    #{item}
                </foreach>
            </if>
            <if test="channel != null and channel.length > 0">
                and (a.channel = #{channel})
            </if>
            <if test="giftIdSet != null and giftIdSet.size > 0">
                and tgr.giftid in
                <foreach collection="giftIdSet" open='(' item='giftId' separator=',' close=')'>
                    #{giftId}
                </foreach>
            </if>
            <if test="countryCodeSet != null and countryCodeSet.size > 0">
                and a.country_code in
                <foreach collection="countryCodeSet" open='(' item='countryCode' separator=',' close=')'>
                    #{countryCode}
                </foreach>
            </if>
            <if test="excludeChannel != null and excludeChannel.length > 0">
                and a.channel &lt;> #{excludeChannel}
            </if>
        </where>
        group by tgr.${uidType}
        order by count desc
        limit #{top}
    </select>

    <select id="selectPersonGiftRecord" resultType="com.quhong.data.vo.CountVO">
        select from_uid       as uid,
               sum(gain)      as `count`,
               sum(real_gain) as realCount
        from kissu.t_gift_record as gr
                 left join kissu.actor as ac on gr.from_uid = ac.uid
        <where>
            to_uid = #{uid}
            <if test="startTime != null">
                and mtime >= #{startTime}
            </if>
            <if test="endTime != null">
                and mtime &lt; #{endTime}
            </if>
            and real_gain > 0
            and ac.del = 0
        </where>
        group by from_uid
    </select>

    <select id="getPKGiftRankList" resultType="com.quhong.data.vo.HostIncomeVO">
        select left(tgr.roomId, 26)         as uid,
               sum(tgr.gnumber * tgr.real_price) as income,
               sum(tgr.gnumber * tgr.real_price) as realIncome
        from kissu.t_gift_record tgr
                     left join kissu.actor a on tgr.from_uid = a.uid
                     left join kissu.actor_external ae on tgr.from_uid = ae.uid
        <where>
            tgr.ctime >= #{start}
                    and tgr.ctime &lt; #{end}
                    and tgr.roomId != ""
                    and from_type in (3, 4)
            <if test="channel != null and channel.length > 0">
                and (a.channel = #{channel})
            </if>
            <if test="excludeChannel != null and excludeChannel.length > 0">
                and a.channel &lt;> #{excludeChannel}
            </if>
            and ae.is_tester &lt;> 1
        </where>
        group by left(tgr.roomId, 26)
        order by realIncome desc
    </select>

    <select id="getPKGiftRankListByRoomId" resultType="com.quhong.data.vo.HostIncomeVO">
        select tgr.from_uid                 as uid,
               sum(tgr.gnumber * tgr.real_price) as income,
               sum(tgr.gnumber * tgr.real_price) as realIncome
        from kissu.t_gift_record tgr
                     left join kissu.actor_external ae on tgr.from_uid = ae.uid
        <where>
            tgr.ctime >= #{start}
                    and tgr.ctime &lt; #{end}
                    and from_type in (3, 4)
                    and tgr.roomId in
            <foreach collection="roomIdList" open='(' item='roomId' separator=',' close=')'>
                #{roomId}
            </foreach>
            and ae.is_tester &lt;> 1
        </where>
        group by from_uid
        order by income desc
    </select>

    <select id="queryGiftGainSum" resultType="com.quhong.data.vo.CountVO">
        select to_uid    as uid,
               sum(real_gain) as `count`,
               sum(real_gain) as realCount
        from kissu.t_gift_record
                where to_uid in
        <foreach collection="toUidSet" open='(' item="uid" separator=',' close=')'>
            #{uid}
        </foreach>
        and from_type = #{fromType}
        and mtime >= #{startTime}
        and mtime &lt; #{endTime}
        and gain > 0
        and status = 6
                group by to_uid
    </select>
    <select id="selectGiftGain" resultType="java.lang.Double">
        select sum(real_gain)
        from kissu.t_gift_record
                where to_uid = #{uid}
        <if test="startTime != null">
            and mtime >= #{startTime}
        </if>
        <if test="endTime != null">
            and mtime &lt;= #{endTime}
        </if>
        and status = 6
    </select>

    <select id="queryUidSetByToUidAndDurationAndFromType" resultType="java.lang.String">
        select from_uid from kissu.t_gift_record
        where to_uid = #{uid}
        and mtime >= #{time}
        and mtime &lt; #{endTime}
        and from_type = #{fromType}
        and status = 6
        group by from_uid
    </select>

    <select id="missRoseTop3Logic"  resultType="com.quhong.data.vo.CountVO">
        select to_uid uid, sum(gnumber * real_price) `count`,
               sum(gnumber * real_price) realCount
        from kissu.t_gift_record
        where to_uid in ('64abdbed9e65082cbefba48d', '64fc656c99129358210c4350', '65b11887738b1145aff1c6f6', '669bdb7f07f98276f180404f')
          and giftid in (671, 672, 673)
          and ctime >= 1725647400
          and ctime &lt; 1725820200
        and status = 6
        group by to_uid
        order by count desc

    </select>

</mapper>
