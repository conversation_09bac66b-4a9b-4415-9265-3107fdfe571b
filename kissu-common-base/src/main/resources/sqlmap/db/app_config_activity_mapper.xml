<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.dao.mapper.db.AppConfigActivityMapper">

    <select id="queryListByCondition" resultType="com.quhong.dao.datas.app.config.AppConfigActivityData">
        select *
        from kissu.app_config_activity
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="activityCode != null">
                and activity_code = #{activityCode}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="rankType != null">
                and rank_type = #{rankType}
            </if>
            <if test="startTime != null">
                and start_time = #{startTime}
            </if>
            <if test="endTime != null">
                and end_time = #{endTime}
            </if>
            <if test="valid != null">
                and valid = #{valid}
            </if>

        </where>
    </select>

    <update id="updateOverGroupEventToInvalid">
        update kissu.app_config_activity
        set valid = 0,
            mtime = #{currTime},
            operator = #{operator}
        where end_time &lt; #{limitTime}
          and event_group = #{group}
          and valid = 1
    </update>
</mapper>
