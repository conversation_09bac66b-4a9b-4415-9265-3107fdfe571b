package com.quhong.common.enums;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;

import java.util.HashMap;
import java.util.Map;

public class HttpCode {
    public static final HttpCode SUCCESS = new HttpCode(0, "");
    public static final HttpCode REPURCHASE_DAU_SELECT_ERROR = new HttpCode(-1, "condition please active one month");
    public static final HttpCode NEED_RECHARGE = new HttpCode(1, "Insufficient Balance. Please recharge.");
    public static final HttpCode BALANCE_NOT_ENOUGH = new HttpCode(1, "your balance is not enough");
    public static final HttpCode SIGN_INVALID = new HttpCode(3, "sign decode error");
    public static final HttpCode AGENT_ALREADY_EXIST = new HttpCode(8, "agent already exist in system");
    public static final HttpCode AGENT_NOT_EXIST = new HttpCode(9, "agent not exist");
    public static final HttpCode ROOM_ITEM_NOT_EXIST = new HttpCode(1144, "The goods don't exist");
    public static final HttpCode ROOM_NOT_EXIST = new HttpCode(1155, "Room don't exist");
    public static final HttpCode CODE_NOT_ALLOW = new HttpCode(10, "Your account has been blocked. If you have any questions, please contact customer service.");
    public static final HttpCode CODE_PARAMETER_ERROR = new HttpCode(12, "login fail, please try again.");
    public static final HttpCode HTTP_SESSION_INVALID = new HttpCode(103, "session invalid");
    public static final HttpCode BANNED_BY_ADMIN = new HttpCode(14, "Your account was banned by admin.");
    public static final HttpCode OTHER_BANNED_BY_ADMIN = new HttpCode(14, "This account has been banned for violating community rules.");
    public static final HttpCode DEL_OPERATION_ERROR = new HttpCode(14, "delete failed");
    public static final HttpCode ACCOUNT_OR_PASSWORD_FAILED = new HttpCode(15, "Your account or password is incorrect, please try again.");
    public static final HttpCode VIDEO_ALREADY_EXIST = new HttpCode(16, "video already exist");
    public static final HttpCode CODE_VERSION_CONTROL = new HttpCode(82, "Sorry, your app version is not up to date, please update to the latest version");
    public static final HttpCode CAN_NOT_FIND_MSG_TYPE = new HttpCode(100, "can not find msg type");
    public static final HttpCode REPEATED_GET_REWARDS = new HttpCode(101, "you have already received the reward");
    public static final HttpCode CAN_NOT_RECEIVE_REWARD = new HttpCode(102, "Unable to receive rewards");
    public static final HttpCode CODE_BLOCKED_WINDOWS = new HttpCode(104, "");

    public static final HttpCode HTTP_IO_ERROR = new HttpCode(301, "http io error");
    public static final HttpCode HTTP_STATUS_ERROR = new HttpCode(302, "http status error");

    public static final HttpCode DID_NOT_FIND_MSG = new HttpCode(1001, "can not find msgRecord");
    public static final HttpCode LOGIN_INVALID = new HttpCode(1001, "Please login.");
    public static final HttpCode SERVER_ERROR = new HttpCode(1002, "server error");
    public static final HttpCode PARAM_ERROR = new HttpCode(1010, "params error");
    public static final HttpCode TIME_OUT = new HttpCode(1109, "time out");
    public static final HttpCode REGISTER_ERROR = new HttpCode(1007, "register failed.");
    public static final HttpCode REGISTER_MIGRATE_LIMIT_ERROR = new HttpCode(1008, "This app is closed. And your account has been transferred. Try it for more fun!");
    public static final HttpCode SESSION_INVALID = new HttpCode(1003, "session invalid");
    public static final HttpCode TRANSLATE_FAILED = new HttpCode(1003, "translate failed");
    public static final HttpCode RECOGNIZE_FAILED = new HttpCode(1004, "recognize audo failed");
    public static final HttpCode TRANSLATE_TOO_LONG = new HttpCode(1005, "Text is too long");
    public static final HttpCode PRIVILEGE_ERROR = new HttpCode(1005, "Insufficient permissions");
    public static final HttpCode AWS_UPLOAD_ERROR = new HttpCode(1006, "upload file failed");
    public static final HttpCode LOGIN_APPLE_CODE_INVALID = new HttpCode(2001, "Your third account token is invalid, please sign in to your Third Account first before your sign in app.");
    public static final HttpCode LOGIN_HOST_FACEBOOK_INVALID = new HttpCode(2002, "Please choose google login");
    public static final HttpCode CAN_NOT_GREETING = new HttpCode(6001, "greeting count use out");
    public static final HttpCode EMPTY_DATA = new HttpCode(6001, "");
    public static final HttpCode IP_FROM_CHINA_FAILED = new HttpCode(9999, "This application is not supported locally");
    public static final HttpCode HOST_ACCOUNT_FROZEN = new HttpCode(8888, "You will be blocked for 1 hour as more than 5 times \"not show face\"!");
    public static final HttpCode INSUFFICIENT_USER_COINS = new HttpCode(1009, "your gold is not enough，please recharge and try again");
    public static final HttpCode ILLEGAL_OPERATION = new HttpCode(1010, "illegal operation");
    public static final HttpCode EVENT_NOT_START = new HttpCode(1081, "The activity has not yet started");
    public static final HttpCode NOT_JOIN = new HttpCode(1082, "your not join this event");

    public static final HttpCode AUTH_ERROR = new HttpCode(1011, "No right to operate");
    public static final HttpCode INVITE_CODE_ERROR = new HttpCode(1012, "invitation code error, you need to fill in the correct invitation code!");
    public static final HttpCode REPEAT_REGISTER_INVALID = new HttpCode(1099, "Only allows one account to log in, and this device already have account of ");
    public static final HttpCode ACTOR_NOT_EXIST = new HttpCode(1098, "actor is not exist");
    public static final HttpCode HAS_BEEN_RECEIVED = new HttpCode(1097, "The reward has been received!");
    public static final HttpCode VERSION_IS_LOW = new HttpCode(2099, "The app version is low, please download and install the latest version.");
    public static final HttpCode PARK_NOTICE = new HttpCode(2098, "Sorry! The service is under maintenance and is expected to last about 1-2 hours.");
    public static final HttpCode FILE_FORMAT_ERROR = new HttpCode(1019, "file format error.");
    public static final HttpCode REFUSE_TO_RECEIVE = new HttpCode(6001, "refuse to receive");
    public static final HttpCode TYPE_ERROR = new HttpCode(6001, "Type error");
    public static final HttpCode CODE_AD = new HttpCode(9008, "Message illegal, Please check it!");
    public static final HttpCode USER_NOT_EXISTS = new HttpCode(11, "User not exists");
    public static final HttpCode OTHER_NOT_EXISTS = new HttpCode(11, "other not exist");
    public static final HttpCode CODE_SENSITIVE_WORD_FILTERING = new HttpCode(9002, "The message send failed . The message you sent contains sensitive words. '\n" + "Please correct and try again.");
    public static final HttpCode CODE_HOSTS_CANNOT_SEND_MESSAGE_EACH_OTHER = new HttpCode(9010, "You only have the right to send message to male user.");
    public static final HttpCode CODE_BIND_FAILED = new HttpCode(9019, "Verification code error.");
    public static final HttpCode CODE_IMAGE_DETECT_FAILED = new HttpCode(9027, "Send failed. Photo suspected of violation.");
    public static final HttpCode NOT_NUMBER_FORMAT = new HttpCode(9028, "Please input number,not input string.");
    public static final HttpCode GOOGLE_LOGIN_FAILED = new HttpCode(9099, "google login error.");
    public static final HttpCode HOST_DEL_ACCOUNT_TIP = new HttpCode(9030, "Please contact customer service to delete the account.");
    public static final HttpCode HOST_SEND_GIFT_TO_HOST = new HttpCode(9601, "Sending gifts is not allowed between hosts.");
    public static final HttpCode GIFT_IS_NOT_EXIST = new HttpCode(9602, "Gift not exist or valid.");
    public static final HttpCode NOT_ENOUGH_TO_SEND_GIFT = new HttpCode(9024, "After the cost of the current call，your balance is not enough to send this gift !");

    public static final HttpCode CODE_LOGIN_FAILED_PHONE_REGISTER_CODE_ERROR = new HttpCode(9030,"Verification code is wrong, please re-enter.");
    public static final HttpCode CODE_LOGIN_FAILED_PHONE_REGISTER_CODE_ERROR_TIMES = new HttpCode(9031, "Due to multiple incorrect verification codes, for the security of your account, you will not be able to use this mobile phone number to continue registration within 12 hours. Please consult your agent for details.");
    public static final HttpCode CODE_LOGIN_FAILED_PHONE_LOGIN_CODE_ERROR_TIME = new HttpCode(9032, "Due to multiple incorrect verification codes, for the security of your account, you will not be able to use this mobile phone number to continue registration within 12 hours. Please consult your agent for details.");
    public static final HttpCode CODE_LOGIN_FAILED_PHONE_REGISTER_CODE_LIMIT_TIMES = new HttpCode(9033, "Today’s number has been used up. Please consult your agent for details.");
    public static final HttpCode CODE_LOGIN_FAILED_PHONE_LOGIN_CODE_LIMIT_TIMES = new HttpCode(9034, "Today’s number has been used up. Please consult your agent for details.");
    public static final HttpCode CODE_LOGIN_FAILED_PHONE_DEVICE_LIMIT = new HttpCode(9035, "For security reasons, one device cannot currently register multiple accounts.");
    public static final HttpCode CODE_LOGIN_FAILED_PHONE_CODE_INVALID = new HttpCode(9036, "Verification code is abnormal. Please try again.");
    public static final HttpCode CODE_BIND_FAILED_PHONE_HAD_EXISTED = new HttpCode(9037, "The mobile number already exists.");
    public static final HttpCode CODE_BIND_PHONE_REPEAT_SEND_WAIT_TIME = new HttpCode(9038, "");


    public static final HttpCode ACTOR_BANK_CARD_HAS_EXIST = new HttpCode(9066, "you have already linked this card");
    //
    // activity 模块 httpCode 从 10000 起递增
    //
    public static final HttpCode HOST_CAN_NOT_SEND_GIFT = new HttpCode(1234, "You can not send gift");
    public static final HttpCode BANNER_UPLOAD_LIMIT = new HttpCode(1235, "Your photo is under review, please try it later");
    public static final HttpCode UNAUTHORIZED_ACCESS = new HttpCode(1236, "Unauthorized access");
    public static final HttpCode HOST_CAN_NOT_EVALUATE = new HttpCode(10, "Host can not do evaluate");
    public static final HttpCode ERROR_CALL = new HttpCode(6001, "Error Call");
    public static final HttpCode CALL_EVALUATED = new HttpCode(6001, "Sorry, This call has been evaluated");
    public static final HttpCode LINK_EXPIRED = new HttpCode(6003, "This link has expired, please contact the sharer for a new link.");
    public static final HttpCode MIGRATE_FAILED = new HttpCode(6005, "");

    public static final HttpCode INNER_CURRENCY_ERROR = new HttpCode(2237, "inner currency error");
    public static final HttpCode FUNCTION_UNAVAILABLE = new HttpCode(1234, "Function unavailable");

    public static final HttpCode LORD_LEVEL_LOW = new HttpCode(1237, "you lord level is low");

    public static final HttpCode AGENT_INVITE_USER_CODE_NOT_MATCH = new HttpCode(1405, "incorrect host code.");
    public static final HttpCode AGENT_INVITE_USER_REPEAT = new HttpCode(1406, "Invitation already sent and cannot be sent again within 24 hours.");
    public static final HttpCode AGENT_INVITE_USER_OTHER_ERROR = new HttpCode(1407, "invite error");
    public static final HttpCode APPLY_INTERVAL_ERROR = new HttpCode(1466, "");
    public static final HttpCode MEMBER_SHIPS_NO_RIGHTS = new HttpCode(1468, "no rights.");





    public static final HttpCode COOPERATION_DEFAULT_ERROR = new HttpCode(100010, "server error");

    /**
     * app无效
     */
    public static final HttpCode COOPERATION_APP_INVALID = new HttpCode(100011, "appId invalid");

    /**
     * 时间戳超限
     */
    public static final HttpCode COOPERATION_TIMESTAMP_EXCEEDED = new HttpCode(100012, "Request timestamp exceeded");

    /**
     * ip没权限
     */
    public static final HttpCode COOPERATION_IP_LIMIT = new HttpCode(100013, "ip has no access.");

    /**
     * 签名错误
     */
    public static final HttpCode COOPERATION_SIGN_INVALID = new HttpCode(100014, "invalid sign");

    /**
     * 参数不全
     */
    public static final HttpCode COOPERATION_MISSING_PARAMETERS = new HttpCode(100015, "Missing parameters");

    /**
     * 用户不存在
     */
    public static final HttpCode COOPERATION_USER_NOT_EXIST = new HttpCode(100016, "user not exist");
    public static final HttpCode EVENT_ENDED = new HttpCode(200002, "event ended");
    public static final HttpCode CHANNEL_NOT_JOIN_EVENT = new HttpCode(200003, "your app not join this event");

    public static final HttpCode ITEM_NOT_ENOUGH = new HttpCode(200004, "item not enough");
    public static final HttpCode NOT_JOIN_EVENT = new HttpCode(200005, "You are not the user targeted by this event");

    private int code;
    private String msg;
    @JSONField(serialize = false)
    private Map<Integer, String> msgMap;
    @JSONField(serialize = false)
    /**
     * 是否需要告警
     */
    private boolean warn;

    public HttpCode() {
    }

    public HttpCode(HttpCode src) {
        this.code = src.code;
        this.msg = src.msg;
        if (src.msgMap != null) {
            this.msgMap = new HashMap<>(src.msgMap);
        }
    }

    public HttpCode(int code, String... langMsg) {
        this(code, false, langMsg);
    }

    public HttpCode(int code, boolean warn, String... langMsg) {
        this.code = code;
        this.warn = warn;
        if (langMsg.length > 0) {
            this.msg = langMsg[0];
        }
        this.msgMap = new HashMap<>();
        int i = 0;
        for (String element : langMsg) {
            this.msgMap.put(i, element);
            i++;
        }
    }

    /**
     * 使用createHttpCode替代
     * @param code
     * @return
     */
    @Deprecated
    public HttpCode setCode(int code) {
        this.code = code;
        return this;
    }

    /**
     * 使用createHttpCode替代
     * @param msg
     * @return
     */
    @Deprecated
    public HttpCode setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public String getMsg(int slang) {
        String ret = this.msgMap.get(slang);
        if (ret == null) {
            return msg;
        }
        return ret;
    }

    public boolean isWarn() {
        return warn;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    /**
     * 创建自定义的HttpCode
     *
     * @param src
     * @param warn
     * @param msg
     * @return
     */
    public static HttpCode createHttpCode(HttpCode src, boolean warn, String msg) {
        return new HttpCode(src.getCode(), warn, msg);
    }
}
