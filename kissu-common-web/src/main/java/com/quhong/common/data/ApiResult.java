package com.quhong.common.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.quhong.common.enums.HttpCode;
import lombok.ToString;

/**
 * create by maoyule on 2020/10/25
 */
@ToString(callSuper = true)
public class ApiResult<T> {
    @JSONField(serialize = false)
    @JsonIgnore
    private HttpCode httpCode;
    private int code;
    private String msg;
    private T data;

    public ApiResult() {
        this(HttpCode.SUCCESS, null);
    }

    public ApiResult(HttpCode httpCode) {
        this(httpCode, null);
    }

    public ApiResult(HttpCode httpCode, T data) {
        this.httpCode = httpCode;
        this.code = httpCode.getCode();
        this.msg = httpCode.getMsg();
        this.data = data;
    }

    public static ApiResult<Boolean> fail(String msg) {
        return new ApiResult<>(HttpCode.createHttpCode(HttpCode.SERVER_ERROR, false, msg));
    }

    @JSONField(serialize = false)
    @JsonIgnore
    public boolean isError() {
        return code != HttpCode.SUCCESS.getCode();
    }

    @JSONField(serialize = false)
    @JsonIgnore
    public HttpCode getHttpCode() {
        if (httpCode == null) {
            httpCode = new HttpCode(code, msg);
        }
        return httpCode;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.httpCode = null;
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <T> ApiResult<T> getOk() {
        return new ApiResult<T>();
    }

    public static <T> ApiResult<T> getOk(T data) {
        return new ApiResult<T>(HttpCode.SUCCESS, data);
    }

    public static <T> ApiResult<T> getError(HttpCode code) {
        return new ApiResult<T>(code);
    }

    public static <T> ApiResult<T> getError(HttpCode code, T data) {
        return new ApiResult<T>(code, data);
    }
}
