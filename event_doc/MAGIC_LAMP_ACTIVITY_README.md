# 神灯活动 V2506 配置说明

## 活动概述
神灯活动是一个送礼排行榜活动，包含送礼榜和收礼榜两个排行榜，用户通过赠送神灯礼物来获得排行榜积分。同时包含节点任务系统，用户赠送指定数量的神灯后可获得节点奖励。

## 技术架构特点
- 模仿 EventFoolController 和 EventFoolService 的代码风格
- 奖励发放使用 RewardService#giveAwardsKeyReward 方法（而非传统的 GiveOutRewardService）
- 通过礼包key配置奖励，更加灵活和统一
- 支持节点任务系统，用户累计送礼达到指定数量可获得节点奖励
- 优化了 BaseNodeRewardRedis 和 EventBaseRankService，添加支持 RewardService 的新方法

## 活动配置

### 1. 事件码配置
已在 EventCode.java 中定义：
```java
@ConstantFiledDesc(name = "神灯活动2506（10115）")
int EVENT_MAGIC_LAMP_2506 = 10115;
```

### 2. 礼包key配置规则
需要在数据库中配置以下礼包key：

#### 送礼榜奖励礼包key
- `magic_lamp_send_rank_{活动码}_1` - 送礼榜第1名奖励
- `magic_lamp_send_rank_{活动码}_2` - 送礼榜第2名奖励  
- `magic_lamp_send_rank_{活动码}_3` - 送礼榜第3名奖励
- `magic_lamp_send_rank_{活动码}_4` - 送礼榜第4~10名奖励

#### 收礼榜奖励礼包key  
- `magic_lamp_receive_rank_{活动码}_1` - 收礼榜第1名奖励
- `magic_lamp_receive_rank_2` - 收礼榜第2名奖励
- `magic_lamp_receive_rank_3` - 收礼榜第3名奖励
- `magic_lamp_receive_rank_4` - 收礼榜第4~10名奖励
#### 节点任务奖励礼包key
- `magic_lamp_node_task_1` - 赠送1个神灯节点奖励
- `magic_lamp_node_task_5` - 赠送5个神灯节点奖励
- `magic_lamp_node_task_10` - 赠送10个神灯节点奖励
- `magic_lamp_node_task_25` - 赠送25个神灯节点奖励
- `magic_lamp_node_task_50` - 赠送50个神灯节点奖励

### 3. 活动配置说明
在 app_config_activity 表中配置活动信息：
- event_code: 10115
- activity_code: EVENT_MAGIC_LAMP_2506
- start_time: 活动开始时间戳
- end_time: 活动结束时间戳
- channel: 支持的渠道（如：Tikko安卓,Tikko IOS）

### 4. 神灯礼物配置
需要在代码中配置神灯礼物ID：
```java
public static final Integer MAGIC_LAMP_GIFT_ID = 2077; // 根据实际神灯礼物ID修改
```

## API接口

### 1. 页面初始化
- **接口**: `/activity/magic_lamp/v2506/pageInit`
- **方法**: GET
- **功能**: 获取活动基本信息和用户累计送礼个数
- **返回字段**:
  - `startTime`: 活动开始时间
  - `endTime`: 活动结束时间  
  - `eventUrl`: 活动链接
  - `sentGiftCount`: 用户累计送礼个数

### 2. 排行榜查询
- **接口**: `/activity/magic_lamp/v2506/rank`
- **方法**: GET
- **参数**: 
  - `rankType`: 排行榜类型（1=送礼榜，2=收礼榜）
- **功能**: 查询排行榜数据

### 3. 测试结算
- **接口**: `/activity/magic_lamp/v2506/settlement`
- **方法**: GET
- **参数**:
  - `rankType`: 排行榜类型（1=送礼榜，2=收礼榜）
- **功能**: 测试环境下手动触发排行榜奖励结算

## 技术实现特点

### 1. 节点任务系统
- 使用 `BaseNodeRewardRedis` 来统计用户累计送礼数量
- 新增 `increaseAndGetCanGetTaskConfigWithAwardsKey` 方法支持 RewardService
- 自动检测节点达成并发放奖励

### 2. 排行榜系统
- 使用 `EventBaseRankService` 管理排行榜数据
- 新增 `rankRewardsWithAwardsKey` 方法支持通过礼包key发放奖励
- 支持送礼榜和收礼榜两个独立排行榜

### 3. MQ消息处理
- 监听送礼成功MQ消息 `SendGiftSuccessMsgData`
- 自动更新排行榜积分和节点任务进度
- 实时发放节点任务奖励

### 4. 奖励发放机制
- 统一使用 `RewardService#giveAwardsKeyReward` 方法
- 支持个人弹窗、全服弹窗、房间飘屏、全服横幅等多种通知方式
- 完整的错误处理和监控报警

## 部署注意事项

1. **礼包key配置**: 确保所有礼包key都在数据库中正确配置
2. **神灯礼物ID**: 根据实际情况修改 `MAGIC_LAMP_GIFT_ID` 常量
3. **活动时间**: 确保活动配置的时间范围正确
4. **渠道配置**: 确保支持的渠道配置正确
5. **MQ消息**: 确保MQ消息监听器正确配置
6. **测试验证**: 在测试环境充分测试排行榜和节点任务功能

## 兼容性说明

- 新增方法不影响原有的奖励发放逻辑
- `BaseNodeRewardRedis` 和 `EventBaseRankService` 的原有方法保持不变
- 可以与其他活动并行运行，互不影响

## 与传统活动的区别

### 优势
1. **统一的奖励管理**: 使用礼包key配置，便于运营人员管理
2. **灵活的通知配置**: 支持多种通知方式的独立配置  
3. **完整的日志记录**: 自动记录奖励发放和变动日志
4. **不影响现有逻辑**: 完全不影响其他活动的奖励发放逻辑

### 注意事项
1. 需要提前在数据库中配置好所有的礼包key
2. 礼包key的命名需要严格按照约定格式
3. 测试时需要验证奖励发放是否正确

## 开发时间
活动时间：2025年6月18日 - 2025年6月24日 
